package dima.duckduckgoAiChat

import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import strikt.api.expectThat

@Serializable
private data class Response(
    val message: String? = null,
    /**
     * Will be null for the first data.
     */
    val model: String? = null
)

private enum class Model(val apiName: String) {
    GPT4oMini("gpt-4o-mini"),

    /**
     * Text-only.
     * https://huggingface.co/meta-llama/Llama-3.3-70B-Instruct
     *
     * Input Modalities: Multilingual Text
     * Output Modalities: Multilingual Text and code
     * Context Length: 128k
     * GQA: Yes
     * Token Count: 15T+
     * Knowledge Cutoff: December 2023
     */
    Llama3_3_70B("meta-llama/Llama-3.3-70B-Instruct-Turbo"),

    /**
     * https://docs.anthropic.com/en/docs/about-claude/models#model-comparison-table
     *
     * Context window: 200k
     * Max output: 8192 tokens
     * Training data cut-off: July 2024
     */
    Claude3Haiku("claude-3-haiku-20240307"),
    o3Mini("o3-mini"),

    /**
     * https://huggingface.co/mistralai/Mistral-Small-24B-Instruct-2501
     *
     * Context window: 32k
     */
    MistralSmall3("mistralai/Mistral-Small-24B-Instruct-2501"),
}

@Serializable
private data class Message(
    val role: String,
    val content: String
)

@Serializable
private data class RequestBody(
    val model: String,
    val messages: List<Message>
)

/**
 * https://duckduckgo.com/?q=DuckDuckGo+AI+Chat&ia=chat&duckai=1
 *
 * Ported parts from
 * https://github.com/mrgick/duck_chat/blob/main/duck_chat/api.py
 */
class DuckDuckGoAiChat {

    @Test
    @Disabled
    fun testDuckAi() {
        val json = Json {
            ignoreUnknownKeys = true
        }
        val client = createHttpClientWithLocalhostProxy()
        runBlocking {
            val statusResponse = client.get("https://duckduckgo.com/duckchat/v1/status") {
                header("x-vqd-accept", "1")
            }
            val statusHeaders = statusResponse.headers
            val vqdHeader = statusHeaders["x-vqd-4"]
            expectThat(statusResponse.status == HttpStatusCode.OK)
            val response: HttpResponse = client.post("https://duckduckgo.com/duckchat/v1/chat") {
                headers.append("Content-Type", "application/json")
                headers.append("x-vqd-4", vqdHeader!!)
                val requestBody = RequestBody(
                    model = Model.Claude3Haiku.apiName,
                    // If assistant is used, the new returned x-vqd-4 header needs to be used.
                    // You can't just use role = "assistant" without the entire sequence.
                    messages = listOf(
                        Message(role = "user", content = "just hi"),
//                        Message(role = "assistant", content = "hi"),
//                        Message(role = "user", content = "now in German"),
                    )
                )
                setBody(Json.encodeToString(requestBody))
            }
            expectThat(response.status == HttpStatusCode.OK)
            println(response.bodyAsText())
            val lines = response.bodyAsText().lines().filter {
                it.isNotBlank() && it != "data: [DONE]"
            }
            val responses = lines.map {
                json.decodeFromString<Response>(it.removePrefix("data: "))
            }.filter {
                // filter out first data response which has an empty message
                it.message != null && it.message != ""
            }.joinToString("") {
                it.message!!
            }
            println(responses)
        }
    }
}