@file:Suppress("PropertyName")

package dima.transcribe

import dima.settings
import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.io.File

@Serializable
data class TranscriptionResponse(
    val task: String,
    val language: String,
    val duration: Double,
    val text: String,
    val segments: List<Segment>,
    val x_groq: XGroq
)

@Serializable
data class Segment(
    val id: Int,
    val seek: Int,
    val start: Double,
    val end: Double,
    val text: String,
    val tokens: List<Int>,
    val temperature: Double,
    val avg_logprob: Double,
    val compression_ratio: Double,
    val no_speech_prob: Double
)

@Serializable
data class XGroq(
    val id: String
)

class TranscribeViaGroqTest {

    @Test
    @Disabled("test.flac does not exist")
    fun testGroq() = runBlocking {
        val client = createHttpClientWithLocalhostProxy()
        val key = settings.llmApiKeys.groq
        if (key == null) {
            throw Exception("Groq key is not set in settings")
        }
        val response = client.submitFormWithBinaryData(
            "https://api.groq.com/openai/v1/audio/transcriptions",
            formData {
                append("model", "whisper-large-v3")
                append("language", "de")
                append("response_format", "verbose_json")
                val file = File("/Users/<USER>/Downloads/test.flac")
                append("file", file.readBytes(), Headers.build {
                    append(HttpHeaders.ContentType, ContentType("audio", "*"))
                    append(HttpHeaders.ContentDisposition, "filename=\"${file.name}\"")
                })
            }
        ) {
            headers {
                bearerAuth(key)
                append(HttpHeaders.ContentType, "multipart/form-data")
            }
        }
        println(response.status)
        val j = Json.decodeFromString<TranscriptionResponse>(response.bodyAsText())
        println(j)
        client.close()
    }
}
