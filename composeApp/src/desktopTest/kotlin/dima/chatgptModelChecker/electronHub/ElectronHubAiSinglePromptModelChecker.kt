package dima.chatgptModelChecker.electronHub

import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import strikt.api.expectThat

@Serializable
private data class Wrapper(
    val `object`: String,
    val data: List<Model>
)

@Serializable
private data class Model(
    val id: String,
    val `object`: String,
    val created: Long,
    @SerialName("owned_by")
    val ownedBy: String,
    val tokens: Int? = null,
    val endpoints: List<String>,
    @SerialName("premium_model")
    val premiumModel: Boolean,
    val metadata: Metadata? = null
)

@Serializable
private data class Metadata(
    val vision: Boolean = false,
    @SerialName("function_call")
    val functionCall: Boolean = false,
    @SerialName("web_search")
    val webSearch: Boolean = false
)

class ElectronHubAiSinglePromptModelChecker {

    @Test
    @Disabled("URL has changed")
    fun testFindModels() {
        val url = "https://api.electronhub.top/v1/models"
        val json = Json {
            ignoreUnknownKeys = true
        }
        runBlocking {
            val response: HttpResponse = createHttpClientWithLocalhostProxy().get(url)
            expectThat(response.status == HttpStatusCode.OK)
            val models = json.decodeFromString<Wrapper>(response.bodyAsText()).data
            val filtered = models.filter {
                it.endpoints.any { endpoint ->
                    endpoint.contains("/chat/")
                } && !it.premiumModel
            }
            filtered.sortedBy {
                it.tokens
            }
                .reversed()
                .forEach {
                    println(it.id)
                }
        }
    }

}