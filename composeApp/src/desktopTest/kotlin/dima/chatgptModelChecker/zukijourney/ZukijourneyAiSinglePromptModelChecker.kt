package dima.chatgptModelChecker.zukijourney

import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.element
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import strikt.api.expectThat

@Serializable
private data class Wrapper(
    val data: List<Model>
)

@Serializable
private data class Model(
    val id: String,
    @SerialName("endpoint")
    @Serializable(with = StringListSerializer::class)
    val endpoints: List<String> = emptyList(),
    @SerialName("is_free")
    val isFree: Boolean,
    val pricing: Pricing
)

@Serializable
private data class Pricing(
    val multiplier: Double
)

/**
 * Holds string or a list of strings.
 */
object StringListSerializer : KSerializer<List<String>> {
    override val descriptor: SerialDescriptor =
        buildClassSerialDescriptor("StringListSerializer") {
            element<String>("element")
        }

    override fun deserialize(decoder: Decoder): List<String> {
        val json = decoder as JsonDecoder
        val element = json.decodeJsonElement()
        return if (element is JsonArray) {
            element.map { it.jsonPrimitive.content }
        } else {
            listOf(element.jsonPrimitive.content)
        }
    }

    override fun serialize(encoder: Encoder, value: List<String>) {
        val json = encoder as JsonEncoder
        json.encodeJsonElement(JsonArray(value.map { JsonPrimitive(it) }))
    }
}

class ZukijourneyAiSinglePromptModelChecker {

    @Test
    @Disabled("URL has changed")
    fun testFindModels() {
        val url = "https://zukijourney.xyzbot.net/v1/models"
        val json = Json {
            ignoreUnknownKeys = true
        }
        runBlocking {
            val response: HttpResponse = createHttpClientWithLocalhostProxy().get(url)
            expectThat(response.status == HttpStatusCode.OK)
            val models = json.decodeFromString<Wrapper>(response.bodyAsText()).data
            val filtered = models.filter {
                val isChat = it.endpoints.any { endpoint ->
                    endpoint.contains("/chat/")
                }
                isChat && it.isFree
            }
            filtered
                .sortedBy {
                    it.pricing.multiplier
                }
                .forEach {
                    println(it.id + " (" + it.pricing.multiplier + ")")
                }
        }
    }

}