package dima.chatgptModelChecker.naga

import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import strikt.api.expectThat

@Serializable
private data class Wrapper(
    val data: List<Model>
)

@Serializable
private data class Model(
    val id: String,
    val `object`: String,
    val pricing: Pricing? = null
)

@Serializable
private data class Pricing(
    @SerialName("per_output_token")
    val perOutputToken: Double? = null
)

class NagaAiSinglePromptModelChecker {

    @Test
    fun testFindModels() {
        val url = "https://api.naga.ac/v1/models"
        val json = Json {
            ignoreUnknownKeys = true
        }
        runBlocking {
            val response: HttpResponse = createHttpClientWithLocalhostProxy().get(url)
            expectThat(response.status == HttpStatusCode.OK)
            val models = json.decodeFromString<Wrapper>(response.bodyAsText()).data
            val filtered = models.filter {
                it.`object` != "proxy"
            }.sortedBy {
                it.pricing?.perOutputToken ?: 0.0
            }
            filtered
                .forEach {
                    println(it.id + " (" + it.pricing?.perOutputToken + ")")
                }
        }
    }

}