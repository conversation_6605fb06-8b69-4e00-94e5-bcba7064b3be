package dima.youtube

import dima.utils.Result
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isA
import strikt.assertions.isEqualTo
import strikt.assertions.isNull

class YouTubeChannelIdInfoTest {

    @Test
    fun `test extract handle from url`() {
        expectThat(extractAtHandle("https://www.youtube.com/@dima"))
            .isEqualTo("dima")
        expectThat(extractAtHandle("https://www.youtube.com/@dima/videos"))
            .isEqualTo("dima")
        expectThat(extractAtHandle("https://www.youtube.com/dima/fail"))
            .isNull()
    }

    @Test
    fun getYouTubeChannelIdFromHandleUrl() {
        runBlocking {
            expectThat(
                YouTubeChannelIdInfo.getYouTubeChannelIdFromHandleUrl("https://www.youtube.com/@fireship")
            )
                .isEqualTo(Result.Success("UCsBjURrPoezykLs9EqgamOA"))
            expectThat(
                YouTubeChannelIdInfo.getYouTubeChannelIdFromHandleUrl(
                    "https://www.youtube.com/@fireship/videos"
                )
            ).isEqualTo(Result.Success("UCsBjURrPoezykLs9EqgamOA"))
            expectThat(
                YouTubeChannelIdInfo.getYouTubeChannelIdFromHandleUrl(
                    "https://www.youtube.com/@fireshioeuoeup______________________________/videos"
                )
            ).isA<Result.Error>()
            expectThat(
                YouTubeChannelIdInfo.getYouTubeChannelIdFromHandleUrl(
                    "https://www.youtube.com/channel/UCTiNWm_Qfiulu45VYWXrHsA"
                )
            ).isEqualTo(Result.Success("UCTiNWm_Qfiulu45VYWXrHsA"))
            expectThat(
                YouTubeChannelIdInfo.getYouTubeChannelIdFromHandleUrl(
                    "https://www.youtube.com/channel/UCTiNWm_Qfiulu45VYWXrHsA/videos"
                )
            ).isEqualTo(Result.Success("UCTiNWm_Qfiulu45VYWXrHsA"))

        }
    }

}