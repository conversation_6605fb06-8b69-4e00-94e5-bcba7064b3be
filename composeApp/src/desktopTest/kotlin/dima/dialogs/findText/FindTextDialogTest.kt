package dima.dialogs.findText

import com.github.pgreze.process.InputSource
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.size

private data class UgResult(
    val fileName: String,
    val lineNumber: Int,
    val columnNumber: Int,
    val matchByteLength: Int,
    val lineContent: String,
)

class FindTextDialogTest {

    @Test
    fun testMadEmoji() {
        val s = """👨‍👦‍👦"""
        expectThat(s.toByteArray(Charsets.UTF_8).size)
            .isEqualTo(18)
        expectThat(s.length)
            .isEqualTo(8)
        expectThat(s.codePoints().count())
            .isEqualTo(5)
        val s2 = "😄"
        expectThat(s2.codePoints().count())
            .isEqualTo(1)
    }

    /**
     * This shows all possible options with --format to get the output ranges.
     * Getting the start column number would be great, but apparently there is no way to get the end column number.
     */
    @Test
    fun testUgByteFormatOutputs() {
        val stdin = """
                x👨‍👦‍👦x
                x😄x
            """.trimIndent()
        runBlocking {
            val p = process(
                "ug", "--format=Byte offset: %b  Byte size: %d  End offset: %e  Column number: %k  %O%~", "x",
                stdin = InputSource.fromString(stdin),
                stdout = Redirect.CAPTURE,
                stderr = Redirect.CAPTURE,
            )
            expectThat(p.resultCode)
                .isEqualTo(0)
            val output = """
Byte offset: 0  Byte size: 1  End offset: 1  Column number: 1  x👨‍👦‍👦x
Byte offset: 19  Byte size: 1  End offset: 20  Column number: 7  x👨‍👦‍👦x
Byte offset: 21  Byte size: 1  End offset: 22  Column number: 1  x😄x
Byte offset: 26  Byte size: 1  End offset: 27  Column number: 3  x😄x
            """.trimIndent()
            expectThat(p.output.joinToString("\n"))
                .isEqualTo(output)
        }
    }

    @Test
    fun testUgWithEmoji() {
        runBlocking {
            val stdin = """
                x👨‍👦‍👦x
                x😄x
            """.trimIndent()
            val p = process(
                // %f filename
                // %n line number
                // %k column number of the match
                // %d length of match
                // %O entire matching line
                "ug", "--format=%f:%n:%k:%d:%O%~", "x",
                stdin = InputSource.fromString(stdin),
                stdout = Redirect.CAPTURE,
                stderr = Redirect.CAPTURE,
            )
            expectThat(p.resultCode)
                .isEqualTo(0)
            val output = """
(standard input):1:1:1:x👨‍👦‍👦x
(standard input):1:7:1:x👨‍👦‍👦x
(standard input):2:1:1:x😄x
(standard input):2:3:1:x😄x
            """.trimIndent()
            expectThat(p.output.joinToString("\n"))
                .isEqualTo(output)
            val results = output.lines().filter {
                it.isNotBlank()
            }.map {
                val parts = it.split(":", limit = 5)
                expectThat(parts)
                    .size
                    .isEqualTo(5)
                UgResult(
                    fileName = parts[0],
                    lineNumber = parts[1].toInt(),
                    columnNumber = parts[2].toInt(),
                    matchByteLength = parts[3].toInt(),
                    lineContent = parts[4],
                )
            }
            expectThat(results)
                .size
                .isEqualTo(4)
            expectThat(getMatchString(results[0]))
                .isEqualTo("x")
            expectThat(getMatchString(results[1]))
                .isEqualTo("x")
            expectThat(getMatchString(results[2]))
                .isEqualTo("x")
            expectThat(getMatchString(results[3]))
                .isEqualTo("x")
        }
    }

    private fun getMatchString(result: UgResult): String {
        val range = FindText.extractMatchString(result.lineContent, result.columnNumber, result.matchByteLength)
        return result.lineContent.substring(range.first, range.second)
    }

}

