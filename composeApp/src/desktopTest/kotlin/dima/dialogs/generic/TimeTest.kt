package dima.dialogs.generic

import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNull

class TimeTest {

    @Test
    fun parseStringIntoTime() {
        expectThat(parseStringIntoTime("12:34"))
            .isEqualTo(GenericDialogRowTime(12, 34))
        expectThat(parseStringIntoTime("12:34:56"))
            .isNull()
        expectThat(parseStringIntoTime("12"))
            .isEqualTo(GenericDialogRowTime(12, 0))
        expectThat(parseStringIntoTime("12:3"))
            .isEqualTo(GenericDialogRowTime(12, 3))
        expectThat(parseStringIntoTime("12:03"))
            .isEqualTo(GenericDialogRowTime(12, 3))
        expectThat(parseStringIntoTime("12:345"))
            .isNull()
        expectThat(parseStringIntoTime("24:00"))  // Hour 24 is invalid
            .isNull()
        expectThat(parseStringIntoTime("12:60"))  // Minute 60 is invalid
            .isNull()
        expectThat(parseStringIntoTime("00:00"))
            .isEqualTo(GenericDialogRowTime(0, 0))
        expectThat(parseStringIntoTime("0:0"))
            .isEqualTo(GenericDialogRowTime(0, 0))
        expectThat(parseStringIntoTime("5"))
            .isEqualTo(GenericDialogRowTime(5, 0))
        expectThat(parseStringIntoTime("05"))
            .isEqualTo(GenericDialogRowTime(5, 0))
        expectThat(parseStringIntoTime("5:00"))
            .isEqualTo(GenericDialogRowTime(5, 0))
        expectThat(parseStringIntoTime("05:00"))
            .isEqualTo(GenericDialogRowTime(5, 0))
        expectThat(parseStringIntoTime("abc"))
            .isNull()
        expectThat(parseStringIntoTime("12:abc"))
            .isNull()
        expectThat(parseStringIntoTime("abc:30"))
            .isNull()
        expectThat(parseStringIntoTime(" 12 : 30 ")) // with spaces
            .isEqualTo(GenericDialogRowTime(12, 30))
        expectThat(parseStringIntoTime(" 8 ")) // with spaces, hour only
            .isEqualTo(GenericDialogRowTime(8,0))

    }

}
