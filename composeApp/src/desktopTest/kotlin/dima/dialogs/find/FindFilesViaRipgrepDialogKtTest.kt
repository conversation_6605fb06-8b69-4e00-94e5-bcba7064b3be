package dima.dialogs.find

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.os.homeWithoutSlash
import dima.utils.normalize
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import strikt.api.expectThat
import strikt.assertions.contains
import strikt.assertions.isEqualTo
import java.io.File
import java.nio.file.Files
import kotlin.io.path.createTempDirectory

class FindFilesViaRipgrepDialogKtTest {

    private lateinit var tempDir: File
    private lateinit var testDir: File

    @BeforeEach
    fun setUp() {
        // Create a temporary directory with umlauts
        tempDir = createTempDirectory("tmp_rovodev_").toFile()
        testDir = File(tempDir, "türkei")
        testDir.mkdirs()
        
        // Create some test files with umlauts in names and content
        File(testDir, "test_ä.txt").writeText("This file contains ä umlaut")
        File(testDir, "test_ö.txt").writeText("This file contains ö umlaut")
        File(testDir, "test_ü.txt").writeText("This file contains ü umlaut")
        File(testDir, "normal.txt").writeText("This is a normal file")
    }

    @AfterEach
    fun tearDown() {
        // Clean up temporary directory
        tempDir.deleteRecursively()
    }

    @Test
    fun testProcessResultEncoding() {
        runBlocking {
            val p = process(
                "ugrep", "--files-with-matches", "--match", ".",
//                "rg", "--files",
                directory = testDir,
                stdout = Redirect.CAPTURE,
                stderr = Redirect.CAPTURE,
                destroyForcibly = true
            )
            expectThat(p.resultCode)
                .isEqualTo(0)
            val output: String = p.output.joinToString("\n")
            expectThat(output.normalize())
                .contains("ä")
        }
    }
}
