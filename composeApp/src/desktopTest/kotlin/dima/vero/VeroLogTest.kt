package dima.vero

import dima.apps.veroLog.VeroLogEntry
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class VeroLogTest {

    @Test
    fun testCalculateWeeklySummariesWithRealData() {
        // Real data from the user's example
        val entries = listOf(
            VeroLogEntry("2025-06-16T06:56:40.995764Z", 41506, "Printouts", "WORK"),
            VeroLogEntry("2025-06-16T06:59:57.763412Z", 51926, "#242 - photo on same row is just showing 2 photos instead of 10", "WORK"),
            VeroLogEntry("2025-06-16T07:37:45.983888Z", 51926, "#242 - photo on same row is just showing 2 photos instead of 10", "END"),
            VeroLogEntry("2025-06-16T07:38:13.108431Z", 51926, "#242 - photo on same row is just showing 2 photos instead of 10", "WORK"),
            VeroLogEntry("2025-06-16T08:06:39.628820Z", 55188, "#436 - New route(s) for MediaPrintV2", "WORK"),
            VeroLogEntry("2025-06-16T08:21:27.300420Z", 53506, "#352 - Schema and HTML for Meeting Minutes", "WORK"),
            VeroLogEntry("2025-06-16T08:58:20.654016Z", 53506, "#352 - Schema and HTML for Meeting Minutes", "END")
        )
        
        // Manual calculation for verification
        // Printouts: 06:56:40 to 06:59:57
        val printoutsStart = Instant.parse("2025-06-16T06:56:40.995764Z")
        val printoutsEnd = Instant.parse("2025-06-16T06:59:57.763412Z")
        val printoutsHours = ChronoUnit.MILLIS.between(printoutsStart, printoutsEnd) / (1000.0 * 60.0 * 60.0)
        
        // #242 first session: 06:59:57 to 07:37:45
        val task242Start1 = Instant.parse("2025-06-16T06:59:57.763412Z")
        val task242End1 = Instant.parse("2025-06-16T07:37:45.983888Z")
        val task242Hours1 = ChronoUnit.MILLIS.between(task242Start1, task242End1) / (1000.0 * 60.0 * 60.0)
        
        // #242 second session: 07:38:13 to 08:06:39
        val task242Start2 = Instant.parse("2025-06-16T07:38:13.108431Z")
        val task242End2 = Instant.parse("2025-06-16T08:06:39.628820Z")
        val task242Hours2 = ChronoUnit.MILLIS.between(task242Start2, task242End2) / (1000.0 * 60.0 * 60.0)
        
        // #436: 08:06:39 to 08:21:27
        val task436Start = Instant.parse("2025-06-16T08:06:39.628820Z")
        val task436End = Instant.parse("2025-06-16T08:21:27.300420Z")
        val task436Hours = ChronoUnit.MILLIS.between(task436Start, task436End) / (1000.0 * 60.0 * 60.0)
        
        // #352: 08:21:27 to 08:58:20
        val task352Start = Instant.parse("2025-06-16T08:21:27.300420Z")
        val task352End = Instant.parse("2025-06-16T08:58:20.654016Z")
        val task352Hours = ChronoUnit.MILLIS.between(task352Start, task352End) / (1000.0 * 60.0 * 60.0)
        
        val expectedTotal = printoutsHours + task242Hours1 + task242Hours2 + task436Hours + task352Hours
        val expected242Total = task242Hours1 + task242Hours2
        
        println("Expected calculations:")
        println("Printouts: ${String.format("%.3f", printoutsHours)}h")
        println("#242 total: ${String.format("%.3f", expected242Total)}h")
        println("#436: ${String.format("%.3f", task436Hours)}h") 
        println("#352: ${String.format("%.3f", task352Hours)}h")
        println("Total: ${String.format("%.3f", expectedTotal)}h")
        
        // Test the function
        val summaries = calculateWeeklySummaries(entries)
        
        assertEquals(1, summaries.size, "Should have exactly one week")
        
        val summary = summaries[0]
        assertEquals(1, summary.days.size, "Should have exactly one day")
        
        val day = summary.days[0]
        assertEquals("Montag", day.dayName, "June 16, 2025 should be Monday")
        
        // Check total hours (should be around 2 hours, not 1.2h)
        println("\nActual calculation:")
        println("Total hours: ${String.format("%.3f", summary.totalHours)}h")
        day.projects.forEach { (project, hours) ->
            println("$project: ${String.format("%.3f", hours)}h")
        }
        
        // Verify the total is approximately 2 hours (allowing small floating point differences)
        assertTrue(summary.totalHours > 1.9, "Total should be close to 2 hours, got ${summary.totalHours}")
        assertTrue(summary.totalHours < 2.1, "Total should be close to 2 hours, got ${summary.totalHours}")
        
        // Verify individual project hours
        assertTrue(day.projects.containsKey("Printouts"))
        assertTrue(day.projects.containsKey("#242 - photo on same row is just showing 2 photos instead of 10"))
        assertTrue(day.projects.containsKey("#436 - New route(s) for MediaPrintV2"))
        assertTrue(day.projects.containsKey("#352 - Schema and HTML for Meeting Minutes"))
    }
}