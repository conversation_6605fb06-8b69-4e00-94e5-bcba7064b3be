package dima.color

import androidx.compose.ui.graphics.toArgb
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo

class ColorHashTest {

    @Test
    fun `test same string returns same color`() {
        val color = "test".toHashedComposeColor()
        val color2 = "test".toHashedComposeColor()
        expectThat(color.toArgb())
            .isEqualTo(color2.toArgb())
    }

}