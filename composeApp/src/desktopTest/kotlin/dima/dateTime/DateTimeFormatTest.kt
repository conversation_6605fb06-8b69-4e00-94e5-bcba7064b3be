package dima.dateTime

import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNotEqualTo
import java.time.LocalDateTime
import java.time.ZoneId

class DateTimeFormatTest {

    @Test
    fun `test UTC timezone`() {
        val start = LocalDateTime.now(ZoneId.systemDefault())
        val inUtc = start
            .atZone(ZoneId.systemDefault())
            .withZoneSameInstant(ZoneId.of("UTC"))
            .format(DateTimeFormat.isoDateTimeWithTandZ)
        val localTimeZone = start.format(DateTimeFormat.isoDateTimeWithTandZ)
        expectThat(inUtc)
            .isNotEqualTo(localTimeZone)
        println(inUtc)
        println(localTimeZone)
        expectThat(inUtc)
            .isEqualTo(DateTimeUtils.nowInUtc().format(DateTimeFormat.isoDateTimeWithTandZ))
    }

}