package dima.apps.email

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class EmailHtmlImageTest {

    @Test
    fun `test parseDimensionFromStyle`() {
        assertEquals(100, parseDimensionFromStyle("width: 100px", "width"))
        assertEquals(100, parseDimensionFromStyle("width:100px", "width"))
        assertEquals(100, parseDimensionFromStyle("width: 100px; height: 200px", "width"))
        assertEquals(50, parseDimensionFromStyle("width:50px;height: 500px", "width"))
        assertEquals(50, parseDimensionFromStyle("width: 50px;height: 50px;max-width: 50px;max-height: 50px;", "width"))
    }

}