package dima.apps.email

import dima.apps.email.models.EmailFrom
import dima.apps.email.models.EmailMessage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.containsExactly
import strikt.assertions.isEmpty
import java.time.LocalDateTime

class EmailThreadMarkingTest {

    @Test
    fun `marks single email when sender not in thread list`() {
        val email = createTestEmailInInbox(
            uid = "uid1",
            from = "<EMAIL>"
        )
        var marked = emptyList<String>()
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = listOf(email),
            markedEmailsIds = emptyList(),
            changeIsPreviewSelected = {},
            threadMarkingSenders = emptyList(),
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )
        expectThat(marked).containsExactly("uid1@inbox")
    }

    @Test
    fun `marks single email when sender in thread list`() {
        val from = "<EMAIL>"
        val email = createTestEmailInInbox(
            uid = "uid1",
            from = from
        )
        var marked = emptyList<String>()
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = listOf(email),
            markedEmailsIds = emptyList(),
            threadMarkingSenders = listOf(from),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )
        expectThat(marked).containsExactly("uid1@inbox")
    }

    @Test
    fun `unmark single email when sender not in thread list`() {
        val email = createTestEmailInInbox(
            uid = "uid1",
            from = "<EMAIL>"
        )
        var marked = listOf("foo")
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = listOf(email),
            markedEmailsIds = listOf("uid1@inbox"),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )
        expectThat(marked).isEmpty()
    }

    @Test
    fun `unmark single email when sender in thread list`() {
        val email = createTestEmailInInbox(
            uid = "uid1",
            from = "<EMAIL>"
        )
        var marked = listOf("foo")
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = listOf(email),
            markedEmailsIds = listOf("uid1@inbox"),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )
        expectThat(marked).isEmpty()
    }

    @Test
    fun `mark full thread without thread marking sender`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "parent", isChild = false, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child1", isChild = true, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child2", isChild = true, from = "<EMAIL>")
        )
        var marked = emptyList<String>()
        markEmailOrThread(
            selectedEmailIndex = 1,
            filteredEmails = emails,
            markedEmailsIds = emptyList(),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {},
            threadMarkingSenders = emptyList()
        )
        expectThat(marked).containsExactly("child1@inbox")
    }

    @Test
    fun `mark full thread when thread marking sender is set`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "parent", isChild = false, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child1", isChild = true, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child2", isChild = true, from = "<EMAIL>")
        )
        var marked = emptyList<String>()
        markEmailOrThread(
            selectedEmailIndex = 1,
            filteredEmails = emails,
            markedEmailsIds = emptyList(),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {},
            threadMarkingSenders = listOf("<EMAIL>")
        )
        expectThat(marked.toSet()).containsExactly("parent@inbox", "child1@inbox", "child2@inbox")
    }

    @Test
    fun `moves selection after marking a thread`() {
        val from = "<EMAIL>"
        val emails = listOf(
            createTestEmailInInbox(uid = "parent", from = from, isChild = false), // index 0
            createTestEmailInInbox(uid = "child1", from = from, isChild = true),  // index 1
            createTestEmailInInbox(uid = "nextParent", from = "<EMAIL>", isChild = false) // index 2
        )
        var newSelected: Int? = null
        markEmailOrThread(
            selectedEmailIndex = 0, // Select the parent of the thread
            filteredEmails = emails,
            markedEmailsIds = emptyList(),
            threadMarkingSenders = listOf(from),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { },
            changeSelected = { newSelected = it }
        )
        assertEquals(2, newSelected)
    }

    @Test
    fun `selection stays on last email of thread if thread is at the end`() {
        val from = "<EMAIL>"
        val emails = listOf(
            createTestEmailInInbox(uid = "otherParent", from = "<EMAIL>", isChild = false), // index 0
            createTestEmailInInbox(uid = "parent", from = from, isChild = false),          // index 1
            createTestEmailInInbox(uid = "child1", from = from, isChild = true)           // index 2
        )
        var newSelected: Int? = null
        markEmailOrThread(
            selectedEmailIndex = 1, // Select the parent of the thread at the end
            filteredEmails = emails,
            markedEmailsIds = emptyList(),
            threadMarkingSenders = listOf(from),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { },
            changeSelected = { newSelected = it }
        )
        assertEquals(2, newSelected)
    }

    @Test
    fun `selection moves to next after marking single email not at end`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "1", from = "<EMAIL>"), // index 0
            createTestEmailInInbox(uid = "2", from = "<EMAIL>"), // index 1
            createTestEmailInInbox(uid = "3", from = "<EMAIL>")  // index 2
        )
        var newSelected: Int? = null
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = emails,
            markedEmailsIds = emptyList(),
            threadMarkingSenders = listOf("<EMAIL>"), // Ensure single email marking
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { },
            changeSelected = { newSelected = it }
        )
        assertEquals(1, newSelected)
    }


    @Test
    fun `unmark 2 already marked emails in thread`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "parent", isChild = false, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child1", isChild = true, from = "<EMAIL>")
        )
        var marked = listOf("parent@inbox", "child1@inbox")
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = emails,
            markedEmailsIds = marked,
            threadMarkingSenders = listOf("<EMAIL>"),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )
        expectThat(marked)
            .isEmpty()
    }

    @Test
    fun `moves selection after single mark`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "1"),
            createTestEmailInInbox(uid = "2")
        )
        var newSelected: Int? = null
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = emails,
            markedEmailsIds = emptyList(),
            changeIsPreviewSelected = {},
            // Pass empty list to ensure single email marking logic
            threadMarkingSenders = emptyList(), changeMarkedEmailsIds = {},
            changeSelected = { newSelected = it }
        )
        assertEquals(1, newSelected)
    }

    @Test
    fun `handles parent email followed by children`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "parent", isChild = false, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child", isChild = true)
        )
        var marked = emptyList<String>()

        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = emails,
            threadMarkingSenders = listOf("<EMAIL>"),
            markedEmailsIds = emptyList(),
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )

        assertEquals(2, marked.size)
    }

    @Test
    fun `unmarks 2 emails outside thread when already marked`() {
        val emails = listOf(
            createTestEmailInInbox(uid = "parent", isChild = false, from = "<EMAIL>"),
            createTestEmailInInbox(uid = "child1", isChild = false, from = "<EMAIL>")
        )
        var marked = listOf("parent@inbox", "child1@inbox")
        markEmailOrThread(
            selectedEmailIndex = 0,
            filteredEmails = emails,
            markedEmailsIds = marked,
            threadMarkingSenders = listOf("<EMAIL>"), // Thread marking for the sender
            changeIsPreviewSelected = {},
            changeMarkedEmailsIds = { marked = it },
            changeSelected = {}
        )
        // Since "parent" is selected, and it's a standalone email (not followed by its children),
        // only "parent" should be unmarked. "child1" should remain marked.
        expectThat(marked).containsExactly("child1@inbox")
    }

    private fun createTestEmailInInbox(
        uid: String,
        isChild: Boolean = false,
        from: String = "<EMAIL>"
    ): EmailMessage = EmailMessage(
        uid = uid,
        folderName = "inbox",
        plainText = null,
        html = null,
        attachments = "",
        attachmentCount = 0,
        subject = "",
        headers = emptyMap(),
        date = LocalDateTime.now(),
        from = listOf(EmailFrom(null, from)),
        to = emptyList(),
        cc = emptyList(),
        bcc = emptyList(),
        isAnswered = false,
        isDeleted = false,
        isDraft = false,
        isFlagged = false,
        isRecent = false,
        isSeen = false,
        wasTrashed = false,
        isChildInThread = isChild
    )
}