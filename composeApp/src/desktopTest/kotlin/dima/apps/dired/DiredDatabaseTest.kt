package dima.apps.dired

import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isFalse
import java.io.File

class DiredDatabaseTest {

    @Test
    fun test() {
        val home = System.getenv("HOME")
        expectThat(File(home).absolutePath.endsWith("/"))
            .isFalse()
        expectThat(File("/Users").absolutePath.endsWith("/"))
            .isFalse()
        expectThat(File("/Users/").absolutePath.endsWith("/"))
            .isFalse()
    }
}