package dima.apps.textEditor

/*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class TextEditorWordsTest : StringSpec({
    "check simple word" {
        TextEditor.getWordBoundaryAt("hello", 0) shouldBe Range(0, 4)
    }
    "check punctuation on comma" {
        TextEditor.getWordBoundaryAt(",,,hallo", 0) shouldBe Range(0, 2)
    }
    "check punctuation" {
        TextEditor.getWordBoundaryAt(",,, hallo", 6) shouldBe Range(4, 8)
    }
    "check null" {
        TextEditor.getWordBoundaryAt("  hello", 0) shouldBe null
    }
})*/
