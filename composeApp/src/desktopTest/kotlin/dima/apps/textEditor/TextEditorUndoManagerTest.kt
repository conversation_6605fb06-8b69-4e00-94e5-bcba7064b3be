package dima.apps.textEditor

/*
import io.kotest.core.spec.style.StringSpec
import io.kotest.matchers.shouldBe

class TextEditorUndoManagerTest : StringSpec({

    "verify undo not crash on empty string" {
        val undoManager = TextEditorUndoManager()
        undoManager.undo().text shouldBe ""
    }

    "verify redo not crash on empty string" {
        val undoManager = TextEditorUndoManager()
        undoManager.redo().text shouldBe ""
    }

    "verify one undo returns empty string" {
        val undoManager = TextEditorUndoManager()
        val text = "foo"
        undoManager.remember(text, Position())
        undoManager.undo().text shouldBe ""
    }

    "verify one redo returns initial text" {
        val undoManager = TextEditorUndoManager()
        val text = "foo"
        undoManager.remember(text, Position())
        undoManager.redo().text shouldBe text
    }

    "verify one undo and one redo returns initial text" {
        val undoManager = TextEditorUndoManager()
        val text = "foo"
        undoManager.remember(text, Position())
        undoManager.undo()
        undoManager.redo().text shouldBe text
    }

    "verify with reset index returns initial text 1" {
        val undoManager = TextEditorUndoManager()
        val text = "foo"
        undoManager.remember(text, Position())
        undoManager.undo()
        undoManager.resetIndex()
        undoManager.undo()
        undoManager.redo().text shouldBe text
    }

    "verify with reset index returns text 2" {
        val undoManager = TextEditorUndoManager()
        var text = "foo"
        undoManager.remember(text, Position())
        text = "foobar"
        undoManager.remember(text, Position())
        undoManager.undo().text shouldBe "foo"
    }

    "verify with reset index returns text 3" {
        val undoManager = TextEditorUndoManager()
        var text = "foo"
        undoManager.remember(text, Position())
        text = "foobar"
        undoManager.remember(text, Position())
        undoManager.undo()
        undoManager.resetIndex()
        undoManager.undo().text shouldBe "foo"
    }

    "verify with reset index returns empty string" {
        val undoManager = TextEditorUndoManager()
        var text = "foo"
        undoManager.remember(text, Position())
        text = "foobar"
        undoManager.remember(text, Position())
        undoManager.resetIndex()
        undoManager.undo()
        undoManager.undo().text shouldBe ""
    }

    "verify undo/redo with reset index" {
        val undoManager = TextEditorUndoManager()
        var text = "foo"
        undoManager.remember(text, Position())
        text = "foobar"
        undoManager.remember(text, Position())
        undoManager.resetIndex()
        undoManager.undo()
        undoManager.redo()
        undoManager.resetIndex()
        undoManager.undo().text shouldBe "foo"
    }

    "verify undo/redo without reset index" {
        val undoManager = TextEditorUndoManager()
        var text = "foo"
        undoManager.remember(text, Position())
        text = "foobar"
        undoManager.remember(text, Position())
        undoManager.resetIndex()
        undoManager.undo()
        undoManager.redo()
        undoManager.undo().text shouldBe "foo"
    }

    "verify remember same string" {
        val undoManager = TextEditorUndoManager()
        val text = "foo"
        undoManager.remember(text, Position())
        undoManager.remember(text, Position())
        undoManager.remember(text, Position())
        undoManager.undo().text shouldBe ""
    }

})
*/
