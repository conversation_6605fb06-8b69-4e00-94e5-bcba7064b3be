package dima.apps.calendar

import androidx.compose.ui.graphics.Color
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.hasSize
import strikt.assertions.isEqualTo
import strikt.assertions.isNotNull
import java.time.LocalDate

/**
 * Tests to verify that lane calculation works optimally per week,
 * allowing events to have different lanes in different weeks for optimal layout.
 */
class EventLaneCalculatorPerWeekTest {

    private val blue = Color.Blue
    private val red = Color.Red

    @Test
    fun `test Vero halber Urlaub gets optimal lanes per week`() {
        // Simulate the real scenario: Vero halber Urlaub spans from Aug 18-22
        // Week 34: Aug 18-24 (Monday to Sunday)
        // In Week 34, only "Vero halber Urlaub" should be present on Aug 20-22
        
        val eventsForWeek34 = listOf(
            CalendarEvent.AllDay(
                id = "2", 
                summary = "Vero halber Urlaub",
                color = red,
                startDate = LocalDate.of(2025, 8, 18),
                endDate = LocalDate.of(2025, 8, 23) // exclusive, so includes 22nd
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(eventsForWeek34)

        // On August 20th (Wednesday, W34): Only Vero halber Urlaub should be in lane 0 (optimal)
        val aug20Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 20))
        expectThat(aug20Events).hasSize(1)
        expectThat(aug20Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(0)

        // On August 21st (Thursday, W34): Only Vero halber Urlaub should be in lane 0 (optimal)
        val aug21Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 21))
        expectThat(aug21Events).hasSize(1)
        expectThat(aug21Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(0)

        // On August 22nd (Friday, W34): Only Vero halber Urlaub should be in lane 0 (optimal)
        val aug22Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 22))
        expectThat(aug22Events).hasSize(1)
        expectThat(aug22Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(0)
    }

    @Test
    fun `test events get different optimal lanes in different weeks`() {
        // Week 1: Event A alone should get lane 0
        val week1Events = listOf(
            CalendarEvent.AllDay(
                id = "A",
                summary = "Event A",
                color = blue,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 3)
            )
        )

        // Week 2: Event A with Event B - Event A might get lane 1 due to sorting
        val week2Events = listOf(
            CalendarEvent.AllDay(
                id = "A",
                summary = "Event A", 
                color = blue,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 10) // spans into week 2
            ),
            CalendarEvent.AllDay(
                id = "B",
                summary = "Event B",
                color = red,
                startDate = LocalDate.of(2025, 8, 8),
                endDate = LocalDate.of(2025, 8, 9)
            )
        )

        val calculator = EventLaneCalculator()
        
        // Week 1 calculation
        val week1LaneAssignments = calculator.calculateLanes(week1Events)
        val aug1Events = week1LaneAssignments.getEventsForDate(LocalDate.of(2025, 8, 1))
        expectThat(aug1Events).hasSize(1)
        expectThat(aug1Events.find { it.getSummary() == "Event A" }?.lane).isEqualTo(0)

        // Week 2 calculation (different optimal layout)
        val week2LaneAssignments = calculator.calculateLanes(week2Events)
        val aug8Events = week2LaneAssignments.getEventsForDate(LocalDate.of(2025, 8, 8))
        expectThat(aug8Events).hasSize(2)
        
        // Both events should be present, lanes assigned optimally for this week
        expectThat(aug8Events.find { it.getSummary() == "Event A" }).isNotNull()
        expectThat(aug8Events.find { it.getSummary() == "Event B" }).isNotNull()
        
        // The exact lane assignment depends on the algorithm's sorting, but both should be assigned
        val assignedLanes = aug8Events.map { it.lane }.toSet()
        expectThat(assignedLanes).isEqualTo(setOf(0, 1))
    }

    @Test
    fun `test original problem scenario with per-week optimization`() {
        // This simulates what happens in Week 34 (Aug 18-24) with all three events
        val week34Events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Kita Ferien",
                color = blue,
                startDate = LocalDate.of(2025, 8, 4),
                endDate = LocalDate.of(2025, 8, 19) // ends on 18th (exclusive)
            ),
            CalendarEvent.AllDay(
                id = "2", 
                summary = "Vero halber Urlaub",
                color = red,
                startDate = LocalDate.of(2025, 8, 18),
                endDate = LocalDate.of(2025, 8, 23) // includes 22nd (exclusive)
            ),
            CalendarEvent.AllDay(
                id = "3",
                summary = "Kita fängt an",
                color = blue,
                startDate = LocalDate.of(2025, 8, 19),
                endDate = LocalDate.of(2025, 8, 20) // only 19th (exclusive)
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(week34Events)

        // August 18th: Kita Ferien and Vero halber Urlaub
        val aug18Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 18))
        expectThat(aug18Events).hasSize(2)
        
        // August 19th: Vero halber Urlaub and Kita fängt an (Kita Ferien ended)
        val aug19Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 19))
        expectThat(aug19Events).hasSize(2)
        
        // August 20th: Only Vero halber Urlaub (others ended)
        val aug20Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 20))
        expectThat(aug20Events).hasSize(1)
        expectThat(aug20Events.find { it.getSummary() == "Vero halber Urlaub" }).isNotNull()
        
        // The key insight: Vero halber Urlaub should maintain the same lane throughout the week
        val veroLaneAug18 = aug18Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane
        val veroLaneAug19 = aug19Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane
        val veroLaneAug20 = aug20Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane
        
        expectThat(veroLaneAug18).isEqualTo(veroLaneAug19)
        expectThat(veroLaneAug19).isEqualTo(veroLaneAug20)
    }
}