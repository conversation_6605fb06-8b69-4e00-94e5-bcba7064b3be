package dima.apps.calendar

import dima.utils.Result
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isA
import java.time.LocalDate

class DateParserTest {

    private val defaultStartDate = LocalDate.of(2023, 1, 10)
    private val defaultStartYear = 2023
    private val defaultStartMonth = 1
    private val defaultStartDay = 10

    @Test
    fun `parse empty string returns error`() {
        val result = parseStringToDate("")
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse blank string returns error`() {
        val result = parseStringToDate("   ")
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse invalid string returns error`() {
        val result = parseStringToDate("not a date", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse only day - future day in current month`() {
        // startDate is Jan 10, 2023. "15" should be Jan 15, 2023.
        val result = parseStringToDate("15", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2023, 1, 15, "Future day in current month")
    }

    @Test
    fun `parse only day - past day in current month, should go to next month`() {
        // startDate is Jan 10, 2023. "5" should be Feb 5, 2023.
        val result = parseStringToDate("5", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2023, 2, 5, "Past day, next month")
    }

    @Test
    fun `parse only day - current day, should go to next month`() {
        // startDate is Jan 10, 2023. "10" should be Feb 10, 2023.
        val result = parseStringToDate("10", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2023, 2, 10, "Current day, next month")
    }

    @Test
    fun `parse only day - next month rollover year`() {
        val dec15at2023 = LocalDate.of(2023, 12, 15)
        // "10" from Dec 15, 2023 should be Jan 10, 2024
        val result = parseStringToDate("10", startDate = dec15at2023)
        result as Result.Success
        assertDateEquals(result.value, 2024, 1, 10, "Day causing year rollover")
    }

    @Test
    fun `parse day and numeric month`() {
        // startDate is Jan 10, 2023. "15 3" should be Mar 15, 2023.
        val result = parseStringToDate("15 3", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2023, 3, 15, "Day and numeric month")
    }

    @Test
    fun `parse day and text month`() {
        // "15 mar" should be Mar 15, 2023 (year from startDate)
        val result = parseStringToDate("15 mar", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 3, 15, "Day and text month")
    }

    @Test
    fun `parse day text month different case`() {
        val result = parseStringToDate("15 MaRcH", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 3, 15, "Day and text month mixed case")
    }

    @Test
    fun `parse German Maerz with umlaut`() {
        val result = parseStringToDate("15 märz", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 3, 15, "Day and German Maerz")
    }

    @Test
    fun `parse German Maerz without umlaut`() {
        val result = parseStringToDate("15 marz", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 3, 15, "Day and German Maerz without umlaut")
    }


    @Test
    fun `parse text month and day`() {
        // "apr 20" should be Apr 20, 2023
        val result = parseStringToDate("apr 20", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 4, 20, "Text month and day")
    }

    @Test
    fun `parse day month year - all numeric`() {
        // "20 5 2024" should be May 20, 2024
        val result = parseStringToDate("20 5 2024", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2024, 5, 20, "Day month year numeric")
    }

    @Test
    fun `parse day month year - 2-digit year`() {
        // "20 5 25" should be May 20, 2025
        val result = parseStringToDate("20 5 25", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2025, 5, 20, "Day month 2-digit year")
    }

    @Test
    fun `parse day text_month year`() {
        // "22 jun 2026" should be June 22, 2026
        val result = parseStringToDate("22 jun 2026", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2026, 6, 22, "Day text_month year")
    }

    @Test
    fun `parse text_month day year`() {
        // "jul 23 2027" should be July 23, 2027
        val result = parseStringToDate("jul 23 2027", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2027, 7, 23, "Text_month day year")
    }

    @Test
    fun `parse with dots as separators`() {
        // "25.8.2028" should be Aug 25, 2028
        val result = parseStringToDate("25.8.2028", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2028, 8, 25, "Dots as separators")
    }

    @Test
    fun `parse ISO yyyy-MM-dd`() {
        val result = parseStringToDate("2025-08-03", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2025, 8, 3, "ISO hyphen-separated date")
    }

    fun `parse with mixed separators and extra spaces`() {
        // " 26 . sep  29 " should be Sep 26, 2029
        val result = parseStringToDate(" 26 . sep  29 ", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2029, 9, 26, "Mixed separators and spaces")
    }

    @Test
    fun `parse only numeric month and year`() {
        // "11 2030" -> Should be Nov (11), Year 2030, Day from startDate (10)
        // Parse state: foundDay=11, foundMonth=null, foundYear=2030.
        // My trace: foundDay=11, foundMonth=1 (defaultStartMonth), foundYear=2030.
        // This matches original test assertion: 2030, Jan, 11.
        val result = parseStringToDate("11 2030", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(
            actual = result.value,
            expectedYear = 2030,
            expectedMonth = defaultStartMonth, // Jan
            expectedDay = 11, // Day "11" from input
            message = "Numeric month and year - current behavior interprets first number as day"
        )
    }

    @Test
    fun `parse only text month and year`() {
        // "dec 2031" -> Dec (day from startDate), 2031 (Dec 10, 2031)
        val result = parseStringToDate("dec 2031", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2031, 12, defaultStartDay, "Text month and year")
    }

    @Test
    fun `parse only year`() {
        // "2032" -> (day and month from startDate), 2032 (Jan 10, 2032)
        val result = parseStringToDate("2032", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2032, defaultStartMonth, defaultStartDay, "Only year")
    }

    @Test
    fun `parse only 2-digit year`() {
        // "33" -> (day and month from startDate), 2033 (Jan 10, 2033)
        val result = parseStringToDate("33", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2033, defaultStartMonth, defaultStartDay, "Only 2-digit year")
    }

    @Test
    fun `parse ambiguous numbers - day first if valid day then month`() {
        // "10 11" -> Day 10, Month 11 (Nov 10, defaultStartYear)
        // startDate is Jan 10, 2023
        val result = parseStringToDate("10 11", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 11, 10, "Ambiguous D M: 10 11")
    }

    @Test
    fun `parse ambiguous numbers - day first then month if valid day`() {
        // "12 1" -> Day 12, Month 1 (Jan 12, defaultStartYear)
        val result = parseStringToDate("12 1", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 1, 12, "Ambiguous D M: 12 1")
    }

    @Test
    fun `parse numbers where first could be day or month, second is month - day is prioritized`() {
        // "3 4 2025" -> Day 3, Month 4, Year 2025
        val result = parseStringToDate("3 4 2025", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2025, 4, 3, "D M Y: 3 4 2025")
    }

    @Test
    fun `parse numbers where first is clearly day, second is month`() {
        // "15 4 2025" -> Day 15, Month 4, Year 2025
        val result = parseStringToDate("15 4 2025", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2025, 4, 15, "D M Y: 15 4 2025")
    }

    @Test
    fun `parse only month text`() {
        // "sep" -> September (day and year from startDate)
        // startDate is Jan 10, 2023. Result: Sep 10, 2023
        val result = parseStringToDate("sep", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, defaultStartYear, 9, defaultStartDay, "Only text month")
    }

    @Test
    fun `parse only numeric month - interpreted as day`() {
        // "8" is interpreted as Day 8.
        // startDate is Jan 10, 2023. "8" means next month (Feb 8, 2023).
        val result = parseStringToDate("8", startDate = defaultStartDate)
        result as Result.Success
        assertDateEquals(result.value, 2023, 2, 8, "Only numeric '8' interpreted as day, advancing month")

        // If we want to test "August (month 8), day and year from startDate":
        // We need to ensure '8' is NOT taken as day. E.g. provide a day first.
        // Example: "1 8" -> Day 1, Month 8 (Aug 1, defaultStartYear)
        val resultAug = parseStringToDate("1 8", startDate = defaultStartDate)
        resultAug as Result.Success
        assertDateEquals(resultAug.value, defaultStartYear, 8, 1, "Day 1, Month 8 (August)")
    }

    @Test
    fun `parse string with number larger than 31 first for D M Y structure`() {
        // "32 3 2024" should be an error due to the D M Y structure validation for 3 numeric parts
        val result = parseStringToDate("32 3 2024", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse string that would result in invalid date like Feb 30`() {
        // "30 feb 2023" should be an error because Feb 30 is not a valid date.
        val result = parseStringToDate("30 feb 2023", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
        // Test "only day" logic leading to invalid date
        // defaultStartDate is Jan 10, 2023. Advancing month due to day "5" (past) gives Feb.
        // If defaultStartDate was Jan 31, input "30" would try Feb 30.
        val jan31 = LocalDate.of(2023, 1, 31)
        val resultInvalidDay = parseStringToDate("30", startDate = jan31) // Should try Feb 30, 2023
        expectThat(resultInvalidDay).isA<Result.Error>()
    }


    @Test
    fun `parse string with too many parts`() {
        val result = parseStringToDate("12 3 2024 50", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse string with month number larger than 12 for D M Y structure`() {
        // "15 13 2024" should be an error due to the D M Y structure validation for 3 numeric parts
        val result = parseStringToDate("15 13 2024", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse relative date plus 1 day`() {
        val today = LocalDate.now()
        val result = parseStringToDate("+1", startDate = today)
        result as Result.Success
        assertDateEquals(result.value, today.plusDays(1).year, today.plusDays(1).monthValue, today.plusDays(1).dayOfMonth, "Relative +1 day")
    }

    @Test
    fun `parse relative date plus 3 months`() {
        val today = LocalDate.now()
        val result = parseStringToDate("+3m", startDate = today)
        result as Result.Success
        assertDateEquals(result.value, today.plusMonths(3).year, today.plusMonths(3).monthValue, today.plusMonths(3).dayOfMonth, "Relative +3 months")
    }

    @Test
    fun `parse relative date plus 90 days`() {
        val today = LocalDate.now()
        val result = parseStringToDate("+90", startDate = today)
        result as Result.Success
        assertDateEquals(result.value, today.plusDays(90).year, today.plusDays(90).monthValue, today.plusDays(90).dayOfMonth, "Relative +90 days")
    }

    @Test
    fun `parse relative date plus 1 year`() {
        val today = LocalDate.now()
        val result = parseStringToDate("+1y", startDate = today)
        result as Result.Success
        assertDateEquals(result.value, today.plusYears(1).year, today.plusYears(1).monthValue, today.plusYears(1).dayOfMonth, "Relative +1 year")
    }

    @Test
    fun `parse invalid relative date format - missing amount`() {
        val result = parseStringToDate("+", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
    }

    @Test
    fun `parse invalid relative date format - invalid unit`() {
        val result = parseStringToDate("+1z", startDate = defaultStartDate)
        expectThat(result).isA<Result.Error>()
    }
}
