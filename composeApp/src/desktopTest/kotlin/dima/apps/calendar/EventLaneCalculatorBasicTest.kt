package dima.apps.calendar

import androidx.compose.ui.graphics.Color
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.hasSize
import strikt.assertions.isEqualTo
import java.time.LocalDate

class EventLaneCalculatorBasicTest {

    private val blue = Color.Blue
    private val red = Color.Red

    @Test
    fun `test simple non-overlapping events`() {
        val events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Event 1",
                color = blue,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 3)
            ),
            CalendarEvent.AllDay(
                id = "2",
                summary = "Event 2",
                color = red,
                startDate = LocalDate.of(2025, 8, 5),
                endDate = LocalDate.of(2025, 8, 7)
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(events)

        val aug1Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 1))
        expectThat(aug1Events).hasSize(1)
        expectThat(aug1Events[0].lane).isEqualTo(0)

        val aug5Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 5))
        expectThat(aug5Events).hasSize(1)
        expectThat(aug5Events[0].lane).isEqualTo(0)
    }

    @Test
    fun `test single day events`() {
        val events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Single Day 1",
                color = blue,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 2)
            ),
            CalendarEvent.AllDay(
                id = "2",
                summary = "Single Day 2",
                color = red,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 2)
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(events)

        val aug1Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 1))
        expectThat(aug1Events).hasSize(2)
        expectThat(aug1Events.map { it.lane }.toSet()).isEqualTo(setOf(0, 1))
    }
}