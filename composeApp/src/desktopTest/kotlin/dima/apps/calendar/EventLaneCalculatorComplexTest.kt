package dima.apps.calendar

import androidx.compose.ui.graphics.Color
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.hasSize
import strikt.assertions.isEqualTo
import java.time.LocalDate

/**
 * Tests for complex overlapping scenarios and lane reuse optimization.
 */
class EventLaneCalculatorComplexTest {

    private val blue = Color.Blue
    private val red = Color.Red
    private val green = Color.Green

    @Test
    fun `test overlapping events with different start dates`() {
        val events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Long Event",
                color = blue,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 10)
            ),
            CalendarEvent.AllDay(
                id = "2",
                summary = "Short Event 1",
                color = red,
                startDate = LocalDate.of(2025, 8, 3),
                endDate = LocalDate.of(2025, 8, 5)
            ),
            CalendarEvent.AllDay(
                id = "3",
                summary = "Short Event 2",
                color = green,
                startDate = LocalDate.of(2025, 8, 7),
                endDate = LocalDate.of(2025, 8, 9)
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(events)

        // On Aug 3rd: Long Event in lane 0, Short Event 1 in lane 1
        val aug3Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 3))
        expectThat(aug3Events).hasSize(2)
        expectThat(aug3Events.find { it.getSummary() == "Long Event" }?.lane).isEqualTo(0)
        expectThat(aug3Events.find { it.getSummary() == "Short Event 1" }?.lane).isEqualTo(1)

        // On Aug 7th: Long Event in lane 0, Short Event 2 in lane 1 (reusing lane from Short Event 1)
        val aug7Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 7))
        expectThat(aug7Events).hasSize(2)
        expectThat(aug7Events.find { it.getSummary() == "Long Event" }?.lane).isEqualTo(0)
        expectThat(aug7Events.find { it.getSummary() == "Short Event 2" }?.lane).isEqualTo(1)
    }

    @Test
    fun `test events with gaps allowing lane reuse`() {
        val events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Early Event",
                color = blue,
                startDate = LocalDate.of(2025, 8, 1),
                endDate = LocalDate.of(2025, 8, 3)
            ),
            CalendarEvent.AllDay(
                id = "2",
                summary = "Late Event",
                color = red,
                startDate = LocalDate.of(2025, 8, 5),
                endDate = LocalDate.of(2025, 8, 7)
            ),
            CalendarEvent.AllDay(
                id = "3",
                summary = "Spanning Event",
                color = green,
                startDate = LocalDate.of(2025, 8, 2),
                endDate = LocalDate.of(2025, 8, 6)
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(events)

        // Early Event should get lane 0
        val aug1Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 1))
        expectThat(aug1Events[0].lane).isEqualTo(0)

        // On Aug 2: Early Event (lane 0), Spanning Event (lane 1)
        val aug2Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 2))
        expectThat(aug2Events).hasSize(2)
        expectThat(aug2Events.find { it.getSummary() == "Early Event" }?.lane).isEqualTo(0)
        expectThat(aug2Events.find { it.getSummary() == "Spanning Event" }?.lane).isEqualTo(1)

        // On Aug 5: Spanning Event (lane 1), Late Event should reuse lane 0
        val aug5Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 5))
        expectThat(aug5Events).hasSize(2)
        expectThat(aug5Events.find { it.getSummary() == "Spanning Event" }?.lane).isEqualTo(1)
        expectThat(aug5Events.find { it.getSummary() == "Late Event" }?.lane).isEqualTo(0)
    }
}