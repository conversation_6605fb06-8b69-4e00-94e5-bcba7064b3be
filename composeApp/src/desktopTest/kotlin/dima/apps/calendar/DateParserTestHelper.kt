package dima.apps.calendar

import strikt.api.expectThat
import strikt.assertions.isEqualTo
import java.time.LocalDate

internal fun assertDateEquals(
    actual: LocalDate,
    expectedYear: Int,
    expectedMonth: Int,
    expectedDay: Int,
    message: String = ""
) {
    val expectedDate = LocalDate.of(expectedYear, expectedMonth, expectedDay)
    expectThat(actual).describedAs(message).isEqualTo(expectedDate)
}

// Define a fixed start date for most tests to ensure predictability
// Let's use January 10, 2023
internal val defaultStartDate: LocalDate = LocalDate.of(2023, 1, 10)
internal val defaultStartYear = defaultStartDate.year
internal val defaultStartMonth: Int = defaultStartDate.monthValue // 1-indexed
internal val defaultStartDay = defaultStartDate.dayOfMonth
