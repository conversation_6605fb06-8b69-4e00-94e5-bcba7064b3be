package dima.apps.calendar

import androidx.compose.ui.graphics.Color
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.hasSize
import strikt.assertions.isEqualTo
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId

class EventLaneCalculatorOriginalProblemTest {

    private val blue = Color.Blue
    private val red = Color.Red

    @Test
    fun `test original problem case - <PERSON><PERSON>, Vero halber Urlaub, Kita faengt an`() {
        val events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Kita Ferien",
                color = blue,
                startDate = LocalDate.of(2025, 8, 4),
                endDate = LocalDate.of(2025, 8, 19) // exclusive, so includes 18th
            ),
            CalendarEvent.AllDay(
                id = "2",
                summary = "Vero halber Urlaub",
                color = red,
                startDate = LocalDate.of(2025, 8, 18),
                endDate = LocalDate.of(2025, 8, 23) // exclusive, so includes 22nd
            ),
            CalendarEvent.AllDay(
                id = "3",
                summary = "Kita fängt an",
                color = blue,
                startDate = LocalDate.of(2025, 8, 19),
                endDate = LocalDate.of(2025, 8, 20) // exclusive, so only 19th
            ),
            CalendarEvent.WithTime(
                id = "44",
                summary = "Tante kommt",
                color = blue,
                startDateTime = LocalDateTime.of(2025, 8, 18, 14, 30).atZone(ZoneId.systemDefault()),
                endDateTime = LocalDateTime.of(2025, 8, 18, 15, 30).atZone(ZoneId.systemDefault()),
            ),
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(events)

        // On August 18th: Kita Ferien should be in lane 0, Vero halber Urlaub in lane 1
        val aug18Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 18))
        expectThat(aug18Events).hasSize(3)
        expectThat(aug18Events.find { it.getSummary() == "Kita Ferien" }?.lane).isEqualTo(0)
        expectThat(aug18Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(1)
        expectThat(aug18Events.find { it.getSummary() == "Tante kommt" }?.lane).isEqualTo(2)

        // On August 19th: Kita Ferien ends on 18th (endDate=19th is exclusive), 
        // so only Vero halber Urlaub (lane 1) and Kita fängt an (should reuse lane 0)
        val aug19Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 19))
        expectThat(aug19Events).hasSize(2)
        expectThat(aug19Events.find { it.getSummary() == "Kita fängt an" }?.lane).isEqualTo(0)
        expectThat(aug19Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(1)

        val aug20Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 20))
        expectThat(aug20Events).hasSize(1)
        expectThat(aug20Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(1)

        // Add Cafe Knirps as a timed event to match reality
        val cafeKnirpsTimedEvent = CalendarEvent.WithTime(
            id = "4",
            summary = "Cafe Knirps",
            color = blue,
            startDateTime = LocalDateTime.of(2025, 8, 21, 16, 0).atZone(ZoneId.systemDefault()),
            endDateTime = LocalDateTime.of(2025, 8, 21, 17, 0).atZone(ZoneId.systemDefault()),
            description = null
        )

        // Test with mixed event types (all-day + timed)
        val mixedEvents = events + cafeKnirpsTimedEvent
        val mixedLaneAssignments = calculator.calculateLanes(mixedEvents)

        val aug21Events = mixedLaneAssignments.getEventsForDate(LocalDate.of(2025, 8, 21))
        expectThat(aug21Events).hasSize(2) // Only Vero halber Urlaub (all-day), Cafe Knirps is timed
        expectThat(aug21Events.find { it.getSummary() == "Cafe Knirps" }?.lane).isEqualTo(0)
        expectThat(aug21Events.find { it.getSummary() == "Vero halber Urlaub" }?.lane).isEqualTo(1)
    }

    @Test
    fun `test horizontal continuity is maintained across week boundaries`() {
        val events = listOf(
            CalendarEvent.AllDay(
                id = "1",
                summary = "Long Event",
                color = blue,
                startDate = LocalDate.of(2025, 8, 15), // Friday
                endDate = LocalDate.of(2025, 8, 19) // Tuesday (exclusive)
            ),
            CalendarEvent.AllDay(
                id = "2",
                summary = "Weekend Event",
                color = red,
                startDate = LocalDate.of(2025, 8, 16), // Saturday
                endDate = LocalDate.of(2025, 8, 18) // Monday (exclusive)
            )
        )

        val calculator = EventLaneCalculator()
        val laneAssignments = calculator.calculateLanes(events)

        // Friday: Long Event starts in lane 0
        val aug15Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 15))
        expectThat(aug15Events).hasSize(1)
        expectThat(aug15Events.find { it.getSummary() == "Long Event" }?.lane).isEqualTo(0)

        // Saturday: Long Event continues in lane 0, Weekend Event in lane 1
        val aug16Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 16))
        expectThat(aug16Events).hasSize(2)
        expectThat(aug16Events.find { it.getSummary() == "Long Event" }?.lane).isEqualTo(0)
        expectThat(aug16Events.find { it.getSummary() == "Weekend Event" }?.lane).isEqualTo(1)

        // Monday: Long Event continues in lane 0 (Weekend Event ended on Sunday)
        val aug18Events = laneAssignments.getEventsForDate(LocalDate.of(2025, 8, 18))
        expectThat(aug18Events).hasSize(1)
        expectThat(aug18Events.find { it.getSummary() == "Long Event" }?.lane).isEqualTo(0)
    }
}