package dima.apps.treeSitter

import dima.treeSitter.TreeSitter
import dima.treeSitter.TreeSitterLanguage
import dima.utils.getStringLengthRangesFromByteRange
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNull

class TreeSitterTest {

    @Test
    fun testEmojis() {
        val s = """1😄123😄456"""
        expectThat(s[0])
            .isEqualTo('1')
        expectThat(s.substring(1, 3))
            .isEqualTo("😄")
    }

    @Test
    fun testEmojiByteRange() {
        val emoji = "😄"
        expectThat(emoji.toByteArray(Charsets.UTF_8).size)
            .isEqualTo(4)
        expectThat(emoji.length)
            .isEqualTo(2)
        val simpleCharacter = "a"
        expectThat(simpleCharacter.toByteArray(Charsets.UTF_8).size)
            .isEqualTo(1)
    }

    /**
     * Rust returns:
     *
     * keyword 0 3 for: val
     * string 11 17 for: "😄"
     */
    @Test
    fun testByteArrayEmojis() {
        val s = "val test = \"😄\""
        val asByteArray = s.toByteArray()
        expectThat(asByteArray.size)
            .isEqualTo(17)
        expectThat("😄".length)
            .isEqualTo(2)
        expectThat("😄".toByteArray().size)
            .isEqualTo(4)
        expectThat("val".toByteArray().size)
            .isEqualTo(3)
        expectThat("😄".toByteArray().slice(0..<4).toByteArray().toString(Charsets.UTF_8))
            .isEqualTo("😄")
        expectThat("val".toByteArray().slice(0..<3).toByteArray())
            .isEqualTo("val".toByteArray(Charsets.UTF_8))
        expectThat(asByteArray.slice(0..<17).toByteArray().toString(Charsets.UTF_8))
            .isEqualTo("val test = \"😄\"".toByteArray(Charsets.UTF_8).toString(Charsets.UTF_8))
    }

    /**
     * Rust returns:
     *
     * keyword 0 3 for: val
     * string 11 17 for: "😄"
     */
    @Test
    fun testEmojiStringByteMapping() {
        val s = """val test = "😄""""
        val valRange = s.getStringLengthRangesFromByteRange(0, 3)
        val valStart = 0
        val valEnd = 3
        expectThat(valRange)
            .isEqualTo(valStart to valEnd)
        expectThat(s.substring(valStart, valEnd))
            .isEqualTo("val")
        val stringWithEmojiRange = s.getStringLengthRangesFromByteRange(11, 17)
        val stringWithEmojiStart = 11
        val stringWithEmojiEnd = 15
        expectThat(stringWithEmojiRange)
            .isEqualTo(stringWithEmojiStart to stringWithEmojiEnd)
        expectThat(s.substring(stringWithEmojiStart, stringWithEmojiEnd))
            .isEqualTo("\"😄\"")
    }

    @Test
    fun testCliWithEmoji() {
        runBlocking {
            val language = TreeSitterLanguage.Kotlin
            val highlights = TreeSitter.startTreeSitterCliAndExtractHighlights(
                "val test = \"😄\"\nval test = \"😄\"",
                language,
                TreeSitter.getHighlightsSchemeFileContents(language)
            )
            expectThat(highlights.error)
                .isNull()
            expectThat(highlights.highlights.size)
                .isEqualTo(8)
            expectThat(highlights.highlights.first())
                .isEqualTo(
                    TreeSitter.CliHighlight(
                        captureName = "keyword",
                        startByte = 0,
                        endByte = 3,
                        content = "val"
                    )
                )
            expectThat(highlights.highlights[3])
                .isEqualTo(
                    TreeSitter.CliHighlight(
                        captureName = "string",
                        startByte = 11,
                        endByte = 15,
                        content = "\"😄\""
                    )
                )
            expectThat(highlights.highlights.last())
                .isEqualTo(
                    TreeSitter.CliHighlight(
                        captureName = "string",
                        startByte = 27,
                        endByte = 31,
                        content = "\"😄\""
                    )
                )
        }
    }

    @Test
    fun testCodePoints() {
        val emojiByteCount = Character.toString("😄".codePointAt(0)).toByteArray(Charsets.UTF_8).size
        expectThat(emojiByteCount)
            .isEqualTo(4)
        val vByteCount = Character.toString("v".codePointAt(0)).toByteArray(Charsets.UTF_8).size
        expectThat(vByteCount)
            .isEqualTo(1)
    }
}
