package dima.clipboard

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNull

class ClipboardTest {

    @Test
    @Disabled("Disabled because it works, but it clears the system clipboard which is annoying")
    fun `test clearing, setting and reading clipboard`() {
        clearClipboard()
        expectThat(readClipboard()).isNull()
        setClipboard("hello")
        expectThat(readClipboard()).isEqualTo("hello")
        clearClipboard()
        expectThat(readClipboard()).isNull()
    }
}