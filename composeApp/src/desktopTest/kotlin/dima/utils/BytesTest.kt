package dima.utils

import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo

class BytesTest {

    @Test
    fun `toNiceHumanByteString handles bytes correctly`() {
        expectThat(0L.toNiceHumanByteString()).isEqualTo("0 Bytes")
        expectThat(1L.toNiceHumanByteString()).isEqualTo("1 Byte")
        expectThat(500L.toNiceHumanByteString()).isEqualTo("500 Bytes")
        expectThat(999L.toNiceHumanByteString()).isEqualTo("999 Bytes")
    }

    @Test
    fun `toNiceHumanByteString handles kilobytes correctly`() {
        expectThat(1000L.toNiceHumanByteString()).isEqualTo("1 KB") // Exactly 1 KB (1000 Bytes base)
        expectThat(1024L.toNiceHumanByteString()).isEqualTo("1 KB") // Common 1 KB
        expectThat(1500L.toNiceHumanByteString()).isEqualTo("2 KB") // Rounds up
        expectThat(1499L.toNiceHumanByteString()).isEqualTo("1 KB") // Rounds down
        expectThat(999_999L.toNiceHumanByteString()).isEqualTo("1000 KB") // Just under 1 MB
    }

    @Test
    fun `toNiceHumanByteString handles megabytes correctly with one decimal place`() {
        expectThat(1_000_000L.toNiceHumanByteString()).isEqualTo("1.0 MB") // Exactly 1 MB
        expectThat(1_048_576L.toNiceHumanByteString()).isEqualTo("1.0 MB") // 1 MiB, close to 1MB
        expectThat(1_500_000L.toNiceHumanByteString()).isEqualTo("1.5 MB")
        expectThat(1_570_000L.toNiceHumanByteString()).isEqualTo("1.6 MB") // Rounding
        expectThat(999_999_999L.toNiceHumanByteString()).isEqualTo("1000.0 MB") // Just under 1 GB
    }

    @Test
    fun `toNiceHumanByteString handles gigabytes correctly with two decimal places`() {
        expectThat(1_000_000_000L.toNiceHumanByteString()).isEqualTo("1.00 GB") // Exactly 1 GB
        expectThat(1_073_741_824L.toNiceHumanByteString()).isEqualTo("1.07 GB") // 1 GiB
        expectThat(1_500_000_000L.toNiceHumanByteString()).isEqualTo("1.50 GB")
        expectThat(1_570_000_000L.toNiceHumanByteString()).isEqualTo("1.57 GB")
        expectThat(1_575_000_000L.toNiceHumanByteString()).isEqualTo("1.58 GB") // Rounding
    }

    @Test
    fun `toNiceHumanByteString handles terabytes correctly with two decimal places`() {
        expectThat(1_000_000_000_000L.toNiceHumanByteString()).isEqualTo("1.00 TB")
        expectThat(1_099_511_627_776L.toNiceHumanByteString()).isEqualTo("1.10 TB") // 1 TiB
        expectThat(1_500_000_000_000L.toNiceHumanByteString()).isEqualTo("1.50 TB")
    }

    @Test
    fun `toNiceHumanByteString handles petabytes correctly with two decimal places`() {
        expectThat(1_000_000_000_000_000L.toNiceHumanByteString()).isEqualTo("1.00 PB")
        expectThat(1_125_899_906_842_624L.toNiceHumanByteString()).isEqualTo("1.13 PB") // 1 PiB
    }

    @Test
    fun `toNiceHumanByteString handles larger units (EB, ZB, YB) correctly`() {
        expectThat(1_000_000_000_000_000_000L.toNiceHumanByteString()).isEqualTo("1.00 EB") // Exabyte
        // Max Long is around 9.22 EB, so YB is too large for Long.
        // Test with a large Long value that should resolve to EB or ZB
        expectThat(9_223_372_036_854_775_807L.toNiceHumanByteString()).isEqualTo("9.22 EB")
    }

    @Test
    fun `toCompactHumanByteString test cases`() {
        expectThat(100L.toCompactHumanByteString()).isEqualTo("100B")
        expectThat(1024L.toCompactHumanByteString()).isEqualTo("1K")
        expectThat(2048L.toCompactHumanByteString()).isEqualTo("2K")
        expectThat(1048576L.toCompactHumanByteString()).isEqualTo("1M") // 1024 * 1024
        expectThat(1073741824L.toCompactHumanByteString()).isEqualTo("1G") // 1024 * 1024 * 1024
    }
}
