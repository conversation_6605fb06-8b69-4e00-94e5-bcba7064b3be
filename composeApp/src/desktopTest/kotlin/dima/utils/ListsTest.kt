package dima.utils

import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNull

class ListsTest {

    @Test
    fun testFlapMap() {
        val start = listOf("1")
        val mapped = start.flatMap {
            listOf("-g", "!$it")
        }
        expectThat(mapped)
            .isEqualTo(listOf("-g", "!1"))
    }

    /**
     * Test if a single minus does not exclude everything.
     */
    @Test
    fun testFilterHumanWithMinus() {
        val first = "#2 - hallo"
        val start = listOf(first, "#200 - bye")
        val filtered = start.filterHuman("#2 -")
        expectThat(filtered)
            .isEqualTo(start)
    }

    data class TestItem(val id: String, val name: String)

    private val testItems = listOf(
        TestItem("id1", "Item 1"),
        TestItem("id2", "Item 2"),
        TestItem("id3", "Item 3")
    )
    private val mapper: (TestItem) -> String = { it.id }

    @Test
    fun `getInitialSelected returns null for empty list`() {
        val result = Lists.getInitialSelected(emptyList(), "id1", mapper)
        expectThat(result).isNull()
    }

    @Test
    fun `getInitialSelected returns 0 if fromSetting is null and list is not empty`() {
        val result = Lists.getInitialSelected(testItems, null, mapper)
        expectThat(result).isEqualTo(0)
    }

    @Test
    fun `getInitialSelected returns correct index if fromSetting matches an item`() {
        val result = Lists.getInitialSelected(testItems, "id2", mapper)
        expectThat(result).isEqualTo(1)
    }

    @Test
    fun `getInitialSelected returns 0 for single item list and matching fromSetting`() {
        val singleItemList = listOf(TestItem("single", "Single Item"))
        val result = Lists.getInitialSelected(singleItemList, "single", mapper)
        expectThat(result).isEqualTo(0)
    }


    @Test
    fun `getSelectedOrNext returns 0 if selected and fromSetting are null and list is not empty`() {
        val result = Lists.getSelectedOrNext(null, testItems, null, mapper)
        expectThat(result).isEqualTo(0)
    }

    @Test
    fun `getSelectedOrNext returns index from fromSetting if selected is null`() {
        val result = Lists.getSelectedOrNext(null, testItems, "id3", mapper)
        expectThat(result).isEqualTo(2)
    }

    @Test
    fun `getSelectedOrNext returns index from fromSetting if selected is valid`() {
        // Even if 'selected' is 0, 'fromSetting' should take precedence if it's valid and different.
        val result = Lists.getSelectedOrNext(0, testItems, "id2", mapper)
        expectThat(result).isEqualTo(1)
    }

    @Test
    fun `getSelectedOrNext coerces selected if it's out of bounds (too high) and fromSetting is null`() {
        val result = Lists.getSelectedOrNext(5, testItems, null, mapper)
        expectThat(result).isEqualTo(0) // Should be 0 as fromSetting is null, it defaults to first.
    }

    @Test
    fun `getSelectedOrNext prioritizes fromSetting even if selected is out of bounds`() {
        val result = Lists.getSelectedOrNext(10, testItems, "id2", mapper)
        expectThat(result).isEqualTo(1) // fromSetting "id2" exists.
    }


    @Test
    fun `getSelectedOrNext returns 0 for single item list, null selected, null fromSetting`() {
        val singleItemList = listOf(TestItem("single", "Single Item"))
        val result = Lists.getSelectedOrNext(null, singleItemList, null, mapper)
        expectThat(result).isEqualTo(0)
    }

    @Test
    fun `getSelectedOrNext returns 0 for single item list, null selected, matching fromSetting`() {
        val singleItemList = listOf(TestItem("single", "Single Item"))
        val result = Lists.getSelectedOrNext(null, singleItemList, "single", mapper)
        expectThat(result).isEqualTo(0)
    }

    @Test
    fun `getSelectedOrNext returns 0 for single item list, selected out of bounds, matching fromSetting`() {
        val singleItemList = listOf(TestItem("single", "Single Item"))
        val result = Lists.getSelectedOrNext(5, singleItemList, "single", mapper)
        expectThat(result).isEqualTo(0)
    }
}