package dima.utils

import com.github.burnett01.expression.expression
import org.junit.jupiter.api.Test
import strikt.api.expectThat
import strikt.assertions.isEqualTo
import strikt.assertions.isNull

class RegexpBuilderTest {

    @Test
    fun testBuilding() {
        val dateRegex = expression({
            start()
            capture {
                digit { exact(4) }
                literal('-')
                digit { exact(2) }
                literal('-')
                digit { exact(2) }
            }
        })!!
        val s = "2024-01-01"
        expectThat(dateRegex.compile().find(s)!!.value)
            .isEqualTo(s)
        val s2 = "2024-01-01 "
        expectThat(dateRegex.compile().find(s2)!!.value)
            .isEqualTo(s)
        val whiteSpaceAtStart = " 2024-01-01 "
        expectThat(dateRegex.compile().find(whiteSpaceAtStart))
            .isNull()
    }

}