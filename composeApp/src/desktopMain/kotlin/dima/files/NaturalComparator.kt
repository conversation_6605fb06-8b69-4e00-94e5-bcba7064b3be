package dima.files

import org.jetbrains.annotations.Contract

// copied from org.jetbrains.kotlin.com.intellij.openapi.util.text package

private fun isDecimalDigit(c: Char): Boolean {
    return c >= '0' && c <= '9'
}

private fun toUpperCase(a: Char): Char {
    return if (a < 'a') {
        a
    } else {
        if (a <= 'z') (a.code + -32).toChar() else a.uppercaseChar()
    }
}

private fun toLowerCase(a: Char): Char {
    return if (a > 'z') {
        a.lowercase<PERSON>har()
    } else {
        if (a >= 'A' && a <= 'Z') (a.code + 32).toChar() else a
    }
}

private fun compare(c1: Char, c2: Char, ignoreCase: Boolean): Int {
    var d = c1.code - c2.code
    if (d != 0 && ignoreCase) {
        val u1: Char = toUpperCase(c1)
        val u2: Char = toUpperCase(c2)
        d = u1.code - u2.code
        if (d != 0) {
            d = toLowerCase(u1).code - toLowerCase(u2).code
        }

        return d
    } else {
        return d
    }
}

class NaturalComparator : Comparator<String?> {

    override fun compare(s1: String?, s2: String?): Int {
        return if (s1 === s2) {
            0
        } else if (s1 == null) {
            -1
        } else {
            if (s2 == null) 1 else naturalCompare(s1, s2, s1.length, s2.length, true)
        }
    }

    companion object {

        @Contract(pure = true)
        private fun naturalCompare(s1: String, s2: String, length1: Int, length2: Int, ignoreCase: Boolean): Int {
            var i = 0

            var j: Int
            j = 0
            while (i < length1 && j < length2) {
                val ch1 = s1[i]
                val ch2 = s2[j]
                if ((isDecimalDigit(ch1) || ch1 == ' ') && (isDecimalDigit(ch2) || ch2 == ' ')) {
                    val start1 = skipChar(s1, skipChar(s1, i, length1, ' '), length1, '0')
                    val start2 = skipChar(s2, skipChar(s2, j, length2, ' '), length2, '0')
                    val end1 = skipDigits(s1, start1, length1)
                    val end2 = skipDigits(s2, start2, length2)
                    val lengthDiff = end1 - start1 - (end2 - start2)
                    if (lengthDiff != 0) {
                        return lengthDiff
                    }

                    val numberDiff = compareCharRange(s1, s2, start1, start2, end1)
                    if (numberDiff != 0) {
                        return numberDiff
                    }

                    val fullLengthDiff = end1 - i - (end2 - j)
                    if (fullLengthDiff != 0) {
                        return fullLengthDiff
                    }

                    val leadingDiff = compareCharRange(s1, s2, i, j, start1)
                    if (leadingDiff != 0) {
                        return leadingDiff
                    }

                    i = end1 - 1
                    j = end2 - 1
                } else {
                    val diff = compareChars(ch1, ch2, ignoreCase)
                    if (diff != 0) {
                        return diff
                    }
                }

                ++i
                ++j
            }

            return if (i < length1) {
                1
            } else if (j < length2) {
                -1
            } else if (length1 != length2) {
                length1 - length2
            } else {
                if (ignoreCase) naturalCompare(s1, s2, length1, length2, false) else 0
            }
        }

        private fun compareCharRange(s1: String, s2: String, offset1: Int, offset2: Int, end1: Int): Int {
            var i = offset1

            var j = offset2
            while (i < end1) {
                val diff = s1[i].code - s2[j].code
                if (diff != 0) {
                    return diff
                }

                ++i
                ++j
            }

            return 0
        }

        private fun compareChars(ch1: Char, ch2: Char, ignoreCase: Boolean): Int {
            return if (ch1 == ' ' && ch2 > ' ' && ch2 < '0') {
                1
            } else {
                if (ch2 == ' ' && ch1 > ' ' && ch1 < '0') -1 else compare(
                    ch1, ch2,
                    ignoreCase
                )
            }
        }

        private fun skipDigits(s: String, start: Int, end: Int): Int {
            var start = start
            while (start < end && isDecimalDigit(s[start])) {
                ++start
            }

            return start
        }

        private fun skipChar(s: String, start: Int, end: Int, c: Char): Int {
            var start = start
            while (start < end && s[start] == c) {
                ++start
            }

            return start
        }
    }
}
