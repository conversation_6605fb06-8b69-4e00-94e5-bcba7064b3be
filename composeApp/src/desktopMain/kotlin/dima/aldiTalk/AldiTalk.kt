package dima.aldiTalk

import org.openqa.selenium.By
import org.openqa.selenium.Keys
import org.openqa.selenium.WebDriver
import org.openqa.selenium.WebElement
import org.openqa.selenium.chrome.ChromeDriver
import org.openqa.selenium.chrome.ChromeOptions
import org.openqa.selenium.support.ui.WebDriverWait
import java.time.Duration.ofSeconds

fun waitUntilElementIsDisplayed(driver: WebDriver, by: By): WebElement {
    WebDriverWait(driver, ofSeconds(9)).until {
        val rect = driver.findElement(by).rect
        return@until rect.width > 0 || rect.height > 0
    }
    return driver.findElement(by)
}

fun fillInPhone(driver: WebDriver) {
    val root = driver.findElement(By.id("idToken3_od")).shadowRoot
    val phoneNumber = root.findElement(By.cssSelector("#input-1"))
    phoneNumber.sendKeys("+4917653951965")
}

fun fillInPassword(driver: WebDriver) {
    val root = driver.findElement(By.id("idToken4_od")).shadowRoot
    val password = root.findElement(By.cssSelector("#input-2"))
    password.sendKeys("AliciaStaude123?")
    password.sendKeys(Keys.ENTER)
}

fun getAldiTalkUsage(): String {
    var driver: WebDriver? = null
    try {
        val options = ChromeOptions()
        options.addArguments("--headless")
        driver = ChromeDriver(options)
        driver.apply {
            get("https://www.alditalk-kundenbetreuung.de/de")
            val el = waitUntilElementIsDisplayed(this, By.id("usercentrics-root"))
            driver.executeScript("arguments[0].style.display = 'none';", el)
            fillInPhone(this)
            fillInPassword(this)
            val usageRemaining = waitUntilElementIsDisplayed(this, By.className("pack__usage-remaining"))
            val usageUnit = findElement(By.className("pack__usage-unit"))
            val ofTotal = findElement(By.className("oftotal"))
            return usageRemaining.text + " " + usageUnit.text + " " + ofTotal.text
        }
    } finally {
        driver?.quit()
    }
}