package dima.dialogs.completion

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.Key
import dima.dialogs.DialogIdentifier
import dima.dialogs.openDialog

data class CompletionDialogCmdContext(val query: String, val dialogId: Long)

/**
 * @param closeDialog if true, the dialog is closed and then [callback] is invoked. [Params.onDismiss] is also invoked.
 */
data class CompletionDialogCmdAction(
    val key: Key,
    val text: String,
    val closeDialog: Boolean = false,
    val callback: (CompletionDialogCmdContext) -> Unit
)

/**
 * @param prefixView a list of composables that are displayed before the text in the candidate.
 *   The modifier is passed to optionally set the background color.
 */
data class CompletionDialogCandidate(
    val text: String,
    val prefixView: @Composable ((Modifier) -> Unit)? = null,
    val additionalSearchString: String? = null
)

data class CompletionDialogAccept(
    val text: String,
    val dialogId: Long,
    val index: Int?,
)

internal data class Params(
    val title: String,
    val candidates: List<CompletionDialogCandidate>,
    val initialQuery: String,
    val allowMatchingLiterally: Boolean,
    val keepDialogOpenOnAccept: Boolean,
    val maxLinesPerCandidate: Int,
    val allowChangingCandidatesViaEventToFixDialogHeight: Boolean,
    val hideCopyCmdActionInBottomBar: Boolean,
    val matchRequired: Boolean,
    val cmdActions: List<CompletionDialogCmdAction>,
    val onDismiss: (() -> Unit)?,
    val onAccept: (CompletionDialogAccept) -> Unit
)

internal data class Candidate(
    val text: String,
    val index: Int,
    val prefixView: @Composable ((Modifier) -> Unit)?,
    val additionalSearchString: String?
)

/**
 * @param allowChangingCandidatesViaEventToFixDialogHeight if false, the dialog always takes the full height.
 * If true, initialize the dialog height based on all initial candidates and never allow changing it, and
 * if true do not use it together with [ChangeCompletionDialogCandidates]. See [CompletionDialog] for more doc on that.
 */
@JvmName("openCompletionDialogString")
fun openCompletionDialog(
    title: String,
    candidates: List<String>,
    initialQuery: String = "",
    allowMatchingLiterally: Boolean = false,
    hideCopyCmdActionInBottomBar: Boolean = false,
    keepDialogOpenOnAccept: Boolean = false,
    maxLinesPerCandidate: Int = 1,
    allowChangingCandidatesViaEventToFixDialogHeight: Boolean = false,
    matchRequired: Boolean = true,
    cmdActions: List<CompletionDialogCmdAction> = emptyList(),
    onDismiss: (() -> Unit)? = null,
    onAccept: (CompletionDialogAccept) -> Unit
) {
    openCompletionDialog(
        title = title,
        candidates = candidates.map { CompletionDialogCandidate(it) },
        initialQuery = initialQuery,
        allowMatchingLiterally = allowMatchingLiterally,
        maxLinesPerCandidate = maxLinesPerCandidate,
        keepDialogOpenOnAccept = keepDialogOpenOnAccept,
        matchRequired = matchRequired,
        hideCopyCmdActionInBottomBar = hideCopyCmdActionInBottomBar,
        cmdActions = cmdActions,
        allowChangingCandidatesViaEventToFixDialogHeight = allowChangingCandidatesViaEventToFixDialogHeight,
        onDismiss = onDismiss,
        onAccept = onAccept
    )
}

/**
 * @param allowChangingCandidatesViaEventToFixDialogHeight if false, the dialog always takes the full height.
 * If true, initialize the dialog height based on all initial candidates and never allow changing it, and
 * if true do not use it together with [ChangeCompletionDialogCandidates]. See [CompletionDialog] for more doc on that.
 */
fun openCompletionDialog(
    title: String,
    candidates: List<CompletionDialogCandidate>,
    initialQuery: String = "",
    hideCopyCmdActionInBottomBar: Boolean = false,
    allowMatchingLiterally: Boolean = false,
    keepDialogOpenOnAccept: Boolean = false,
    maxLinesPerCandidate: Int = 1,
    matchRequired: Boolean = true,
    allowChangingCandidatesViaEventToFixDialogHeight: Boolean = false,
    cmdActions: List<CompletionDialogCmdAction> = emptyList(),
    onDismiss: (() -> Unit)? = null,
    onAccept: (CompletionDialogAccept) -> Unit
) {
    validateDialogParams(cmdActions, allowMatchingLiterally)
    val params = Params(
        title = title,
        candidates = candidates,
        initialQuery = initialQuery,
        allowMatchingLiterally = allowMatchingLiterally,
        keepDialogOpenOnAccept = keepDialogOpenOnAccept,
        maxLinesPerCandidate = maxLinesPerCandidate,
        hideCopyCmdActionInBottomBar = hideCopyCmdActionInBottomBar,
        matchRequired = matchRequired,
        allowChangingCandidatesViaEventToFixDialogHeight = allowChangingCandidatesViaEventToFixDialogHeight,
        cmdActions = cmdActions,
        onDismiss = onDismiss,
        onAccept = onAccept
    )
    openDialog(DialogIdentifier.Completion, params) { id, data ->
        CompletionDialog(id, data!!)
    }
}

private fun validateDialogParams(
    cmdActions: List<CompletionDialogCmdAction>,
    allowMatchingLiterally: Boolean
) {
    val isAlreadyBoundForCopy = cmdActions.any { it.key == Key.E }
    if (isAlreadyBoundForCopy) {
        throw Exception("cmdActions specifies Key.E, but this is not allowed since copy candidate already requires it")
    }
    if (allowMatchingLiterally) {
        val isAlreadyBound = cmdActions.any { it.key == Key.Minus }
        if (isAlreadyBound) {
            throw Exception("cmdActions specifies Key.Minus, but matching literally also requires it")
        }
    }
}
