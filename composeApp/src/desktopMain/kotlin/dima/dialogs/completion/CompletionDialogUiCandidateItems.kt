package dima.dialogs.completion

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dima.dialogs.closeDialogWithId
import dima.text.TextMarked
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.truncateWithEllipsis

@Composable
internal fun CompletionDialogUiCandidateItemWithoutPrefixView(
    candidate: Candidate,
    listState: LazyListState,
    query: String,
    isSelected: <PERSON><PERSON><PERSON>,
    maxLines: Int,
    dialogWidth: Dp,
    onClick: () -> Unit
) {
    var modifier = Modifier
        .then(
            if (listState.canScrollForward || listState.canScrollBackward) {
                Modifier.padding(end = CompletionDialogStyling.rowEndPaddingForScrollbar)
            } else {
                Modifier
            }
        )
        .width(dialogWidth)
        .pointerHoverIcon(PointerIcon.Hand)
        .clickableWithoutBackgroundRipple(onClick = onClick)

    if (isSelected) {
        modifier = modifier.background(GlobalStyling.getSelectedBackgroundColor())
    }

    modifier = modifier.padding(
        horizontal = 8.dp,
        vertical = if (maxLines == 1) 4.dp else 8.dp
    )

    TextMarked(
        text = candidate.text.truncateWithEllipsis(900),
        searchQuery = query,
        color = GlobalStyling.getTextColor(),
        highlightNewLines = true,
        maxLines = maxLines,
        overflow = TextOverflow.Ellipsis,
        modifier = modifier
    )
}

@Composable
internal fun CompletionDialogUiCandidateItemWithPrefixView(
    dialogWidth: Dp,
    listState: LazyListState,
    params: Params,
    candidate: Candidate,
    dialogId: Long,
    currentCandidate: Candidate?,
    prefixView: @Composable (Modifier) -> Unit,
    query: String
) {
    var modifier = Modifier
        .then(
            if (listState.canScrollForward || listState.canScrollBackward) {
                Modifier.padding(end = CompletionDialogStyling.rowEndPaddingForScrollbar)
            } else {
                Modifier
            }
        )
        .width(dialogWidth)
        .pointerHoverIcon(PointerIcon.Hand)
        .clickableWithoutBackgroundRipple {
            if (!params.keepDialogOpenOnAccept) {
                params.onAccept(
                    CompletionDialogAccept(
                        text = candidate.text,
                        index = candidate.index,
                        dialogId = dialogId
                    )
                )
            }
            closeDialogWithId(dialogId)
        }
    if (currentCandidate == candidate) {
        modifier = modifier.background(GlobalStyling.getSelectedBackgroundColor())
    }
    modifier = modifier.padding(
        horizontal = 8.dp,
        vertical = if (params.maxLinesPerCandidate == 1) 4.dp else 8.dp
    )
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
    ) {
        prefixView.invoke(Modifier)
        TextMarked(
            // truncate since the dialog width is limited anyway
            text = candidate.text.truncateWithEllipsis(900),
            searchQuery = query,
            color = GlobalStyling.getTextColor(),
            highlightNewLines = true,
            maxLines = params.maxLinesPerCandidate,
            overflow = TextOverflow.Ellipsis,
            modifier = modifier
        )
    }
}
