package dima.dialogs.completion

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dima.utils.SmartTextFieldCandidateCount
import dima.utils.TextFieldWithCandidateCountWithoutAnimation

@Composable
internal fun CompletionDialogSearchTextField(
    queryState: TextFieldValue,
    onQueryChange: (TextFieldValue) -> Unit,
    title: String,
    totalCandidates: Int,
    filteredCandidates: Int,
    focusRequester: FocusRequester,
    dialogWidth: Dp
) {
    Row(
        modifier = Modifier
            .padding(horizontal = 24.dp, vertical = 12.dp)
    ) {
        TextFieldWithCandidateCountWithoutAnimation(
            value = queryState,
            onValueChange = onQueryChange,
            count = SmartTextFieldCandidateCount(filteredCandidates, totalCandidates),
            topLabel = title,
            focusRequester = focusRequester,
            modifier = Modifier
                .widthIn(min = dialogWidth.coerceAtMost(200.dp), max = 900.dp)
        )
    }
}
