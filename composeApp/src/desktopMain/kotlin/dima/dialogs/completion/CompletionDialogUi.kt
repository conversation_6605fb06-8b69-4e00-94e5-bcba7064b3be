package dima.dialogs.completion

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dima.dialogs.closeDialogWithId
import dima.utils.scrollbarStyleThemed

@Composable
internal fun CompletionDialogUi(
    dialogWidth: Dp,
    dialogHeight: Dp,
    query: String,
    queryState: TextFieldValue,
    params: Params,
    initialCandidates: List<Candidate>,
    filteredCandidates: List<Candidate>,
    searchFocusRequester: FocusRequester,
    listState: LazyListState,
    currentCandidate: Candidate?,
    dialogId: Long,
    shouldMatchHumanly: Boolean,
    onQueryChange: (String) -> Unit,
    onQueryStateChange: (TextFieldValue) -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(dialogWidth)
            .then(
                if (params.allowChangingCandidatesViaEventToFixDialogHeight) {
                    Modifier
                } else {
                    if (dialogHeight == Dp.Unspecified) {
                        Modifier
                    } else {
                        Modifier.height(dialogHeight)
                    }
                }
            )
    ) {
        CompletionDialogSearchTextField(
            queryState = queryState,
            onQueryChange = {
                onQueryStateChange(it)
                onQueryChange(it.text)
            },
            title = params.title,
            totalCandidates = initialCandidates.size,
            filteredCandidates = filteredCandidates.size,
            focusRequester = searchFocusRequester,
            dialogWidth = dialogWidth
        )
        if (filteredCandidates.isEmpty() && params.matchRequired) {
            Text(
                "No matches found",
                color = GlobalStyling.getRedTextColor(),
                modifier = Modifier
                    .padding(12.dp)
            )
        }
        Box(
            modifier = Modifier.weight(
                // slight fill hack to measure dialog height initially with all candidates displayed
                1f, fill = if (params.allowChangingCandidatesViaEventToFixDialogHeight) {
                    true
                } else {
                    dialogHeight != Dp.Unspecified
                }
            )
        ) {
            LazyColumn(state = listState) {
                items(filteredCandidates) { candidate ->
                    if (candidate.prefixView == null) {
                        CompletionDialogUiCandidateItemWithoutPrefixView(
                            candidate = candidate,
                            listState = listState,
                            query = query,
                            isSelected = currentCandidate == candidate,
                            maxLines = params.maxLinesPerCandidate,
                            dialogWidth = dialogWidth,
                            onClick = {
                                params.onAccept(
                                    CompletionDialogAccept(
                                        text = candidate.text,
                                        dialogId = dialogId,
                                        index = candidate.index
                                    )
                                )
                                if (!params.keepDialogOpenOnAccept) {
                                    closeDialogWithId(dialogId)
                                }
                            }
                        )
                    } else {
                        CompletionDialogUiCandidateItemWithPrefixView(
                            dialogWidth = dialogWidth,
                            listState = listState,
                            params = params,
                            candidate = candidate,
                            dialogId = dialogId,
                            currentCandidate = currentCandidate,
                            prefixView = candidate.prefixView,
                            query = query
                        )
                    }
                }
            }
            // insert only into layout after the LazyColumn since if this is initially in the layout,
            // it spans the whole height
            Box(modifier = Modifier.matchParentSize()) {
                VerticalScrollbar(
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = listState),
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(horizontal = 3.dp),
                )
            }
        }
        CompletionDialogUiBottomHelpKeys(params, dialogWidth, shouldMatchHumanly)
    }
}
