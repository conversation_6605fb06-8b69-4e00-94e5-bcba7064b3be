package dima.dialogs.completion

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dima.dialogs.help.HelpDialogKeyRow
import dima.utils.asLowerCase

@Composable
internal fun CompletionDialogUiBottomHelpKeys(
    params: Params,
    dialogWidth: Dp,
    shouldMatchHumanly: Boolean
) {
    val actions: List<Pair<List<String>, String>> = buildList {
        params.cmdActions.forEach { cmdAction ->
            add(listOf(cmdAction.key.asLowerCase()) to cmdAction.text)
        }
        if (params.allowMatchingLiterally) {
            if (shouldMatchHumanly) {
                add(listOf("-") to "Match literally")
            } else {
                add(listOf("-") to "Match fuzzy")
            }
        }
        if (!params.hideCopyCmdActionInBottomBar) {
            add(listOf("e") to "Copy")
        }
        if (!params.matchRequired) {
            add(listOf("⏎") to "Accept with query")
        }
    }

    if (actions.isNotEmpty()) {
        Row(
            horizontalArrangement = Arrangement.End,
            modifier = Modifier
                .width(dialogWidth)
                .padding(12.dp)
        ) {
            actions.forEach {
                HelpDialogKeyRow(
                    keys = it.first,
                    text = it.second,
                    isCmd = true
                )
            }
        }
    }
}