package dima.dialogs.completion

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dialogs
import dima.events.collectEvent
import dima.os.copyToClipboard
import dima.utils.RecentHistory
import dima.utils.filterHuman

/**
 * There was a time before the [ChangeCompletionDialogCandidates] event, where the dialog height was
 * initially calculated with all visible candidates and then set as a fixed height.
 * But on refreshing the candidates, it is pretty difficult to calculate the new height because it needs to temporarily
 * display all candidates. Sometimes, not all candiates are visible, depending on query.
 * Therefore, the dialog height now always fills the entire height.
 */
@Composable
fun CompletionDialog(id: Long, data: Any) {
    val params = remember { data as Params }
    var initialCandidates: List<Candidate> by remember {
        mutableStateOf(
            params.candidates.mapIndexed { i, candidate ->
                Candidate(candidate.text, i, candidate.prefixView, candidate.additionalSearchString)
            }
        )
    }
    var filteredCandidates by remember { mutableStateOf(initialCandidates) }
    var shouldMatchHumanly by remember { mutableStateOf(true) }
    var query by remember { mutableStateOf(params.initialQuery) }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }
    var dialogWidth by remember { mutableStateOf(Dp.Unspecified) }
    var dialogHeight by remember { mutableStateOf(Dp.Unspecified) }
    var currentCandidate: Candidate? by remember { mutableStateOf(filteredCandidates.getOrNull(0)) }
    val searchFocusRequester = remember { FocusRequester() }
    val listState: LazyListState = rememberLazyListState()

    DialogOverlay(
        dialogId = id,
        dialogPadding = null,
        widthFraction = null,
        // avoid initial flicker until the query text field width is set
        alpha = if (dialogWidth == Dp.Unspecified) 0f else 1f,
        onInitialSize = { width, height ->
            dialogWidth = width
            if (!params.allowChangingCandidatesViaEventToFixDialogHeight) {
                dialogHeight = height
            }
        },
        onPreviewKeyEvent = onPreviewKeyEvent@{
            // use KeyEventType.KeyUp since the key up event would otherwise bleed to the next opened dialog which
            // happened for ArchiveScannedDialog
            if (it.type == KeyEventType.KeyUp && it.key == Key.Enter && !it.isMetaPressed) {
                if (filteredCandidates.isEmpty()) {
                    if (!params.matchRequired) {
                        RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
                        params.onAccept.invoke(CompletionDialogAccept(text = query, index = null, dialogId = id))
                        if (!params.keepDialogOpenOnAccept) {
                            closeDialogWithId(id)
                        }
                    }
                } else {
                    if (currentCandidate != null) {
                        RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
                        params.onAccept.invoke(
                            CompletionDialogAccept(
                                text = currentCandidate!!.text,
                                dialogId = id,
                                index = currentCandidate!!.index
                            )
                        )
                        if (!params.keepDialogOpenOnAccept) {
                            closeDialogWithId(id)
                        }
                    }
                }
                return@onPreviewKeyEvent true
            }
            // listen on KeyUp so escape does not cancel the dialog below, as well
            if (it.key == Key.Escape && it.type == KeyEventType.KeyUp) {
                RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
                closeDialogWithId(id)
                params.onDismiss?.invoke()
                return@onPreviewKeyEvent true
            }
            val isKeyDown = it.type == KeyEventType.KeyDown
            if (!isKeyDown) {
                return@onPreviewKeyEvent false
            }

            if (it.key == Key.DirectionRight && !it.isMetaPressed && !it.isCtrlPressed && !it.isShiftPressed && !it.isAltPressed) {
                if (currentCandidate != null) {
                    query = currentCandidate!!.text
                    queryState = TextFieldValue(
                        text = currentCandidate!!.text,
                        selection = TextRange(currentCandidate!!.text.length)
                    )
                    return@onPreviewKeyEvent true
                }
            }

            if (it.isMetaPressed) {
                if (!params.matchRequired && it.key == Key.Enter) {
                    RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
                    params.onAccept.invoke(CompletionDialogAccept(text = query, index = null, dialogId = id))
                    if (!params.keepDialogOpenOnAccept) {
                        closeDialogWithId(id)
                    }
                    return@onPreviewKeyEvent true
                }
                if (it.key == Key.Backspace) {
                    query = ""
                    queryState = TextFieldValue()
                    return@onPreviewKeyEvent true
                }
                if (it.key == Key.E) {
                    copyToClipboard(currentCandidate?.text ?: "")
                    return@onPreviewKeyEvent true
                }
                if (params.allowMatchingLiterally && it.key == Key.Minus) {
                    shouldMatchHumanly = !shouldMatchHumanly
                }
                val cmdAction = params.cmdActions.firstOrNull { cmdAction ->
                    cmdAction.key == it.key
                }
                if (cmdAction != null) {
                    if (cmdAction.closeDialog) {
                        if (currentCandidate == null) {
                            RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
                        } else {
                            RecentHistory.rememberText(currentCandidate!!.text, ignoreShortText = true, trimText = true)
                        }
                        closeDialogWithId(id)
                        params.onDismiss?.invoke()
                    }
                    cmdAction.callback.invoke(CompletionDialogCmdContext(query, id))
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionDown) {
                if (filteredCandidates.isEmpty()) {
                    return@onPreviewKeyEvent true
                }
                val index = filteredCandidates.indexOfFirst { candidate -> candidate == currentCandidate }
                currentCandidate = if (index == -1) {
                    filteredCandidates[0]
                } else {
                    filteredCandidates.getOrElse(index + 1) { filteredCandidates[0] }
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionUp) {
                if (filteredCandidates.isEmpty()) {
                    return@onPreviewKeyEvent true
                }
                val index = filteredCandidates.indexOfFirst { candidate -> candidate == currentCandidate }
                currentCandidate = if (index == -1) {
                    filteredCandidates[0]
                } else {
                    filteredCandidates.getOrElse(index - 1) { filteredCandidates[filteredCandidates.size - 1] }
                }
                return@onPreviewKeyEvent true
            }
            false
        }) {
        CompletionDialogUi(
            dialogWidth = dialogWidth,
            dialogHeight = dialogHeight,
            queryState = queryState,
            query = query,
            params = params,
            initialCandidates = initialCandidates,
            filteredCandidates = filteredCandidates,
            searchFocusRequester = searchFocusRequester,
            listState = listState,
            currentCandidate = currentCandidate,
            dialogId = id,
            shouldMatchHumanly = shouldMatchHumanly,
            onQueryChange = { query = it },
            onQueryStateChange = { queryState = it }
        )
    }

    fun setFilteredCandidates() {
        filteredCandidates = if (shouldMatchHumanly) {
            initialCandidates.filterHuman(query) {
                if (it.additionalSearchString == null) {
                    it.text
                } else {
                    it.text + " " + it.additionalSearchString
                }
            }
        } else {
            initialCandidates.filter {
                val searchText = if (it.additionalSearchString == null) {
                    it.text
                } else {
                    it.text + " " + it.additionalSearchString
                }
                searchText.contains(query, ignoreCase = true)
            }
        }
        currentCandidate = if (filteredCandidates.isEmpty()) {
            null
        } else {
            val index = filteredCandidates.indexOfFirst { candidate -> candidate == currentCandidate }
            if (index == -1) {
                filteredCandidates[0]
            } else {
                filteredCandidates[index]
            }
        }
    }

    LaunchedEffect(Unit) {
        searchFocusRequester.requestFocus()
        if (params.allowChangingCandidatesViaEventToFixDialogHeight) {
            collectEvent<ChangeCompletionDialogCandidates> { event ->
                if (event.dialogId == id) {
                    val indexedCandidates: List<Candidate> = event.newCandidates.mapIndexed { i, candidate ->
                        Candidate(candidate.text, i, candidate.prefixView, candidate.additionalSearchString)
                    }
                    if (initialCandidates != indexedCandidates) {
                        initialCandidates = indexedCandidates
                        setFilteredCandidates()
                    }
                }
            }
        }
    }
    LaunchedEffect(currentCandidate) {
        if (currentCandidate != null) {
            val mappedIndex = filteredCandidates.indexOfFirst { it.index == currentCandidate!!.index }
            if (mappedIndex != -1) {
                listState.animateScrollToItem(mappedIndex, scrollOffset = -200)
            }
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            searchFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(query, shouldMatchHumanly) {
        setFilteredCandidates()
    }
}

