package dima.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.dialogs.help.Key

data class DialogBottomKey(
    val keyText: String,
    val text: String,
    val isCmd: Boolean = false,
    val isCtrl: Boolean = false
)

private val miniDialogFontSize = 14.sp
private val miniDialogKeySize = 18.dp

@OptIn(ExperimentalLayoutApi::class)
@Composable
internal fun DialogBottomKeyInfo(
    keys: List<DialogBottomKey>,
    leftText: String? = null,
    leftTextBackgroundColor: Color = TailwindCssColors.blue600,
    modifier: Modifier = Modifier
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
    ) {
        Text(
            // use empty string so the height is the same
            leftText ?: "",
            fontSize = miniDialogFontSize,
            color = GlobalStyling.getTextColor(),
            modifier = Modifier
                .then(
                    if (leftText == null) {
                        Modifier
                    } else {
                        Modifier.background(
                            leftTextBackgroundColor,
                            RoundedCornerShape(5.dp)
                        )
                    }
                )
                .padding(horizontal = 6.dp, vertical = 2.dp)
        )
        FlowRow(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            keys.forEach { key ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(0.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    if (key.isCmd) {
                        Key(
                            "⌘",
                            size = miniDialogKeySize,
                            fontSize = miniDialogFontSize
                        )
                    }
                    if (key.isCtrl) {
                        Key(
                            "^",
                            size = miniDialogKeySize,
                            fontSize = miniDialogFontSize
                        )
                    }
                    Key(
                        key.keyText,
                        size = miniDialogKeySize,
                        fontSize = miniDialogFontSize
                    )
                    Text(
                        key.text,
                        color = GlobalStyling.getTextColor(),
                        fontSize = miniDialogFontSize,
                    )
                }
            }
        }
    }
}
