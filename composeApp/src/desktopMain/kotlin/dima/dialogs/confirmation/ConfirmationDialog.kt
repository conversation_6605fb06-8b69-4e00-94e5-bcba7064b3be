package dima.dialogs.confirmation

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dialogs
import dima.color.TailwindCssColors
import dima.dialogs.*
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.os.copyToClipboard
import dima.utils.ScrollVelocity
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.isQuestionMark

fun openConfirmationDialog(
    title: String,
    subTitle: String? = null,
    confirmButtonText: String = "Confirm",
    onDismiss: (() -> Unit)? = null,
    onConfirm: () -> Unit,
) {
    val params = Params(title, subTitle, confirmButtonText, onDismiss, onConfirm)
    openDialog(DialogIdentifier.Confirmation, params) { id, data ->
        ConfirmationDialog(id, data)
    }
}

private data class Params(
    val title: String,
    val subTitle: String? = null,
    val confirmButtonText: String = "Confirm",
    val onDismiss: (() -> Unit)? = null,
    val onConfirm: () -> Unit,
)

@Composable
fun ConfirmationDialog(id: Long, data: Any?) {
    val params = data as Params
    val scrollState = rememberScrollState()
    val scrollVelocity = remember { ScrollVelocity(scrollState) }
    var buttonRowWidth by remember { mutableStateOf<Int?>(null) }
    val dialogFocusRequester = remember { FocusRequester() }
    var dialogWidth by remember { mutableStateOf(Dp.Unspecified) }

    DialogOverlay(
        dialogId = id,
        showHelpIcon = true,
        widthFraction = null,
        onInitialSize = { width, _ ->
            dialogWidth = width
        },
        alpha = if (buttonRowWidth == null) 0f else 1f,
        onPreviewKeyEvent = {
            if (it.isQuestionMark() && it.type == KeyEventType.KeyDown) {
                openMiniHelpDialog(
                    "Confirmation Dialog", keys = listOf(
                        MiniHelpDialogKey(listOf(Key.J), "Copy title and subtitle"),
                        MiniHelpDialogKey(listOf(Key.C), "Scroll subtitle up"),
                        MiniHelpDialogKey(listOf(Key.T), "Scroll subtitle down"),
                        MiniHelpDialogKey(listOf(Key.V), "Scroll subtitle up more"),
                        MiniHelpDialogKey(listOf(Key.M), "Scroll subtitle down more"),
                        MiniHelpDialogKey(listOf(Key.Escape), "Dismiss dialog"),
                        MiniHelpDialogKey(listOf(Key.Enter), "Confirm dialog"),
                    )
                )
            } else if (it.key == Key.J && it.type == KeyEventType.KeyDown) {
                var text = params.title
                if (params.subTitle != null) {
                    text += "\n\n" + params.subTitle
                }
                copyToClipboard(text)
            } else if (it.key == Key.C && params.subTitle != null) {
                if (it.type == KeyEventType.KeyDown) {
                    scrollVelocity.onScrollUpKeyPressed()
                } else if (it.type == KeyEventType.KeyUp) {
                    scrollVelocity.onKeyReleased()
                }
            } else if (it.key == Key.T && params.subTitle != null) {
                if (it.type == KeyEventType.KeyDown) {
                    scrollVelocity.onScrollDownKeyPressed()
                } else if (it.type == KeyEventType.KeyUp) {
                    scrollVelocity.onKeyReleased()
                }
            } else if (it.key == Key.V && params.subTitle != null) {
                if (it.type == KeyEventType.KeyDown) {
                    scrollVelocity.onScrollUpMoreKeyPressed()
                } else if (it.type == KeyEventType.KeyUp) {
                    scrollVelocity.onKeyReleased()
                }
            } else if (it.key == Key.M && params.subTitle != null) {
                if (it.type == KeyEventType.KeyDown) {
                    scrollVelocity.onScrollDownMoreKeyPressed()
                } else if (it.type == KeyEventType.KeyUp) {
                    scrollVelocity.onKeyReleased()
                }
            } else if (it.key == Key.Escape && it.type == KeyEventType.KeyDown) {
                params.onDismiss?.invoke()
                closeDialogWithId(id)
            } else if (it.key == Key.Enter && it.type == KeyEventType.KeyUp) {
                // use KeyUp to not bleed the key event down to apps which handle Enter
                params.onConfirm()
                closeDialogWithId(id)
            }
            true
        }) {
        Column(
            verticalArrangement = Arrangement.spacedBy(15.dp),
            modifier = Modifier
                .then(
                    if (dialogWidth == Dp.Unspecified) {
                        Modifier
                    } else {
                        Modifier.width(dialogWidth)
                    }
                )
                .widthIn(max = 600.dp)
                .focusable()
                .focusRequester(dialogFocusRequester)
        ) {
            Text(
                params.title,
                fontSize = 15.sp,
                color = GlobalStyling.getTextColor(),
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .widthIn(min = 300.dp)
                    .onSizeChanged {
                        if (params.subTitle == null) {
                            buttonRowWidth = it.width
                        }
                    }
            )
            if (params.subTitle != null) {
                Box(
                    modifier = Modifier
                        .weight(1f, fill = false)
                ) {
                    Column(
                        modifier = Modifier
                            .verticalScroll(scrollState)
                    ) {
                        Text(
                            params.subTitle,
                            fontSize = 13.sp,
                            color = GlobalStyling.getGrayColor(),
                            modifier = Modifier
                                .widthIn(min = 300.dp)
                                // add padding only when scrollbar is visible
                                .padding(end = if (scrollState.maxValue == 0) 0.dp else 12.dp)
                                .onSizeChanged {
                                    buttonRowWidth = it.width
                                }
                        )
                    }
                    // wrap with Box, so the scroll bar does not take the full height
                    Box(modifier = Modifier.matchParentSize()) {
                        VerticalScrollbar(
                            modifier = Modifier.align(Alignment.CenterEnd),
                            adapter = rememberScrollbarAdapter(scrollState)
                        )
                    }
                }
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.End,
                modifier = Modifier
                    .then(
                        if (buttonRowWidth == null) {
                            Modifier
                        } else {
                            Modifier.widthIn(min = buttonRowWidth!!.dp)
                        }
                    )
            ) {
                Text(
                    "Cancel",
                    textAlign = TextAlign.Center,
                    color = GlobalStyling.getGrayColor(),
                    modifier = Modifier
                        .clip(RoundedCornerShape(5.dp))
                        .clickableWithoutBackgroundRipple {
                            params.onDismiss?.invoke()
                            closeDialogWithId(id)
                        }
                        .padding(start = 8.dp, top = 8.dp, bottom = 8.dp, end = 16.dp)
                )
                Text(
                    params.confirmButtonText,
                    textAlign = TextAlign.Center,
                    color = TailwindCssColors.white,
                    modifier = Modifier
                        .shadow(1.dp, RoundedCornerShape(5.dp))
                        .background(TailwindCssColors.blue600, RoundedCornerShape(5.dp))
                        .clickableWithoutBackgroundRipple {
                            params.onConfirm()
                            closeDialogWithId(id)
                        }
                        .padding(8.dp)
                )
            }
        }
    }

    LaunchedEffect(Unit) {
        scrollVelocity.loopForeverAndTick()
    }

    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            dialogFocusRequester.requestFocus()
        }
    }
}