package dima.dialogs.findFilesViaRipgrep

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import dima.apps.AppType
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dialogs
import dima.dialogs.findFilesViaRipgrep.ui.DialogContent
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.os.homeWithoutSlash
import dima.utils.RecentHistory
import dima.utils.ScrollVelocity
import dima.utils.abbreviatePath
import dima.utils.filterHuman
import kotlinx.coroutines.launch
import setCurrentApp
import java.io.File

/**
 * On confirming, open file in Dired.
 */
@Composable
fun FindFilesViaRipgrepDialog(id: Long, data: Any) {
    val params = data as Params
    val isHomeDirectory = params.path.abbreviatePath() == "~"
    var selectedFile by remember { mutableStateOf<String?>(null) }
    var files by remember { mutableStateOf(listOf<String>()) }
    var filteredFiles by remember { mutableStateOf(listOf<String>()) }
    var isLoading by remember { mutableStateOf(true) }
    var considerGitIgnore by remember { mutableStateOf(true) }
    var extensionFilter by remember { mutableStateOf("") }
    val coroutineScope = rememberCoroutineScope()
    val scrollState = rememberLazyListState()
    val searchFocusRequester = remember { FocusRequester() }
    var search by remember { mutableStateOf(params.initialSearch ?: "") }
    var searchState by remember {
        mutableStateOf(
            TextFieldValue(
                text = search,
                selection = when {
                    search.isEmpty() -> TextRange.Zero
                    else -> TextRange(search.length, search.length)
                }
            )
        )
    }
    val filePreviewScrollState: ScrollState = rememberScrollState()
    val filePreviewScrollVelocity = remember { ScrollVelocity(filePreviewScrollState) }

    DialogOverlay(
        dialogId = id,
        widthFraction = null,
        dialogPadding = null,
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.type == KeyEventType.KeyUp) {
                if (it.key == Key.C || it.key == Key.T || it.key == Key.M || it.key == Key.V) {
                    filePreviewScrollVelocity.onKeyReleased()
                }
                return@onPreviewKeyEvent false
            }
            if (it.type != KeyEventType.KeyDown) {
                return@onPreviewKeyEvent false
            }

            if (it.isCtrlPressed) {
                when (it.key) {
                    Key.C -> {
                        filePreviewScrollVelocity.onScrollUpKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.T -> {
                        filePreviewScrollVelocity.onScrollDownKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.V -> {
                        filePreviewScrollVelocity.onScrollUpMoreKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.M -> {
                        filePreviewScrollVelocity.onScrollDownMoreKeyPressed()
                        return@onPreviewKeyEvent true
                    }
                }
            }
            if (it.key == Key.T && it.isMetaPressed) {
                closeDialogWithId(id)
                // It might be easier to just modify the mutable state for this dialog, instead of opening a new one.
                // One needs to take special care that all the remembered state is reset.
                // But this is quick and there is no visual difference, so this is fine.
                openFindFilesViaRipgrepDialog(
                    homeWithoutSlash,
                    initialSearch = search.ifBlank { null },
                    additionalRipGrepArgs = FindFilesViaRipgrepDialog.getAdditionalRipGrepArgsForHomeDir()
                )
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.C && it.isMetaPressed) {
                considerGitIgnore = !considerGitIgnore
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionDown) {
                if (filteredFiles.isEmpty() || selectedFile == null) {
                    return@onPreviewKeyEvent true
                }
                val newIndex = (filteredFiles.indexOf(selectedFile) + 1).coerceAtMost(filteredFiles.size - 1)
                selectedFile = filteredFiles[newIndex]
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionUp) {
                if (filteredFiles.isEmpty() || selectedFile == null) {
                    return@onPreviewKeyEvent true
                }
                val newIndex = (filteredFiles.indexOf(selectedFile) - 1).coerceAtLeast(0)
                selectedFile = filteredFiles[newIndex]
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Enter) {
                if (filteredFiles.isEmpty() || selectedFile == null) {
                    return@onPreviewKeyEvent true
                }
                RecentHistory.rememberText(search, ignoreShortText = true, trimText = true)
                closeDialogWithId(id)
                val file = File(params.path, selectedFile!!)
                RecentHistory.rememberFile(file)
                GlobalState.dired = GlobalState.dired.copy(
                    directory = file.parent,
                    selectedFile = file.absolutePath
                )
                setCurrentApp(AppType.Dired)
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Escape) {
                RecentHistory.rememberText(search, ignoreShortText = true, trimText = true)
                closeDialogWithId(id)
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.L && it.isMetaPressed) {
                openMiniHelpDialog(
                    "Find Files Dialog",
                    keys = buildList {
                        addAll(
                            listOf(
                                MiniHelpDialogKey(listOf(Key.DirectionDown), "Select next file"),
                                MiniHelpDialogKey(listOf(Key.DirectionUp), "Select previous file"),
                                MiniHelpDialogKey(listOf(Key.C), "Scroll preview up", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.T), "Scroll preview down", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.V), "Scroll preview up more", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.M), "Scroll preview down more", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.C), "Toggle ignore", isCmd = true),
                            )
                        )
                        if (!isHomeDirectory) {
                            add(MiniHelpDialogKey(listOf(Key.T), "Search from home", isCmd = true))
                        }
                        add(MiniHelpDialogKey(listOf(Key.Escape), "Close dialog"))
                        add(MiniHelpDialogKey(listOf(Key.Enter), "Open file in editor"))
                    }
                )
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.E && it.isMetaPressed) {
                openTextInputDialog(
                    "Filter by extension",
                    initialText = extensionFilter,
                    onDismiss = {
                        extensionFilter = ""
                    }
                ) { text ->
                    extensionFilter = text
                    TextInputDialogConfirmAction.Close
                }
                return@onPreviewKeyEvent true
            }
            return@onPreviewKeyEvent false
        }
    ) {
        DialogContent(
            searchState = searchState,
            search = search,
            searchFocusRequester = searchFocusRequester,
            onSearchStateChange = { searchState = it },
            onSearchChange = { search = it },
            onSelectedFileChange = { selectedFile = it },
            params = params,
            considerGitIgnore = considerGitIgnore,
            files = files,
            filteredFiles = filteredFiles,
            isLoading = isLoading,
            selectedFile = selectedFile,
            isHomeDirectory = isHomeDirectory,
            scrollState = scrollState,
            extensionFilter = extensionFilter,
            filePreviewScrollState = filePreviewScrollState
        )
    }

    LaunchedEffect(Unit) {
        filePreviewScrollVelocity.loopForeverAndTick()
    }
    LaunchedEffect(Unit, data, considerGitIgnore) {
        searchFocusRequester.requestFocus()
        runRg(
            coroutineScope = coroutineScope,
            params = params,
            considerGitIgnore = considerGitIgnore,
            onFilesChange = { files = it },
            onIsLoadingChange = { isLoading = it }
        )
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            searchFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(files, search, extensionFilter) {
        filteredFiles = files
            .filterHuman(search)
            .filter { file ->
                if (extensionFilter.isEmpty()) true
                else file.endsWith(".$extensionFilter", ignoreCase = true)
            }
    }
    LaunchedEffect(filteredFiles, selectedFile) {
        launch {
            filePreviewScrollVelocity.scrollToTop(instantly = true)
        }
        if (filteredFiles.isEmpty()) {
            selectedFile = null
            return@LaunchedEffect
        }
        if (selectedFile == null) {
            selectedFile = filteredFiles.first()
            return@LaunchedEffect
        }
        val index = filteredFiles.indexOf(selectedFile)
        if (index == -1) {
            selectedFile = filteredFiles.first()
        } else {
            launch {
                scrollState.animateScrollToItem(index, -500)
            }
        }
    }
}

