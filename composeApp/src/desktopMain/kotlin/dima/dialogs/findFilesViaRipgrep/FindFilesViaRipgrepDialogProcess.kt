package dima.dialogs.findFilesViaRipgrep

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.utils.RecentHistory
import dima.utils.normalize
import dima.utils.sortedByHistory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

internal fun runRg(
    coroutineScope: CoroutineScope,
    params: Params,
    considerGitIgnore: Boolean,
    onFilesChange: (List<String>) -> Unit,
    onIsLoadingChange: (Boolean) -> Unit
) {
    coroutineScope.launch(Dispatchers.IO) {
        // rg is faster than ugrep for getting all files recursively
        val args = mutableListOf(
            "rg",
            "--files",
            "--sort", "path",
            *(params.additionalRipGrepArgs.toTypedArray())
        )
        if (!considerGitIgnore) {
            args.add("--no-ignore")
        }
        val result = process(
            *args.toTypedArray(),
            directory = File(params.path),
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
            destroyForcibly = true
        )
        // allow to correctly match Umlaute
        val output = result.output.map {
            it.normalize()
        }
        // ignore exit code 2 which happens in ~/Pictures because of directory permissions - it still returns the list of files
        if (result.resultCode != 0 && result.resultCode != 1 && result.resultCode != 2) {
            showErrorNotification("rg failed with ${result.resultCode}", output.joinToString("\n"))
        } else {
            if (result.resultCode == 1 || result.resultCode == 2) {
                onFilesChange(emptyList())
            }
            val recentFiles: List<String> = RecentHistory.getRecentFiles()
            val (fromHistory, notInHistory) = output
                .filter {
                    it.isNotBlank()
                }
                .partition {
                    recentFiles.contains(File(params.path, it).absolutePath)
                }
            // ripgrep returns relative paths to the passed directory, but the file history contains absolute paths,
            // so convert to absolute paths first for the sorting, then convert back to relative paths
            val fromHistoryAbsolute = fromHistory.map {
                File(params.path, it).absolutePath
            }
            onFilesChange(
                buildList {
                    addAll(fromHistoryAbsolute.sortedByHistory(recentFiles).map {
                        it.removePrefix(params.path).removePrefix(File.separator)
                    })
                    addAll(notInHistory)
                })
        }
        onIsLoadingChange(false)
    }
}
