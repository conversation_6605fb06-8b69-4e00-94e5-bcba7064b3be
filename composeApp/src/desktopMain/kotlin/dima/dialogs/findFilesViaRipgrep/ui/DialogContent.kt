package dima.dialogs.findFilesViaRipgrep.ui

import GlobalStyling
import androidx.compose.foundation.LocalScrollbarStyle
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dima.apps.dired.getFileType
import dima.dialogs.DialogBottomKey
import dima.dialogs.DialogBottomKeyInfo
import dima.dialogs.findFilesViaRipgrep.Params
import dima.utils.scrollbarStyleForDarkBackground
import java.io.File

@Composable
internal fun DialogContent(
    searchState: TextFieldValue,
    search: String,
    scrollState: LazyListState,
    filePreviewScrollState: ScrollState,
    searchFocusRequester: FocusRequester,
    onSearchStateChange: (TextFieldValue) -> Unit,
    onSearchChange: (String) -> Unit,
    onSelectedFileChange: (String) -> Unit,
    params: Params,
    considerGitIgnore: Boolean,
    files: List<String>,
    filteredFiles: List<String>,
    isLoading: Boolean,
    selectedFile: String?,
    isHomeDirectory: Boolean,
    extensionFilter: String
) {
    val directoryPreviewLazyListState = rememberLazyListState()
    val isTreeSitterFile = selectedFile?.let {
        File(it).getFileType().type.isTreeSitterLanguage
    } ?: false

    Column {
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f, fill = false)
        ) {
            Column(
                modifier = Modifier
                    .padding(top = 12.dp)
                    .weight(0.5f)
            ) {
                FindFilesViaRipgrepUiSearchInput(
                    searchState = searchState,
                    onSearchChange = { value ->
                        onSearchStateChange(value)
                        onSearchChange(value.text)
                    },
                    focusRequester = searchFocusRequester,
                    path = params.path,
                    considerGitIgnore = considerGitIgnore,
                    files = files,
                    filteredFiles = filteredFiles
                )
                if (filteredFiles.isEmpty() && !isLoading) {
                    Text(
                        "No files found",
                        textAlign = TextAlign.Center,
                        color = GlobalStyling.getRedTextColor(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 30.dp)
                    )
                } else {
                    FileList(
                        files = filteredFiles,
                        selectedFile = selectedFile,
                        searchQuery = search,
                        onFileSelected = { onSelectedFileChange(it) },
                        scrollState = scrollState,
                        modifier = Modifier
                            .weight(1f)
                    )
                }
            }
            Box(
                modifier = Modifier
                    .weight(0.5f)
                    .fillMaxHeight()
            ) {
                Column {
                    RightSideFilePreview(
                        selectedFile = selectedFile,
                        basePath = params.path,
                        lazyListState = directoryPreviewLazyListState,
                        filePreviewScrollState = filePreviewScrollState
                    )
                }
                VerticalScrollbar(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .fillMaxHeight(),
                    style = if (isTreeSitterFile) scrollbarStyleForDarkBackground() else LocalScrollbarStyle.current,
                    adapter = if (selectedFile?.let { File(it).isDirectory } == true) {
                        rememberScrollbarAdapter(scrollState = directoryPreviewLazyListState)
                    } else {
                        rememberScrollbarAdapter(scrollState = filePreviewScrollState)
                    }
                )
            }
        }
        val ripGrepArgs = mutableListOf<String>()
        if (params.additionalRipGrepArgs.isNotEmpty()) {
            ripGrepArgs.add(params.additionalRipGrepArgs.joinToString(" "))
        }
        DialogBottomKeyInfo(
            buildList {
                addAll(
                    listOf(
                        DialogBottomKey("c", "toggle ignore", isCmd = true),
                        DialogBottomKey("e", "filter by extension", isCmd = true)
                    )
                )
                if (!isHomeDirectory) {
                    add(DialogBottomKey("t", "search from home", isCmd = true))
                }
                add(DialogBottomKey("l", "open help", isCmd = true))
            },
            leftText = buildList {
                if (ripGrepArgs.isNotEmpty()) {
                    add(ripGrepArgs.joinToString(" "))
                }
                if (extensionFilter.isNotEmpty()) {
                    add("Extension filter: $extensionFilter")
                }
            }.joinToString("    ").takeIf { it.isNotEmpty() },
            modifier = Modifier.padding(12.dp)
        )
    }
}