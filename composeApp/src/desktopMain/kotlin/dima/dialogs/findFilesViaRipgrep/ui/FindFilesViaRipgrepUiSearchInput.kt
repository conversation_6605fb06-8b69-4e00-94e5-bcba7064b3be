package dima.dialogs.findFilesViaRipgrep.ui

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.utils.SmartTextFieldCandidateCount
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import dima.utils.abbreviatePath

@Composable
internal fun FindFilesViaRipgrepUiSearchInput(
    searchState: TextFieldValue,
    onSearchChange: (TextFieldValue) -> Unit,
    focusRequester: FocusRequester,
    path: String,
    considerGitIgnore: Boolean,
    files: List<String>,
    filteredFiles: List<String>,
    modifier: Modifier = Modifier
) {
    val parts = mutableListOf<String>()
    if (considerGitIgnore) {
        parts.add("considering ignored")
    } else {
        parts.add("no ignored")
    }
    val topLabel = "Find file in " + path.abbreviatePath() + " (" + parts.joinToString(" ") + ")"
    Row(
        modifier = Modifier
            .padding(bottom = 12.dp, start = 12.dp)
    ) {
        TextFieldWithCandidateCountWithoutAnimation(
            value = searchState,
            onValueChange = onSearchChange,
            count = SmartTextFieldCandidateCount(filteredFiles.size, files.size),
            singleLine = true,
            topLabel = topLabel,
            focusRequester = focusRequester,
            modifier = modifier
                .fillMaxWidth()

        )
    }
}
