package dima.dialogs.findFilesViaRipgrep.ui

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.text.TextMarked
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleThemed

@Composable
fun FileList(
    files: List<String>,
    selectedFile: String?,
    scrollState: LazyListState,
    searchQuery: String,
    onFileSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val color = GlobalStyling.getTextColor()
    Box(modifier = modifier) {
        LazyColumn(state = scrollState) {
            itemsIndexed(files, key = { index, _ -> index }) { _, s ->
                val isSelected = (selectedFile == s)
                TextMarked(
                    text = s,
                    searchQuery = searchQuery,
                    color = color,
                    modifier = Modifier
                        .fillMaxWidth()
                        .then(
                            if (isSelected) {
                                Modifier.background(GlobalStyling.getSelectedBackgroundColor())
                            } else {
                                Modifier
                            }
                        )
                        .clickableWithoutBackgroundRipple {
                            onFileSelected(s)
                        }
                        .padding(horizontal = 12.dp, vertical = 4.dp)
                )
            }
        }
        Box(modifier = Modifier.matchParentSize()) {
            VerticalScrollbar(
                style = scrollbarStyleThemed(),
                adapter = rememberScrollbarAdapter(scrollState = scrollState),
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(x = 10.dp)
                    .padding(bottom = 12.dp),
            )
        }
    }
}
