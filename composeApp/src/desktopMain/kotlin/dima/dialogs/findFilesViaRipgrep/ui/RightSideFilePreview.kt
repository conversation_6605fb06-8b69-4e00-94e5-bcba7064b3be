package dima.dialogs.findFilesViaRipgrep.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import dima.apps.dired.Dired
import dima.apps.dired.getFileType
import dima.apps.dired.preview.DiredPreview
import dima.globalState.DiredImageState
import java.io.File

@Composable
fun RightSideFilePreview(
    selectedFile: String?,
    filePreviewScrollState: ScrollState,
    lazyListState: LazyListState,
    basePath: String,
) {
    if (selectedFile != null) {
        val file = File(basePath, selectedFile)
        DiredPreview(
            Dired.Entry(
                file = file,
                fileTypeWithColor = file.getFileType(),
                fileSizeBytes = file.length(),
                lastModified = file.lastModified()
            ),
            drawSelectedBorder = false,
            rightPreviewScrollState = filePreviewScrollState,
            imageState = DiredImageState(),
            roundedCorners = false
        )
    }
}
