package dima.dialogs.findFilesViaRipgrep

import dima.dialogs.DialogIdentifier
import dima.dialogs.openDialog

fun openFindFilesViaRipgrepDialog(
    path: String,
    initialSearch: String? = null,
    additionalRipGrepArgs: List<String> = emptyList()
) {
    val params = Params(path, initialSearch, additionalRipGrepArgs)
    openDialog(DialogIdentifier.RipgrepFindFile, params) { id, data ->
        FindFilesViaRipgrepDialog(id, data!!)
    }
}

internal data class Params(
    val path: String,
    val initialSearch: String? = null,
    val additionalRipGrepArgs: List<String>
)
