package dima.dialogs.findFilesViaRipgrep

import dima.settings

object FindFilesViaRipgrepDialog {

    /**
     * Returns the additional arguments for ripgrep to exclude directories.
     */
    fun getAdditionalRipGrepArgsForHomeDir(): List<String> {
        return settings.findFileDialogExcludeDirectoriesForHome.flatMap {
            // --glob = -g
            // prefixing with ! is to exclude the directory
            listOf("--glob", "!$it")
        }
    }
}
