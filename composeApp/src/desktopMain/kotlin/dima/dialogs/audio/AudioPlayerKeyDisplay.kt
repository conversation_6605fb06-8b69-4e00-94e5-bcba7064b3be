package dima.dialogs.audio

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.audio.Audio
import dima.dialogs.help.HelpDialogKeyRow
import dima.globalState.GlobalState
import dima.settings

@Composable
internal fun KeyDisplay(id: Long) {
    Row(
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .padding(top = 6.dp)
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .weight(0.5f)
                .padding(end = 24.dp)
        ) {
            if (GlobalState.audioPlayer.isPlaying) {
                HelpDialogKeyRow(listOf("␣"), "Pause") {
                    togglePlayPause(id)
                }
            } else {
                HelpDialogKeyRow(listOf("␣"), "Play") {
                    togglePlayPause(id)
                }
            }
            HelpDialogKeyRow(listOf("o"), "Pick track to play from start") {
                pickTrackToPlay(id)
            }
            HelpDialogKeyRow(listOf("r"), "Play random track at random time") {
                playRandomTrack(id)
            }
            HelpDialogKeyRow(
                listOf("q"),
                when (settings.audio.engine) {
                    Audio.Engine.VLC -> "Clear track/playlist"
                    Audio.Engine.Cmus -> "Stop playback"
                    Audio.Engine.Rust -> "Stop playback"
                }
            ) {
                Audio.clearTrack()
            }
            HelpDialogKeyRow(listOf("z"), "Open audio database") {
                openAudioDatabaseInDired(id)
            }
        }
        Column(
            modifier = Modifier
                .weight(0.5f)
        ) {
            if (GlobalState.audioPlayer.randomMode) {
                HelpDialogKeyRow(listOf("a"), "Disable random track playing") {
                    toggleRandomMode()
                }
            } else {
                HelpDialogKeyRow(listOf("a"), "Play random tracks non-stop") {
                    toggleRandomMode()
                }
            }
            if (settings.audio.engine == Audio.Engine.VLC || settings.audio.engine == Audio.Engine.Rust) {
                if (GlobalState.audioPlayer.quietVolume) {
                    HelpDialogKeyRow(listOf("v"), "Change to normal volume") {
                        Audio.toggleBetweenQuietAndNormalVolume()
                    }
                } else {
                    HelpDialogKeyRow(listOf("v"), "Change to quiet volume") {
                        Audio.toggleBetweenQuietAndNormalVolume()
                    }
                }
            }
            if (GlobalState.audioPlayer.loopTrack) {
                HelpDialogKeyRow(listOf("l"), "Disable track looping") {
                    toggleTrackLooping()
                }
            } else {
                HelpDialogKeyRow(listOf("l"), "Enable track looping") {
                    toggleTrackLooping()
                }
            }
        }
        Column(
            modifier = Modifier
                .weight(0.5f)
        ) {
            HelpDialogKeyRow(listOf("-"), "Seek to random time") {
                seekToRandomTime()
            }
            HelpDialogKeyRow(listOf("0"), "Seek to start") {
                seekToStart(id)
            }
            HelpDialogKeyRow(listOf("9"), "Seek to end") {
                seekToEnd()
            }
            // keys like in mpv
            HelpDialogKeyRow(listOf("n"), "Seek forward") {
                seekToStart(id)
            }
            HelpDialogKeyRow(listOf("h"), "Seek backward") {
                seekToEnd()
            }
            HelpDialogKeyRow(listOf("c"), "Seek forward more") {
                seekToEnd()
            }
            HelpDialogKeyRow(listOf("t"), "Seek backward more") {
                seekToEnd()
            }
        }
    }
}
