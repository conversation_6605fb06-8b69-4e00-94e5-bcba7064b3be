package dima.dialogs.audio

import androidx.compose.ui.input.key.*
import dima.audio.Audio
import dima.dialogs.closeDialogWithId
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.settings
import dima.utils.isQuestionMark

internal fun handleKeys(dialogId: Long): (KeyEvent) -> Boolean = (onPreviewKeyEvent@{ keyEvent ->
    if (keyEvent.isQuestionMark()) {
        openMiniHelpDialog(
            "Audio Dialog", keys = buildList {
                addAll(
                    listOf(
                        MiniHelpDialogKey(listOf(Key.Spacebar), "Toggle play/pause"),
                        MiniHelpDialogKey(listOf(Key.Escape), "Dismiss dialog"),
                        MiniHelpDialogKey(listOf(Key.Minus), "Seek to random time"),
                    )
                )
                val isVlc = settings.audio.engine == Audio.Engine.VLC
                val supportsVolumeControl = settings.audio.engine == Audio.Engine.VLC || settings.audio.engine == Audio.Engine.Rust
                addAll(
                    listOf(
                        MiniHelpDialogKey(listOf(Key.Zero), "Seek to start"),
                        MiniHelpDialogKey(listOf(Key.Nine), "Seek to end"),
                        MiniHelpDialogKey(listOf(Key.A), "Toggle random track playing"),
                        MiniHelpDialogKey(listOf(Key.C), "Seek forward more"),
                        MiniHelpDialogKey(listOf(Key.H), "Seek backward"),
                        MiniHelpDialogKey(listOf(Key.L), "Enable track looping"),
                        MiniHelpDialogKey(listOf(Key.N), "Seek forward"),
                        MiniHelpDialogKey(listOf(Key.U), "Pick track to play from start"),
                        MiniHelpDialogKey(listOf(Key.Q), if (isVlc) "Clear track/playlist" else "Stop playback"),
                        MiniHelpDialogKey(listOf(Key.R), "Play random track at random time"),
                        MiniHelpDialogKey(listOf(Key.T), "Seek backward more"),
                        MiniHelpDialogKey(listOf(Key.Z), "Open audio database"),
                    )
                )
                if (supportsVolumeControl) {
                    add(MiniHelpDialogKey(listOf(Key.V), "Toggle quiet volume"))
                }
            }
        )
        return@onPreviewKeyEvent true
    }
    return@onPreviewKeyEvent when (keyEvent.key) {
        Key.Escape -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                closeDialogWithId(dialogId)
            }
            true
        }

        Key.Minus -> {
            if (keyEvent.type == KeyEventType.KeyDown) {
                seekToRandomTime()
            }
            true
        }

        Key.A -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                toggleRandomMode()
            }
            true
        }

        Key.L -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                toggleTrackLooping()
            }
            true
        }

        Key.T -> {
            if (keyEvent.type == KeyEventType.KeyDown) {
                Audio.seekBackward(seekMoreOffset)
            }
            true
        }

        Key.Z -> {
            if (keyEvent.type == KeyEventType.KeyDown) {
                openAudioDatabaseInDired(dialogId)
            }
            true
        }

        Key.H -> {
            if (keyEvent.type == KeyEventType.KeyDown) {
                Audio.seekBackward(seekOffset)
            }
            true
        }

        Key.C -> {
            if (keyEvent.type == KeyEventType.KeyDown) {
                Audio.seekForward(seekMoreOffset)
            }
            true
        }

        Key.N -> {
            if (keyEvent.type == KeyEventType.KeyDown) {
                Audio.seekForward(seekOffset)
            }
            true
        }

        Key.V -> {
            if ((settings.audio.engine == Audio.Engine.VLC || settings.audio.engine == Audio.Engine.Rust) && keyEvent.type == KeyEventType.KeyUp) {
                Audio.toggleBetweenQuietAndNormalVolume()
            }
            true
        }

        Key.Q -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                Audio.clearTrack()
            }
            true
        }

        Key.Nine -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                seekToEnd()
            }
            true
        }

        Key.Zero -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                seekToStart(dialogId)
            }
            true
        }

        Key.O -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                pickTrackToPlay(dialogId)
            }
            true
        }

        Key.Spacebar -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                togglePlayPause(dialogId)
            }
            true
        }

        Key.R -> {
            if (keyEvent.type == KeyEventType.KeyUp) {
                playRandomTrack(dialogId)
            }
            true
        }

        else -> {
            false
        }
    }
})
