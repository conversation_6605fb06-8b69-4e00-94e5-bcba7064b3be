package dima.dialogs.audio

import GlobalStyling
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.toComposeImageBitmap
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dialogs
import dima.apps.dired.openDirectoryInDired
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.audio.Audio
import dima.color.TailwindCssColors
import dima.dialogs.*
import dima.dialogs.completion.openCompletionDialog
import dima.globalState.GlobalState
import dima.globalState.GlobalStateStorage
import dima.settings
import dima.utils.DummyFocusable
import dima.utils.RecentHistory
import dima.utils.TextNumbersSameWidth
import dima.utils.sortedByHistory
import org.bytedeco.librealsense.global.RealSense.color
import org.jaudiotagger.audio.AudioFileIO
import org.jaudiotagger.audio.exceptions.CannotReadException
import java.io.ByteArrayInputStream
import java.io.File
import java.nio.file.Paths
import javax.imageio.ImageIO
import kotlin.io.path.absolutePathString
import kotlin.io.path.exists
import kotlin.io.path.nameWithoutExtension

internal const val seekOffset = 5000L
internal const val seekMoreOffset = 60_000L

fun openAudioDialog() {
    openDialog(DialogIdentifier.Audio, data = null) { id, _ ->
        AudioDialog(id)
    }
}

internal fun toggleRandomMode() {
    GlobalState.audioPlayer = GlobalState.audioPlayer.copy(randomMode = !GlobalState.audioPlayer.randomMode)
}

internal fun toggleTrackLooping() {
    GlobalState.audioPlayer = GlobalState.audioPlayer.copy(loopTrack = !GlobalState.audioPlayer.loopTrack)
}

internal fun seekToStart(dialogId: Long) {
    if (GlobalState.audioPlayer.currentTrack == null) {
        showErrorNotification("No track is playing")
    } else {
        Audio.seek(0)
        closeDialogWithId(dialogId)
    }
}

internal fun seekToRandomTime() {
    if (GlobalState.audioPlayer.currentTrack == null) {
        showErrorNotification("No track is playing")
    } else {
        Audio.seekToRandomTime()
    }
}

internal fun seekToEnd() {
    if (GlobalState.audioPlayer.currentTrack == null) {
        showErrorNotification("No track is playing")
    } else {
        Audio.seekToEnd()
    }
}

internal fun pickTrackToPlay(dialogId: Long) {
    closeDialogWithId(dialogId)
    val tracks = Audio.getAllAudioFilesInAudioDirectory()
    if (tracks.isEmpty()) {
        showNotification("No tracks found")
        return
    }
    val mappedTracks = tracks.map { track ->
        track.nameWithoutExtension
    }
    val sortedTracks = mappedTracks.sortedByHistory(RecentHistory.getRecentTexts())
    openCompletionDialog("Play track", sortedTracks) {
        RecentHistory.rememberText(it.text)
        val index = mappedTracks.indexOf(it.text)
        if (index == -1) {
            throw IllegalStateException("index is -1?")
        }
        Audio.playTrack(tracks[index].absolutePathString(), startPlaying = true)
    }
}

internal fun playRandomTrack(dialogId: Long) {
    if (Audio.playRandomTrack()) {
        closeDialogWithId(dialogId)
    }
}

internal fun togglePlayPause(dialogId: Long) {
    Audio.togglePlayPause()
    closeDialogWithId(dialogId)
}

internal fun openAudioDatabaseInDired(dialogId: Long) {
    if (settings.audio.databasePath == null) {
        showErrorNotification("The audio database is not set in the settings")
        return
    }
    closeDialogWithId(dialogId)
    settings.audio.databasePath.openDirectoryInDired()
}


/**
 * Assume that [GlobalState.audioPlayer] has a current track.
 */
private fun loadCover(): ImageBitmap? {
    val path = GlobalState.audioPlayer.currentTrack ?: return null
    val file = File(path)
    if (!file.exists()) {
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(currentTrack = null)
        return null
    }
    val baseCoverPath = Paths.get(path).absolutePathString().substringBeforeLast(".")
    val cover = if (Paths.get("$baseCoverPath.jpg").exists()) {
        "$baseCoverPath.jpg"
    } else if (Paths.get("$baseCoverPath.webp").exists()) {
        // check WEBP for covers from YouTube downloads via yt-dlp
        "$baseCoverPath.webp"
    } else {
        null
    }
    if (cover != null) {
        return ImageIO.read(File(cover)).toComposeImageBitmap()
    }
    try {
        val audioMetadata = AudioFileIO.read(file)
        val binaryImage = audioMetadata.tag?.firstArtwork?.binaryData ?: return null
        return ImageIO.read(ByteArrayInputStream(binaryImage)).toComposeImageBitmap()
    } catch (_: CannotReadException) {
        // for file extensions like opus
        return null
    }
}

@Composable
fun AudioDialog(id: Long) {
    val dialogFocusRequester = remember { FocusRequester() }
    var dialogIndex by remember { mutableStateOf(0) }

    DialogOverlay(
        dialogId = id,
        showHelpIcon = true,
        widthFraction = if (GlobalState.mainWindow.widthInDp >= 1100) 0.5f else 0.9f,
        onPreviewKeyEvent = handleKeys(id)
    ) {
        Column(
            modifier = Modifier
                .focusable()
                .focusRequester(dialogFocusRequester)
                .fillMaxWidth()
        ) {
            val color = GlobalStyling.getTextColor()
            val currentPositionFormatted = Audio.formatMsDuration(GlobalState.audioPlayer.positionMs)
            val totalDuration = Audio.formatMsDuration(GlobalState.audioPlayer.totalDurationMs)
            val percentagePlayed: String = if (GlobalState.audioPlayer.totalDurationMs != 0L) {
                ((GlobalState.audioPlayer.positionMs / GlobalState.audioPlayer.totalDurationMs.toDouble()) * 100.0).toInt()
                    .toString()
            } else {
                "-"
            }
            DummyFocusable()
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                Column {
                    if (GlobalState.audioPlayer.currentTrack != null) {
                        Text(
                            Paths.get(GlobalState.audioPlayer.currentTrack!!).fileName.toString()
                                .substringBeforeLast("."),
                            fontSize = 17.sp,
                            color = color,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp)
                        )
                        val fontSize = 16.sp
                        Row(
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            TextNumbersSameWidth(
                                currentPositionFormatted,
                                color = color,
                                fontSize = fontSize
                            )
                            Text("  /  ", color = color)
                            TextNumbersSameWidth(totalDuration, color = color, fontSize = fontSize)
                        }
                        Text(
                            "$percentagePlayed%",
                            fontSize = fontSize,
                            color = color,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 2.dp)
                        )
                    }
                }
                val cover = loadCover()
                if (cover == null) {
                    Text(
                        "Global Audio Player",
                        fontWeight = FontWeight.Bold,
                        color = color,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(top = if (GlobalState.audioPlayer.currentTrack == null) 0.dp else 45.dp)
                            .fillMaxWidth()
                    )
                } else {
                    Image(
                        painter = BitmapPainter(cover),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxWidth()
                            // set for 1280 x 800 px MacBook resolution
                            .heightIn(max = (GlobalState.mainWindow.heightInDp / 2.5f).dp)
                            .aspectRatio(cover.width / cover.height.toFloat(), matchHeightConstraintsFirst = true)
                            .clip(RoundedCornerShape(5.dp))
                            .background(TailwindCssColors.red600)
                    )
                }
                KeyDisplay(id)
            }
        }
    }
    LaunchedEffect(Unit) {
        dialogFocusRequester.requestFocus()
        GlobalStateStorage.write()
        dialogIndex = dialogs.size
    }
    // fix focus for the mini help dialog
    LaunchedEffect(dialogs) {
        if (dialogs.size == dialogIndex) {
            dialogFocusRequester.requestFocus()
        }
    }
}
