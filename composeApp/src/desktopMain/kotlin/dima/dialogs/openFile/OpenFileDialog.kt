package dima.dialogs.openFile

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dialogs
import dima.apps.dired.Dired
import dima.apps.dired.getFileType
import dima.apps.dired.openDirectoryInDired
import dima.apps.dired.openFileInDired
import dima.apps.dired.preview.DiredPreview
import dima.dialogs.*
import dima.globalState.DiredImageState
import dima.text.TextMarked
import dima.utils.*
import java.io.File

/**
 * @throws IllegalArgumentException if files is empty
 */
fun openFileDialog(files: List<File>) {
    if (files.isEmpty()) {
        throw IllegalArgumentException("files must not be empty")
    }
    val params = Params(files)
    openDialog(DialogIdentifier.RipgrepFindFile, params) { id, data ->
        OpenFileDialog(id, data!!)
    }
}

private data class Params(
    val files: List<File>,
)

/**
 * On confirming, open file in Dired.
 *
 * This is very similar to [dima.dialogs.findFilesViaRipgrep.FindFilesViaRipgrepDialog], it just displays a list of files.
 */
@Composable
fun OpenFileDialog(id: Long, data: Any) {
    val params = data as Params
    var selectedFile by remember { mutableStateOf<File?>(null) }
    var filteredFiles by remember { mutableStateOf(listOf<File>()) }
    val scrollState = rememberLazyListState()
    val previewScrollState = rememberScrollState()
    val directoryPreviewLazyListState = rememberLazyListState()
    val searchFocusRequester = remember { FocusRequester() }
    var search by remember { mutableStateOf("") }
    var searchState by remember {
        mutableStateOf(
            TextFieldValue(
                text = search,
                selection = when {
                    search.isEmpty() -> TextRange.Zero
                    else -> TextRange(search.length, search.length)
                }
            )
        )
    }

    DialogOverlay(
        dialogId = id,
        widthFraction = null,
        dialogPadding = null,
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.type != KeyEventType.KeyDown) {
                return@onPreviewKeyEvent false
            }
            if (it.key == Key.DirectionDown) {
                if (filteredFiles.isEmpty() || selectedFile == null) {
                    return@onPreviewKeyEvent true
                }
                val newIndex = (filteredFiles.indexOf(selectedFile) + 1).coerceAtMost(filteredFiles.size - 1)
                selectedFile = filteredFiles[newIndex]
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionUp) {
                if (filteredFiles.isEmpty() || selectedFile == null) {
                    return@onPreviewKeyEvent true
                }
                val newIndex = (filteredFiles.indexOf(selectedFile) - 1).coerceAtLeast(0)
                selectedFile = filteredFiles[newIndex]
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Enter) {
                if (filteredFiles.isEmpty() || selectedFile == null) {
                    return@onPreviewKeyEvent true
                }
                closeDialogWithId(id)
                RecentHistory.rememberFile(selectedFile!!)
                if (selectedFile!!.isDirectory) {
                    selectedFile!!.openDirectoryInDired()
                } else {
                    selectedFile!!.openFileInDired()
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Escape) {
                RecentHistory.rememberText(search, ignoreShortText = true, trimText = true)
                closeDialogWithId(id)
                return@onPreviewKeyEvent true
            }
            return@onPreviewKeyEvent false
        }
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false)
            ) {
                Column(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .weight(0.5f)
                ) {
                    Row(modifier = Modifier.padding(bottom = 12.dp, start = 12.dp)) {
                        TextFieldWithCandidateCountWithoutAnimation(
                            value = searchState,
                            count = SmartTextFieldCandidateCount(filteredFiles.size, params.files.size),
                            onValueChange = {
                                searchState = it
                                search = it.text
                            },
                            topLabel = "Open recent file in Dired",
                            singleLine = true,
                            modifier = Modifier
                                .focusRequester(searchFocusRequester)
                                .fillMaxWidth()
                        )
                    }
                    if (filteredFiles.isEmpty()) {
                        Text(
                            "No files found",
                            textAlign = TextAlign.Center,
                            color = GlobalStyling.getRedTextColor(),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 30.dp)
                        )
                    } else {
                        val color = GlobalStyling.getTextColor()
                        Box(
                            modifier = Modifier
                                .weight(1f)
                        ) {
                            LazyColumn(
                                state = scrollState,
                            ) {
                                itemsIndexed(filteredFiles, key = { index, _ -> index }) { _, s ->
                                    val isSelected = (selectedFile == s)
                                    Box(
                                        modifier = Modifier
                                            .padding(end = 12.dp)
                                            .then(
                                                if (isSelected) {
                                                    Modifier.background(GlobalStyling.getSelectedBackgroundColor())
                                                } else {
                                                    Modifier
                                                }
                                            )
                                    ) {
                                        TextMarked(
                                            s.abbreviatePath(),
                                            search,
                                            color = color,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .clickableWithoutBackgroundRipple {
                                                    selectedFile = s
                                                }
                                                .padding(horizontal = 12.dp, vertical = 4.dp)
                                        )
                                    }
                                }
                            }
                            // wrap with Box, so the scroll bar does not take the full height
                            Box(modifier = Modifier.matchParentSize()) {
                                VerticalScrollbar(
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(bottom = 12.dp),
                                    adapter = rememberScrollbarAdapter(scrollState = scrollState)
                                )
                            }
                        }
                    }
                }
                Column(
                    modifier = Modifier
                        .weight(0.5f)
                ) {
                    if (selectedFile != null && selectedFile!!.exists()) {
                        val file = selectedFile!!
                        Box(modifier = Modifier.fillMaxSize()) {
                            DiredPreview(
                                Dired.Entry(
                                    file = file,
                                    fileTypeWithColor = file.getFileType(),
                                    fileSizeBytes = file.length(),
                                    lastModified = file.lastModified()
                                ),
                                drawSelectedBorder = false,
                                rightPreviewScrollState = previewScrollState,
                                imageState = DiredImageState(),
                                roundedCorners = false,
                            )
                            VerticalScrollbar(
                                modifier = Modifier.align(Alignment.CenterEnd),
                                adapter = if (file.isDirectory) {
                                    rememberScrollbarAdapter(scrollState = directoryPreviewLazyListState)
                                } else
                                    rememberScrollbarAdapter(scrollState = previewScrollState)
                            )
                        }
                    }
                }
            }
            DialogBottomKeyInfo(
                buildList {
                    addAll(
                        listOf(
                            DialogBottomKey("↓", "select next file"),
                            DialogBottomKey("↑", "select previous file"),
                        )
                    )
                },
                modifier = Modifier
                    .padding(top = 12.dp, end = 12.dp, bottom = 12.dp)
            )
        }
    }

    LaunchedEffect(Unit) {
        searchFocusRequester.requestFocus()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            searchFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(search) {
        filteredFiles = params.files.filterHuman(search) {
            it.absolutePath
        }
    }
    LaunchedEffect(filteredFiles, selectedFile) {
        if (filteredFiles.isEmpty()) {
            selectedFile = null
            return@LaunchedEffect
        }
        if (selectedFile == null) {
            selectedFile = filteredFiles.first()
            return@LaunchedEffect
        }
        val index = filteredFiles.indexOf(selectedFile)
        if (index == -1) {
            selectedFile = filteredFiles.first()
        } else {
            scrollState.animateScrollToItem(index, -500)
        }
    }
}