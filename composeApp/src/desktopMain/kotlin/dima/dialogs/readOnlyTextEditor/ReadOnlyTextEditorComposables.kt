package dima.dialogs.readOnlyTextEditor

import Globals
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.*
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import dima.apps.textEditor.*
import dima.color.TailwindCssColors
import dima.treeSitter.IntellijDarculaTheme
import dima.utils.getBoundingBoxSafe

internal fun isLineMarkedInVisualLineMode(
    lineNumber: Int,
    visualStartAnchor: Position?,
    position: Position
): Boolean {
    if (visualStartAnchor == null) {
        return false
    }
    val startLine = minOf(visualStartAnchor.lineNumber, position.lineNumber)
    val endLine = maxOf(visualStartAnchor.lineNumber, position.lineNumber)
    return lineNumber in startLine..endLine
}

internal fun isLineMarkedInVisualMode(
    lineNumber: Int,
    visualStartAnchor: Position?,
    position: Position
): Boolean {
    if (visualStartAnchor == null) {
        return false
    }

    val startLine = minOf(visualStartAnchor.lineNumber, position.lineNumber)
    val endLine = maxOf(visualStartAnchor.lineNumber, position.lineNumber)

    return lineNumber in startLine..endLine
}

internal fun measureTextLine(text: String, width: Dp, column: Int, isMonospace: Boolean): Rect {
    if (text.isEmpty()) {
        // For empty lines, return a rectangle with height of one line
        val paragraph = getCachedMultiParagraph("x", width, isMonospace)
        val box = paragraph.getBoundingBox(0)
        return Rect(0f, box.top, 0f, box.bottom)
    }

    val paragraph = getCachedMultiParagraph(text, width, isMonospace)

    return when {
        // Handle end-of-line case
        column >= text.length -> {
            val lastCharBox = paragraph.getBoundingBox(text.length - 1)
            Rect(
                lastCharBox.right,
                lastCharBox.top,
                lastCharBox.right + lastCharBox.width,
                lastCharBox.bottom
            )
        }
        // Handle normal case
        else -> try {
            paragraph.getBoundingBox(column)
        } catch (e: IllegalArgumentException) {
            // Fallback for any other out-of-bounds cases
            val safeColumn = (column - 1).coerceIn(0, text.length - 1)
            paragraph.getBoundingBox(safeColumn)
        }
    }
}

internal fun getCachedMultiParagraph(text: String, width: Dp, isMonospace: Boolean): MultiParagraph {
    val constraints = Constraints(maxWidth = with(Globals.density) { width.roundToPx() })
    return TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
        .measure(
            text = text,
            style = TextStyle(
                fontFamily = if (isMonospace) FontFamily.Monospace else FontFamily.Default,
                fontSize = ReadOnlyTextEditorDialogStyling.fontSize
            ),
            constraints = constraints
        ).multiParagraph
}


@Composable
private fun VisualModifierForLine(
    line: Line,
    isMonospace: Boolean,
    position: Position,
    visualStartAnchor: Position,
    point: (Position) -> Int,
    textWidth: Dp,
    visualBackground: Color
): Modifier {
    return Modifier
        .drawBehind {
            var startPosition = position
            var endPosition = visualStartAnchor
            if (point(startPosition) > point(endPosition)) {
                val temp = startPosition
                startPosition = endPosition
                endPosition = temp
            }
            val paragraph = getCachedMultiParagraph(line.text, textWidth, isMonospace)
            when {
                line.lineNumber == startPosition.lineNumber &&
                        line.lineNumber == endPosition.lineNumber -> {
                    val startBox = paragraph.getBoundingBoxSafe(startPosition.column)
                    val endBox = paragraph.getBoundingBoxSafe(endPosition.column)
                    drawVisualRectangle(
                        visualBackground,
                        startBox.left,
                        startBox.top,
                        endBox.right - startBox.left,
                        startBox.height
                    )
                }

                line.lineNumber == startPosition.lineNumber -> {
                    val startBox = paragraph.getBoundingBoxSafe(startPosition.column)
                    drawVisualRectangle(
                        visualBackground,
                        startBox.left,
                        startBox.top,
                        size.width - startBox.left,
                        startBox.height
                    )
                }

                line.lineNumber == endPosition.lineNumber -> {
                    val endBox = paragraph.getBoundingBoxSafe(endPosition.column)
                    drawVisualRectangle(
                        visualBackground,
                        0f,
                        endBox.top,
                        endBox.right,
                        endBox.height
                    )
                }

                else -> {
                    val box = paragraph.getBoundingBoxSafe(0)
                    drawVisualRectangle(
                        visualBackground,
                        0f,
                        box.top,
                        size.width,
                        box.height,
                    )
                }
            }
        }
}

@Composable
internal fun LineContent(
    line: Line,
    lines: List<Line>,
    position: Position,
    textEditorMode: ReadOnlyTextEditorMode,
    isMonospace: Boolean,
    lineHighlights: LineHighlights,
    visualStartAnchor: Position?,
    textWidth: Dp,
    point: (Position) -> Int,
    charJumpers: List<CharJumperEntry>,
    selectedJumpToChars: List<Key>
) {
    val isLightTheme = lineHighlights.highlights.isEmpty()
    val textColor = if (isLightTheme) Color.Black else IntellijDarculaTheme.textColor
    val visualBackground = TextEditorStyling.getVisualHightlingBackgroundColor()
    val cursorColor = if (isLightTheme) TailwindCssColors.gray500 else TailwindCssColors.gray400
    val annotatedString = buildAnnotatedString {
        val lineStartOffset = lines.subList(0, line.lineNumber).sumOf { it.text.length + 1 }
        append(line.text)
        lineHighlights.highlights.forEach { highlight ->
            val highlightStart = (highlight.start - lineStartOffset).coerceAtLeast(0)
            val highlightEnd = (highlight.end - lineStartOffset).coerceAtMost(line.text.length)
            if (highlightStart < line.text.length && highlightEnd > 0 && highlightStart < highlightEnd) {
                addStyle(
                    SpanStyle(color = highlight.color),
                    highlightStart,
                    highlightEnd
                )
            }
        }
        if (line.lineNumber == position.lineNumber && position.column < line.text.length) {
            addStyle(
                SpanStyle(color = Color.White),
                position.column,
                position.column + 1
            )
        }
    }
    val isLineMarked = when (textEditorMode) {
        ReadOnlyTextEditorMode.VisualLine -> isLineMarkedInVisualLineMode(line.lineNumber, visualStartAnchor, position)
        ReadOnlyTextEditorMode.Visual -> isLineMarkedInVisualMode(line.lineNumber, visualStartAnchor, position)
        else -> false
    }
    val isCurrentLine = line.lineNumber == position.lineNumber

    val backgroundModifier = when (textEditorMode) {
        ReadOnlyTextEditorMode.VisualLine -> {
            if (isLineMarked) Modifier.background(visualBackground) else Modifier
        }

        ReadOnlyTextEditorMode.Visual -> {
            if (visualStartAnchor != null && isLineMarked) {
                VisualModifierForLine(
                    line = line,
                    isMonospace = isMonospace,
                    position = position,
                    visualStartAnchor = visualStartAnchor,
                    point = point,
                    textWidth = textWidth,
                    visualBackground = visualBackground
                )
            } else {
                Modifier
            }
        }

        else -> Modifier
    }

    Text(
        text = annotatedString,
        fontFamily = if (isMonospace) FontFamily.Monospace else FontFamily.Default,
        fontSize = ReadOnlyTextEditorDialogStyling.fontSize,
        color = textColor,
        modifier = Modifier
            .fillMaxWidth()
            .renderJumpCharHints(charJumpers, selectedJumpToChars, line, textWidth)
            .then(backgroundModifier)
            .drawBehind {
                if (!isCurrentLine) {
                    return@drawBehind
                }
                val box = if (position.column == line.text.length) {
                    // Handle newline position
                    if (line.text.isEmpty()) {
                        // For empty lines, use a default width
                        val paragraph = getCachedMultiParagraph("x", textWidth, isMonospace)
                        val charBox = paragraph.getBoundingBox(0)
                        Rect(0f, charBox.top, charBox.width, charBox.bottom)
                    } else {
                        // Use the width of the last character
                        val paragraph = getCachedMultiParagraph(line.text, textWidth, isMonospace)
                        val lastCharBox = paragraph.getBoundingBox(line.text.length - 1)
                        Rect(
                            lastCharBox.right,
                            lastCharBox.top,
                            lastCharBox.right + lastCharBox.width,
                            lastCharBox.bottom
                        )
                    }
                } else {
                    measureTextLine(
                        line.text,
                        textWidth,
                        position.column,
                        isMonospace
                    )
                }
                drawRect(
                    color = cursorColor,
                    topLeft = Offset(x = box.left, y = box.top),
                    size = Size(width = box.width, height = box.height)
                )
            }
    )
}
