package dima.dialogs.readOnlyTextEditor

import GlobalEvent
import Globals
import TextEditorCharJumpMode
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dialogs
import dima.apps.notifications.showErrorNotification
import dima.apps.textEditor.*
import dima.treeSitter.TreeSitter
import dima.treeSitter.TreeSitterLanguage
import dima.dialogs.*
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.os.copyToClipboard
import dima.treeSitter.IntellijDarculaTheme
import dima.utils.LaunchedEffectGlobalEventForDialogs
import globalEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import textEditorJumpCharMode


internal enum class ReadOnlyTextEditorMode {
    Command,
    Visual,
    VisualLine,
}

private data class Params(
    val title: String?,
    val content: String,
    val language: TreeSitterLanguage? = null,
    val showLineNumbers: Boolean = false
)

internal data class LineHighlights(
    val highlights: List<TreeSitter.Highlight> = emptyList(),
    val error: String? = null
)

fun openReadOnlyTextEditorDialog(
    content: String,
    title: String? = null,
    language: TreeSitterLanguage? = null,
    showLineNumbers: Boolean = false
) {
    val params = Params(title, content, language, showLineNumbers)
    openDialog(DialogIdentifier.ReadOnlyTextEditor, params) { id, data ->
        ReadOnlyTextEditorDialogDialog(id, data!!)
    }
}

@Composable
fun ReadOnlyTextEditorDialogDialog(id: Long, data: Any) {
    val params = data as Params
    val lines = remember { params.content.split("\n").mapIndexed { index, line -> Line(line, index) } }
    var lineHighlights by remember { mutableStateOf(LineHighlights()) }
    val scrollState = rememberLazyListState()
    val dialogFocusRequester = remember { FocusRequester() }
    var textWidth by remember { mutableStateOf(800.dp) }
    var isMonospace by remember { mutableStateOf(false) }
    var position by remember { mutableStateOf(Position()) }
    var columnGoal by remember { mutableStateOf(0) }
    var visualStartAnchor by remember { mutableStateOf<Position?>(null) }
    var textEditorMode by remember { mutableStateOf(ReadOnlyTextEditorMode.Command) }
    val coroutineScope = rememberCoroutineScope()
    val isDarkMode = remember { params.language != null }
    var charJumpers by remember { mutableStateOf<List<CharJumperEntry>>(emptyList()) }
    var selectedJumpToChars by remember { mutableStateOf(emptyList<Key>()) }
    var selectedChars by remember { mutableStateOf(0) }
    var selectedLines by remember { mutableStateOf(0) }
    val maxLineNumberCharCount by remember { mutableStateOf(lines.size.toString().length) }

    fun scrollToLine(line: Int) {
        coroutineScope.launch {
            val defaultOffset = -500
            scrollState.animateScrollToItem(line.coerceIn(0, lines.size - 1), defaultOffset)
        }
    }

    fun point(pos: Position = position): Int {
        var point = 0
        for (lineIndex in 0 until pos.lineNumber) {
            point += lines[lineIndex].text.length + 1
        }
        return point + pos.column
    }

    fun updateSelectionCounts(start: Int, end: Int) {
        val selectedText = params.content.substring(start, end)
        selectedChars = selectedText.length
        selectedLines = selectedText.count { it == '\n' }
    }

    fun updateLineSelectionCounts() {
        var lineCount = 0
        var charCount = 0
        lines.forEach {
            if (isLineMarkedInVisualMode(
                    lineNumber = it.lineNumber,
                    visualStartAnchor = visualStartAnchor,
                    position = position
                )
            ) {
                lineCount++
                charCount += it.text.length + 1 // +1 for newline
            }
        }
        selectedLines = lineCount
        selectedChars = charCount
    }

    fun getVisualModeText(): String {
        var start = point()
        var end = point(visualStartAnchor!!)
        if (start > end) {
            val temp = start
            start = end
            end = temp
        }
        updateSelectionCounts(start, end)
        return params.content.substring(start, end)
    }

    fun getVisualLineModeText(): String {
        updateLineSelectionCounts()
        return buildString {
            lines.forEach {
                if (isLineMarkedInVisualMode(
                        lineNumber = it.lineNumber,
                        visualStartAnchor = visualStartAnchor,
                        position = position
                    )
                ) {
                    append(it.text)
                    append("\n")
                }
            }
        }
    }

    fun moveCursor(deltaLine: Int, deltaColumn: Int) {
        val newLine = (position.lineNumber + deltaLine).coerceIn(0, lines.size - 1)

        // When moving horizontally, update columnGoal
        if (deltaColumn != 0) {
            columnGoal = position.column + deltaColumn
        }

        // Calculate new column, maintaining columnGoal when moving vertically
        val newColumn = when {
            deltaLine != 0 -> columnGoal.coerceIn(0, lines[newLine].text.length)
            else -> (position.column + deltaColumn).coerceIn(0, lines[newLine].text.length)
        }
        position = position.copy(lineNumber = newLine, column = newColumn)
        scrollToLine(newLine)

        if (textEditorMode == ReadOnlyTextEditorMode.Visual || textEditorMode == ReadOnlyTextEditorMode.VisualLine) {
            if (visualStartAnchor != null) {
                if (textEditorMode == ReadOnlyTextEditorMode.Visual) {
                    getVisualModeText() // This will update selectedLines and selectedChars
                } else {
                    var lineCount = 0
                    lines.forEach {
                        if (isLineMarkedInVisualMode(
                                lineNumber = it.lineNumber,
                                visualStartAnchor = visualStartAnchor,
                                position = position
                            )
                        ) {
                            lineCount++
                        }
                    }
                    selectedLines = lineCount
                    selectedChars = getVisualLineModeText().length
                }
            }
        }
    }

    fun goToLineUp() {
        // Column goal is maintained by moveCursor
        moveCursor(-1, 0)
    }

    fun goToLineDown() {
        // Column goal is maintained by moveCursor
        moveCursor(1, 0)
    }

    fun goToTop() {
        position = Position()
        columnGoal = 0 // Reset column goal when moving to top
        scrollToLine(0)
    }

    fun goToBottom() {
        val lastColumn = lines.last().text.length
        position = Position(lines.size - 1, lastColumn)
        columnGoal = lastColumn // Update column goal when moving to bottom
        scrollToLine(lines.size - 1)
    }

    fun onEscape() {
        visualStartAnchor = null
        when (textEditorMode) {
            ReadOnlyTextEditorMode.Command -> closeDialogWithId(id)
            ReadOnlyTextEditorMode.Visual, ReadOnlyTextEditorMode.VisualLine -> {
                selectedChars = 0
                selectedLines = 0
                textEditorMode = ReadOnlyTextEditorMode.Command
            }
        }
    }

    fun findPositionByTextIndex(index: Int): Position {
        var currentIndex = 0
        for ((lineNumber, line) in lines.withIndex()) {
            val lineLength = line.text.length + 1
            if (currentIndex + lineLength > index) {
                return Position(lineNumber, index - currentIndex)
            }
            currentIndex += lineLength
        }
        return Position(lines.size - 1, lines.last().text.length)
    }

    fun jumpTo(to: CharJumperEntry) {
        val newLine = lines[to.line]
        val paragraph = getCachedMultiParagraph(newLine.text, textWidth, isMonospace)
        val offset = paragraph.getOffsetForPosition(Offset(x = to.x, y = to.y + 1f))
        position = position.copy(lineNumber = newLine.lineNumber, column = offset)
        columnGoal = offset // Update column goal when jumping to new position
        scrollToLine(newLine.lineNumber)
    }

    fun onWaitingForFirstCharToJumpToHint(it: KeyEvent): Boolean {
        handleWaitingForFirstCharToJumpToHint(
            it = it,
            lines = lines,
            scrollState = scrollState,
            textWidth = textWidth,
            isMonospace = isMonospace,
            position = position,
            onCharJumpersUpdate = { newCharJumpers ->
                charJumpers = newCharJumpers
                if (newCharJumpers.size == 1) {
                    jumpTo(newCharJumpers.first())
                }
            },
            textSize = ReadOnlyTextEditorDialogStyling.fontSize,
            onJumpModeChange = { newMode ->
                textEditorJumpCharMode = newMode
            }
        )
        return true
    }

    fun onHintsVisibleAndWaitingForSelection(it: KeyEvent): Boolean {
        selectedJumpToChars = selectedJumpToChars + it.key
        val found = charJumpers.find { charJumperEntry ->
            charJumperEntry.keyToTrigger == selectedJumpToChars
        }
        if (found == null) {
            val first = charJumpers.find { charJumperEntry ->
                charJumperEntry.keyToTrigger.take(1) == selectedJumpToChars.take(1)
            }
            if (first == null) {
                showErrorNotification("No such candidate: ${it.key}")
                selectedJumpToChars = emptyList()
            }
        } else {
            jumpTo(found)
            textEditorJumpCharMode = TextEditorCharJumpMode.NotActive
            selectedJumpToChars = emptyList()
            charJumpers = emptyList()
        }
        return true
    }

    DialogOverlay(
        dialogId = id,
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.type != KeyEventType.KeyDown) {
                return@onPreviewKeyEvent false
            }
            if (it.key == Key.Escape) {
                if (textEditorJumpCharMode != TextEditorCharJumpMode.NotActive) {
                    if (selectedJumpToChars.isNotEmpty()) {
                        selectedJumpToChars = emptyList()
                        return@onPreviewKeyEvent true
                    }
                    textEditorJumpCharMode = TextEditorCharJumpMode.NotActive
                    charJumpers = emptyList()
                    return@onPreviewKeyEvent true
                }
                onEscape()
                return@onPreviewKeyEvent true
            }
            if (textEditorJumpCharMode == TextEditorCharJumpMode.HintsVisibleAndWaitingForSelection) {
                return@onPreviewKeyEvent onHintsVisibleAndWaitingForSelection(it)
            }
            if (textEditorJumpCharMode == TextEditorCharJumpMode.WaitingForFirstCharToJumpTo) {
                onWaitingForFirstCharToJumpToHint(it)
                return@onPreviewKeyEvent true
            }
            when (it.key) {
                Key.U -> {
                    if (it.isMetaPressed) {
                        isMonospace = !isMonospace
                    } else {
                        showErrorNotification("Insert mode is not allowed", durationMillis = 500)
                    }
                    return@onPreviewKeyEvent true
                }

                Key.C -> {
                    goToLineUp()
                    return@onPreviewKeyEvent true
                }

                Key.T -> {
                    goToLineDown()
                    return@onPreviewKeyEvent true
                }

                Key.H -> {
                    if (position.column == 0 && position.lineNumber > 0) {
                        moveCursor(-1, lines[position.lineNumber - 1].text.length)
                    } else {
                        moveCursor(0, -1)
                    }
                    if (textEditorMode == ReadOnlyTextEditorMode.VisualLine || textEditorMode == ReadOnlyTextEditorMode.Visual) {
                        updateLineSelectionCounts()
                    }
                    return@onPreviewKeyEvent true
                }

                Key.N -> {
                    val currentLine = lines[position.lineNumber]
                    when {
                        // If at the end of the line and not on the last line, move to start of next line
                        position.column >= currentLine.text.length && position.lineNumber < lines.size - 1 -> {
                            moveCursor(1, -position.column)
                        }
                        // If not at the end of the line, move right one character
                        position.column < currentLine.text.length -> {
                            moveCursor(0, 1)
                        }
                        // Otherwise do nothing (we're at the end of a line)
                    }
                    if (textEditorMode == ReadOnlyTextEditorMode.VisualLine || textEditorMode == ReadOnlyTextEditorMode.Visual) {
                        updateLineSelectionCounts()
                    }
                    return@onPreviewKeyEvent true
                }

                Key.D -> {
                    position = position.copy(column = 0)
                    columnGoal = 0 // Reset column goal when moving to start of line
                    if (textEditorMode == ReadOnlyTextEditorMode.VisualLine || textEditorMode == ReadOnlyTextEditorMode.Visual) {
                        updateLineSelectionCounts()
                    }
                    return@onPreviewKeyEvent true
                }

                Key.S -> {
                    val endColumn = lines[position.lineNumber].text.length
                    position = position.copy(column = endColumn)
                    columnGoal = endColumn // Update column goal when moving to end of line
                    if (textEditorMode == ReadOnlyTextEditorMode.VisualLine || textEditorMode == ReadOnlyTextEditorMode.Visual) {
                        updateLineSelectionCounts()
                    }
                    return@onPreviewKeyEvent true
                }

                Key.M -> {
                    repeat(TextEditor.LINES_TO_JUMP_FOR_M_AND_V) {
                        moveCursor(1, 0)
                    }
                    return@onPreviewKeyEvent true
                }

                Key.V -> {
                    repeat(TextEditor.LINES_TO_JUMP_FOR_M_AND_V) {
                        moveCursor(-1, 0)
                    }
                    return@onPreviewKeyEvent true
                }

                Key.G -> {
                    var index = point()
                    val current = TextEditor.getWordBoundaryAt(params.content, index)
                    if (current != null && index <= current.startInclusive) {
                        index = current.startInclusive
                    }
                    var offset = 1
                    while (true) {
                        if (index - offset <= 0) {
                            globalEvent = GlobalEvent.ScrollToTop
                            break
                        }
                        val previousWord = TextEditor.getWordBoundaryAt(params.content, index - offset)
                        if (previousWord != null) {
                            position = findPositionByTextIndex(previousWord.startInclusive)
                            break
                        }
                        offset++
                    }
                    if (textEditorMode == ReadOnlyTextEditorMode.VisualLine || textEditorMode == ReadOnlyTextEditorMode.Visual) {
                        updateLineSelectionCounts()
                    }
                    return@onPreviewKeyEvent true
                }

                Key.R -> {
                    var index = point()
                    val current = TextEditor.getWordBoundaryAt(params.content, index)
                    if (current != null) {
                        index = current.endInclusive
                    }
                    var offset = 1
                    while (true) {
                        if (index + offset >= params.content.length) {
                            globalEvent = GlobalEvent.ScrollToBottom
                            break
                        }
                        val nextWord = TextEditor.getWordBoundaryAt(params.content, index + offset)
                        if (nextWord != null) {
                            position = findPositionByTextIndex(nextWord.startInclusive)
                            break
                        }
                        offset++
                    }
                    if (textEditorMode == ReadOnlyTextEditorMode.VisualLine || textEditorMode == ReadOnlyTextEditorMode.Visual) {
                        updateLineSelectionCounts()
                    }
                    return@onPreviewKeyEvent true
                }

                Key.Y -> {
                    if (textEditorMode == ReadOnlyTextEditorMode.Command) {
                        visualStartAnchor = position
                        textEditorMode = ReadOnlyTextEditorMode.Visual
                    }
                    return@onPreviewKeyEvent true
                }

                Key.J -> {
                    when (textEditorMode) {
                        ReadOnlyTextEditorMode.VisualLine -> {
                            val toCopy = getVisualLineModeText()
                            copyToClipboard(toCopy)
                            visualStartAnchor = null
                            textEditorMode = ReadOnlyTextEditorMode.Command
                        }

                        ReadOnlyTextEditorMode.Visual -> {
                            val selectedText = getVisualModeText()
                            copyToClipboard(selectedText)
                            visualStartAnchor = null
                            textEditorMode = ReadOnlyTextEditorMode.Command
                        }

                        else -> {
                            val line = lines[position.lineNumber].text
                            copyToClipboard(line)
                        }
                    }
                    return@onPreviewKeyEvent true
                }

                Key.DirectionUp -> {
                    goToLineUp()
                    return@onPreviewKeyEvent true
                }

                Key.DirectionDown -> {
                    goToLineDown()
                    return@onPreviewKeyEvent true
                }

                Key.DirectionLeft -> {
                    if (position.column == 0 && position.lineNumber > 0) {
                        moveCursor(-1, lines[position.lineNumber - 1].text.length)
                    } else {
                        moveCursor(0, -1)
                    }
                    return@onPreviewKeyEvent true
                }

                Key.DirectionRight -> {
                    if (position.column >= lines[position.lineNumber].text.length && position.lineNumber < lines.size - 1) {
                        moveCursor(1, -position.column)
                    } else {
                        moveCursor(0, 1)
                    }
                    return@onPreviewKeyEvent true
                }

                Key.L -> {
                    if (it.isMetaPressed) {
                        openMiniHelpDialog(
                            "Read-only Text Editor Dialog",
                            keys = buildList {
                                addAll(
                                    listOf(
                                        MiniHelpDialogKey(listOf(Key.J), "Copy marked area or current line"),
                                        MiniHelpDialogKey(listOf(Key.H), "Move one character left"),
                                        MiniHelpDialogKey(listOf(Key.L), "Move one character right"),
                                        MiniHelpDialogKey(listOf(Key.G), "Move one word left"),
                                        MiniHelpDialogKey(listOf(Key.R), "Move one word right"),
                                        MiniHelpDialogKey(listOf(Key.M), "Move multiple lines down"),
                                        MiniHelpDialogKey(listOf(Key.V), "Move multiple lines up"),
                                        MiniHelpDialogKey(listOf(Key.Y), "Enter visual mode"),
                                        MiniHelpDialogKey(listOf(Key.Spacebar, Key.V), "Enter visual line mode"),
                                        MiniHelpDialogKey(listOf(Key.L), "Jump to character"),
                                        MiniHelpDialogKey(listOf(Key.DirectionUp), "Move one line up"),
                                        MiniHelpDialogKey(listOf(Key.DirectionDown), "Move one line right"),
                                        MiniHelpDialogKey(listOf(Key.DirectionLeft), "Move one character left"),
                                        MiniHelpDialogKey(listOf(Key.DirectionRight), "Move one character right"),
                                        MiniHelpDialogKey(listOf(Key.Escape), "Exit visual mode or close dialog")
                                    )
                                )
                            }
                        )
                        return@onPreviewKeyEvent true
                    }
                    textEditorJumpCharMode = TextEditorCharJumpMode.WaitingForFirstCharToJumpTo
                    selectedJumpToChars = emptyList()
                    charJumpers = emptyList()
                    return@onPreviewKeyEvent true
                }

                else -> return@onPreviewKeyEvent false
            }
        }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .focusRequester(dialogFocusRequester)
                .fillMaxWidth()
        ) {
            if (params.title != null) {
                Text(
                    params.title,
                    fontSize = ReadOnlyTextEditorDialogStyling.fontSize * 1.2f,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(bottom = 12.dp)
                        .fillMaxWidth()
                        .weight(0.1f)
                )
            }
            Text(
                "$textEditorMode (Line ${position.lineNumber + 1}, Column ${position.column})",
                fontSize = 18.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(bottom = 12.dp)
                    .fillMaxWidth()
            )
            Box(
                contentAlignment = Alignment.TopCenter,
                modifier = Modifier
                    .fillMaxWidth(0.7f)
                    .weight(1f)
                    .then(
                        if (textEditorMode == ReadOnlyTextEditorMode.Visual || textEditorMode == ReadOnlyTextEditorMode.VisualLine) {
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                if (isDarkMode) IntellijDarculaTheme.visualBackground else TextEditorStyling.visualModeBorderColor,
                                TextEditorStyling.roundedShape
                            )
                        } else if (textEditorJumpCharMode != TextEditorCharJumpMode.NotActive) {
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                TextEditorStyling.jumpCharBorderColor,
                                TextEditorStyling.roundedShape
                            )
                        } else if (!isDarkMode) {
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                TextEditorStyling.greyBorderColor,
                                TextEditorStyling.roundedShape
                            )
                        } else {
                            Modifier
                        }
                    )
                    .background(
                        if (isDarkMode) IntellijDarculaTheme.background else Color.White,
                        RoundedCornerShape(5.dp)
                    )
                    .padding(2.dp)
            ) {
                SelectionContainer {
                    LazyColumn(
                        state = scrollState,
                        modifier = Modifier
                            .fillMaxWidth()
//                            .padding(horizontal = 12.dp)
                            .onSizeChanged {
                                with(Globals.density) {
                                    textWidth = it.width.toDp()
                                }
                            }
                    ) {
                        items(lines) { line ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                            ) {
                                if (params.showLineNumbers) {
                                    LineNumber(
                                        lineNumber = (line.lineNumber + 1).toString(),
                                        maxLineNumberCharCount = maxLineNumberCharCount,
                                        isCurrentLine = line.lineNumber == position.lineNumber,
                                        lineHeight = ReadOnlyTextEditorDialogStyling.fontSize
                                    )
                                }
                                LineContent(
                                    line = line,
                                    lines = lines,
                                    position = position,
                                    textEditorMode = textEditorMode,
                                    isMonospace = isMonospace,
                                    lineHighlights = lineHighlights,
                                    visualStartAnchor = visualStartAnchor,
                                    textWidth = textWidth,
                                    point = { point(it) },
                                    charJumpers = charJumpers,
                                    selectedJumpToChars = selectedJumpToChars
                                )
                            }
                        }
                    }
                }
                VerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd),
                    adapter = rememberScrollbarAdapter(scrollState = scrollState)
                )
            }
            DialogBottomKeyInfo(
                listOf(
                    DialogBottomKey("l", "open help", isCmd = true)
                ),
                leftText = when {
                    selectedChars > 0 && selectedLines == 0 -> {
                        if (selectedChars == 1) "1 char" else "$selectedChars chars"
                    }

                    selectedChars > 0 && selectedLines > 0 -> {
                        val charsText = if (selectedChars == 1) "1 char" else "$selectedChars chars"
                        val linesText = if (selectedLines == 1) "1 line" else "$selectedLines lines"
                        "$charsText   $linesText"
                    }

                    else -> null
                },
                leftTextBackgroundColor = TextEditorStyling.selectedCharLineBackgroundColor,
                modifier = Modifier
                    .padding(horizontal = 12.dp, vertical = 8.dp)
                    .fillMaxWidth()
            )
        }
    }

    LaunchedEffect(Unit) {
        dialogFocusRequester.requestFocus()
        textEditorJumpCharMode = TextEditorCharJumpMode.NotActive
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            dialogFocusRequester.requestFocus()
        }
    }
    LaunchedEffectGlobalEventForDialogs(id) {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> goToTop()
            GlobalEvent.ScrollToBottom -> goToBottom()
            GlobalEvent.CopyAll -> copyToClipboard(params.content)

            GlobalEvent.EnterVisualLineMode -> {
                textEditorMode = ReadOnlyTextEditorMode.VisualLine
                visualStartAnchor = position
            }

            else -> {}
        }
    }
    LaunchedEffect(params.content, params.language) {
        if (params.language == null) {
            lineHighlights = LineHighlights()
            return@LaunchedEffect
        }

        withContext(Dispatchers.IO) {
            val newText = TreeSitter.patchCodeToHighlight(params.content, params.language)
            val highlightsScheme = TreeSitter.getHighlightsSchemeFileContents(params.language)
            val cliResult = TreeSitter.startTreeSitterCliAndExtractHighlights(
                newText,
                params.language,
                highlightsScheme
            )

            if (cliResult.error != null) {
                lineHighlights = LineHighlights(error = cliResult.error)
                return@withContext
            }

            val highlights = cliResult.highlights.mapNotNull { cliHighlight ->
                val color = TreeSitter.getColorMappingValueByCaptureName(
                    params.language.toString().lowercase(),
                    cliHighlight.captureName
                ) ?: return@mapNotNull null

                TreeSitter.mapCliHighlightToColorHighlight(
                    cliHighlight,
                    color,
                    params.language,
                    emptyList()
                )
            }

            lineHighlights = LineHighlights(highlights = highlights)
        }
    }
}
