package dima.dialogs

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.composables.icons.lucide.CircleHelp
import com.composables.icons.lucide.Lucide
import dima.dialogs.help.openHelpDialog
import dima.utils.clickableWithoutBackgroundRipple

/**
 * If shown, tying ? opens a help dialog.
 */
@Composable
fun BoxScope.DialogHelpIcon(xOffset: Dp = 6.dp, yOffset: Dp = 6.dp) {
    Box(
        modifier = Modifier
            .align(Alignment.BottomEnd)
            .offset(x = xOffset, y = yOffset)
            .background(GlobalStyling.getWindowBackgroundColor(), shape = CircleShape)
    ) {
        Icon(
            Lucide.CircleHelp,
            contentDescription = "Help",
            tint = GlobalStyling.getGrayColor(),
            modifier = Modifier
                .size(18.dp)
                .clickableWithoutBackgroundRipple {
                    openHelpDialog()
                }
        )
    }
}