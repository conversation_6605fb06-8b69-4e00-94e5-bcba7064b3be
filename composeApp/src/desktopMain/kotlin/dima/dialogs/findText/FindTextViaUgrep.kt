package dima.dialogs.findText

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.utils.Ring
import dima.utils.getStringLengthRangesFromByteRange
import org.zeroturnaround.exec.ProcessExecutor
import java.io.ByteArrayOutputStream
import java.io.File
import kotlin.streams.toList

internal enum class MatchEngine {
    /**
     * Splits entered query on spaces and searches lines which contain all matches.
     * Casing is ignored.
     */
    AnyWordInLine,

    /**
     * Casing is significant.
     */
    Literal
}

/**
 * Uses [FindText.search].
 */
internal class UgrepThread : Thread() {

    /**
     * About the format passed to ugrep:
     *
     * - %b gives the byte start offset in the entire file. I don't use since I need to read in the entire file again,
     *   to calculate the offsets for the line highlight
     */
    override fun run() {
        val stdout = ByteArrayOutputStream()
        val stderr = ByteArrayOutputStream()
        val searchParts = FindText.search.trim().split(" ")
        val process = with(ProcessExecutor()) {
            // format explanation:
            // %f filename
            // %n line number
            // %k column number of the match
            // %d byte length of match
            // %b byte start of the match in the file
            // %O entire matching line
            val commonArgs =
                mutableListOf("ug", "--ignore-binary", "--format=%f:%n:%k:%d:%b:%O%~")
            val searchText = if (FindText.matchEngine == MatchEngine.Literal) {
                commonArgs.add("--fixed-strings")
                FindText.search
            } else {
                commonArgs.add("--ignore-case")
                FindText.search.trim()
            }
            if (searchParts.size <= 1) {
                command(*commonArgs.toTypedArray(), searchText, ".")
            } else {
                when (FindText.matchEngine) {
                    MatchEngine.AnyWordInLine -> {
                        // this also works: ug -% "jeweils kg" and I think it matches with AND on space?
                        command(*commonArgs.toTypedArray(), "--bool", searchParts.joinToString(" AND "), ".")
                    }

                    MatchEngine.Literal -> {
                        command(*commonArgs.toTypedArray(), searchText, ".")
                    }
                }
            }
            destroyOnExit()
            directory(FindText.path!!)
            redirectOutput(stdout)
            redirectError(stderr)
            execute()
        }
        FindText.processExitCode = process.exitValue
        FindText.processStdout = stdout.toString()
        if (FindText.processExitCode == 0) {
            FindText.results = FindText.createSearchResultsFromProcessStdout()
            // ignore stderr on successful exit code
            FindText.processStderr = null
        } else {
            FindText.results = emptyList()
            FindText.processStderr = stderr.toString()
        }
    }
}

internal object FindText {

    const val LINES_BEFORE_AND_AFTER = 40

    data class FileContent(
        val file: File,
        val content: String,
    )

    private val readFiles = Ring<FileContent>(5)

    /**
     * Stores result in [readFiles].
     */
    fun getFileContent(file: File): String {
        val fileContent = readFiles.find { it.file == file }
        if (fileContent == null) {
            val content = file.readText()
            readFiles.add(FileContent(file, content))
            return content
        }
        return fileContent.content
    }

    data class SearchResult(
        val fileName: String,
        /**
         * Ugrep starts counting at 1. I subtract 1 here, so it's 0-based.
         */
        val lineNumber: Int,
        /**
         * The start byte of the match in the entire file (inclusive).
         */
        val byteStart: Int,
        val matchAnnotationStringStart: Int,
        val matchAnnotationStringEnd: Int,
        val lineContent: String,

        /**
         * Added here because a LazyColumn is used. Initialized in [createSearchResultsFromProcessStdout].
         */
        val showFileNameHeader: Boolean = false,
        val isSameLineNumberAsPrevious: Boolean = false,
    )

    internal var search by mutableStateOf("")
    internal var results by mutableStateOf(emptyList<SearchResult>())
    internal var matchEngine by mutableStateOf(MatchEngine.AnyWordInLine)
    internal var path by mutableStateOf<File?>(null)
    internal var processStdout by mutableStateOf<String?>(null)
    internal var processStderr by mutableStateOf<String?>(null)
    private var thread: Thread? = null

    /**
     * Null when not run.
     */
    internal var processExitCode by mutableStateOf<Int?>(null)

    /**
     * The thread uses [search] for its query.
     */
    fun startThreadWithSearch() {
        abortThread()
        thread = UgrepThread()
        thread!!.start()
    }

    fun abortThread() {
        processExitCode = null
        processStderr = null
        processStdout = null
        if (thread == null) {
            return
        }
        if (!thread!!.isAlive) {
            return
        }
        thread!!.interrupt()
    }

    fun createSearchResultsFromProcessStdout(): List<SearchResult> {
        if (processStdout == null) {
            return emptyList()
        }
        val output = processStdout!!.trim()
        val lines = output.lines()
        var lastLineNumber: Int? = null
        val mapped = lines.map {
            val parts = it.split(":", limit = 6)
            if (parts.size != 6) {
                throw IllegalArgumentException("Invalid format for search result line: $it")
            }
            val lineNumber = parts[1].toInt() - 1
            val columnNumber = parts[2].toInt()
            val byteLength = parts[3].toInt()
            val byteStart = parts[4].toInt()
            val lineContent = parts[5]
            val match = extractMatchString(lineContent, columnNumber, byteLength)
            val result = SearchResult(
                fileName = parts[0],
                lineNumber = lineNumber,
                byteStart = byteStart,
                matchAnnotationStringStart = match.first,
                matchAnnotationStringEnd = match.second,
                lineContent = lineContent,
                isSameLineNumberAsPrevious = lastLineNumber == lineNumber
            )
            lastLineNumber = lineNumber
            result
        }
        var lastFileName: String? = null
        return mapped.map {
            when (lastFileName) {
                null -> {
                    lastFileName = it.fileName
                    it
                }

                it.fileName -> it

                else -> {
                    lastFileName = it.fileName
                    it.copy(showFileNameHeader = true)
                }
            }
        }
    }

    /**
     * @return the range for AnnotatedString.addStyle()
     */
    fun extractMatchString(lineContent: String, columnNumber: Int, matchByteLength: Int): Pair<Int, Int> {
        var totalBytes = 0
        var i = 0
        for (it in lineContent.codePoints().toList()) {
            // subtract 1 since ugrep's column number starts at 1
            if (i == columnNumber - 1) {
                break
            }
            totalBytes += Character.toString(it).toByteArray(Charsets.UTF_8).size
            i++
        }
        return lineContent.getStringLengthRangesFromByteRange(totalBytes, matchByteLength + totalBytes)
    }

    data class PreviewLine(
        val text: String,
        val lineNumber: Int,
        val isCurrentLine: Boolean,
        val matchStart: Int?,
        val matchEnd: Int?
    )

    fun buildPreviewLines(content: String, selectedResult: SearchResult): List<PreviewLine> {
        val lines = content.lines()
        val startLine = maxOf(0, selectedResult.lineNumber - LINES_BEFORE_AND_AFTER)
        val endLine = minOf(lines.size, selectedResult.lineNumber + LINES_BEFORE_AND_AFTER)

        return (startLine until endLine).map { i ->
            val line = lines[i]
            PreviewLine(
                text = line,
                lineNumber = i,
                isCurrentLine = i == selectedResult.lineNumber,
                matchStart = if (i == selectedResult.lineNumber) selectedResult.matchAnnotationStringStart else null,
                matchEnd = if (i == selectedResult.lineNumber) selectedResult.matchAnnotationStringEnd else null
            )
        }
    }

}
