package dima.dialogs.findText

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import GlobalStyling
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.utils.abbreviatePath
import java.io.File

@Composable
internal fun RowScope.RightSideLinesPreview(
    textPreviewScrollState: ScrollState,
    onPreviewTextWidth: (Dp) -> Unit,
    selectedResult: FindText.SearchResult?,
    params: Params,
    filteredResults: List<FindText.SearchResult>,
) {
    Box(
        modifier = Modifier.Companion
            .weight(0.5f)
    ) {
        Column(
            modifier = Modifier
                .verticalScroll(textPreviewScrollState)
                .onSizeChanged {
                    with(Globals.density) {
                        onPreviewTextWidth(it.width.toDp())
                    }
                }
                .padding(end = 12.dp)
        ) {
            if (selectedResult != null) {
                SelectionContainer {
                    Text(
                        text = File(params.path, selectedResult.fileName).abbreviatePath(),
                        color = GlobalStyling.getTextColor(),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier
                            .padding(bottom = 16.dp, end = 8.dp)
                    )
                }
                val content = FindText.getFileContent(File(params.path, selectedResult.fileName))
                val previewLines = FindText.buildPreviewLines(content, selectedResult)
                SelectionContainer {
                    Column {
                        previewLines.forEach { line ->
                            TextPreviewLine(line, filteredResults)
                        }
                    }
                }
            }
        }
        VerticalScrollbar(
            modifier = Modifier
                .align(Alignment.TopEnd),
            adapter = rememberScrollbarAdapter(scrollState = textPreviewScrollState)
        )
    }
}

