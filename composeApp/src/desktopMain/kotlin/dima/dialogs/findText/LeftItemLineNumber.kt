package dima.dialogs.findText

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors

@Composable
internal fun LeftItemLineNumber(result: FindText.SearchResult) {
    Text(
        buildAnnotatedString {
            append(
                AnnotatedString(
                    result.lineNumber.toString() + "   ",
                    spanStyle = SpanStyle(
                        fontWeight = if (result.isSameLineNumberAsPrevious) FontWeight.Normal else FontWeight.SemiBold,
                        color = if (result.isSameLineNumberAsPrevious) TailwindCssColors.gray400 else FindTextDialogStyling.headerColor()
                    )
                )
            )
        },
        modifier = Modifier
            .padding(start = 12.dp, top = 4.dp)
            // set large min width, so the text is  aligned in most cases except on very large line numbers
            .widthIn(min = 60.dp)
    )
}
