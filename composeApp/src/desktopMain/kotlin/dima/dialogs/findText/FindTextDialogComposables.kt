package dima.dialogs.findText

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import GlobalStyling
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.sp
import dima.text.textMarkedLightSpanStyle
import dima.text.textMarkedSpanStyleSecondary
import kotlin.math.max
import kotlin.math.min

@Composable
internal fun TextPreviewLine(line: FindText.PreviewLine, allResults: List<FindText.SearchResult>) {
    Text(
        text = buildAnnotatedString {
            append(line.text)
            if (line.isCurrentLine) {
                allResults
                    .filter { result ->
                        result.lineNumber == line.lineNumber &&
                                result.matchAnnotationStringStart != line.matchStart &&
                                result.matchAnnotationStringEnd != line.matchEnd
                    }
                    .forEach { result ->
                        val originalStart = result.matchAnnotationStringStart
                        val originalEnd = result.matchAnnotationStringEnd
                        val coercedStart = max(0, min(originalStart, line.text.length))
                        val coercedEnd = max(coercedStart, min(originalEnd, line.text.length))
                        if (coercedStart < coercedEnd) { // Only add style if the range is valid
                            addStyle(
                                textMarkedSpanStyleSecondary,
                                coercedStart,
                                coercedEnd
                            )
                        }
                    }
                if (line.matchStart != null &&
                    line.matchEnd != null
                ) {
                    val originalStart = line.matchStart
                    val originalEnd = line.matchEnd
                    val coercedStart = max(0, min(originalStart, line.text.length))
                    val coercedEnd = max(coercedStart, min(originalEnd, line.text.length))
                    if (coercedStart < coercedEnd) { // Only add style if the range is valid
                        addStyle(
                            textMarkedLightSpanStyle,
                            coercedStart,
                            coercedEnd
                        )
                    }
                }
            }
        },
        color = GlobalStyling.getTextColor(),
        fontSize = 14.sp,
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (line.isCurrentLine) {
                    Modifier
                        .background(FindTextDialogStyling.currentLineBackgroundColor())
                } else {
                    Modifier
                }
            )
    )
}
