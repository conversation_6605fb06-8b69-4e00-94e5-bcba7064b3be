package dima.dialogs.findText

import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import GlobalStyling

@Composable
internal fun Header(fileName: String, modifier: Modifier) {
    SelectionContainer {
        Text(
            fileName,
            color = GlobalStyling.getTextColor(),
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = modifier
        )
    }
}
