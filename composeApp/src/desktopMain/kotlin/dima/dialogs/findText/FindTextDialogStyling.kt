package dima.dialogs.findText

import GlobalStyling
import androidx.compose.ui.graphics.Color

internal object FindTextDialogStyling {

    fun headerColor(): Color {
        return GlobalStyling.getBoldTextColor()
    }
    fun currentLineBackgroundColor(): Color {
        // subtle background for selected/current line that works for both themes
        return GlobalStyling.getSelectedBackgroundColor().copy(alpha = if (dima.globalState.GlobalState.isDarkMode) 0.35f else 0.6f)
    }

}