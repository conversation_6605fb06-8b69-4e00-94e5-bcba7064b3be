package dima.dialogs.findText

import Globals
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.sp
import dialogs
import dima.dialogs.findText.FindText.LINES_BEFORE_AND_AFTER
import dima.dialogs.findText.FindText.processExitCode
import dima.dialogs.findText.FindText.processStderr
import dima.dialogs.findText.FindText.processStdout
import dima.dialogs.findText.FindText.search
import dima.utils.ScrollVelocity
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

@Composable
internal fun FindTextDialogEffects(
    searchFocusRequester: FocusRequester,
    textPreviewScrollVelocity: ScrollVelocity,
    filteredResults: List<FindText.SearchResult>,
    fileFilter: String,
    selectedResult: FindText.SearchResult?,
    id: Long,
    resultsScrollState: LazyListState,
    previewTextWidth: Dp,
    params: Params,
    textPreviewScrollState: ScrollState,
    onFilteredResultsChange: (List<FindText.SearchResult>) -> Unit,
    onSelectedResultChange: (FindText.SearchResult?) -> Unit
) {
    LaunchedEffect(Unit) {
        searchFocusRequester.requestFocus()
        textPreviewScrollVelocity.loopForeverAndTick()
    }

    val scope = rememberCoroutineScope()
    var debounceJob: Job? by remember { mutableStateOf(null) }
    LaunchedEffect(search) {
        debounceJob?.cancel()
        if (search.trim() == "") {
            processStdout = null
            processStderr = null
            processExitCode = null
            FindText.results = emptyList()
            onFilteredResultsChange(emptyList())
            return@LaunchedEffect
        }
        debounceJob = scope.launch {
            delay(250)
            if (search.trim() != "") {
                FindText.startThreadWithSearch()
            }
        }
    }

    LaunchedEffect(FindText.results, fileFilter) {
        val newFilteredResults = if (fileFilter.isEmpty()) {
            FindText.results
        } else {
            FindText.results.filter { result ->
                result.fileName.contains(fileFilter, ignoreCase = true)
            }
        }
        onFilteredResultsChange(newFilteredResults)
        if (newFilteredResults.isEmpty()) {
            onSelectedResultChange(null)
            return@LaunchedEffect
        }
        if (selectedResult == null) {
            onSelectedResultChange(newFilteredResults.first())
            return@LaunchedEffect
        }
        val index = newFilteredResults.indexOf(selectedResult)
        if (index == -1) {
            onSelectedResultChange(newFilteredResults.first())
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            searchFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(filteredResults, selectedResult) {
        if (selectedResult == null) {
            return@LaunchedEffect
        }
        val index = filteredResults.indexOf(selectedResult)
        if (index == -1) {
            return@LaunchedEffect
        }
        // use launch to not lag the scroll below
        launch {
            resultsScrollState.animateScrollToItem(index, scrollOffset = -300)
        }
        val measurer = TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
        val constraints = Constraints(maxWidth = with(Globals.density) {
            previewTextWidth.roundToPx()
        })
        var startLine = maxOf(0, selectedResult.lineNumber - LINES_BEFORE_AND_AFTER)
        var lineContents = ""
        val lines = FindText.getFileContent(File(params.path, selectedResult.fileName)).lines()
        var offset = 0
        if (startLine == selectedResult.lineNumber) {
            lineContents += lines[startLine]
        } else {
            do {
                offset += lines[startLine].length + 1
                lineContents += lines[startLine] + "\n"
                startLine += 1
            } while (startLine != selectedResult.lineNumber)
        }
        val multiParagraph = measurer.measure(
            lineContents,
            style = TextStyle(
                fontSize = 14.sp
            ),
            constraints = constraints
        ).multiParagraph
        val rect = try {
            multiParagraph.getBoundingBox(offset)
        } catch (_: IllegalArgumentException) {
            multiParagraph.getBoundingBox(offset - 1)
        } catch (e: ArrayIndexOutOfBoundsException) {
            // Rethrow or handle, for now, rethrow to see the stack trace if it's still happening here
            throw e
        }
        textPreviewScrollState.scrollTo(rect.top.toInt() - 500)
    }

    DisposableEffect(Unit) {
        onDispose {
            search = ""
            FindText.abortThread()
        }
    }
}
