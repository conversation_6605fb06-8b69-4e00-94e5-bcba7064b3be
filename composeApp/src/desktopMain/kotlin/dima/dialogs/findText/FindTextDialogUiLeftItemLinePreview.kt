package dima.dialogs.findText

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import GlobalStyling
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import dima.text.textMarkedLightSpanStyle

@Composable
internal fun FindTextDialogUiLeftItemLinePreview(result: FindText.SearchResult) {
    Text(
        text = buildAnnotatedString {
            val offset = 120
            val start = maxOf(0, result.matchAnnotationStringStart - offset)
            val isStartTruncated =
                result.matchAnnotationStringStart - offset >= 0
            val end = minOf(
                result.lineContent.length,
                result.matchAnnotationStringEnd + offset
            )
            val isEndTruncated =
                result.matchAnnotationStringEnd + offset < result.lineContent.length
            val line = buildString {
                if (isStartTruncated) {
                    append("...")
                }
                append(result.lineContent.substring(start, end))
                if (isEndTruncated) {
                    append("...")
                }
            }
            append(line)
            var adjustedStart = result.matchAnnotationStringStart - start
            var adjustedEnd = result.matchAnnotationStringEnd - start
            if (isStartTruncated) {
                adjustedStart += 3
                adjustedEnd += 3
            }
            addStyle(
                textMarkedLightSpanStyle,
                adjustedStart,
                adjustedEnd
            )
        },
        color = GlobalStyling.getTextColor(),
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = 12.dp, top = 4.dp, start = 12.dp, bottom = 4.dp)
    )
}
