package dima.dialogs.findText

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import GlobalStyling
import dima.dialogs.findText.FindText.processExitCode
import dima.dialogs.findText.FindText.processStderr
import dima.utils.clickableWithoutBackgroundRipple

@Composable
@OptIn(ExperimentalFoundationApi::class)
internal fun FindTextDialogUiLeftSide(
    resultsScrollState: LazyListState,
    filteredResults: List<FindText.SearchResult>,
    selectedResult: FindText.SearchResult?,
    onSelectedResultChange: (FindText.SearchResult?) -> Unit
) {
    Column {
        if (processExitCode == 1) {
            Text(
                "Text not found",
                textAlign = TextAlign.Center,
                color = GlobalStyling.getRedTextColor(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 30.dp)
            )
        } else if (processExitCode != null && processExitCode != 0 && processExitCode != 1) {
            Text(
                "Exit code: $processExitCode",
                color = GlobalStyling.getRedTextColor(),
                modifier = Modifier
                    .padding(bottom = 20.dp)
            )
        }
        if (processStderr != null) {
            Text(
                processStderr!!,
                color = GlobalStyling.getRedTextColor()
            )
        }
        if (processExitCode == 0) {
            Box(
                modifier = Modifier
                    .weight(1f)
            ) {
                LazyColumn(
                    state = resultsScrollState,
                    modifier = Modifier
                        // add padding for scroll bar
                        .padding(end = 12.dp)
                ) {
                    // this check is vital for Compose 1.8.0, otherwise it will crash - it worked before on 1.7.3 without crashing
                    if (filteredResults.isNotEmpty()) {
                        stickyHeader {
                            val selected = filteredResults.find {
                                it == selectedResult
                            }
                            if (selected != null) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(GlobalStyling.getWindowBackgroundColor())
                                ) {
                                    Header(
                                        selected.fileName,
                                        Modifier
                                            .padding(start = 12.dp, bottom = 8.dp)
                                    )
                                }
                            }
                        }
                    }
                    itemsIndexed(filteredResults, key = { index, _ -> index }) { _, result ->
                        val isSelected = (result == selectedResult)
                        if (result.showFileNameHeader) {
                            Header(
                                result.fileName, Modifier
                                    .padding(start = 12.dp, bottom = 8.dp, top = 24.dp)
                            )
                        }
                        Box(
                            modifier = Modifier
                                .then(
                                    if (isSelected) {
                                        Modifier.background(GlobalStyling.getSelectedBackgroundColor())
                                    } else {
                                        Modifier
                                    }
                                )
                                .clickableWithoutBackgroundRipple {
                                    onSelectedResultChange(result)
                                }
                        ) {
                            Row {
                                LeftItemLineNumber(result)
                                FindTextDialogUiLeftItemLinePreview(result)
                            }
                        }
                    }
                }
                Box(modifier = Modifier.matchParentSize()) {
                    VerticalScrollbar(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(bottom = 12.dp),
                        adapter = rememberScrollbarAdapter(scrollState = resultsScrollState)
                    )
                }
            }
        }
    }
}
