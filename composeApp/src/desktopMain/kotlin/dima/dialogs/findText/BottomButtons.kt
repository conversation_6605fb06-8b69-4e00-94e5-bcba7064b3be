package dima.dialogs.findText

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.dialogs.DialogBottomKey
import dima.dialogs.DialogBottomKeyInfo

@Composable
internal fun BottomButtons(fileCount: Int, fileFilter: String) {
    DialogBottomKeyInfo(
        buildList {
            if (fileCount >= 2) {
                add(Dialog<PERSON>ott<PERSON><PERSON>ey("t", "jump to file", isCmd = true))
            }
            add(DialogBottom<PERSON>ey("f", "filter by file", isCmd = true))
            when (FindText.matchEngine) {
                MatchEngine.AnyWordInLine -> add(Dialog<PERSON>ottom<PERSON>ey("m", "match literally", isCmd = true))
                MatchEngine.Literal -> add(Dialog<PERSON><PERSON><PERSON><PERSON>ey("m", "match fuzzy on space", isCmd = true))
            }
            add(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("l", "open help", isCmd = true))
        },
        leftText = if (fileFilter.isEmpty()) null else "File filter case-insensitive: $fileFilter",
        modifier = Modifier
            .padding(12.dp)
    )
}
