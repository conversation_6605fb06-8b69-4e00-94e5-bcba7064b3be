package dima.dialogs.findText

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.Text

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.findText.FindText.processExitCode
import dima.dialogs.findText.FindText.search
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.os.Emacs
import dima.utils.RecentHistory
import dima.utils.ScrollVelocity
import dima.utils.abbreviatePath
import dima.dialogs.help.HelpDialogSearchBar
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun FindTextViaUgrepDialog(id: Long, data: Any) {
    val params = data as Params
    remember {
        FindText.path = File(params.path)
    }
    var selectedResult by remember { mutableStateOf<FindText.SearchResult?>(null) }
    val resultsScrollState = rememberLazyListState()
    val textPreviewScrollState = rememberScrollState()
    val textPreviewScrollVelocity = remember { ScrollVelocity(textPreviewScrollState) }
    val searchFocusRequester = remember { FocusRequester() }
    var filteredResults: List<FindText.SearchResult> by remember { mutableStateOf(FindText.results) }
    var previewTextWidth by remember { mutableStateOf(800.dp) }
    var searchState by remember {
        mutableStateOf(
            TextFieldValue(
                text = search,
                selection = when {
                    search.isEmpty() -> TextRange.Zero
                    else -> TextRange(search.length, search.length)
                }
            )
        )
    }

    var fileFilter by remember { mutableStateOf("") }
    DialogOverlay(
        dialogId = id,
        widthFraction = null,
        dialogPadding = PaddingValues(top = 12.dp),
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.type == KeyEventType.KeyUp) {
                textPreviewScrollVelocity.onKeyReleased()
                return@onPreviewKeyEvent false
            }
            if (it.type != KeyEventType.KeyDown) {
                return@onPreviewKeyEvent false
            }
            if (it.isCtrlPressed) {
                when (it.key) {
                    Key.C -> {
                        textPreviewScrollVelocity.onScrollUpKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.T -> {
                        textPreviewScrollVelocity.onScrollDownKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.V -> {
                        textPreviewScrollVelocity.onScrollUpMoreKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.M -> {
                        textPreviewScrollVelocity.onScrollDownMoreKeyPressed()
                        return@onPreviewKeyEvent true
                    }
                }
            }
            if (it.key == Key.F && it.isMetaPressed) {
                openTextInputDialog(
                    "Filter results",
                    minLines = 1,
                    initialText = fileFilter
                ) { text ->
                    fileFilter = text
                    TextInputDialogConfirmAction.Close
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Escape) {
                FindText.abortThread()
                closeDialogWithId(id)
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.T && it.isMetaPressed) {
                val files = filteredResults.distinctBy { result ->
                    result.fileName
                }
                if (files.size <= 1) {
                    return@onPreviewKeyEvent true
                }
                val fileNames = files.map { file ->
                    val fileCount = filteredResults.count { r ->
                        r.fileName == file.fileName
                    }
                    if (fileCount == 1) {
                        file.fileName + " (1 match)"
                    } else {
                        file.fileName + " ($fileCount matches)"
                    }
                }
                openCompletionDialog("Select file", fileNames) { result ->
                    selectedResult = files[result.index!!]
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionDown) {
                if (selectedResult == null && filteredResults.isNotEmpty()) {
                    selectedResult = filteredResults.first()
                    return@onPreviewKeyEvent true
                }
                val index = filteredResults.indexOf(selectedResult)
                when {
                    index == -1 -> selectedResult = filteredResults.firstOrNull()
                    index != filteredResults.size - 1 -> selectedResult = filteredResults[index + 1]
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionUp) {
                if (selectedResult == null && filteredResults.isNotEmpty()) {
                    selectedResult = filteredResults.first()
                    return@onPreviewKeyEvent true
                }
                val index = filteredResults.indexOf(selectedResult)
                when {
                    index == -1 -> selectedResult = filteredResults.firstOrNull()
                    index != 0 -> selectedResult = filteredResults[index - 1]
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Enter) {
                if (selectedResult == null) {
                    return@onPreviewKeyEvent true
                }
                RecentHistory.rememberText(search, ignoreShortText = true, trimText = true)
                val result: FindText.SearchResult = selectedResult!!
                CoroutineScope(Dispatchers.IO).launch {
                    val file = File(params.path, result.fileName)
                    val column = Emacs.calculateColumnFromByteStart(file, result.byteStart)
                    Emacs.openFileContentAndFocus(file, result.lineNumber + 1, column)
                }
                return@onPreviewKeyEvent true
            }
            if (it.isMetaPressed && it.key == Key.M) {
                FindText.matchEngine = when (FindText.matchEngine) {
                    MatchEngine.AnyWordInLine -> MatchEngine.Literal
                    MatchEngine.Literal -> MatchEngine.AnyWordInLine
                }
                if (search.trim() != "") {
                    FindText.abortThread()
                    FindText.startThreadWithSearch()
                }
            }
            if (it.key == Key.L && it.isMetaPressed) {
                openMiniHelpDialog(
                    "Find Text Dialog",
                    keys = buildList {
                        addAll(
                            listOf(
                                MiniHelpDialogKey(listOf(Key.DirectionDown), "Select next file"),
                                MiniHelpDialogKey(listOf(Key.DirectionUp), "Select previous file"),
                                MiniHelpDialogKey(listOf(Key.C), "Scroll preview up", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.T), "Scroll preview down", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.V), "Scroll preview up more", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.M), "Scroll preview down more", isCtrl = true),
                                MiniHelpDialogKey(listOf(Key.T), "Jump to file", isCmd = true),
                                MiniHelpDialogKey(listOf(Key.F), "Filter by file", isCmd = true),
                                MiniHelpDialogKey(
                                    listOf(Key.L), when (FindText.matchEngine) {
                                        MatchEngine.AnyWordInLine -> "Match literally"
                                        MatchEngine.Literal -> "Match fuzzy on space"
                                    }, isCmd = true
                                ),
                                MiniHelpDialogKey(listOf(Key.Escape), "Close dialog"),
                                MiniHelpDialogKey(listOf(Key.Enter), "Open file in editor"),
                            )
                        )
                    }
                )
                return@onPreviewKeyEvent true
            }
            return@onPreviewKeyEvent false
        }) {
        Column {
            val fileCount = filteredResults.distinctBy {
                it.fileName
            }.size
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f, fill = false)
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier
                        .fillMaxSize()
                        .weight(0.5f)
                ) {
                    val resultsSize = filteredResults.size
                    val fileCountText = when (fileCount) {
                        0 -> ""
                        1 -> "$fileCount file"
                        else -> "$fileCount files"
                    }
                    val suffix = if (processExitCode == 1 || resultsSize == 0) {
                        ""
                    } else {
                        if (resultsSize == 1) {
                            " ($fileCountText, $resultsSize match)"
                        } else {
                            " ($fileCountText, $resultsSize matches)"
                        }
                    }
                    HelpDialogSearchBar(
                        queryState = searchState,
                        onQueryChange = { searchState = it; search = it.text },
                        focusRequester = searchFocusRequester
                    )
                    FindTextDialogUiLeftSide(resultsScrollState, filteredResults, selectedResult) {
                        selectedResult = it
                    }
                }
                RightSideLinesPreview(
                    textPreviewScrollState = textPreviewScrollState,
                    onPreviewTextWidth = { previewTextWidth = it },
                    selectedResult = selectedResult,
                    params = params,
                    filteredResults = filteredResults
                )
            }
            BottomButtons(fileCount, fileFilter)
        }
    }

    FindTextDialogEffects(
        searchFocusRequester = searchFocusRequester,
        textPreviewScrollVelocity = textPreviewScrollVelocity,
        filteredResults = filteredResults,
        fileFilter = fileFilter,
        selectedResult = selectedResult,
        id = id,
        resultsScrollState = resultsScrollState,
        previewTextWidth = previewTextWidth,
        params = params,
        textPreviewScrollState = textPreviewScrollState,
        onFilteredResultsChange = { filteredResults = it },
        onSelectedResultChange = { selectedResult = it }
    )
}