package dima.dialogs.textInput

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dialogs
import dima.color.TailwindCssColors
import dima.dialogs.*
import dima.utils.RecentHistory
import dima.utils.StringResult
import dima.utils.bottomBorder

enum class TextInputDialogConfirmAction {
    KeepOpen,
    Close
}

/**
 * @param validator [StringResult] is used to also show the success string, when [showValidationSuccess] is true
 * @param showValidationSuccess see [validator]
 */
fun openTextInputDialog(
    title: String,
    subTitle: String? = null,
    initialText: String? = null,
    minLines: Int = 3,
    /**
     * 1.0 for full width.
     */
    dialogWidth: Float? = null,
    validator: ((String) -> StringResult)? = null,
    showValidationSuccess: Boolean = false,
    onDismiss: (() -> Unit)? = null,
    onConfirm: (String) -> TextInputDialogConfirmAction,
) {
    val params = Params(
        title = title,
        subTitle = subTitle,
        initialText = initialText,
        minLines = minLines,
        dialogWidth = dialogWidth,
        validator = validator,
        showValidationSuccess = showValidationSuccess,
        onDismiss = onDismiss,
        onConfirm = onConfirm
    )
    openDialog(DialogIdentifier.TextCompletion, params) { id, data ->
        TextInputDialog(id, data!!)
    }
}

private data class Params(
    val title: String,
    val subTitle: String? = null,
    val initialText: String? = null,
    val minLines: Int = 3,
    val dialogWidth: Float? = null,
    val validator: ((String) -> StringResult)? = null,
    val showValidationSuccess: Boolean = false,
    val onDismiss: (() -> Unit)? = null,
    val onConfirm: (String) -> TextInputDialogConfirmAction,
)

@Composable
fun TextInputDialog(id: Long, data: Any) {
    val params = data as Params
    val input by mutableStateOf(params.initialText ?: "")
    var inputState by remember {
        mutableStateOf(
            TextFieldValue(
                text = input,
                selection = when {
                    input.isEmpty() -> TextRange.Zero
                    else -> TextRange(input.length, input.length)
                }
            )
        )
    }
    val searchFocusRequester = remember { FocusRequester() }
    var validationErrorMessage by remember { mutableStateOf<String?>(null) }
    var validationSuccessMessage by remember { mutableStateOf<String?>(null) }

    val borderColor = if (validationErrorMessage == null) {
        TailwindCssColors.gray100 // Original background, effectively no border or border matches background
    } else {
        TailwindCssColors.red500
    }

    fun updateValidation() {
        if (params.validator != null) {
            val validationResult = params.validator.invoke(inputState.text)
            validationErrorMessage = when (validationResult) {
                is StringResult.Error -> validationResult.error
                is StringResult.Success -> {
                    if (params.showValidationSuccess) {
                        validationSuccessMessage = validationResult.value
                    }
                    null
                }
            }
        }
    }

    DialogOverlay(
        dialogId = id,
        widthFraction = params.dialogWidth,
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.key == Key.Escape && it.type == KeyEventType.KeyDown) {
                RecentHistory.rememberText(inputState.text, ignoreShortText = true, trimText = true)
                params.onDismiss?.invoke()
                closeDialogWithId(id)
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Enter) {
                if (it.type == KeyEventType.KeyUp) {
                    val currentText = inputState.text
                    updateValidation()
                    RecentHistory.rememberText(currentText, ignoreShortText = true, trimText = true)
                    val result = params.onConfirm.invoke(currentText)
                    when (result) {
                        TextInputDialogConfirmAction.KeepOpen -> {}
                        TextInputDialogConfirmAction.Close -> {
                            closeDialogWithId(id)
                        }
                    }
                }
                // avoid inserting newline before confirming the dialog
                return@onPreviewKeyEvent true
            }
            return@onPreviewKeyEvent false
        }) {
        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            val color = GlobalStyling.getTextColor()
            Text(
                params.title,
                color = color,
                fontWeight = FontWeight.Bold
            )
            if (params.subTitle != null) {
                Text(
                    params.subTitle,
                    color = color,
                )
            }
            Column {
                Row(horizontalArrangement = Arrangement.Center) {
                    val modifier = Modifier
                        .focusRequester(searchFocusRequester)
                        .shadow(1.dp, RoundedCornerShape(5.dp))
                        .background(TailwindCssColors.gray100, RoundedCornerShape(5.dp))
                        .then(
                            if (params.dialogWidth == null) {
                                Modifier.widthIn(min = 400.dp)
                            } else {
                                Modifier.fillMaxWidth()
                            }
                        )
                        .then(
                            if (validationErrorMessage == null) {
                                Modifier
                            } else {
                                Modifier.bottomBorder(2.dp, borderColor)
                            }
                        )
                        .padding(8.dp)

                    BasicTextField(
                        value = inputState,
                        onValueChange = {
                            inputState = it
                            updateValidation()
                        },
                        minLines = params.minLines,
                        modifier = modifier,
                        cursorBrush = SolidColor(TailwindCssColors.gray800)
                    )
                }
                if (validationErrorMessage != null) {
                    Text(
                        text = validationErrorMessage!!,
                        color = GlobalStyling.getRedTextColor(),
                        fontSize = 13.sp,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                } else if (validationSuccessMessage != null) {
                    Text(
                        text = validationSuccessMessage!!,
                        color = GlobalStyling.getGreenColor(),
                        fontSize = 13.sp,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        searchFocusRequester.requestFocus()
        updateValidation()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            searchFocusRequester.requestFocus()
        }
    }
}