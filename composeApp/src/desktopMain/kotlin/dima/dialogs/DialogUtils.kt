package dima.dialogs

import GlobalStyling
import Globals
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.KeyEvent
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dialogs
import dima.globalState.GlobalState
import dima.utils.clickableWithoutBackgroundRipple
import handleLostFocus

/**
 * Storing the [id] and [data] is a bit bulky, but it is the only way I found to allow opening the same dialog multiple times.
 *
 * I was using this before in the dialogs, but it got cached and then all dialogs had the same data:
 *      val params = remember { params!! }
 */
data class DialogData(
    /**
     * The first parameter is [id] and the second one [data].
     */
    val composable: @Composable (Long, Any?) -> Unit,
    /**
     * Can be used to check what kind of dialog is opened.
     */
    val identifier: DialogIdentifier,
    val data: Any? = null,
    /**
     * The unique ID of a dialog for closing dialogs reliably.
     * Never pass anything else than the provided default value!
     */
    val id: Long = DialogUtils.getCounter(),
)

private object DialogUtils {

    private var dialogCounter = 0L

    fun getCounter(): Long {
        dialogCounter++
        return dialogCounter
    }
}

fun openDialog(identifier: DialogIdentifier, data: Any? = null, composable: @Composable (Long, Any?) -> Unit) {
    dialogs = dialogs + DialogData(
        composable = composable,
        identifier = identifier,
        data = data
    )
}

fun closeDialogWithId(id: Long) {
    dialogs = dialogs.filter {
        it.id != id
    }.toMutableList()
}

/**
 * @param widthFraction if null, then the dialog will take the least amount of width content space possible
 * @param dialogPadding if null, no inner padding will be applied
 * @param alpha the alpha of the dialog, can be used for sizing hacks to not have a frame with incorrect size
 * @param onInitialSize the width and height of the dialog after measuring it the first time. This is only called once!
 * @param showHelpIcon if true, shows a help icon in the bottom right corner that opens the help dialog when clicked
 */
@Composable
internal fun DialogOverlay(
    dialogId: Long,
    onPreviewKeyEvent: (KeyEvent) -> Boolean,
    widthFraction: Float? = 0.7f,
    onInitialSize: ((Dp, Dp) -> Unit)? = null,
    alpha: Float = 1f,
    dialogPadding: PaddingValues? = PaddingValues(12.dp),
    showHelpIcon: Boolean = false,
    content: @Composable BoxScope.() -> Unit
) {
    val focusRequester = remember { FocusRequester() }
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .alpha(alpha)
            .fillMaxSize()
            .focusable()
            .focusRequester(focusRequester)
            .onPreviewKeyEvent(onPreviewKeyEvent)
            .handleLostFocus()
    ) {
        // This Box is required because the click listener needs to be on the backdrop only. If set on the parent Box,
        // clicking the dialog content also closes the dialog.
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(GlobalStyling.Dialog.backdropColor)
                .clickableWithoutBackgroundRipple {
                    closeDialogWithId(dialogId)
                }
        )
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .padding(30.dp)
        ) {
            Box {
                DialogContent(
                    widthFraction = widthFraction,
                    dialogPadding = dialogPadding,
                    onInitialSize = onInitialSize,
                    content = content
                )
                if (showHelpIcon) {
                    DialogHelpIcon()
                }
            }
        }
    }
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}

@Composable
private fun DialogContent(
    widthFraction: Float?,
    onInitialSize: ((Dp, Dp) -> Unit)? = null,
    dialogPadding: PaddingValues? = PaddingValues(12.dp),
    content: @Composable BoxScope.() -> Unit
) {
    var width by remember { mutableStateOf(Dp.Unspecified) }
    var height by remember { mutableStateOf(Dp.Unspecified) }

    var modifier = Modifier
        .clip(GlobalStyling.Dialog.smallRoundedCorners)
        .background(
            GlobalStyling.getWindowBackgroundColor(),
            shape = GlobalStyling.Dialog.smallRoundedCorners
        )
        .then(
            if (GlobalState.isDarkMode) {
                Modifier.border(
                    1.dp,
                    GlobalStyling.Dialog.darkThemeBorderColor,
                    shape = GlobalStyling.Dialog.smallRoundedCorners
                )
            } else {
                Modifier
            }
        )
        .pointerHoverIcon(PointerIcon.Default)
        .onSizeChanged { size ->
            if (width == Dp.Unspecified) {
                with(Globals.density) {
                    width = size.width.toDp()
                    height = size.height.toDp()
                    onInitialSize?.let { size -> size(width, height) }
                }
            }
        }
    if (widthFraction != null) {
        modifier = modifier.fillMaxWidth(widthFraction)
    }
    if (dialogPadding != null) {
        modifier = modifier.padding(dialogPadding)
    }
    Box(modifier = modifier) {
        content()
    }
}