package dima.dialogs.archiveScanned

import GlobalEvent
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.github.burnett01.expression.expression
import dialogs
import dima.apps.AppType
import dima.apps.dired.Dired
import dima.apps.dired.FileType
import dima.apps.dired.getFileType
import dima.apps.dired.preview.DiredPreview
import dima.apps.notifications.showErrorNotification
import dima.color.TailwindCssColors
import dima.dateTime.DateTimeUtils
import dima.dialogs.*
import dima.git.Git
import dima.globalState.DiredImageState
import dima.globalState.GlobalState
import dima.settings
import dima.utils.*
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import kotlin.time.Duration.Companion.milliseconds

fun openArchiveScannedDialog(file: File) {
    val params = Params(file)
    openDialog(DialogIdentifier.ArchiveScanned, params) { id, data ->
        ArchiveScannedDialog(id, data)
    }
}

fun canOpenArchiveScannedDialog(file: File): Boolean {
    return when (file.getFileType().type) {
        FileType.Pdf, FileType.Image -> true
        else -> false
    }
}

private data class Params(
    val file: File,
)

private val dateRegex = expression({
    start()
    capture {
        digit { exact(4) }
        literal('-')
        digit { exact(2) }
        literal('-')
        digit { exact(2) }
    }
})!!.compile()

private const val imageZoomStep = 0.05f
private const val imageTranslationStep = 20f

@Composable
fun ArchiveScannedDialog(id: Long, data: Any?) {
    val params = data as Params
    val fileTypeWithColor = remember { params.file.getFileType() }
    var imageState by remember { mutableStateOf(DiredImageState()) }
    val heldCtrlKeys: MutableSet<Key> = remember { mutableSetOf() }
    var query by remember {
        val match = dateRegex.find(params.file.name)
        if (match == null) {
            val s = DateTimeUtils.nowAsIsoDate()
            mutableStateOf("$s ")
        } else {
            mutableStateOf(match.value + " ")
        }
    }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }
    val queryFocusRequester = remember { FocusRequester() }
    val scrollState = rememberScrollState()
    val directoryPreviewLazyListState = rememberLazyListState()
    val scrollVelocity = remember { ScrollVelocity(scrollState) }

    fun handleEnter() {
        val trimmed = query.trim()
        if (trimmed.isBlank()) {
            showErrorNotification("Text is not allowed to be empty")
            return
        }
        val areAnyDisallowed = FileSystem.disallowedChars.any { c ->
            trimmed.contains(c)
        }
        if (areAnyDisallowed) {
            showErrorNotification(
                "New file name is not allowed to have certain characters",
                FileSystem.disallowedChars.joinToString(" ")
            )
            return
        }
        if (!params.file.exists()) {
            showErrorNotification("The file does not exist anymore!", params.file.abbreviatePath())
            return
        }
        val newFile = File(settings.scannedDirectory!!, trimmed + "." + params.file.extension)
        if (newFile.exists()) {
            showErrorNotification(
                "File with the same name already exists",
                settings.scannedDirectory.abbreviatePath()
            )
            return
        }
        RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
        val success = params.file.renameTo(newFile)
        if (success) {
            closeDialogWithId(id)
            if (GlobalState.app == AppType.Dired) {
                Dired.hideNextReloadNotification = true
                globalEvent = GlobalEvent.Reload
            }
            CoroutineScope(Dispatchers.IO).launch {
                Git.commitAndPushAllChanges("scanned archive", settings.scannedDirectory)
            }
        } else {
            showErrorNotification("Error on moving file into scanned directory")
        }
    }

    DialogOverlay(
        dialogId = id,
        onPreviewKeyEvent = onPreviewKeyEvent@{
            // close dialog on KeyUp to not pass Escape to dialog/app below
            if (it.type == KeyEventType.KeyUp && it.key == Key.Escape) {
                RecentHistory.rememberText(query, ignoreShortText = true, trimText = true)
                closeDialogWithId(id)
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.Enter && it.type == KeyEventType.KeyUp) {
                handleEnter()
                return@onPreviewKeyEvent true
            }
            if (it.type == KeyEventType.KeyUp) {
                heldCtrlKeys.remove(it.key)
                scrollVelocity.onKeyReleased()
            }
            if (it.type == KeyEventType.KeyDown) {
                if (it.isCtrlPressed) {
                    heldCtrlKeys.add(it.key)
                    when (it.key) {
                        Key.R -> imageState = DiredImageState()
                    }
                } else {
                    if (it.key == Key.DirectionUp) {
                        scrollVelocity.onScrollUpKeyPressed()
                    } else if (it.key == Key.DirectionDown) {
                        scrollVelocity.onScrollDownKeyPressed()
                    }
                }
            }
            return@onPreviewKeyEvent false
        }
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                "Move   " + params.file.absolutePath.abbreviatePath() + "   into   " + settings.scannedDirectory!!.absolutePath.abbreviatePath(),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.fillMaxWidth()
            ) {
                TextField(
                    value = queryState,
                    onValueChange = {
                        queryState = it
                        query = it.text
                    },
                    label = { Text("Description without file extension with date prefix (2025-01-01 ...)") },
                    singleLine = true,
                    colors = TextFieldDefaults.textFieldColors(backgroundColor = TailwindCssColors.transparent),
                    leadingIcon = TextFieldSearchIcon(),
                    modifier = Modifier
                        .focusRequester(queryFocusRequester)
                )
            }
            Box(
                modifier = Modifier
                    .weight(1f)
            ) {
                DiredPreview(
                    Dired.Entry(
                        file = params.file,
                        fileTypeWithColor = fileTypeWithColor,
                        fileSizeBytes = params.file.length(),
                        lastModified = params.file.lastModified()
                    ),
                    drawSelectedBorder = false,
                    rightPreviewScrollState = scrollState,
                    imageState = imageState,
                    roundedCorners = false
                )
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .offset(x = 12.dp)
                ) {
                    VerticalScrollbar(
                        modifier = Modifier
                            .align(Alignment.CenterEnd),
                        style = scrollbarStyleThemed(),
                        adapter = rememberScrollbarAdapter(scrollState)
                    )
                }
            }
            DialogBottomKeyInfo(
                listOf(
                    DialogBottomKey("↑", "scroll up"),
                    DialogBottomKey("↓", "scroll down"),
                    DialogBottomKey("c", "shift up", isCtrl = true),
                    DialogBottomKey("t", "shift down", isCtrl = true),
                    DialogBottomKey("h", "shift left", isCtrl = true),
                    DialogBottomKey("n", "shift right", isCtrl = true),
                    DialogBottomKey("m", "zoom in", isCtrl = true),
                    DialogBottomKey("v", "zoom out", isCtrl = true),
                    DialogBottomKey("r", "reset PDF shift and zoom", isCtrl = true),
                )
            )
        }
    }

    LaunchedEffect(Unit) {
        queryFocusRequester.requestFocus()
        launch {
            val sleepDuration = 20.milliseconds.inWholeMilliseconds
            while (true) {
                heldCtrlKeys.forEach { key ->
                    when (key) {
                        Key.C -> imageState = imageState.copy(offsetY = imageState.offsetY + imageTranslationStep)
                        Key.N -> imageState = imageState.copy(offsetX = imageState.offsetX - imageTranslationStep)
                        Key.H -> imageState = imageState.copy(offsetX = imageState.offsetX + imageTranslationStep)
                        Key.T -> imageState = imageState.copy(offsetY = imageState.offsetY - imageTranslationStep)
                        Key.M -> imageState = imageState.copy(zoom = imageState.zoom + imageZoomStep)
                        Key.V -> {
                            if (imageState.zoom >= 0.2f) {
                                imageState = imageState.copy(zoom = imageState.zoom - imageZoomStep)
                            }
                        }
                    }
                }
                scrollVelocity.callTickInLoop()
                delay(sleepDuration)
            }
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            queryFocusRequester.requestFocus()
        }
    }
}