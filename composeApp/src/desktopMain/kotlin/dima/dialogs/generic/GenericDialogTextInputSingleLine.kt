package dima.dialogs.generic

import GlobalStyling
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.utils.SimpleResult
import dima.utils.bottomBorder
import isTextFieldFocused

@Composable
internal fun GenericDialogTextInputSingleLine(
    row: GenericDialogRow.TextInputSingleLine,
    drawSelectedBackground: Boolean,
    textFieldFocusRequesters: MutableMap<String, FocusRequester>,
    state: List<GenericDialogRow>,
    onStateChange: (List<GenericDialogRow>) -> Unit,
    onCommandModeChange: (Boolean) -> Unit,
    parentIsDisabled: Boolean,
    currentAlpha: Float
) {
    var errorMessage by remember { mutableStateOf<String?>(null) }
    val borderColor = if (errorMessage == null) {
        TailwindCssColors.gray500
    } else {
        TailwindCssColors.red500
    }

    GenericDialogBackgroundWrapper(drawSelectedBackground) {
        Column(modifier = Modifier.alpha(currentAlpha)) {
            Column(
                modifier = Modifier
                    .bottomBorder(2.dp, borderColor, dashed = row.isDisabled?.invoke(state) == true)
                    .padding(bottom = 6.dp)
            ) {
                TitleWithRequiredAsterisk(
                    title = row.title,
                    isRequired = row.isRequired,
                    textColor = GlobalStyling.getTextColor()
                )
                val focusRequester = remember { FocusRequester() }
                LaunchedEffect(row.id) {
                    textFieldFocusRequesters[row.id!!] = focusRequester
                }
                BasicTextField(
                    value = row.content,
                    onValueChange = { text ->
                        val newState = state.map { stateRow ->
                            if (stateRow.id == row.id) {
                                (stateRow as GenericDialogRow.TextInputSingleLine).copy(content = text)
                            } else {
                                stateRow
                            }
                        }
                        onStateChange(newState)
                        if (text.isEmpty()) {
                            errorMessage = null
                        } else {
                            val validationResult = row.validator?.invoke(text)
                            errorMessage = when (validationResult) {
                                is SimpleResult.Error -> validationResult.error
                                null, SimpleResult.Success -> null
                            }
                        }
                    },
                    textStyle = LocalTextStyle.current.copy(color = GlobalStyling.getTextColor()),
                    cursorBrush = GlobalStyling.TextField.cursorBrush,
                    singleLine = true,
                    enabled = !parentIsDisabled,
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .focusRequester(focusRequester)
                        .onFocusChanged { focus ->
                            if (!parentIsDisabled) {
                                onCommandModeChange(!focus.isFocused)
                                isTextFieldFocused = focus.isFocused
                            }
                        }
                        .widthIn(min = 300.dp)
                )
            }
            // Display error message if validation fails
            if (errorMessage != null) {
                Text(
                    text = errorMessage!!,
                    color = GlobalStyling.getRedTextColor(),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}