package dima.dialogs.generic

import dima.apps.notifications.showErrorNotification
import dima.utils.SimpleResult

/**
 * Shows required fields notification and only allows to confirm when all text field validators are fine.
 */
internal fun List<GenericDialogRow>.tryToConfirm(onConfirm: () -> Unit) {
    val missingFields = filter {
        val enabled = it.isDisabled?.invoke(this) != true
        it.isRequired && enabled && when (it) {
            // If a checkbox is required, it must be checked.
            // So, it's "missing" if it's required, enabled, and NOT checked.
            is GenericDialogRow.Checkbox -> !it.isChecked
            is GenericDialogRow.TextInputSingleLine -> it.content.isEmpty()
        }
    }

    if (missingFields.isEmpty()) {
        val textFields: List<GenericDialogRow.TextInputSingleLine> =
            this.filterIsInstance<GenericDialogRow.TextInputSingleLine>()

        val errors = textFields.mapNotNull { textField ->
            val enabled = textField.isDisabled?.invoke(this) != true
            if (textField.isRequired && enabled) {
                val result = textField.validator?.invoke(textField.content)
                if (result is SimpleResult.Error) {
                    textField.title to result.error
                } else {
                    null
                }
            } else {
                null
            }
        }

        if (errors.isEmpty()) {
            onConfirm()
            return
        }
        val errorMessages = errors.joinToString("\n") { (title, error) ->
            "$title: $error"
        }
        showErrorNotification(
            "Validation errors",
            errorMessages,
        )
    } else {
        val fieldNames = missingFields.joinToString(", ") { it.title }
        showErrorNotification(
            "Required fields missing",
            fieldNames,
        )
    }
}