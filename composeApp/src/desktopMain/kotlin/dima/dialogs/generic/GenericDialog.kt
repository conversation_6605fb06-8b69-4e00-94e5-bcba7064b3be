package dima.dialogs.generic

import GlobalStyling
import androidx.compose.foundation.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.*
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.clipboard.readClipboard
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dialogs
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.utils.isQuestionMark
import isTextFieldFocused

@Composable
fun GenericDialog(id: Long, data: Any?) {
    val params = data as Params
    val scrollState = rememberScrollState()
    var state by remember { mutableStateOf(params.layout) }
    var isCommandMode by remember { mutableStateOf(true) }
    var selectedIndex by remember { mutableStateOf(0) }
    val dialogFocusRequester = remember { FocusRequester() }
    val textFieldFocusRequesters = remember { mutableMapOf<String, FocusRequester>() }
    val checkboxFocusRequesters = remember { mutableMapOf<String, FocusRequester>() }

    fun getCurrent() = state[selectedIndex]

    DialogOverlay(
        dialogId = id,
        showHelpIcon = true,
        widthFraction = null,
        dialogPadding = null,
        onPreviewKeyEvent = {
            val currentElement = getCurrent()
            val isDisabled = currentElement.isDisabled?.invoke(state) ?: false

            if (it.type == KeyEventType.KeyDown && it.key == Key.V && it.isMetaPressed && isCommandMode) {
                if (isDisabled) return@DialogOverlay true // Ignore if disabled
                val clipboard = readClipboard()
                if (clipboard == null) {
                    return@DialogOverlay true
                }
                val current = getCurrent()
                when (current) {
                    is GenericDialogRow.Checkbox -> {}
                    is GenericDialogRow.TextInputSingleLine -> {
                        state = state.map { row ->
                            if (row.id == current.id && row is GenericDialogRow.TextInputSingleLine) {
                                row.copy(content = row.content + clipboard)
                            } else {
                                row
                            }
                        }
                        isTextFieldFocused = true
                        textFieldFocusRequesters[current.id]!!.requestFocus()
                        isCommandMode = false
                    }
                }
                return@DialogOverlay true
            }
            if (it.type == KeyEventType.KeyDown && it.isQuestionMark()) {
                openMiniHelpDialog(
                    "Generic Dialog", keys = listOf(
                        MiniHelpDialogKey(listOf(Key.C), "Move up"),
                        MiniHelpDialogKey(listOf(Key.T), "Move down"),
                        MiniHelpDialogKey(listOf(Key.N), "Confirm dialog in command mode"),
                        MiniHelpDialogKey(listOf(Key.V), "Paste into text field", isCmd = true),
                        MiniHelpDialogKey(listOf(Key.Escape), "Dismiss dialog"),
                        MiniHelpDialogKey(
                            listOf(Key.Enter),
                            "Toggle checkbox, enter text field content or confirm dialog in text field"
                        ),
                    )
                )
                return@DialogOverlay true
            }
            if (it.type == KeyEventType.KeyDown && it.key == Key.C) {
                if (isCommandMode) {
                    if (selectedIndex >= 1) {
                        selectedIndex--
                    }
                    return@DialogOverlay true
                }
            }
            if (it.type == KeyEventType.KeyDown && it.key == Key.T) {
                if (isCommandMode) {
                    if (selectedIndex < state.size - 1) {
                        selectedIndex++
                    }
                    return@DialogOverlay true
                }
            }
            if (it.type != KeyEventType.KeyUp) {
                return@DialogOverlay false
            }
            if (it.key == Key.N) {
                if (isDisabled) return@DialogOverlay true // Ignore if disabled
                val current = getCurrent()
                if (current is GenericDialogRow.Checkbox) {
                    state.tryToConfirm {
                        // this is to avoid the focus bug when it will be true when hitting Enter in TextField
                        isTextFieldFocused = false
                        params.onConfirm.invoke(state)
                        closeDialogWithId(id)
                    }
                }
            }
            if (it.key == Key.Enter) {
                if (isDisabled && currentElement !is GenericDialogRow.TextInputSingleLine) return@DialogOverlay true // Ignore checkbox toggle if disabled

                val current = getCurrent()
                when (current) {
                    is GenericDialogRow.Checkbox -> {
                        if (!isDisabled) {
                            state = state.map { row ->
                                if (row.id == current.id && row is GenericDialogRow.Checkbox) {
                                    row.copy(isChecked = !row.isChecked)
                                } else {
                                    row
                                }
                            }
                        }
                        return@DialogOverlay true
                    }

                    is GenericDialogRow.TextInputSingleLine -> {}
                }
                if (isCommandMode) {
                    if (!isDisabled) { // Only allow entering edit mode if not disabled
                        isTextFieldFocused = true
                        textFieldFocusRequesters[current.id]!!.requestFocus()
                        isCommandMode = false
                    }
                } else {
                    state.tryToConfirm {
                        // this is to avoid the focus bug when it will be true when hitting Enter in TextField
                        isTextFieldFocused = false
                        params.onConfirm.invoke(state)
                        closeDialogWithId(id)
                    }
                }
                return@DialogOverlay true
            }
            if (it.key == Key.Escape) {
                if (isCommandMode) {
                    params.onDismiss?.invoke(state)
                    closeDialogWithId(id)
                } else {
                    isCommandMode = true
                    dialogFocusRequester.requestFocus()
                }
                return@DialogOverlay true
            }
            false
        }) {
        Box {
            // only a text field works to fix the focus issue to revert back to the real text field, neither Box nor Text work
            TextField(
                "",
                onValueChange = {},
                modifier = Modifier
                    .alpha(0f)
                    .focusable()
                    .focusRequester(dialogFocusRequester)
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(15.dp),
                modifier = Modifier
                    .widthIn(max = 600.dp)
                    .verticalScroll(scrollState)
            ) {
                Text(
                    params.title,
                    color = GlobalStyling.getTextColor(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .widthIn(min = 300.dp)
                        .padding(12.dp)
                )
                state.forEach { row ->
                    val isSelected = (state.indexOf(row) == selectedIndex)
                    val drawSelectedBackground = isSelected && isCommandMode
                    val isDisabled = row.isDisabled?.invoke(state) ?: false
                    val currentAlpha = if (isDisabled) GlobalStyling.DISABLED_ALPHA else 1f

                    when (row) {
                        is GenericDialogRow.Checkbox -> {
                            GenericDialogBackgroundWrapper(drawSelectedBackground) {
                                val focusRequester = remember { FocusRequester() }
                                LaunchedEffect(row.id) {
                                    checkboxFocusRequesters[row.id!!] = focusRequester
                                }
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .alpha(currentAlpha)
                                        .clickable(
                                            enabled = !isDisabled, // Disable click if disabled
                                            onClickLabel = null,
                                            onClick = {
                                                val isChecked = !row.isChecked
                                                state = state.map { r ->
                                                    if (r.id == row.id) {
                                                        (r as GenericDialogRow.Checkbox).copy(isChecked = isChecked)
                                                    } else {
                                                        r
                                                    }
                                                }
                                            },
                                            role = null,
                                            indication = null,
                                            interactionSource = remember { MutableInteractionSource() },
                                        )
                                        .pointerHoverIcon(if (!isDisabled) PointerIcon.Hand else PointerIcon.Default)
                                        .focusRequester(focusRequester)
                                        .onFocusChanged { focus ->
                                            isCommandMode = !focus.isFocused
                                        }
                                ) {
                                    Checkbox(
                                        checked = row.isChecked,
                                        // use null, so no ripple hover is displayed
                                        onCheckedChange = null,
                                        enabled = !isDisabled, // Disable checkbox if disabled
                                        colors = CheckboxDefaults.colors(
                                            uncheckedColor = GlobalStyling.getCheckboxUncheckedColor(),
                                            checkedColor = GlobalStyling.getCheckboxCheckedColor(),
                                        )
                                    )
                                    TitleWithRequiredAsterisk(
                                        row.title,
                                        row.isRequired,
                                        textColor = if (isSelected && !isCommandMode && !isDisabled) {
                                            GlobalStyling.getSelectedBackgroundColor()
                                        } else {
                                            GlobalStyling.getTextColor()
                                        }
                                    )
                                }
                            }
                        }

                        is GenericDialogRow.TextInputSingleLine -> {
                            GenericDialogTextInputSingleLine(
                                row = row,
                                drawSelectedBackground = drawSelectedBackground,
                                textFieldFocusRequesters = textFieldFocusRequesters,
                                state = state,
                                onStateChange = { newState -> state = newState },
                                onCommandModeChange = { newCommandMode -> isCommandMode = newCommandMode },
                                parentIsDisabled = isDisabled, // Pass disabled state explicitly
                                currentAlpha = currentAlpha
                            )
                        }
                    }
                }
            }
            // wrap with Box, so the scroll bar does not take the full height
            Box(modifier = Modifier.matchParentSize()) {
                VerticalScrollbar(
                    adapter = rememberScrollbarAdapter(scrollState),
                    modifier = Modifier.align(Alignment.CenterEnd),
                )
            }
        }
    }

    LaunchedEffect(Unit) {
        dialogFocusRequester.requestFocus()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            dialogFocusRequester.requestFocus()
        }
    }
}