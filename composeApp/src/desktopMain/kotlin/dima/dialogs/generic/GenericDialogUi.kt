package dima.dialogs.generic

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors

@Composable
internal fun GenericDialogBackgroundWrapper(drawSelectedBackground: Boolean, content: @Composable () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (drawSelectedBackground) {
                    Modifier.background(GlobalStyling.getSelectedBackgroundColor())
                } else {
                    Modifier
                }
            )
            .padding(12.dp)
    ) {
        content()
    }
}

/**
 * Creates a Text component with the title and a red asterisk if required.
 */
@Composable
fun TitleWithRequiredAsterisk(title: String, isRequired: <PERSON>ole<PERSON>, textColor: Color? = null) {
    if (isRequired) {
        val annotatedString = buildAnnotatedString {
            if (textColor == null) {
                append(title)
            } else {
                withStyle(style = SpanStyle(color = textColor)) {
                    append(title)
                }
            }
            append(" ")
            withStyle(style = SpanStyle(color = Color.Red)) {
                append("*")
            }
        }
        Text(annotatedString)
    } else {
        Text(
            title,
            color = textColor ?: Color.Unspecified,
        )
    }
}