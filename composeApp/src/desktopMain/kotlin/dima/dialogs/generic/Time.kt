package dima.dialogs.generic

internal fun parseStringIntoTime(s: String): GenericDialogRowTime? {
    val trimmed = s.trim()
    if (trimmed.contains(":")) {
        val parts = trimmed.split(":")
        if (parts.size != 2) {
            return null
        }
        val hour = parts[0].trim().toIntOrNull()
        val minute = parts[1].trim().toIntOrNull()

        if (hour == null || minute == null || hour !in 0..23 || minute !in 0..59) {
            return null
        }
        return GenericDialogRowTime(hour, minute)
    }
    trimmed.toIntOrNull()?.let {
        if (it in 0..23) {
            // If only hour is provided, assume 00 minutes
            return GenericDialogRowTime(it, 0)
        }
    }
    return null
}
