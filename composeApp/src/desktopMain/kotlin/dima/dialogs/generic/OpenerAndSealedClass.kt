package dima.dialogs.generic

import dima.dialogs.DialogIdentifier
import dima.dialogs.openDialog
import dima.utils.SimpleResult

data class GenericDialogRowTime(val hour: Int, val minute: Int)

fun GenericDialogNumberValidator(content: String): SimpleResult {
    return if (content.toIntOrNull() == null) {
        SimpleResult.Error("Needs to be a number")
    } else {
        SimpleResult.Success
    }
}

fun GenericDialogTimeValidator(content: String): SimpleResult {
    return if (parseStringIntoTime(content) == null) {
        SimpleResult.Error("Needs HH or HH:MM")
    } else {
        SimpleResult.Success
    }
}

sealed class GenericDialogRow(
    open val title: String,
    /**
     * If null, will be the same as [title]. This will crash on duplicated titles.
     */
    open val id: String? = null,
    open val isRequired: Boolean = false,
    open val isDisabled: ((List<GenericDialogRow>) -> Boolean)? = null,
) {
    data class Checkbox(
        override val title: String,
        /**
         * If null, will be the same as [title]. This will crash on duplicated titles.
         */
        override val id: String? = null,
        override val isRequired: Boolean = false,
        override val isDisabled: ((List<GenericDialogRow>) -> Boolean)? = null,
        val isChecked: Boolean = false
    ) : GenericDialogRow(title, id, isRequired, isDisabled)

    data class TextInputSingleLine(
        override val title: String,
        /**
         * If null, will be the same as [title]. This will crash on duplicated titles.
         */
        override val id: String? = null,
        override val isRequired: Boolean = false,
        override val isDisabled: ((List<GenericDialogRow>) -> Boolean)? = null,
        val content: String = "",
        val validator: ((String) -> SimpleResult)? = null
    ) : GenericDialogRow(title, id, isRequired, isDisabled)
}

fun List<GenericDialogRow>.isCheckboxTicked(id: String): Boolean {
    return ((find { it.id == id } ?: throw Exception("No such id: $id")) as GenericDialogRow.Checkbox).isChecked
}

fun List<GenericDialogRow>.getTextInputSingleLineContent(id: String): String {
    return ((find { it.id == id }
        ?: throw Exception("No such id: $id")) as GenericDialogRow.TextInputSingleLine).content
}

private fun injectTitleAsId(layout: List<GenericDialogRow>): List<GenericDialogRow> {
    val ids = mutableSetOf<String>()
    return layout.map { row ->
        val id = row.id ?: row.title
        if (id in ids) {
            throw Exception("Duplicated id: $id")
        }
        ids.add(id)
        if (row.id == null) {
            when (row) {
                is GenericDialogRow.Checkbox -> row.copy(id = id)
                is GenericDialogRow.TextInputSingleLine -> row.copy(id = id)
            }
        } else {
            row
        }
    }
}

fun openGenericDialog(
    title: String,
    layout: List<GenericDialogRow>,
    onDismiss: ((List<GenericDialogRow>) -> Unit)? = null,
    onConfirm: (List<GenericDialogRow>) -> Unit,
) {
    if (layout.isEmpty()) {
        throw Exception("Layout must not be empty for the generic dialog")
    }
    val newLayout = injectTitleAsId(layout)
    val params = Params(title, newLayout, onDismiss, onConfirm)
    openDialog(DialogIdentifier.Generic, params) { id, data ->
        GenericDialog(id, data)
    }
}

internal data class Params(
    val title: String,
    val layout: List<GenericDialogRow>,
    val onDismiss: ((List<GenericDialogRow>) -> Unit)? = null,
    val onConfirm: (List<GenericDialogRow>) -> Unit,
)