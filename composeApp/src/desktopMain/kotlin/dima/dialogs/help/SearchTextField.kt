package dima.dialogs.help

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.utils.TextFieldWithCandidateCountWithoutAnimation

@Composable
internal fun HelpDialogSearchBar(
    queryState: TextFieldValue,
    onQueryChange: (TextFieldValue) -> Unit,
    focusRequester: FocusRequester
) {
    Row(
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 12.dp)
    ) {
        TextFieldWithCandidateCountWithoutAnimation(
            value = queryState,
            topLabel = "Query",
            onValueChange = onQueryChange,
            focusRequester = focusRequester,
            modifier = Modifier
                .fillMaxWidth(0.5f)
        )
    }
}
