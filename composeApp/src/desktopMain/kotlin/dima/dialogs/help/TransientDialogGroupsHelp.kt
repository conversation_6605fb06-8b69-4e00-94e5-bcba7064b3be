package dima.dialogs.help

import androidx.compose.ui.input.key.Key
import dima.apps.AppType
import dima.globalState.GlobalState
import dima.utils.AppKey

/**
 * I tried making this dynamic, but it complicates the code even more, since those are filled dynamically.
 *
 * @return empty array when no transient keys are defined for the current app
 */
internal fun getTransientKeysForApp(): List<AppKey> {
    return when (GlobalState.app) {
        AppType.Dired -> list<PERSON>f(
            <PERSON><PERSON><PERSON><PERSON>(Key.A, "Archive into scanned directory"),
            <PERSON><PERSON><PERSON><PERSON>(Key.B, "Open last modified file"),
            <PERSON><PERSON><PERSON><PERSON>(Key.Tab, "Toggle row image preview"),
            <PERSON><PERSON><PERSON><PERSON>(Key.C, "Copy image to clipboard"),
            <PERSON><PERSON><PERSON><PERSON>(Key.DirectionLeft, "Rotate image left"),
            <PERSON><PERSON><PERSON><PERSON>(Key.DirectionRight, "Rotate image right"),
            <PERSON><PERSON><PERSON><PERSON>(Key.F, "Reveal in Finder.app"),
            <PERSON><PERSON><PERSON><PERSON>(Key.G, "Open in Gimp"),
            <PERSON><PERSON><PERSON><PERSON>(Key.N, "Create new directory"),
            <PERSON><PERSON><PERSON><PERSON>(Key.P, "Open first PDF page"),
            <PERSON><PERSON><PERSON><PERSON>(Key.S, "Toggle sort: Name, Size, Last Modified"),
            <PERSON><PERSON><PERSON><PERSON>(Key.T, "Send to Telegram"),
            <PERSON><PERSON><PERSON><PERSON>(Key.U, "Transcribe audio"),
            App<PERSON>ey(Key.Z, "Convert to MP3/PNG"),
        )

        else -> emptyList()
    }
}
