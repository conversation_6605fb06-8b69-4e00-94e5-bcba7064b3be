package dima.dialogs.help

import GlobalEvent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dima.apps.AppType
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.leader.Leader
import dima.leader.LeaderHelpKey
import dima.utils.AppKey
import dima.utils.asLowerCase
import globalEvent

private fun getUnboundKeysHeader(query: String): String {
    if (query.length == 1 && query[0] == ' ') {
        return "Unbound keys for " + Leader.SPACE_ABBREV
    }
    if (query.length == 2 && query[0] == ' ') {
        return "Unbound keys for " + Leader.SPACE_ABBREV + "-" + query[1]
    }
    return "Unbound keys"
}

private fun getUnboundKeys(query: String, leaderKeys: List<LeaderHelpKey>, sortedAppKeys: List<AppKey>): List<String> {
    if (query.length >= 3 && query[0] == ' ') {
        return emptyList()
    }
    val keysToConsider = mutableListOf<String>()
    // from A to Z
    for (i in 0x41..0x5A) {
        // ignore W which might be later used to switch between windows
        if (i != 0x57) {
            keysToConsider.add(Key(i).asLowerCase())
        }
    }
    if (query.length == 1 && query[0] == ' ') {
        val relevantLeaderKeys = leaderKeys.filter {
            it.secondKey == null
        }
        return keysToConsider.filter { key ->
            relevantLeaderKeys.find {
                it.firstKey.asLowerCase() == key
            } == null
        }
    }
    if (query.length == 2 && query[0] == ' ') {
        val relevantLeaderKeys = leaderKeys.filter {
            it.firstKey.asLowerCase() == query[1].lowercase()
        }
        return keysToConsider.filter { key ->
            relevantLeaderKeys.find {
                it.secondKey != null && it.secondKey.asLowerCase() == key
            } == null
        }
    }
    return keysToConsider.filter { key ->
        sortedAppKeys.find { it.key.asLowerCase() == key } == null
    }
}

@Composable
private fun renderLeaderKeyRows(
    keys: List<LeaderHelpKey>,
    spaceLeaderKey: String,
    query: String,
    filterPredicate: (LeaderHelpKey) -> Boolean = { true }
) {
    keys.filter(filterPredicate).forEach { key ->
        val first = key.firstKey.asLowerCase()
        if (key.secondKey == null) {
            HelpDialogKeyRow(
                keys = listOf(spaceLeaderKey, first),
                text = key.title,
                query = if (query.length >= 2) query else "",
                onClick = key.callback
            )
        } else {
            HelpDialogKeyRow(
                keys = listOf(spaceLeaderKey, first, key.secondKey.asLowerCase()),
                text = key.title,
                query = if (query.length >= 2) query else "",
                onClick = key.callback
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
internal fun HelpDialogContent(
    subTitle: String?,
    globalKeys: List<GlobalKeys>,
    leaderKeys: List<LeaderHelpKey>,
    appKeys: List<AppKey>,
    transientKeys: List<AppKey>,
    query: String
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        if (subTitle != null) {
            Text(
                subTitle,
                color = GlobalStyling.getTextColor(),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(top = 8.dp, bottom = 16.dp)
                    .fillMaxWidth()
            )
        }
        Row(modifier = Modifier.fillMaxWidth()) {
            Column(modifier = Modifier.weight(0.5f)) {
                HeaderText("Globals")
                globalKeys.forEach {
                    HelpDialogKeyRow(
                        keys = listOf(it.key),
                        text = it.text,
                        isCmd = it.isCmd,
                        query = if (query.length >= 2) query else "",
                        onClick = if (it.key == "+") {
                            {
                                globalEvent = GlobalEvent.Reload
                            }
                        } else {
                            null
                        }
                    )
                }
                HeaderText("Apps", addTopPadding = true)
                val spaceLeaderKey = Key.Spacebar.asLowerCase()
                renderLeaderKeyRows(
                    keys = leaderKeys,
                    spaceLeaderKey = spaceLeaderKey,
                    query = query,
                    filterPredicate = { it.isAppSwitcher }
                )
                HeaderText("Leader", addTopPadding = true)
                renderLeaderKeyRows(
                    keys = leaderKeys,
                    spaceLeaderKey = spaceLeaderKey,
                    query = query,
                    filterPredicate = { !it.isAppSwitcher }
                )
            }
            Column(
                modifier = Modifier
                    .weight(0.5f)
                    .padding(start = 16.dp)
            ) {
                HeaderText(GlobalState.app.displayName)
                appKeys.forEach {
                    HelpDialogKeyRow(
                        keys = listOf(it.key.asLowerCase()),
                        text = it.text,
                        query = if (query.length >= 2) query else "",
                        isShift = it.isShift,
                        isCmd = it.isCmd,
                        isCtrl = it.isCtrl,
                        onClick = it.onKeyDown
                    )
                }
                if (GlobalState.app == AppType.Dired) {
                    HeaderText(GlobalState.app.displayName + " Transient", addTopPadding = true)
                    transientKeys.forEach {
                        HelpDialogKeyRow(
                            keys = if (it.key == Key.Tab) {
                                listOf("⇥")
                            } else {
                                listOf(it.key.asLowerCase())
                            },
                            text = it.text,
                            query = if (query.length >= 2) query else "",
                        )
                    }
                }
                val unbound = getUnboundKeys(query, leaderKeys, appKeys)
                if (unbound.isNotEmpty()) {
                    HeaderText(getUnboundKeysHeader(query), addTopPadding = true)
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        unbound.forEach {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center,
                                modifier = Modifier
                                    .shadow(1.dp, RoundedCornerShape(5.dp))
                                    .background(TailwindCssColors.gray200)
                                    .size(20.dp)
                            ) {
                                Text(it)
                            }
                        }
                    }
                }
            }
        }
    }
}
