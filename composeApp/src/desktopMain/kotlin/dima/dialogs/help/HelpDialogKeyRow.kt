package dima.dialogs.help

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.text.TextMarked
import dima.utils.clickableWithoutBackgroundRipple

@Composable
internal fun HeaderText(text: String, addTopPadding: Boolean = false) {
    Text(
        text,
        color = GlobalStyling.getTextColor(),
        fontWeight = FontWeight.Bold,
        modifier = Modifier
            .padding(bottom = 4.dp, top = if (addTopPadding) 8.dp else 0.dp)
    )
}

/**
 * @param query is used to highlight the text
 */
@Composable
fun HelpDialogKeyRow(
    keys: List<String>,
    text: String,
    query: String = "",
    isShift: Boolean = false,
    isCmd: Boolean = false,
    isCtrl: Boolean = false,
    onClick: (() -> Unit)? = null
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(vertical = 1.dp)
            .then(
                if (onClick == null) {
                    Modifier
                } else {
                    Modifier.clickableWithoutBackgroundRipple {
                        onClick()
                    }
                }
            )
            .padding(vertical = 3.dp, horizontal = 4.dp)
    ) {
        val keysToDisplay = keys.toMutableList()
        if (isCmd) {
            keysToDisplay.add(0, "⌘")
        }
        if (isCtrl) {
            keysToDisplay.add(0, "^")
        }
        keysToDisplay.forEach {
            Key(it, isShift)
        }
        TextMarked(
            text, query,
            color = GlobalStyling.getTextColor(),
            modifier = Modifier.padding(start = 6.dp)
        )
    }
}

@Composable
internal fun Key(s: String, isShift: Boolean = false, size: Dp = 20.dp, fontSize: TextUnit = TextUnit.Unspecified) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(end = 4.dp)
            .shadow(1.dp, RoundedCornerShape(5.dp))
            .background(TailwindCssColors.gray200)
            .size(size)
    ) {
        if (isShift) {
            Text(s.uppercase(), fontSize = fontSize)
        } else {
            Text(s, fontSize = fontSize)
        }
    }
}