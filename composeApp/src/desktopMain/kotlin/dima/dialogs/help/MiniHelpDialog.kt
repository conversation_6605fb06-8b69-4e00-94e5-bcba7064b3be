package dima.dialogs.help

import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.dialogs.DialogIdentifier
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dima.dialogs.openDialog
import dima.leader.LeaderKeyState
import dima.leader.getLeaderHelpKeys
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import dima.utils.asLowerCase
import dima.utils.filterHuman
import kotlinx.coroutines.launch

data class MiniHelpDialogKey(
    val keys: List<Key>,
    val text: String,
    val isLeader: Boolean = false,
    val isShift: Boolean = false,
    val isCmd: Boolean = false,
    val isCtrl: Boolean = false,
    val callback: (() -> Unit)? = null
)

fun openMiniHelpDialog(title: String, subTitle: String? = null, keys: List<MiniHelpDialogKey>) {
    miniHelpDialogParams = MiniHelpDialogParams(title, subTitle, keys)
    openDialog(DialogIdentifier.MiniHelp, data = null) { id, _ ->
        MiniHelpDialog(id)
    }
}

fun openMiniHelpDialogForLeaderState(leaderKeyState: LeaderKeyState) {
    val initialLeaderKeys = getLeaderHelpKeys()
    if (leaderKeyState.first == null && leaderKeyState.second == null) {
        // show all in leader
        val keys = initialLeaderKeys.map {
            if (it.secondKey == null) {
                MiniHelpDialogKey(listOf(it.firstKey), text = it.title, isLeader = true)
            } else {
                MiniHelpDialogKey(listOf(it.firstKey, it.secondKey), it.title, isLeader = true)
            }
        }
        miniHelpDialogParams = MiniHelpDialogParams("Leader", keys = keys)
    } else {
        // first key is set, both keys will never be set since that triggers the leader callback
        val keys = initialLeaderKeys
            .filter { it.firstKey == leaderKeyState.first && it.secondKey != null }
            .map {
                MiniHelpDialogKey(listOf(it.secondKey!!), it.title, isLeader = true)
            }
        miniHelpDialogParams = MiniHelpDialogParams("Leader", keys = keys)
    }
    openDialog(DialogIdentifier.MiniHelp, data = null) { id, _ ->
        MiniHelpDialog(id)
    }
}

private data class MiniHelpDialogParams(
    val title: String,
    val subTitle: String? = null,
    val keys: List<MiniHelpDialogKey>,
)

private var miniHelpDialogParams: MiniHelpDialogParams? = null

/**
 * Mimics a bit how [HelpDialog] works in terms of key input.
 */
@Composable
fun MiniHelpDialog(id: Long) {
    val params = remember { miniHelpDialogParams!! }
    var filteredKeys by remember { mutableStateOf(params.keys) }
    var filterNormally by remember { mutableStateOf(false) }
    var query by remember { mutableStateOf("") }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }
    val searchFocusRequester = remember { FocusRequester() }
    val scrollState = rememberScrollState()
    val scrollOffset = 240f
    val coroutineScope = rememberCoroutineScope()
    var dialogWidth by remember { mutableStateOf(Dp.Unspecified) }
    var dialogHeight by remember { mutableStateOf(Dp.Unspecified) }

    DialogOverlay(
        dialogId = id,
        widthFraction = null,
        dialogPadding = null,
        onInitialSize = { width, height ->
            dialogWidth = width
            dialogHeight = height
        },
        onPreviewKeyEvent = onPreviewKeyEvent@{
            // close dialog on KeyUp to not pass Escape to dialog/app below
            if (it.type == KeyEventType.KeyUp && it.key == Key.Escape) {
                closeDialogWithId(id)
                return@onPreviewKeyEvent true
            }
            if (it.type != KeyEventType.KeyDown) {
                return@onPreviewKeyEvent false
            }
            if (it.key == Key.DirectionDown) {
                coroutineScope.launch {
                    scrollState.animateScrollBy(scrollOffset)
                }
            }
            if (it.key == Key.DirectionUp) {
                coroutineScope.launch {
                    scrollState.animateScrollBy(-scrollOffset)
                }
            }
            if (it.key == Key.Enter) {
                if (query.isEmpty()) {
                    val text = "enter"
                    val length = text.length
                    queryState = TextFieldValue(
                        text = text,
                        selection = TextRange(length, length)
                    )
                    query = text
                }
            }
            return@onPreviewKeyEvent false
        }
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .then(
                    if (dialogWidth == Dp.Unspecified) {
                        Modifier
                            .widthIn(max = 600.dp)
                    } else {
                        Modifier
                            .width(dialogWidth)
                            .height(dialogHeight)
                    }
                )
                .padding(12.dp)
        ) {
            if (params.subTitle != null) {
                Text(
                    params.subTitle,
                    fontSize = 13.sp,
                    modifier = Modifier
                )
            }
            TextFieldWithCandidateCountWithoutAnimation(
                value = queryState,
                onValueChange = {
                    queryState = it
                    query = it.text
                },
                topLabel = params.title,
                focusRequester = searchFocusRequester,
                modifier = Modifier.fillMaxWidth(0.6f)
            )
            Column(
                modifier = Modifier
                    .verticalScroll(scrollState)
                    .padding(end = 12.dp)
                    .fillMaxWidth()
            ) {
                filteredKeys.forEach { key ->
                    HelpDialogKeyRow(
                        key.keys.map { it.asLowerCase() },
                        key.text,
                        query = when {
                            filterNormally -> query
                            query.length >= 3 -> query
                            else -> ""
                        },
                        isCmd = key.isCmd,
                        isCtrl = key.isCtrl
                    )
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        searchFocusRequester.requestFocus()
    }
    LaunchedEffect(query) {
        if (query == " ") {
            filteredKeys = params.keys.filter {
                it.keys.contains(Key.Spacebar)
            }
            return@LaunchedEffect
        }
        val trimmedQuery = query.trim().lowercase()
        if (trimmedQuery.isBlank()) {
            filteredKeys = params.keys
            return@LaunchedEffect
        }

        fun filterNormally(): List<MiniHelpDialogKey> {
            return params.keys.filterHuman(query) { it.text }
        }

        filteredKeys = if (trimmedQuery == "enter") {
            filterNormally = true
            params.keys.filter {
                it.keys.firstOrNull() == Key.Enter
            }
        } else if (trimmedQuery.count() == 1) {
            filterNormally = true
            params.keys.filter {
                it.keys.firstOrNull()?.asLowerCase() == trimmedQuery
            }
        } else if (trimmedQuery.count() == 2) {
            val hasSecond = params.keys.any {
                it.keys.count() >= 2
            }
            if (hasSecond) {
                filterNormally = false
                params.keys.filter {
                    it.keys.firstOrNull()?.asLowerCase() == trimmedQuery[0].toString() &&
                            it.keys.lastOrNull()?.asLowerCase() == trimmedQuery[1].toString()
                }
            } else {
                filterNormally = true
                filterNormally()
            }
        } else {
            filterNormally = true
            filterNormally()
        }
    }
}