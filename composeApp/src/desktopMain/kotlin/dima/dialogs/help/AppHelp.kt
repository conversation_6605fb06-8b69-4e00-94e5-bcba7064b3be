package dima.dialogs.help

import dima.apps.AppType
import dima.globalState.GlobalState.app

/**
 * Can return newline characters which are correctly rendered in the help dialog.
 */
fun getHelpForCurrentApp(): String? {
    return when (app) {
        AppType.AiSinglePrompt -> "Mouse scroll over prompt to change models"
        AppType.Calculator -> """Operators: + - * / % ^ && ||
Variables: pi e
Functions: abs() sum() floor() ceil() round() min() max() if() sin() cos()"""

        else -> null
    }
}