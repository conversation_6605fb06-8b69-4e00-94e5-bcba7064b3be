package dima.dialogs.help

import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dialogs
import dima.dialogs.*
import dima.globalState.GlobalState
import dima.globalState.GlobalStateStorage
import dima.leader.LeaderHelpKey
import dima.leader.getLeaderHelpKeys
import dima.utils.*
import isTextFieldFocused
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

fun openHelpDialog() {
    openDialog(DialogIdentifier.Help, data = null) { id, _ ->
        HelpDialog(id)
    }
}

/**
 * Set this in an App and then use via [handleAppMap].
 */
var appKeys by mutableStateOf(listOf<AppKey>())

/**
 * Filter out keys with same key event type because [ScrollVelocity] requires
 * both key up and down to be handled.
 */
private fun getFilteredAppKeys(): List<AppKey> {
    return appKeys.distinctBy {
        it.key.toString() + it.isCmd + it.isShift + it.isCtrl
    }
}

internal data class GlobalKeys(
    val key: String,
    val text: String,
    val isCmd: Boolean = false
)

@Composable
fun HelpDialog(id: Long) {
    val initialGlobalKeys = remember {
        listOf(
            GlobalKeys("-", "Open audio player dialog"),
            GlobalKeys("+", "Reload entry"),
            GlobalKeys("1", "Make single pane"),
            GlobalKeys("4", "Toggle window decoration"),
            GlobalKeys("6", "Maximize window for hot reload"),
            GlobalKeys("?", "Open help dialog or restore focus"),
            GlobalKeys("+", "Copy recent text", isCmd = true),
            GlobalKeys("t", "Open new pane with same app", isCmd = true),
            GlobalKeys("w", "Close current pane", isCmd = true)
        )
    }
    val initialLeaderKeys: List<LeaderHelpKey> = remember { getLeaderHelpKeys() }
    val initialAppKeys = remember { getFilteredAppKeys().sorted() }
    val initialTransientKeys = remember { getTransientKeysForApp().sorted() }
    var globalKeys by remember { mutableStateOf(initialGlobalKeys) }
    var leaderKeys by remember { mutableStateOf(initialLeaderKeys) }
    var appKeys by remember { mutableStateOf(initialAppKeys) }
    var transientKeys by remember { mutableStateOf(initialTransientKeys) }
    val subTitle = remember { getHelpForCurrentApp() }
    var query by remember { mutableStateOf("") }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }
    val searchFocusRequester = remember { FocusRequester() }
    val scrollState = rememberScrollState()
    val scrollVelocity = remember { ScrollVelocity(scrollState) }
    val coroutineScope = rememberCoroutineScope()

    DialogOverlay(
        dialogId = id,
        dialogPadding = PaddingValues(top = 12.dp, bottom = 12.dp, start = 12.dp, end = 0.dp),
        onPreviewKeyEvent = onPreviewKeyEvent@{
            when (it.type) {
                KeyEventType.KeyDown -> {
                    when (it.key) {
                        Key.Escape -> {
                            GlobalState.helpDialog = GlobalState.helpDialog.copy(visible = false, query = "")
                            closeDialogWithId(id)
                            return@onPreviewKeyEvent true
                        }

                        Key.DirectionUp -> {
                            scrollVelocity.onScrollUpKeyPressed()
                            return@onPreviewKeyEvent true
                        }

                        Key.DirectionDown -> {
                            scrollVelocity.onScrollDownKeyPressed()
                            return@onPreviewKeyEvent true
                        }

                        Key.Enter -> {
                            if (query.isEmpty()) {
                                val text = "enter"
                                val length = text.length
                                queryState = TextFieldValue(
                                    text = text,
                                    selection = TextRange(length, length)
                                )
                                query = text
                            }
                            return@onPreviewKeyEvent true
                        }

                        else -> {
                            // If text field is not focused, focus it on any other key press
                            if (!isTextFieldFocused) {
                                searchFocusRequester.requestFocus()
                                // Let the TextField handle the character input, don't consume here.
                                return@onPreviewKeyEvent false
                            }
                            return@onPreviewKeyEvent false
                        }
                    }
                }

                KeyEventType.KeyUp -> {
                    when (it.key) {
                        Key.DirectionUp, Key.DirectionDown -> {
                            scrollVelocity.onKeyReleased()
                            return@onPreviewKeyEvent true
                        }

                        else -> {
                            return@onPreviewKeyEvent false
                        }
                    }
                }

                else -> return@onPreviewKeyEvent false
            }
        }) {
        Column {
            HelpDialogSearchBar(
                queryState = queryState,
                onQueryChange = {
                    queryState = it
                    query = it.text
                },
                focusRequester = searchFocusRequester
            )
            Box(
                modifier = Modifier
                    .weight(1f)
            ) {
                Column(
                    modifier = Modifier
                        .verticalScroll(scrollState)
                        .padding(end = 12.dp)
                        .fillMaxWidth()
                ) {
                    HelpDialogContent(
                        subTitle = subTitle,
                        globalKeys = globalKeys,
                        leaderKeys = leaderKeys,
                        appKeys = appKeys,
                        transientKeys = transientKeys,
                        query = query
                    )
                }
                VerticalScrollbar(
                    adapter = rememberScrollbarAdapter(scrollState = scrollState),
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .fillMaxHeight(),
                )
            }
            DialogBottomKeyInfo(
                listOf(
                    DialogBottomKey("↓", "scroll down"),
                    DialogBottomKey("↑", "scroll up"),
                ),
                modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxWidth()
            )
        }
    }
    LaunchedEffect(Unit) {
        GlobalState.helpDialog = GlobalState.helpDialog.copy(visible = true)
        GlobalStateStorage.write()
        searchFocusRequester.requestFocus()
        coroutineScope.launch {
            // fix so the initial dialog height can be calculated
            delay(1)
            query = GlobalState.helpDialog.query
            // Start the scroll velocity loop
            scrollVelocity.loopForeverAndTick()
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            searchFocusRequester.requestFocus()
        }
    }
    var ignoreFirstQueryForDialogHeight by remember { mutableStateOf(true) }
    LaunchedEffect(query) {
        val trimmedQuery = query.trim().lowercase()

        appKeys = if (trimmedQuery.isEmpty()) {
            initialAppKeys
        } else if (trimmedQuery == "escape") {
            initialAppKeys.filter { it.key == Key.Escape }
        } else if (trimmedQuery == "enter") {
            initialAppKeys.filter { it.key == Key.Enter }
        } else if (trimmedQuery.length == 1 && query.firstOrNull() != ' ') {
            initialAppKeys.filter { it.key.asLowerCase() == trimmedQuery }
        } else {
            initialAppKeys.filterHuman(query) { it.text }
        }

        transientKeys = if (trimmedQuery.isEmpty()) {
            initialTransientKeys
        } else if (trimmedQuery == "escape") {
            initialTransientKeys.filter { it.key == Key.Escape }
        } else if (trimmedQuery == "enter") {
            initialTransientKeys.filter { it.key == Key.Enter }
        } else if (trimmedQuery.length == 1 && query.firstOrNull() != ' ') {
            initialTransientKeys.filter { it.key.asLowerCase() == trimmedQuery }
        } else {
            initialTransientKeys.filterHuman(query) { it.text }
        }

        globalKeys = if (trimmedQuery.isEmpty()) {
            initialGlobalKeys
        } else if (trimmedQuery.length == 1 && query.firstOrNull() != ' ') {
            initialGlobalKeys.filter { it.key.lowercase() == trimmedQuery }
        } else {
            initialGlobalKeys.filterHuman(query) { it.text }
        }

        leaderKeys = if (trimmedQuery.isEmpty() && query.firstOrNull() != ' ') {
            initialLeaderKeys
        } else if (query.firstOrNull() == ' ') {
            val lowerQuery = query.lowercase()
            when (lowerQuery.length) {
                1 -> initialLeaderKeys // Query is just " "
                2 -> initialLeaderKeys.filter { it.firstKey.asLowerCase() == lowerQuery[1].toString() }
                3 -> initialLeaderKeys.filter {
                    it.firstKey.asLowerCase() == lowerQuery[1].toString() &&
                            it.secondKey != null &&
                            it.secondKey.asLowerCase() == lowerQuery[2].toString()
                }

                else -> initialLeaderKeys.filterHuman(query) { it.title }
            }
        } else if (trimmedQuery.length == 1) {
            initialLeaderKeys.filter { it.firstKey.asLowerCase() == trimmedQuery }
        } else {
            initialLeaderKeys.filterHuman(query) { it.title }
        }

        if (ignoreFirstQueryForDialogHeight) {
            ignoreFirstQueryForDialogHeight = false
            val length = GlobalState.helpDialog.query.length
            queryState = TextFieldValue(
                text = GlobalState.helpDialog.query,
                selection = TextRange(length, length)
            )
        } else {
            GlobalState.helpDialog = GlobalState.helpDialog.copy(query = query)
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            if (dialogs.isEmpty()) {
                GlobalState.helpDialog = GlobalState.helpDialog.copy(visible = false)
            }
        }
    }
}