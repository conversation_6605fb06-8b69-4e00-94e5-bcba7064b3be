package dima.dialogs.loading

import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.sharp.Refresh
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.dialogs.DialogIdentifier
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dima.dialogs.openDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

data class LoadingDialogProcessResult(val stdout: String, val stderr: String, val exitCode: Int)

private const val showAfterMs = 200L

private class LoadingDialogProcessThread(private val params: LoadingDialogParams) : Thread() {
    override fun run() {
        val process = ProcessBuilder(params.args).apply {
            if (params.cwd != null) {
                directory(params.cwd)
            }
        }.start()
        val exitCode = process.waitFor()
        processResult = LoadingDialogProcessResult(
            stdout = process.inputStream.bufferedReader().readText(),
            stderr = process.errorStream.bufferedReader().readText(),
            exitCode = exitCode
        )
    }
}

internal var processResult by mutableStateOf<LoadingDialogProcessResult?>(null)

private var thread: Thread? = null

/**
 * @param onFinish never called when the process was aborted
 * @param onAbort called when the dialog is closed and the process is aborted forcibly
 */
fun openLoadingDialog(
    title: String,
    args: List<String>,
    cwd: File? = null,
    onFinish: ((LoadingDialogProcessResult) -> Unit)? = null,
    onAbort: (() -> Unit)? = null,
) {
    val params = LoadingDialogParams(
        title = title,
        cwd = cwd,
        args = args,
        onFinish = onFinish,
        onAbort = onAbort
    )
    processResult = null
    openDialog(DialogIdentifier.Loading, data = params) { id, data ->
        LoadingDialog(id, data)
    }
    thread = LoadingDialogProcessThread(params)
    thread!!.start()
}

private data class LoadingDialogParams(
    val title: String,
    val args: List<String>,
    val cwd: File?,
    val onFinish: ((LoadingDialogProcessResult) -> Unit)?,
    val onAbort: (() -> Unit)?,
)

/**
 * There can only be one active loading dialog at a time.
 */
@Composable
fun LoadingDialog(id: Long, data: Any?) {
    val params = remember { data as LoadingDialogParams }
    var alpha by remember { mutableStateOf(0f) }
    val infiniteTransition = rememberInfiniteTransition()
    val angle by infiniteTransition.animateFloat(
        initialValue = 0F,
        targetValue = 360F,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing)
        )
    )

    DialogOverlay(
        dialogId = id,
        widthFraction = null,
        alpha = alpha,
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.type != KeyEventType.KeyDown) {
                return@onPreviewKeyEvent false
            }
            if (it.key == Key.Escape) {
                closeDialogWithId(id)
                thread!!.interrupt()
                params.onAbort?.invoke()
                return@onPreviewKeyEvent true
            }
            return@onPreviewKeyEvent false
        }) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                params.title,
                fontWeight = FontWeight.SemiBold
            )
            Icon(
                Icons.Sharp.Refresh,
                contentDescription = null,
                tint = TailwindCssColors.gray600,
                modifier = Modifier
                    .size(42.dp)
                    .rotate(angle)
            )
        }
    }

    LaunchedEffect(Unit) {
        // alpha could be animated in instead of instantly showing, but this is fine for now
        // https://medium.com/@development.karthickramanathan/explore-compose-transition-class-a715bfe51f08
        launch {
//            delay(showAfterMs)
        }
            alpha = 1f
    }
    LaunchedEffect(processResult) {
        if (processResult == null) {
            return@LaunchedEffect
        }
        closeDialogWithId(id)
        params.onFinish?.invoke(processResult!!)
    }
}