package dima.dialogs.transient

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dialogs
import dima.apps.notifications.showErrorNotification
import dima.dialogs.*
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.dialogs.transient.Styling.subTitleMaxWidth
import dima.utils.*
import handleLostFocus
import dima.dialogs.help.Key as HelpKey

/**
 * Represents a key in the transient dialog with its display text and callback
 */
data class TransientDialogKey(
    val key: Key,
    val text: String,
    /** If null, [text] is used */
    val helpText: String? = null,
    val keepOpen: Boolean = false,
    val callback: () -> Unit
)

/**
 * Represents a group of keys in the transient dialog
 */
data class TransientDialogGroup(
    val name: String,
    val keys: List<TransientDialogKey>
)

internal data class Params(
    val groups: List<TransientDialogGroup>,
    val subTitle: String?,
    val openedByKey: Key? = null // Field to track the key that opened the dialog
)

/**
 * Opens a transient dialog at the bottom of the screen with the provided groups of keys
 *
 * @param groups if only one group is provided, the title is displayed centered
 * @param subTitle optional subtitle to display. If there's only one group, it's displayed below the title.
 *               If there are multiple groups, it's displayed at the top. Always displayed in gray.
 * @param openedByKey The key event (e.g., Key.Z) that triggered the opening of this dialog.
 *                    Used to prevent the dialog from immediately closing due to the KeyUp of the same key.
 */
fun openTransientDialog(
    groups: List<TransientDialogGroup>,
    subTitle: String? = null,
    openedByKey: Key? = null
) {
    val params = Params(groups, subTitle, openedByKey)
    openDialog(DialogIdentifier.Transient, data = params) { id, data ->
        TransientDialog(id, data!!)
    }
}

/**
 * Shows a help dialog with all keys in the transient dialog
 */
private fun showTransientHelpDialog(groups: List<TransientDialogGroup>) {
    val helpKeys = mutableListOf<MiniHelpDialogKey>()
    groups.forEach { group ->
        group.keys.forEach { key ->
            // Add indicator for keys that keep the dialog open
            val displayText = if (key.keepOpen) {
                "${key.text} (keeps dialog open)"
            } else {
                key.text
            }
            helpKeys.add(MiniHelpDialogKey(listOf(key.key), displayText))
        }
    }
    val title = if (groups.size == 1 && groups[0].name.isNotEmpty()) {
        groups[0].name
    } else {
        "Available Commands"
    }
    openMiniHelpDialog(title, keys = helpKeys)
}

@Composable
private fun KeyBadge(key: Key) {
    HelpKey(
        s = key.toDisplayString(),
        size = Styling.helpKeySize,
        fontSize = Styling.keyDescriptionFontSize,
    )
}

@Composable
fun TransientDialog(id: Long, data: Any) {
    val params = remember { data as Params }
    val groups = remember { params.groups }
    val subTitle = remember { params.subTitle }
    val dialogFocusRequester = remember { FocusRequester() }
    val isSingleGroup = groups.size == 1
    val textColor = GlobalStyling.getTextColor()
    var keyThatOpenedDialog by remember { mutableStateOf(params.openedByKey) }

    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(GlobalStyling.Dialog.backdropColor)
                .clickableWithoutBackgroundRipple {
                    closeDialogWithId(id)
                }
        )
        Column(
            modifier = Modifier
                .wrapContentWidth()
                .padding(bottom = Styling.dialogOuterBottomPadding)
                .background(
                    color = GlobalStyling.getWindowBackgroundColor(),
                    shape = GlobalStyling.Dialog.largeRoundedCorners,
                )
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .focusable()
                .focusRequester(dialogFocusRequester)
                .handleLostFocus()
                .onPreviewKeyEvent { keyEvent ->
                    if (keyEvent.type == KeyEventType.Unknown) {
                        return@onPreviewKeyEvent true
                    }
                    if (keyEvent.type == KeyEventType.KeyUp) {
                        if (keyEvent.key == keyThatOpenedDialog) {
                            keyThatOpenedDialog = null
                            return@onPreviewKeyEvent true
                        }
                        val allKeys = groups.flatMap { it.keys }
                        val matchingKey = allKeys.find { keyEvent.key == it.key }
                        if (matchingKey != null) {
                            matchingKey.callback()
                            if (!matchingKey.keepOpen) {
                                closeDialogWithId(id)
                            }
                            return@onPreviewKeyEvent true
                        }
                    }

                    if (keyEvent.type == KeyEventType.KeyDown) {
                        if (keyEvent.key == Key.Escape) {
                            closeDialogWithId(id)
                            return@onPreviewKeyEvent true
                        }
                        if (keyEvent.isQuestionMark()) {
                            showTransientHelpDialog(groups)
                            return@onPreviewKeyEvent true
                        }

                        val allKeys = groups.flatMap { it.keys }
                        val isBoundKeyForAction = allKeys.any { keyEvent.key == it.key }
                        if (!isBoundKeyForAction && !keyEvent.key.isModifierKey()) {
                            val keyToDisplayString = keyEvent.key.toDisplayString()
                            if (!keyToDisplayString.contains("unknown keycode")) {
                                showErrorNotification(
                                    "Key is not bound: $keyToDisplayString",
                                    durationMillis = 1000
                                )
                            }
                        }
                    }
                    return@onPreviewKeyEvent true
                },
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DummyFocusable()

            // Display subtitle at the top if there are multiple groups
            if (!isSingleGroup && subTitle != null) {
                Text(
                    text = subTitle,
                    color = textColor,
                    fontSize = Styling.subTitleFontSize,
                    modifier = Modifier
                        .widthIn(max = subTitleMaxWidth)
                        .padding(bottom = Styling.titleOrSubTitleBottomPadding)
                )
            }

            // Display title centered if there's only one group
            if (isSingleGroup && groups[0].name.isNotEmpty()) {
                Text(
                    text = groups[0].name,
                    color = textColor,
                    fontSize = Styling.groupNameFontSize,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier
                        .widthIn(max = subTitleMaxWidth)
                        // Less padding if subtitle follows
                        .padding(bottom = if (subTitle == null) Styling.titleOrSubTitleBottomPadding else 4.dp)
                )

                // Display subtitle below the title for single group
                if (subTitle != null) {
                    Text(
                        text = subTitle,
                        color = textColor,
                        fontSize = Styling.subTitleFontSize,
                        modifier = Modifier
                            .widthIn(max = subTitleMaxWidth)
                            .padding(bottom = Styling.titleOrSubTitleBottomPadding)
                    )
                }
            }

            Box {
                Row(
                    modifier = Modifier.wrapContentWidth(),
                    horizontalArrangement = Arrangement.spacedBy(24.dp, Alignment.CenterHorizontally)
                ) {
                    groups.forEach { group ->
                        Column {
                            // Only show group name in the column if there are multiple groups
                            if (!isSingleGroup && group.name.isNotEmpty()) {
                                Text(
                                    text = group.name,
                                    color = textColor,
                                    fontSize = Styling.groupNameFontSize,
                                    fontWeight = FontWeight.Medium,
                                    modifier = Modifier.padding(bottom = 4.dp)
                                )
                            }

                            group.keys.forEach { key ->
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(vertical = 4.dp)
                                ) {
                                    KeyBadge(key.key)
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = key.text,
                                        color = GlobalStyling.getTextColor(),
                                        fontSize = Styling.keyDescriptionFontSize
                                    )
                                }
                            }
                        }
                    }
                }
                DialogHelpIcon(xOffset = 20.dp, yOffset = 16.dp)
            }
        }
    }

    LaunchedEffect(Unit) {
        dialogFocusRequester.requestFocus()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == id) {
            dialogFocusRequester.requestFocus()
        }
    }
}