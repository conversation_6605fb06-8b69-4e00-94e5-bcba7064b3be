package dima.treeSitter

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.settings
import java.io.File

data class TreeSitterCliGraphvizResult(
    val graphvizCode: String? = null,
    /**
     * Always check first, if this is set. If it is set, [graphvizCode] is empty.
     */
    val error: String? = null,
)

object TreeSitterGraphviz {

    suspend fun startTreeSitterCliAndGetGraphvizCode(
        code: String,
        language: TreeSitterLanguage
    ): TreeSitterCliGraphvizResult {
        val p = process(
            File(settings.cli.treeSitterCliRootDirectory, "target/release/tree-sitter-cli-via-rust").absolutePath,
            "--language", language.treeSitterDirectorySuffix,
            "--code", code,
            "--graphviz-only",
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        val output = p.output.joinToString("\n").trim()
        if (p.resultCode != 0) {
            return TreeSitterCliGraphvizResult(error = output)
        }
        return TreeSitterCliGraphvizResult(graphvizCode = output)
    }

}

