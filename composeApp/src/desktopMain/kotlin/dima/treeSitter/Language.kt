package dima.treeSitter

enum class TreeSitterLanguage(
    val markdownCodeFenceLanguages: List<String>,
    /**
     * This is mainly taken from running: `tree-sitter dump-languages`
     * After using `tree-sitter build` in the tree-sitter grammar directories.
     */
    val fileNameExtensions: List<String>,
    /**
     * This is the suffix for the directory like "kotlin". The full directory name would be: "tree-sitter-kotlin"
     */
    val treeSitterDirectorySuffix: String,
) {
    /**
     * The language for build.gradle files.
     */
    Groovy(
        markdownCodeFenceLanguages = listOf("gradle", "groovy"),
        fileNameExtensions = listOf("gradle"),
        treeSitterDirectorySuffix = "groovy"
    ),
    Kotlin(
        markdownCodeFenceLanguages = listOf("kotlin", "kt", "kts", "gradle.kts"),
        fileNameExtensions = listOf("kt", "kts"),
        treeSitterDirectorySuffix = "kotlin"
    ),
    JSO<PERSON>(
        markdownCodeFenceLanguages = listOf("json"),
        fileNameExtensions = listOf("json"),
        treeSitterDirectorySuffix = "json"
    ),
    HTML(
        markdownCodeFenceLanguages = listOf("html"),
        fileNameExtensions = listOf("html", "htm"),
        treeSitterDirectorySuffix = "html"
    ),
    PHP(
        markdownCodeFenceLanguages = listOf("php"),
        fileNameExtensions = listOf("php"),
        treeSitterDirectorySuffix = "php"
    ),
    Bash(
        markdownCodeFenceLanguages = listOf("bash", "shell", "sh"),
        fileNameExtensions = listOf("bash", "shell", "sh"),
        treeSitterDirectorySuffix = "bash"
    ),
    Dockerfile(
        markdownCodeFenceLanguages = listOf("dockerfile", "docker"),
        fileNameExtensions = listOf("dockerfile"),
        treeSitterDirectorySuffix = "dockerfile"
    ),
    Python(
        markdownCodeFenceLanguages = listOf("python", "py"),
        fileNameExtensions = listOf("py"),
        treeSitterDirectorySuffix = "python"
    ),
    Java(
        markdownCodeFenceLanguages = listOf("java"),
        fileNameExtensions = listOf("java"),
        treeSitterDirectorySuffix = "java"
    ),
    Rust(
        markdownCodeFenceLanguages = listOf("rust"),
        fileNameExtensions = listOf("rs"),
        treeSitterDirectorySuffix = "rust"
    ),
    Toml(
        markdownCodeFenceLanguages = listOf("toml"),
        fileNameExtensions = listOf("toml"),
        treeSitterDirectorySuffix = "toml"
    ),
    CSS(
        markdownCodeFenceLanguages = listOf("css"),
        fileNameExtensions = listOf("css"),
        treeSitterDirectorySuffix = "css"
    ),
    JavaScript(
        markdownCodeFenceLanguages = listOf("javascript", "js"),
        fileNameExtensions = listOf("js", "mjs"),
        treeSitterDirectorySuffix = "javascript"
    );

    fun matchesExtension(lowerExtension: String): Boolean {
        return fileNameExtensions.contains(lowerExtension)
    }

    companion object {

        fun getForMarkdown(language: String): TreeSitterLanguage? {
            val lowerLanguage = language.lowercase()
            return entries.find { it.markdownCodeFenceLanguages.contains(lowerLanguage) }
        }
    }
}

