package dima.treeSitter.app

import com.github.pgreze.process.InputSource
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.dired.openFileInDired
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dialogs.completion.openCompletionDialog
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.os.homeWithoutSlash
import dima.treeSitter.TreeSitter
import dima.treeSitter.TreeSitterDebug
import dima.treeSitter.TreeSitterGraphviz
import dima.utils.FileSystem
import dima.utils.truncateWithEllipsis
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.io.IOException
import java.io.File

internal fun openCopyPartsDialog(highlightsScmFileContent: String?) {
    val language = TreeSitter.getCurrentLanguageAsClass()
    val debugEntries = TreeSitterDebug.debugEntries
    val newCode = TreeSitter.patchCodeToHighlight(GlobalState.treeSitter.textToHighlight, language)
    val candidates = buildList {
        add("DotGraph")
        add("DotGraph as PNG")
        if (debugEntries.isNotEmpty()) {
            add("Highlighted entries")
        }
        if (highlightsScmFileContent != null) {
            add("highlights.scm file content")
        }
        if (failedCaptures.isNotEmpty()) {
            add("Failed captures")
        }
    }
    if (candidates.isEmpty()) {
        showErrorNotification("No candidates for the copy parts dialog")
        return
    }
    openCompletionDialog("Copy", candidates, hideCopyCmdActionInBottomBar = true) {
        when (it.text) {
            "highlights.scm file content" -> {
                copyToClipboard(highlightsScmFileContent!!)
            }

            "Highlighted entries" -> {
                val joined = debugEntries.joinToString("\n") { s ->
                    s.captureName + "    " + s.nodeContent + " --> " + s.startByte + " to " + s.endByte
                }
                copyToClipboard(joined)
            }

            "DotGraph as PNG" -> {
                CoroutineScope(Dispatchers.IO).launch {
                    val notification = showLoadingNotification("Generating tree-sitter PNG graph...")
                    val treeSitterProcess =
                        TreeSitterGraphviz.startTreeSitterCliAndGetGraphvizCode(newCode, language)
                    if (treeSitterProcess.error != null) {
                        notification.toError("tree-sitter CLI failed", treeSitterProcess.error)
                        return@launch
                    }
                    val newOutputFile = FileSystem.getUniqueFileByFileName(
                        File(homeWithoutSlash, "Downloads"),
                        "dot.png"
                    )
                    val p = try {
                        process(
                            "dot",
                            "-Tpng",
                            "-o", newOutputFile.absolutePath,
                            stdin = InputSource.fromString(treeSitterProcess.graphvizCode!!),
                            stdout = Redirect.CAPTURE,
                            stderr = Redirect.CAPTURE,
                        )
                    } catch (_: IOException) {
                        // Cannot run program "dot": error=2, No such file or directory
                        notification.toError("Can not find application \"dot\" in PATH")
                        null
                    }
                    if (p == null) {
                        return@launch
                    }
                    notification.toInfo("Converted graph to PNG", p.output.joinToString("\n"))
                    newOutputFile.openFileInDired()
                }
            }

            "DotGraph" -> {
                CoroutineScope(Dispatchers.IO).launch {
                    val notification = showLoadingNotification("Generating tree-sitter graph...")
                    val p = TreeSitterGraphviz.startTreeSitterCliAndGetGraphvizCode(newCode, language)
                    if (p.error != null) {
                        notification.toError("tree-sitter CLI failed", p.error)
                        return@launch
                    }
                    val code = p.graphvizCode!!
                    notification.toInfo(
                        "Copied tree-sitter graph to clipboard",
                        message = code.truncateWithEllipsis(200),
                        durationMillis = 2000
                    )
                    copyToClipboard(code, showNotification = false)
                }
            }

            "Failed captures" -> {
                val text = failedCaptures.joinToString("\n") { s ->
                    "Capture name (${s.captureName}) with content (${s.content}) is not mapped to any color"
                }
                copyToClipboard(text)
            }

            else -> {
                showErrorNotification("Unhandled $it")
            }
        }
    }
}
