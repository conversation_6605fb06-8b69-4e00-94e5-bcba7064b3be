package dima.treeSitter.app

import GlobalEvent
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.apps.notifications.showNotification
import dialogs
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.treeSitter.IntellijDarculaTheme
import dima.treeSitter.TreeSitter
import dima.treeSitter.TreeSitterColors
import dima.treeSitter.TreeSitterColors.VOID_CAPTURE_NAME
import dima.treeSitter.TreeSitterDebug
import dima.utils.AppKey
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import dima.utils.handleAppMap
import globalEvent
import isTextFieldFocused
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun TreeSitterApp() {
    var highlights by remember { mutableStateOf(emptyList<TreeSitter.Highlight>()) }
    var highlightsScmFileContent by remember { mutableStateOf<String?>(null) }
    val appFocusRequester = remember { FocusRequester() }
    val queryFocusRequester = remember { FocusRequester() }
    val previewScrollState = rememberScrollState()
    val previewScrollVelocity = remember { ScrollVelocity(previewScrollState) }
    var queryError by remember { mutableStateOf<String?>(null) }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = GlobalState.treeSitter.query,
                selection = when {
                    GlobalState.treeSitter.query.isEmpty() -> TextRange.Zero
                    else -> TextRange(GlobalState.treeSitter.query.length, GlobalState.treeSitter.query.length)
                }
            )
        )
    }
    val contentFocusRequester = remember { FocusRequester() }
    var content by remember { mutableStateOf(GlobalState.treeSitter.textToHighlight) }
    var contentState by remember {
        mutableStateOf(
            TextFieldValue(
                text = content,
                selection = when {
                    content.isEmpty() -> TextRange.Zero
                    else -> TextRange(content.length, content.length)
                }
            )
        )
    }

    remember {
        appKeys = listOf(
            AppKey(Key.Escape, "Leave text fields") { appFocusRequester.requestFocus() },
            AppKey(Key.L, "Change language ") { changeLanguage() },
            // use KeyEventType.KeyUp to not insert the key into the text field
            AppKey(Key.Q, "Focus query", onKeyUp = { queryFocusRequester.requestFocus() }),
            // use KeyEventType.KeyUp to not insert the key into the text field
            AppKey(Key.C, "Focus content", onKeyUp = { contentFocusRequester.requestFocus() }),
            AppKey(
                Key.C, "Scroll preview up",
                isCtrl = true,
                onKeyUp = { previewScrollVelocity.onKeyReleased() },
                onKeyDown = { previewScrollVelocity.onScrollUpKeyPressed() }
            ),
            AppKey(
                Key.T, "Scroll preview down",
                isCtrl = true,
                onKeyUp = { previewScrollVelocity.onKeyReleased() },
                onKeyDown = { previewScrollVelocity.onScrollDownKeyPressed() }
            ),
            AppKey(
                Key.M, "Scroll preview down more",
                isCtrl = true,
                onKeyUp = { previewScrollVelocity.onKeyReleased() },
                onKeyDown = { previewScrollVelocity.onScrollDownMoreKeyPressed() }
            ),
            AppKey(
                Key.V, "Scroll preview up more",
                isCtrl = true,
                onKeyUp = { previewScrollVelocity.onKeyReleased() },
                onKeyDown = { previewScrollVelocity.onScrollUpMoreKeyPressed() }
            ),
            AppKey(Key.J, "Copy parts") { openCopyPartsDialog(highlightsScmFileContent) },
        )
    }

    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxSize()
            .focusable()
            .focusRequester(appFocusRequester)
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    if (it.key == Key.Escape && it.type == KeyEventType.KeyDown) {
                        appFocusRequester.requestFocus()
                        return@onPreviewKeyEvent true
                    }
                    return@onPreviewKeyEvent false
                }
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        Ui(
            queryState = queryState,
            queryFocusRequester = queryFocusRequester,
            queryError = queryError,
            contentState = contentState,
            contentFocusRequester = contentFocusRequester,
            previewScrollState = previewScrollState,
            highlights = highlights,
            onQueryStateChange = { queryState = it },
            onContentStateChange = { contentState = it },
            onContentChange = { content = it }
        )
    }

    suspend fun reload() {
        TreeSitterDebug.debugEntries.clear()
        val language = TreeSitter.getCurrentLanguageAsClass()
        highlightsScmFileContent = TreeSitter.getHighlightsSchemeFileContents(language)
        val newHighlights = mutableListOf<TreeSitter.Highlight>()
        failedCaptures = mutableListOf()
        val newText = TreeSitter.patchCodeToHighlight(GlobalState.treeSitter.textToHighlight, language)
        val cliResult = TreeSitter.startTreeSitterCliAndExtractHighlights(newText, language, highlightsScmFileContent!!)
        if (cliResult.error != null) {
            highlights = emptyList()
            queryError = cliResult.error
            return
        }
        cliResult.highlights.forEach {
            val languageString = language.toString().lowercase()
            val ignored = TreeSitterColors.ignoredCaptureNamesForColorMapping[languageString]
            if (ignored != null && ignored.contains(it.captureName)) {
                return@forEach
            }
            val color = if (it.captureName == VOID_CAPTURE_NAME) {
                IntellijDarculaTheme.textColor
            } else {
                TreeSitter.getColorMappingValueByCaptureName(languageString, it.captureName)
            }
            if (color == null) {
                failedCaptures.add(FailedCapture(it.captureName, it.content))
                newHighlights.add(
                    TreeSitter.Highlight(
                        TreeSitterColors.noCaptureGroupColor,
                        it.startByte,
                        it.endByte,
                        isBackgroundColor = true
                    )
                )
            } else {
                val highlight = TreeSitter.mapCliHighlightToColorHighlight(
                    it,
                    color,
                    language,
                    newHighlights,
                    shouldAppendToDebugEntries = true
                )
                if (highlight != null) {
                    newHighlights.add(highlight)
                }
            }
        }
        val trimmedQuery = GlobalState.treeSitter.query.trim()
        if (trimmedQuery.isNotEmpty()) {
            val customQueryCliResult =
                TreeSitter.startTreeSitterCliAndExtractHighlights(newText, language, trimmedQuery)
            if (customQueryCliResult.error != null) {
                highlights = emptyList()
                queryError = customQueryCliResult.error
                return
            }
            customQueryCliResult.highlights.forEach {
                newHighlights.add(
                    TreeSitter.Highlight(
                        TreeSitterColors.queryHit,
                        it.startByte,
                        it.endByte,
                        isBackgroundColor = true
                    )
                )
            }
        }
        queryError = null
        highlights = newHighlights
    }

    LaunchedEffect(Unit) {
        contentFocusRequester.requestFocus()
        previewScrollVelocity.loopForeverAndTick()
    }
    LaunchedEffect(Unit, GlobalState.treeSitter) {
        CoroutineScope(Dispatchers.IO).launch {
            reload()
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(content) {
        GlobalState.treeSitter = GlobalState.treeSitter.copy(textToHighlight = content)
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.Reload -> {
                showNotification("Reloading with new highlights.scm contents", durationMillis = 500L)
                reload()
            }

            GlobalEvent.ScrollToTop -> previewScrollVelocity.scrollToTop()
            GlobalEvent.ScrollToBottom -> previewScrollVelocity.scrollToBottom()
            else -> {}
        }
    }
}