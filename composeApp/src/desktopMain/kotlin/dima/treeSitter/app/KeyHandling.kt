package dima.treeSitter.app

import dima.dialogs.completion.openCompletionDialog
import dima.globalState.GlobalState
import dima.treeSitter.TreeSitterLanguage

internal fun changeLanguage() {
    val candidates = TreeSitterLanguage.entries.map {
        it.treeSitterDirectorySuffix
    }
    openCompletionDialog("Pick language", candidates) {
        GlobalState.treeSitter = GlobalState.treeSitter.copy(language = it.text)
    }
}
