package dima.treeSitter.app

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.treeSitter.IntellijDarculaTheme
import dima.treeSitter.TreeSitter
import dima.utils.TextFieldSearchIcon
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleForDarkBackground
import isTextFieldFocused

@Composable
internal fun RowScope.Ui(
    queryState: TextFieldValue,
    onQueryStateChange: (TextFieldValue) -> Unit,
    queryFocusRequester: FocusRequester,
    queryError: String?,
    contentState: TextFieldValue,
    onContentStateChange: (TextFieldValue) -> Unit,
    onContentChange: (String) -> Unit,
    contentFocusRequester: FocusRequester,
    previewScrollState: ScrollState,
    highlights: List<TreeSitter.Highlight>
) {
    val language = TreeSitter.getCurrentLanguageAsClass()
    Column(
        verticalArrangement = Arrangement.spacedBy(20.dp),
        modifier = Modifier
            .focusable()
            .padding(start = 12.dp, top = 12.dp, bottom = 12.dp)
            .weight(0.5f)
    ) {
        Text(
            "Language: $language",
            modifier = Modifier
                .padding(20.dp)
                .clickableWithoutBackgroundRipple {
                    changeLanguage()
                }
        )
        TextField(
            value = queryState,
            onValueChange = {
                onQueryStateChange(it)
                GlobalState.treeSitter = GlobalState.treeSitter.copy(query = it.text)
            },
            label = { Text("Query") },
            colors = TextFieldDefaults.textFieldColors(backgroundColor = TailwindCssColors.transparent),
            leadingIcon = TextFieldSearchIcon(),
            modifier = Modifier
                .onFocusChanged {
                    isTextFieldFocused = it.isFocused
                }
                .focusRequester(queryFocusRequester)
                .fillMaxWidth()
        )
        if (queryError != null) {
            Text(
                queryError,
                color = TailwindCssColors.red600,
                fontSize = 16.sp,
                modifier = Modifier
            )
        }
        TextField(
            value = contentState,
            onValueChange = {
                onContentStateChange(it)
                onContentChange(it.text)
            },
            label = { Text("Content") },
            colors = TextFieldDefaults.textFieldColors(backgroundColor = TailwindCssColors.transparent),
            modifier = Modifier
                .onKeyEvent {
                    return@onKeyEvent it.key == Key.Enter
                }
                .focusable()
                .focusRequester(contentFocusRequester)
                .verticalScroll(rememberScrollState())
                .onFocusChanged {
                    isTextFieldFocused = it.isFocused
                }
                .fillMaxSize()
        )
    }
    Box(
        modifier = Modifier
            .weight(0.5f)
            .fillMaxSize()
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(previewScrollState)
                .background(IntellijDarculaTheme.background)
        ) {
            SelectionContainer {
                val newText = TreeSitter.patchCodeToHighlight(GlobalState.treeSitter.textToHighlight, language)
                if (highlights.isEmpty()) {
                    Text(
                        newText,
                        color = IntellijDarculaTheme.textColor,
                        fontSize = 16.sp,
                        modifier = Modifier
                            .padding(vertical = 12.dp)
                            .padding(12.dp)
                    )
                } else {
                    val annotated = buildAnnotatedString {
                        append(newText)
                        highlights.forEach {
                            if (it.end > newText.length) {
                                return@forEach
                            }
                            if (it.isBackgroundColor) {
                                addStyle(
                                    SpanStyle(background = it.color.copy(alpha = 1f)),
                                    it.start,
                                    it.end
                                )
                            } else {
                                addStyle(
                                    SpanStyle(color = it.color.copy(alpha = 1f)),
                                    it.start,
                                    it.end
                                )
                            }
                        }
                    }
                    Text(
                        annotated,
                        color = IntellijDarculaTheme.textColor,
                        fontSize = 16.sp,
                        modifier = Modifier
                            .padding(vertical = 12.dp)
                            .padding(12.dp)
                    )
                }
            }
        }
        VerticalScrollbar(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .fillMaxHeight(),
            style = scrollbarStyleForDarkBackground(),
            adapter = rememberScrollbarAdapter(scrollState = previewScrollState)
        )
    }
}
