package dima.treeSitter

import androidx.compose.ui.graphics.Color
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.settings
import dima.treeSitter.TreeSitterColors.VOID_CAPTURE_NAME
import dima.treeSitter.TreeSitterColors.colorMapping
import dima.utils.getStringLengthRangesFromByteRange
import java.io.File

object TreeSitter {

    val defaultAppLanguageName = TreeSitterLanguage.JSON.treeSitterDirectorySuffix

    data class CliHighlight(
        val captureName: String,
        val startByte: Int,
        val endByte: Int,
        /**
         * The matched string of the capture.
         */
        val content: String,
    )

    data class Highlight(
        val color: Color,
        /**
         * The start position for AnnotatedString's addStyle(). This is the same range as String.length().
         */
        val start: Int,
        /**
         * The end position for AnnotatedString's addStyle(). This is the same range as String.length().
         */
        val end: Int,
        val isBackgroundColor: Boolean = false,
    )

    fun patchCodeToHighlight(code: String, language: TreeSitterLanguage): String {
        if (language == TreeSitterLanguage.PHP) {
            val trimmed = code.trim()
            // append starting PHP tag so the highlighting works at all
            if (!trimmed.startsWith("<?")) {
                return "<? $trimmed"
            }
            return code
        }
        return code
    }

    fun getCurrentLanguageAsClass(): TreeSitterLanguage {
        return try {
            TreeSitterLanguage.entries.find { it.treeSitterDirectorySuffix == GlobalState.treeSitter.language }!!
        } catch (_: Exception) {
            TreeSitterLanguage.JSON
        }
    }

    /**
     * @return can return an empty string, when the queries/highlights.scm file does not exist
     */
    fun getHighlightsSchemeFileContents(language: TreeSitterLanguage): String {
        val file = File(
            File(
                settings.cli.treeSitterCliRootDirectory,
                "tree-sitter-grammars/tree-sitter-${language.treeSitterDirectorySuffix}"
            ),
            "queries/highlights.scm"
        )
        if (!file.exists()) {
            return ""
        }
        return file.readText()
    }

    /**
     * @returns null when  the color map does not contain the capture name in the given language and the generic map
     */
    fun getColorMappingValueByCaptureName(language: String, captureName: String): Color? {
        val lowerLanguage = language.lowercase()

        fun getFromMap(map: Map<Color, List<String>>): Color? {
            return map.firstNotNullOfOrNull {
                if (it.value.contains(captureName)) {
                    it.key
                } else {
                    null
                }
            }
        }

        val color = if (colorMapping.containsKey(lowerLanguage)) {
            getFromMap(colorMapping[lowerLanguage]!!) ?: getFromMap(colorMapping[""]!!)
        } else {
            getFromMap(colorMapping[""]!!)
        }
        return color
    }

    suspend fun highlightCode(
        code: String,
        language: TreeSitterLanguage,
        shouldAppendToDebugEntries: Boolean = false
    ): List<Highlight> {
        val highlightsFileContent = getHighlightsSchemeFileContents(language)
        val newHighlights = mutableListOf<Highlight>()
        val cliResult = startTreeSitterCliAndExtractHighlights(code, language, highlightsFileContent)
        if (cliResult.error != null) {
            showErrorNotification("tree-sitter error", cliResult.error)
            return newHighlights
        }
        cliResult.highlights.forEach {
            val languageString = language.toString().lowercase()
            val ignored = TreeSitterColors.ignoredCaptureNamesForColorMapping[languageString]
            if (ignored != null && ignored.contains(it.captureName)) {
                return@forEach
            }
            val color = if (it.captureName == VOID_CAPTURE_NAME) {
                IntellijDarculaTheme.textColor
            } else {
                getColorMappingValueByCaptureName(languageString, it.captureName)
            }
            if (color == null) {
                // use println() here since this function is used in the Markdown renderer or file previews, so it is not intrusive
                println("Capture name is not mapped to color : " + it.captureName + "   " + it.content)
                newHighlights.add(
                    Highlight(
                        TailwindCssColors.red200,
                        it.startByte,
                        it.endByte
                    )
                )
            } else {
                val highlight = mapCliHighlightToColorHighlight(
                    it,
                    color,
                    language,
                    newHighlights,
                    shouldAppendToDebugEntries
                )
                if (highlight != null) {
                    newHighlights.add(highlight)
                }
            }
        }
        return newHighlights
    }

    /**
     * @param language depending on the language, a different algorithm is used to allow coloring nested values
     *   where the parent colors the entire area already like " ${...} "
     * @param highlights the previous highlights for the algorithm to override color or not
     */
    fun mapCliHighlightToColorHighlight(
        cliHighlight: CliHighlight,
        captureColor: Color,
        language: TreeSitterLanguage,
        highlights: List<Highlight>,
        shouldAppendToDebugEntries: Boolean = false
    ): Highlight? {
        val isRustBlockComment =
            (language == TreeSitterLanguage.Rust && cliHighlight.captureName == "comment.documentation")
        // set this to true when you want an inner capture group to override the color
        val useSimple = (
                (language == TreeSitterLanguage.JavaScript && cliHighlight.captureName == "embedded") ||
                        (language == TreeSitterLanguage.Bash && cliHighlight.captureName == "embedded") ||
                        (language == TreeSitterLanguage.Python && cliHighlight.captureName == "embedded") ||
                        (language == TreeSitterLanguage.Java && cliHighlight.captureName == "string.escape") ||
                        (language == TreeSitterLanguage.CSS && cliHighlight.captureName == "unit") ||
                        isRustBlockComment
                )
        if (useSimple) {
            if (shouldAppendToDebugEntries) {
                TreeSitterDebug.debugEntries.add(
                    TreeSitterDebug.Entry(
                        cliHighlight.captureName,
                        cliHighlight.content,
                        cliHighlight.startByte,
                        cliHighlight.endByte
                    )
                )
            }
            val alreadyColored = if (isRustBlockComment) {
                // set false because the 'comment' capture name has the exact byte range as 'comment.documentation'
                false
            } else {
                // Do not color the exact same position multiple times.
                // This happens for JSON key strings which would get the incorrect color
                highlights.any {
                    it.start == cliHighlight.startByte && it.end == cliHighlight.endByte
                }
            }
            if (!alreadyColored) {
                return Highlight(captureColor, cliHighlight.startByte, cliHighlight.endByte)
            }
        } else {
            if (shouldAppendToDebugEntries) {
                TreeSitterDebug.debugEntries.add(
                    TreeSitterDebug.Entry(
                        cliHighlight.captureName,
                        cliHighlight.content,
                        cliHighlight.startByte,
                        cliHighlight.endByte
                    )
                )
            }
            val overlappingHighlights = highlights.filter {
                it.start < cliHighlight.endByte && it.end > cliHighlight.startByte
            }
            if (overlappingHighlights.isEmpty()) {
                return Highlight(captureColor, cliHighlight.startByte, cliHighlight.endByte)
            }
            if (overlappingHighlights.any { it.start <= cliHighlight.startByte && it.end >= cliHighlight.endByte }) {
                return null
            }
            var adjustedStart = cliHighlight.startByte
            var adjustedEnd = cliHighlight.endByte
            for (existingHighlight in overlappingHighlights) {
                if (existingHighlight.start <= adjustedStart && existingHighlight.end > adjustedStart) {
                    adjustedStart = existingHighlight.end
                }
                if (existingHighlight.end >= adjustedEnd && existingHighlight.start < adjustedEnd) {
                    adjustedEnd = existingHighlight.start
                }
                if (adjustedStart >= adjustedEnd) {
                    adjustedStart = cliHighlight.startByte
                    adjustedEnd = cliHighlight.endByte
                    continue
                }
            }
            if (adjustedStart < adjustedEnd) {
                return Highlight(
                    captureColor,
                    adjustedStart,
                    adjustedEnd
                )
            }
        }
        return null
    }

    data class TreeSitterCliHighlightsResult(
        val highlights: List<CliHighlight> = emptyList(),
        /**
         * Always check first, if this is set. If it is set, [highlights] is empty.
         */
        val error: String? = null,
    )

    suspend fun startTreeSitterCliAndExtractHighlights(
        code: String,
        language: TreeSitterLanguage,
        highlights: String
    ): TreeSitterCliHighlightsResult {
        val cachedResult = highlightCache.find { entry ->
            entry.code == code && entry.language == language && entry.highlights == highlights
        }
        if (cachedResult != null) {
            return cachedResult.result
        }
        val p = process(
            File(settings.cli.treeSitterCliRootDirectory, "target/release/tree-sitter-cli-via-rust").absolutePath,
            "--language", language.treeSitterDirectorySuffix,
            "--code", code,
            "--highlights", highlights,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        if (p.resultCode != 0) {
            val error = p.output.joinToString("\n").trim()
            val result = TreeSitterCliHighlightsResult(error = error)
            highlightCache.add(HighlightCacheEntry(code, language, highlights, result))
            return result
        }
        val codeBytes = code.toByteArray(Charsets.UTF_8)
        val cliHighlights = p.output.map {
            val parts = it.split(" ")
            val startByte = parts[1].toInt()
            val endByte = parts[2].toInt()
            val range = code.getStringLengthRangesFromByteRange(startByte, endByte)
            CliHighlight(
                captureName = parts[0],
                startByte = range.first,
                endByte = range.second,
                content = codeBytes.slice(startByte..<endByte).toByteArray().toString(Charsets.UTF_8)
            )
        }
        val result = TreeSitterCliHighlightsResult(cliHighlights)
        highlightCache.add(HighlightCacheEntry(code, language, highlights, result))
        return result
    }

}