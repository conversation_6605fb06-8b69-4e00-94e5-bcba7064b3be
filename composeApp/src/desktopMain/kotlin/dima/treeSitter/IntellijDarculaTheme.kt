package dima.treeSitter

import androidx.compose.ui.graphics.Color

/**
 * Colors from the default IntelliJ Darcula theme.
 */
object IntellijDarculaTheme {
    val orange = Color(204, 120, 50)
    val yellow = Color(255, 198, 109)
    val lavender = Color(152, 118, 170)
    val blue = Color(70, 124, 218)
    val cyan = Color(78, 173, 229)
    val darkerCyan = Color(104, 151, 187)
    val green = Color(106, 135, 89)
    val docCommentGreen = Color(98, 151, 85)
    val brightGreen = Color(165, 194, 97)

    val visualBackground = Color(40, 64, 125)
    val textColor = Color(169, 183, 198)
    val background = Color(43, 43, 43)
}