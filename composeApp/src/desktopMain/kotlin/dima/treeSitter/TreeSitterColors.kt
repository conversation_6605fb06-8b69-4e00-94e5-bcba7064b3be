package dima.treeSitter

import androidx.compose.ui.graphics.Color
import dima.color.TailwindCssColors

object TreeSitterColors {
    /**
     * Hack to avoid coloring multiple times and voiding some excessive coloring.
     */
    const val VOID_CAPTURE_NAME = "void"

    val noCaptureGroupColor = TailwindCssColors.red600
    val queryHit = TailwindCssColors.green500

    /**
     * The listed capture names are ignored and are simply colored with the default color: [IntellijDarculaTheme.textColor]
     */
    val ignoredCaptureNamesForColorMapping = mapOf(
        "html" to listOf(
            "attribute",
        ),
        "css" to listOf(
            "punctuation.bracket",
            "property",
            "variable",
        ),
        "javascript" to listOf(
            "punctuation.bracket"
        ),
        "groovy" to listOf(
            "variable",
            "string.escape"
        ),
        "rust" to listOf(
            "punctuation.delimiter",
            "punctuation.bracket",
            "constructor",
            "operator",
            "type",
            "variable.parameter",
        ),
        "toml" to listOf(
            "punctuation.bracket",
            "punctuation.delimiter",
            "operator"
        ),
        "java" to listOf(
            "type",
            "variable",
            "constant"
        ),
        "python" to listOf(
            // almost everything is variable or constructor
            "variable",
            "constructor",
            "constant",
            "property",
            "function.builtin",
            // for the function return types
            "type"
        ),
        "bash" to listOf(
            // this is the $ in: ... -i "$i" ...
            "operator"
        ),
        "php" to listOf(
            "type"
        ),
        "kotlin" to listOf(
            "punctuation.delimiter",
            "type",
            "punctuation.bracket",
            "constructor",
            "function.builtin",
            "variable.builtin",
            "type.builtin",
            "variable",
            "operator",
            "parameter",
            "_class",
            "none" // I think this "foo@templateString"
        ),
    )

    val colorMapping = mapOf(
        "html" to mapOf(
            IntellijDarculaTheme.yellow to listOf(
                "punctuation.bracket",
                "tag"
            )
        ),
        "css" to mapOf(
            IntellijDarculaTheme.yellow to listOf(
                "tag",
                "attribute"
            ),
            IntellijDarculaTheme.orange to listOf(
                "punctuation.delimiter"
            ),
            IntellijDarculaTheme.brightGreen to listOf(
                "type",
                "unit",
            ),
            IntellijDarculaTheme.darkerCyan to listOf(
                "string.special"
            )
        ),
        "groovy" to mapOf(
            IntellijDarculaTheme.green to listOf(
                "type"
            )
        ),
        "rust" to mapOf(
            IntellijDarculaTheme.cyan to listOf(
                "function.macro"
            ),
            IntellijDarculaTheme.docCommentGreen to listOf(
                "comment.documentation"
            )
        ),
        "toml" to mapOf(
            IntellijDarculaTheme.orange to listOf(
                "property"
            )
        ),
        "java" to mapOf(
            IntellijDarculaTheme.orange to listOf(
                "string.escape"
            )
        ),
        "python" to mapOf(
            IntellijDarculaTheme.textColor to listOf(
                "embedded"
            )
        ),
        "bash" to mapOf(
            IntellijDarculaTheme.orange to listOf(
                "function"
            ),
            IntellijDarculaTheme.textColor to listOf(
                "embedded"
            )
        ),
        "php" to mapOf(
            IntellijDarculaTheme.yellow to listOf(
                "constructor"
            ),
            IntellijDarculaTheme.lavender to listOf(
                "operator",
                "variable",
                "variable.builtin",
            ),
            IntellijDarculaTheme.orange to listOf(
                "function.builtin"
            )
        ),
        "javascript" to mapOf(
            IntellijDarculaTheme.textColor to listOf(
                "embedded"
            ),
            IntellijDarculaTheme.yellow to listOf(
                "function",
                "method",
                "constructor",
            ),
            IntellijDarculaTheme.orange to listOf(
                "variable.builtin",
                "function.builtin",
                "punctuation.delimiter",
            ),
            IntellijDarculaTheme.green to listOf(
                "type",
            ),
            IntellijDarculaTheme.lavender to listOf(
                "variable",
            ),
        ),
        // this is for all languages, applied last
        "" to mapOf(
            IntellijDarculaTheme.lavender to listOf(
                "string.special.key", // JSON keys
                "constant",
                "property",
            ),
            Color(106, 136, 89) to listOf(
                "string",
                "string.regex"
            ),
            Color(104, 151, 187) to listOf(
                "number",
                "float"
            ),
            IntellijDarculaTheme.yellow to listOf(
                "_function",
                "function",
                "function.method",
            ),
            IntellijDarculaTheme.orange to listOf(
                "tag",
                "constant.builtin",
                "escape",
                "namespace",
                "operator",
                "type.builtin",
                "exception",
                "keyword",
                "keyword.function",
                "keyword.return",
                "boolean",
                "variable.builtin",
                "conditional",
                "punctuation.special",
                "repeat",
                "include"
            ),
            Color(128, 128, 128) to listOf("comment"),
            Color(187, 181, 41) to listOf("attribute"),
            IntellijDarculaTheme.blue to listOf("label"),
            Color(98, 151, 85) to listOf(
                "comment.doc"
            ),
        )
    )

}
