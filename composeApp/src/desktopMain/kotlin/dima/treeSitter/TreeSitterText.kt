package dima.treeSitter

import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @param language when null, no syntax highlighting is applied
 * @param modifier the modifier which is applied to the only used [Text] composable without any modifications
 */
@Composable
fun TreeSitterText(
    text: String,
    language: TreeSitterLanguage? = null,
    shouldAppendToDebugEntries: Boolean = false,
    modifier: Modifier = Modifier
        .background(IntellijDarculaTheme.background, RoundedCornerShape(8.dp))
) {
    var annotatedString by remember {
        mutableStateOf(buildAnnotatedString {
            append(text)
        })
    }

    Text(
        annotatedString,
        color = IntellijDarculaTheme.textColor,
        fontSize = 16.sp,
        modifier = modifier
    )

    LaunchedEffect(Unit, text, language) {
        if (language == null) {
            annotatedString = buildAnnotatedString {
                append(text)
            }
            return@LaunchedEffect
        }
        CoroutineScope(Dispatchers.IO).launch {
            val newText = TreeSitter.patchCodeToHighlight(text, language)
            val highlights = TreeSitter.highlightCode(newText, language, shouldAppendToDebugEntries)
            annotatedString = buildAnnotatedString {
                append(newText)
                highlights.forEach {
                    val end = it.end.coerceAtMost(newText.length)
                    if (it.start >= end) {
                        return@forEach
                    }
                    addStyle(
                        SpanStyle(color = it.color.copy(alpha = 1f)),
                        it.start,
                        end
                    )
                }
            }
        }
    }
}
