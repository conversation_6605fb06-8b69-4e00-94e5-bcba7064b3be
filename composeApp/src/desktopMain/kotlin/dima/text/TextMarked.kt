package dima.text

import GlobalStyling
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import dev.snipme.highlights.internal.indicesOf
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

val textMarkedLightSpanStyle = SpanStyle(
    color = TailwindCssColors.orange700,
    background = TailwindCssColors.orange100,
)

val textMarkedDarkSpanStyle = SpanStyle(
    color = TailwindCssColors.white,
    background = TailwindCssColors.orange700,
)

val textMarkedSpanStyleSecondary = SpanStyle(
    color = TailwindCssColors.black,
    background = TailwindCssColors.orange100,
)

private data class Part(val start: Int, val end: Int, val mark: Boolean)
private data class HighlightedChar(val char: Char, val mark: Boolean)

private fun highlightChars(text: String, highlights: List<String>): List<HighlightedChar> {
    val highlightedChars = mutableListOf<HighlightedChar>()
    text.forEach {
        highlightedChars.add(HighlightedChar(it, false))
    }
    highlights.forEach {
        var startIndex = 0
        while (true) {
            val index = text.indexOf(it, startIndex)
            if (index == -1) {
                break
            }
            for (i in index..<index + it.length) {
                highlightedChars[i] = HighlightedChar(text[i], true)
            }
            startIndex = index + it.length
        }
    }
    return highlightedChars
}

private fun convertToRanges(highlightedChars: List<HighlightedChar>): List<Part> {
    val ranges = mutableListOf<Part>()
    var currentRangeStart = 0
    var currentRangeHighlight = highlightedChars.firstOrNull()?.mark ?: false
    highlightedChars.forEachIndexed { index, char ->
        if (char.mark != currentRangeHighlight) {
            ranges.add(Part(currentRangeStart, index, currentRangeHighlight))
            currentRangeStart = index
            currentRangeHighlight = char.mark
        }
    }
    ranges.add(Part(currentRangeStart, highlightedChars.size, currentRangeHighlight))
    return ranges
}

/**
 * Mark text case-insensitively with optional highlighting of newlines.
 *
 * @param searchQuery the query is split on spaces and all parts are marked
 */
@Composable
fun TextMarked(
    text: String,
    searchQuery: String,
    fontWeight: FontWeight? = null,
    maxLines: Int = Int.MAX_VALUE,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    highlightNewLines: Boolean = false,
    overflow: TextOverflow = TextOverflow.Clip,
    modifier: Modifier = Modifier
) {

    @Composable
    fun insertTextWithMarkedNewLines() {
        if (highlightNewLines) {
            val hasNewline = text.contains("\n")
            if (!hasNewline) {
                Text(
                    text,
                    color = color,
                    fontWeight = fontWeight,
                    fontSize = fontSize,
                    maxLines = maxLines,
                    overflow = overflow,
                    modifier = modifier
                )
                return
            }
            val newLines = text.indicesOf("\n")
            val highlightedNewLinesText = buildAnnotatedString {
                var start = 0
                for (newLineIndex in newLines) {
                    append(text.substring(start, newLineIndex))
                    append(
                        AnnotatedString("↵", SpanStyle(color = GlobalStyling.getGrayColor()))
                    )
                    start = newLineIndex + 1
                }
                if (start < text.length) {
                    append(text.substring(start))
                }
            }
            Text(
                highlightedNewLinesText,
                fontWeight = fontWeight,
                fontSize = fontSize,
                color = color,
                maxLines = maxLines,
                overflow = overflow,
                modifier = modifier
            )
            return
        } else {
            Text(
                text,
                fontWeight = fontWeight,
                fontSize = fontSize,
                color = color,
                maxLines = maxLines,
                overflow = overflow,
                modifier = modifier
            )
            return
        }
    }

    val trimmed = searchQuery.trim()
    if (trimmed.isEmpty()) {
        insertTextWithMarkedNewLines()
        return
    }
    val highlights = trimmed.lowercase().split(" ").filter { it.isNotEmpty() }
    if (highlights.isEmpty()) {
        insertTextWithMarkedNewLines()
        return
    }
    val parts = convertToRanges(highlightChars(text.lowercase(), highlights))
    val builder = AnnotatedString.Builder().apply {
        parts.forEach {
            val partText = text.substring(it.start, it.end)
            if (it.mark) {
                append(
                    AnnotatedString(
                        partText,
                        if (GlobalState.isDarkMode) textMarkedDarkSpanStyle else textMarkedLightSpanStyle
                    )
                )
            } else {
                val newLines = partText.indicesOf("\n")
                var start = 0
                for (newLineIndex in newLines) {
                    append(partText.substring(start, newLineIndex))
                    append(
                        AnnotatedString("↵", SpanStyle(color = GlobalStyling.getGrayColor()))
                    )
                    start = newLineIndex + 1
                }
                if (start < text.length) {
                    append(partText.substring(start))
                }
            }
        }
    }
    Text(
        builder.toAnnotatedString(),
        color = color,
        fontWeight = fontWeight,
        fontSize = fontSize,
        maxLines = maxLines,
        overflow = overflow,
        modifier = modifier
    )
}