package dima.vero

import androidx.compose.ui.input.key.Key
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dialogs.transient.TransientDialogGroup
import dima.dialogs.transient.TransientDialogKey
import dima.leader.LeaderState
import dima.settings
import dima.utils.StringResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Returns a list of transient dialog groups for Vero operations
 */
fun getVeroTransientGroups(): List<TransientDialogGroup> {
    return listOf(
        TransientDialogGroup(
            name = "Vero",
            keys = buildList {
                add(
                    TransientDialogKey(Key.L, "Login by POST login to get token on default_test") {
                        CoroutineScope(Dispatchers.IO).launch {
                            val token = VeroHourBooking.downloadTokenForHourBookingByLoggingIn(
                                365, "1", "default_test"
                            )
                            when (token) {
                                is StringResult.Error -> showErrorNotification("Failed to download token", token.error)
                                is StringResult.Success -> showNotification("Downloaded token for default_test", token.value)
                            }
                        }
                    }
                )
                add(
                    TransientDialogKey(Key.R, "Refresh today's Vero hours") {
                        CoroutineScope(Dispatchers.IO).launch {
                            VeroHourBooking.updateTotalTodayHourDuration(forceDownload = true, showNotifications = true)
                        }
                    }
                )
                val defaultProject = settings.vero.getDefaultProject()
                if (defaultProject != null) {
                    add(
                        TransientDialogKey(Key.P, "Book on Vero " + defaultProject.name) {
                            CoroutineScope(Dispatchers.IO).launch {
                                VeroHourBooking.bookOnProject(
                                    defaultProject.id,
                                    VeroHourBooking.HoursState.Work,
                                    defaultProject.name
                                )
                            }
                        }
                    )
                }
                add(
                    TransientDialogKey(Key.S, "Stop Vero booking") {
                        CoroutineScope(Dispatchers.IO).launch {
                            VeroHourBooking.stopBooking()
                        }
                    }
                )
                add(
                    TransientDialogKey(Key.N, "Book on Vero project") {
                        CoroutineScope(Dispatchers.IO).launch {
                            VeroHourBooking.pickProjectToBookOn()
                        }
                    }
                )
            }
        )
    )
}

/**
 * Maps a key to a LeaderState for Vero operations
 */
fun mapKeyToVeroLeaderState(key: Key): LeaderState {
    val transientKeys = getVeroTransientGroups().firstOrNull()?.keys ?: emptyList()
    val matchingKey = transientKeys.find { it.key == key }
    return if (matchingKey != null) {
        LeaderState(matchingKey.text) {
            matchingKey.callback()
        }
    } else {
        LeaderState(title = null) {
        }
    }
}
