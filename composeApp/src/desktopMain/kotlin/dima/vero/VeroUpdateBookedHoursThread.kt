package dima.vero

import dima.dateTime.DateTimeUtils
import dima.globalState.GlobalState
import kotlinx.coroutines.runBlocking
import java.time.DayOfWeek
import java.time.LocalDate
import kotlin.math.abs
import kotlin.time.Duration.Companion.seconds

class VeroUpdateBookedHoursThread : Thread() {
    override fun run() {

        fun download(forceDownload: Boolean) {
            runBlocking {
                VeroHourBooking.updateTotalTodayHourDuration(forceDownload, showNotifications = false)
            }
        }

        if (!VeroHourBooking.cacheDirectory.exists()) {
            VeroHourBooking.cacheDirectory.mkdirs()
        }
        val sleepDuration = 30.seconds.inWholeMilliseconds
        VeroHourBooking.readApiHoursFromCache()
        while (true) {
            val dayOfWeek = LocalDate.now().dayOfWeek
            if ((dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY) && !GlobalState.vero.isWorking) {
                VeroHourBooking.todayHoursTray = null
                VeroHourBooking.todayHoursModeline = null
            } else {
                if (GlobalState.vero.lastUpdate == null) {
                    download(forceDownload = true)
                } else {
                    val lastUpdate = DateTimeUtils.fromIsoToLocalDateTime(GlobalState.vero.lastUpdate!!)
                    val duration = DateTimeUtils.differenceToNowInIso(lastUpdate)
                    download(forceDownload = abs(duration.toMinutes()) >= 20)
                }
            }
            sleep(sleepDuration)
        }
    }
}
