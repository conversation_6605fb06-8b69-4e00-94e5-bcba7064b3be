package dima.vero

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.networkActivity.HttpResponse
import dima.apps.networkActivity.getLogged
import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dateTime.DateTimeUtils
import dima.globalState.GlobalState
import dima.settings
import dima.utils.StringResult
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.truncateWithEllipsis
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import kotlinx.serialization.json.Json
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate

internal fun List<ApiProject>.filterSortAndMapProjects(): List<DbProject> {
    if (settings.vero.hourBooking.rootProjects.isEmpty()) {
        return filter { project -> project.parentId != null }
            .map { project ->
                val parent = find { it.id == project.parentId }!!
                DbProject(project.name, project.id, parent.name)
            }.sortedBy {
                it.parentProject
            }
    }
    return filter { project ->
        project.parentId != null && settings.vero.hourBooking.rootProjects.any { it.id == project.parentId }
    }.map { project ->
        val parent = settings.vero.hourBooking.rootProjects.find { it.id == project.parentId }!!
        DbProject(project.name, project.id, parent.name)
    }.sortedBy {
        it.parentProject
    }
}

object VeroHourBooking {

    enum class HoursState {
        Work,
        End;

        fun toApiString(): String {
            return when (this) {
                Work -> "WORK"
                End -> "END"
            }
        }
    }

    data class HoursModeline(val hourText: String, val projectName: String)

    var todayHoursTray by mutableStateOf<String?>(null)
    var todayHoursModeline by mutableStateOf<HoursModeline?>(null)
    internal val cacheDirectory = File("cache/vero")
    internal val client = createHttpClientWithLocalhostProxy()
    internal val json = Json {
        ignoreUnknownKeys = true
    }
    private var apiProjects: List<ApiProject> = emptyList()
    private var apiHours: List<V1HoursAll> = emptyList()

    suspend fun stopBooking() {
        warnWhenProxyIsActive()
        val notification = showLoadingNotification("Loading hours to stop booking...")
        val today = DateTimeUtils.nowAsIsoDate()
        val response = client.getLogged("${settings.vero.hourBooking.baseUrlWithoutTrailingSlash}/v1/hours/all") {
            timeout {
                requestTimeoutMillis = 60_000
                connectTimeoutMillis = 60_000
            }
            parameter("resnr", settings.vero.hourBooking.pnr.toString())
            parameter("date", today)
            parameter("to_date", today)
            headers.appendAll(commonHeaders)
        }
        if (response.statusCode != 200) {
            notification.toError("Failed to download projects", response.body)
            return
        }
        val hours = json.decodeFromString<List<V1HoursAll>>(response.body)
        val withoutEnd = hours.find { it.end == null }
        if (withoutEnd == null) {
            notification.toError("You are not booked into any project!")
            GlobalState.vero = GlobalState.vero.copy(isWorking = false)
        } else {
            bookOnProject(withoutEnd.ktr, HoursState.End, withoutEnd.name, notification)
        }
    }

    /**
     * Caches the downloaded projects.
     */
    internal suspend fun getProjectsOrDownload(forceDownload: Boolean): List<ApiProject> {
        warnWhenProxyIsActive()

        // happens on a few only
        fun replaceBrokenUmlauteCharactersInProjectTitle(title: String): String = title
            .replace("a¨", "ä")
            .replace("u¨", "ü")
            .replace("o¨", "ö")

        val cacheFile = File(cacheDirectory, "projects.json")

        suspend fun download() {
            val notification = showLoadingNotification("Downloading Vero projects...", "GET v3/projects")
            val response = client.getLogged("${settings.vero.hourBooking.baseUrlWithoutTrailingSlash}/v3/projects") {
                timeout {
                    requestTimeoutMillis = 30_000
                    connectTimeoutMillis = 30_000
                }
                parameter("filter[projectStatus]", "2")
                parameter("filter[projectType]", "3")
                parameter("filter[projectValidEndDate][gt]", "2022-01-18")
                parameter("partial", "projectNo,parentId,projectName")
                headers.appendAll(commonHeaders)
            }
            val body = response.body
            if (response.statusCode != 200) {
                notification.toError("Failed to download projects", body)
                return
            }
            apiProjects = json.decodeFromString<List<ApiProject>>(body).distinct().map {
                it.copy(name = replaceBrokenUmlauteCharactersInProjectTitle(it.name))
            }
            cacheFile.writeText(body)
            notification.toInfo("Downloaded Vero projects", durationMillis = 1000)
        }

        if (forceDownload) {
            download()
        } else {
            if (apiProjects.isNotEmpty()) {
                return apiProjects
            }
            if (cacheFile.exists()) {
                apiProjects = json.decodeFromString<List<ApiProject>>(cacheFile.readText())
            } else if (apiProjects.isEmpty()) {
                download()
            }
        }
        return apiProjects
    }

    internal fun readApiHoursFromCache() {
        val cacheFile = File(cacheDirectory, "hours.json")
        if (cacheFile.exists()) {
            apiHours = json.decodeFromString(cacheFile.readText())
        }
    }

    suspend fun updateTotalTodayHourDuration(forceDownload: Boolean, showNotifications: Boolean) {

        suspend fun download() {
            var notification: LoadingNotification? = null

            fun onError(response: HttpResponse) {
                val title = "GET v1/hours/all failed"
                if (showNotifications) {
                    if (notification == null) {
                        showErrorNotification(title, response.body)
                    } else {
                        notification!!.toError(title, response.body)
                    }
                }
            }

            if (showNotifications) {
                notification = showLoadingNotification("Downloading Vero hours...", "GET v1/hours/all")
            }
            val currentDate = LocalDate.now()
            val currentYear = currentDate.year
            val formattedDay = currentDate.dayOfMonth.toString().padStart(2, '0')
            val formattedMonth = currentDate.monthValue.toString().padStart(2, '0')
            val date = "$currentYear-$formattedMonth-$formattedDay"
            if (GlobalState.vero.token == null) {
                val newToken = downloadTokenForHourBookingByLoggingIn(
                    settings.vero.hourBooking.pnr,
                    settings.vero.hourBooking.password,
                    settings.vero.hourBooking.principal
                )
                if (newToken is StringResult.Error) {
                    notification?.toError("Failed to download token", newToken.error)
                    return
                }
            }

            suspend fun doApiCall(): HttpResponse {
                return client.getLogged("${settings.vero.hourBooking.baseUrlWithoutTrailingSlash}/v1/hours/all") {
                    timeout {
                        requestTimeoutMillis = 60_000
                        connectTimeoutMillis = 60_000
                    }
                    parameter("type", "unapproved")
                    parameter("resnr", settings.vero.hourBooking.pnr.toString())
                    parameter("date", date)
                    parameter("to_date", date)
                    headers.appendAll(commonHeaders)
                    bearerAuth(GlobalState.vero.token!!)
                }
            }

            val response = doApiCall()
            val body = response.body
            if (response.statusCode == 401) {
                val newToken = downloadTokenForHourBookingByLoggingIn(
                    settings.vero.hourBooking.pnr,
                    settings.vero.hourBooking.password,
                    settings.vero.hourBooking.principal
                )
                when (newToken) {
                    is StringResult.Error -> {
                        notification?.toError("Failed to download token", newToken.error)
                        return
                    }

                    is StringResult.Success -> {}
                }
                val secondResponse = doApiCall()
                if (secondResponse.statusCode == 401) {
                    notification?.toError(
                        "Got 401 after downloading token?",
                        "GET v1/hours/all",
                    )
                    return
                } else if (secondResponse.statusCode != 200) {
                    onError(secondResponse)
                    return
                }
            } else if (response.statusCode != 200) {
                onError(response)
                return
            }
            notification?.dismiss()
            val hours = json.decodeFromString<List<V1HoursAll>>(body)
            val cacheFile = File(cacheDirectory, "hours.json")
            cacheFile.writeText(body)
            apiHours = hours
        }

        if (forceDownload) {
            download()
        }
        val diff = getHoursAllStartEndDataWithoutBreaksAsDuration(apiHours)
        val currentProject = apiHours.find { it.end == null }
        GlobalState.vero = GlobalState.vero.copy(
            lastUpdate = DateTimeUtils.nowAsIsoDateTime(),
            isWorking = currentProject != null,
            todayDuration = diff
        )
        todayHoursTray = diff.toInt().toString() + "h"
        val minutes = BigDecimal(diff).remainder(BigDecimal.ONE).multiply(BigDecimal(60)).toInt().toString()
        val projectName = currentProject?.name?.truncateWithEllipsis(60) ?: ""
        val text = if (diff < 1.0) {
            "${minutes}m"
        } else {
            if (minutes == "0") {
                "${diff.toInt()}h"
            } else {
                "${diff.toInt()}h ${minutes}m"
            }
        }
        todayHoursModeline = HoursModeline(text, projectName)
    }

}
