package dima.vero

import GlobalStyling
import Globals
import androidx.compose.runtime.Composable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.rememberTrayState
import dima.globalState.GlobalState
import dima.utils.drawRoundBackground

private class TrayIcon(val text: String) : <PERSON>() {
    override val intrinsicSize = Size(256f, 256f)

    override fun DrawScope.onDraw() {
        drawRoundBackground()
        drawText(
            TextMeasurer(Globals.fontFamilyResolver, Density(100f), LayoutDirection.Ltr),
            style = TextStyle(
                fontSize = 8.sp,
                textAlign = TextAlign.Center,
                color = GlobalStyling.Tray.foregroundColor,
            ),
            overflow = TextOverflow.Visible,
            topLeft = Offset(0f, 3f),
            size = Size(21.dp.toPx(), 20.dp.toPx()),
            text = "Vero"
        )
        drawText(
            TextMeasurer(Globals.fontFamilyResolver, Density(100f), LayoutDirection.Ltr),
            style = TextStyle(
                fontSize = 10.sp,
                textAlign = TextAlign.Center,
                color = GlobalStyling.Tray.foregroundColor,
            ),
            overflow = TextOverflow.Visible,
            topLeft = Offset(0f, 18f),
            size = Size(21.dp.toPx(), 20.dp.toPx()),
            text = text
        )
    }
}

@Composable
fun ApplicationScope.VeroTray() {
    val state = rememberTrayState()
    if (GlobalState.vero.isWorking && VeroHourBooking.todayHoursTray != null) {
        Tray(
            state = state,
            icon = TrayIcon(VeroHourBooking.todayHoursTray!!)
        )
    }
}
