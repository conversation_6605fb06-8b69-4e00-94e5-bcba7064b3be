package dima.vero

/**
 * @return total hours as a [Double] without breaks. 4.5 means 4 hours and 30 minutes.
 */
internal fun getHoursAllStartEndDataWithoutBreaksAsDuration(hours: List<V1HoursAll>): Double {
    val now = System.currentTimeMillis()
    val currentHours = (now / 3600000) % 24
    val currentMinutes = (now / 60000) % 60
    val totalHours = hours.fold(0.0) { acc, curr ->
        val endTime = curr.end ?: String.format("%02d:%02d", currentHours, currentMinutes)
        val startTime = curr.start!!.split(":")
        val startHour = startTime[0].toInt()
        val startMinute = startTime[1].toInt()
        val endTimeSplit = endTime.split(":")
        val endHour = endTimeSplit[0].toInt()
        val endMinute = endTimeSplit[1].toInt()
        val total = (endHour - startHour) + (endMinute - startMinute) / 60.0
        acc + total
    }
    return totalHours
}
