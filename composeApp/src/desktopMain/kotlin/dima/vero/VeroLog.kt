package dima.vero

import dima.apps.veroLog.WeekSummary
import dima.apps.veroLog.DaySummary
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.time.temporal.WeekFields
import java.util.*
import kotlin.time.Duration.Companion.milliseconds

@Serializable
private data class VeroLogEntry(
    val timestamp: String,
    val projectId: Int? = null,
    val projectName: String? = null,
    val state: String? = null
)

object VeroLog {
    internal val cacheDirectory = File("cache/vero")
    private val logFile = File(cacheDirectory, "hour-bookings.jsonl")
    private val json = Json { encodeDefaults = true }

    fun logEvent(
        projectId: Int,
        projectName: String,
        state: VeroHourBooking.HoursState
    ) {
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        val entry = VeroLogEntry(
            timestamp = Instant.now().toString(),
            projectId = projectId,
            projectName = projectName,
            state = state.toApiString()
        )
        val jsonString = json.encodeToString(entry)
        logFile.appendText(jsonString + "\n")
    }
}

internal fun calculateWeeklySummaries(entries: List<dima.apps.veroLog.VeroLogEntry>): List<WeekSummary> {
    if (entries.isEmpty()) return emptyList()

    val workSessions = mutableListOf<Pair<dima.apps.veroLog.VeroLogEntry, dima.apps.veroLog.VeroLogEntry?>>()
    var currentWork: dima.apps.veroLog.VeroLogEntry? = null

    // Process entries to create work sessions
    for (i in entries.indices) {
        val entry = entries[i]
        when (entry.state) {
            "WORK" -> {
                // If we have a previous WORK without END, end it with the current WORK timestamp
                if (currentWork != null) {
                    workSessions.add(currentWork to entry)
                }
                currentWork = entry
            }

            "END" -> {
                if (currentWork != null) {
                    workSessions.add(currentWork to entry)
                    currentWork = null
                }
            }
        }
    }

    // Handle final WORK entry without END - estimate end time or use 0
    if (currentWork != null) {
        workSessions.add(currentWork to null)
    }

    // Group sessions by week and day, then calculate hours
    val weeklyData = mutableMapOf<LocalDate, MutableMap<LocalDate, MutableMap<String, Double>>>()

    for ((workEntry, endEntry) in workSessions) {
        val workTime = Instant.parse(workEntry.timestamp)
        val workDate = workTime.atZone(ZoneId.systemDefault()).toLocalDate()
        val weekStart = workDate.minusDays(workDate.dayOfWeek.value - 1L)

        val hours = if (endEntry != null) {
            val endTime = Instant.parse(endEntry.timestamp)
            val durationMillis = ChronoUnit.MILLIS.between(workTime, endTime)
            durationMillis / (1000.0 * 60.0 * 60.0) // Convert milliseconds to hours
        } else {
            0.0 // No END entry, assume 0 hours
        }

        weeklyData.getOrPut(weekStart) { mutableMapOf() }
            .getOrPut(workDate) { mutableMapOf() }
            .merge(workEntry.projectName, hours) { existing, new -> existing + new }
    }

    val germanDayNames = mapOf(
        1 to "Montag",
        2 to "Dienstag", 
        3 to "Mittwoch",
        4 to "Donnerstag",
        5 to "Freitag",
        6 to "Samstag",
        7 to "Sonntag"
    )

    return weeklyData.map { (weekStart, dailyData) ->
        val weekEnd = weekStart.plusDays(6)
        val weekFields = WeekFields.of(Locale.GERMANY)
        val weekNumber = weekStart.get(weekFields.weekOfYear())
        
        val days = mutableListOf<DaySummary>()
        var totalWeekHours = 0.0
        
        // Create day summaries for each day of the week
        for (dayOffset in 6 downTo 0) {
            val currentDate = weekStart.plusDays(dayOffset.toLong())
            val dayProjects = dailyData[currentDate] ?: emptyMap()
            val dayTotalHours = dayProjects.values.sum()
            totalWeekHours += dayTotalHours
            
            if (dayTotalHours > 0) { // Only include days with work
                val dayName = germanDayNames[currentDate.dayOfWeek.value] ?: "Unknown"
                days.add(DaySummary(currentDate, dayName, dayTotalHours, dayProjects))
            }
        }
        
        WeekSummary(weekStart, weekEnd, weekNumber, totalWeekHours, days)
    }
}