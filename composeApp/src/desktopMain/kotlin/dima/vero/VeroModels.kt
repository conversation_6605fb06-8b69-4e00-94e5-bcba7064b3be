package dima.vero

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
internal data class V3HoursStateRequest(
    val eventTime: String,
    val workingDate: String,
    val state: String,
    val project: Int,
    val workingOrder: Int,
    val sender: Int,
    val employees: List<Int>,
)

@Serializable
internal data class V1HoursAll(
    // project name
    val name: String,
    // project number
    val ktr: Int,
    // working order number
    val aanr: Int,
    // 10:30
    val start: String?,
    /**
     * 10:30. If null, one is currently booked into this project.
     */
    val end: String?,
    // 2024-12-31
    val date: String,
    // "hours" means approved and "TimeTrackEvent" means unapproved.
    val origin: String
)

data class VeroRootProject(val id: Int, val name: String, val isDefaultForBooking: Boolean = false)

@Serializable
internal data class ApiProject(
    @SerialName("projectName") val name: String,
    @SerialName("projectNo") val id: Int,
    val parentId: Int?,
)

@Serializable
internal data class DbProject(
    val name: String,
    val id: Int,
    val parentProject: String,
)

@Serializable
internal data class LoginRequest(
    val username: String,
    val password: String,
)

@Serializable
internal data class OauthLoginResponse(
    @SerialName("access_token")
    val accessToken: String,
)

@Serializable
internal data class LoginResponse(
    val oauth: OauthLoginResponse,
)