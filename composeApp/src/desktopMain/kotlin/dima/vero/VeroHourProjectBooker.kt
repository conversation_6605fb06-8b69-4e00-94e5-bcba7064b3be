package dima.vero

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.unit.dp
import dima.apps.networkActivity.postLogged
import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showLoadingNotification
import dima.color.ColorHelper
import dima.color.TailwindCssColors
import dima.dateTime.DateTimeFormat
import dima.dateTime.DateTimeUtils
import dima.dialogs.completion.ChangeCompletionDialogCandidates
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.CompletionDialogCmdAction
import dima.dialogs.completion.openCompletionDialog
import dima.events.emitEasy
import dima.globalState.GlobalState
import dima.settings
import dima.utils.RecentHistory
import dima.utils.sortedByHistory
import dima.vero.VeroHourBooking.HoursState
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File

/**
 * @param projectName is used for display
 * @param notification should be a loading notification without a duration
 */
internal suspend fun VeroHourBooking.bookOnProject(
    projectId: Int,
    state: HoursState,
    projectName: String,
    notification: LoadingNotification? = null
) {
    warnWhenProxyIsActive()
    val stateString = state.toApiString()
    val currentNotification = if (notification == null) {
        showLoadingNotification("Booking $stateString...", projectName)
    } else {
        notification.update(message = "Booking $stateString...")
        notification
    }
    val isoDate = DateTimeUtils.nowInUtc().format(DateTimeFormat.isoDateTimeWithTandZ)
    val response = client.postLogged("${settings.vero.hourBooking.baseUrlWithoutTrailingSlash}/v3/hours/state") {
        timeout {
            requestTimeoutMillis = 60_000
            connectTimeoutMillis = 60_000
        }
        setBody(
            Json.encodeToString(
                listOf(
                    V3HoursStateRequest(
                        eventTime = isoDate,
                        workingDate = isoDate.substring(0, 10),
                        state = stateString,
                        project = projectId,
                        workingOrder = 0,
                        sender = settings.vero.hourBooking.pnr,
                        employees = listOf(settings.vero.hourBooking.pnr),
                    )
                )
            )
        )
        headers.appendAll(commonHeaders)
    }
    if (response.statusCode == 200) {
        VeroLog.logEvent(
            projectId = projectId,
            projectName = projectName,
            state = state
        )
        GlobalState.vero = GlobalState.vero.copy(
            lastUpdate = DateTimeUtils.nowAsIsoDateTime(),
            isWorking = state == HoursState.Work
        )
        currentNotification.toInfo("Booked $stateString", "")
        updateTotalTodayHourDuration(forceDownload = true, showNotifications = false)
    } else {
        currentNotification.toError(
            "Failed to book $stateString", response.body,
        )
    }
}

private fun List<DbProject>.mapToCompletionDialogCandidate(): List<CompletionDialogCandidate> {
    return this.map {
        val prefixView = @Composable { modifier: Modifier ->
            Row(modifier = modifier) {
                Text(
                    it.parentProject,
                    color = TailwindCssColors.white,
                    modifier = Modifier
                        .padding(horizontal = 8.dp)
                        .background(
                            ColorHelper.getRandomByString(it.parentProject, 0.3f),
                            RoundedCornerShape(5.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 3.dp)
                )
            }
        }
        CompletionDialogCandidate(it.name, prefixView, it.parentProject)
    }
}

suspend fun VeroHourBooking.pickProjectToBookOn(initialQuery: String = "") {
    warnWhenProxyIsActive()
    val historyFile = File(cacheDirectory, "recent-projects.json")
    val history: List<DbProject> = RecentHistory.readFromJsonFile<DbProject>(historyFile)
    val projects: List<DbProject> = getProjectsOrDownload(forceDownload = false).filterSortAndMapProjects()
    var sortedProjects = projects.sortedByHistory(history)
    val candidates: List<CompletionDialogCandidate> = sortedProjects.mapToCompletionDialogCandidate()
    openCompletionDialog(
        "Pick project to book on",
        candidates,
        initialQuery,
        allowChangingCandidatesViaEventToFixDialogHeight = true,
        allowMatchingLiterally = true,
        cmdActions = listOf(
            CompletionDialogCmdAction(Key.R, "Reload projects") {
                CoroutineScope(Dispatchers.IO).launch {
                    val newCandidates =
                        getProjectsOrDownload(forceDownload = true)
                            .filterSortAndMapProjects()
                            .sortedByHistory(history)
                    sortedProjects = newCandidates
                    ChangeCompletionDialogCandidates(
                        dialogId = it.dialogId,
                        newCandidates = newCandidates.mapToCompletionDialogCandidate()
                    ).emitEasy()
                }
            }
        )
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            val project: DbProject = sortedProjects[it.index!!]
            RecentHistory.rememberAndPersistToFileAsJson(project, historyFile)
            bookOnProject(project.id, HoursState.Work, project.name)
        }
    }
}
