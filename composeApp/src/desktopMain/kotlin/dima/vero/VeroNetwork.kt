package dima.vero

import dima.apps.notifications.showErrorNotification
import dima.settings
import io.ktor.util.*

internal val commonHeaders = StringValues.build {
    append("Content-Type", "application/json")
    append("Principal", settings.vero.hourBooking.principal)
}

internal fun warnWhenProxyIsActive() {
    if (settings.networkRequestLocalhostProxyPort == null) {
        return
    }
    showErrorNotification("Vero network calls will fail because proxy is active", "Ktor library is bugged?")
}