package dima.vero

import Globals
import dima.apps.networkActivity.postLogged
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dialogs.completion.openCompletionDialog
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.settings
import dima.settings.Settings
import dima.utils.JsonIgnoreUnknown
import dima.utils.StringResult
import dima.utils.setJsonBody
import dima.vero.VeroHourBooking.client
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Saves the token to [GlobalState.vero] on success.
 */
suspend fun VeroHourBooking.downloadTokenForHourBookingByLoggingIn(
    username: Int,
    password: String,
    principal: String
): StringResult {
    warnWhenProxyIsActive()
    val response = client.postLogged("${settings.vero.hourBooking.baseUrlWithoutTrailingSlash}/login") {
        setJsonBody(
            LoginRequest(
                username = username.toString(),
                password = password
            )
        )
        headers.append("Authorization", "Basic ${settings.vero.basicAuthTokenForLogin}")
        headers.append("Principal", principal)
    }
    if (response.statusCode == 200) {
        val token = try {
            json.decodeFromString<LoginResponse>(response.body).oauth.accessToken
        } catch (e: Exception) {
            return StringResult.Error("Failed to parse login response: ${e.message}\n${response.body}")
        }
        GlobalState.vero = GlobalState.vero.copy(token = token)
        return StringResult.Success(token)
    }
    return StringResult.Error(response.statusCode.toString() + ": " + response.body)
}

suspend fun loginAndCopyVeroTokenToClipboard() {
    if (settings.vero.loginData.isEmpty()) {
        showErrorNotification("Vero login data is not set in settings")
        return
    }
    warnWhenProxyIsActive()

    suspend fun downloadToken(login: Settings.Vero.LoginData) {
        val notification = showLoadingNotification("Downloading new token for ${login.principal}...")
        try {
            val response = client.postLogged("${login.baseUrlWithoutTrailingSlash}/login") {
                setJsonBody(
                    LoginRequest(
                        username = login.pnr.toString(),
                        password = login.password,
                    )
                )
                headers.append("Principal", login.principal)
                headers.append("Authorization", "Basic ${settings.vero.basicAuthTokenForLogin}")
            }

            if (response.statusCode == 200) {
                val loginResponse = try {
                    JsonIgnoreUnknown.decodeFromString<LoginResponse>(response.body)
                } catch (e: Exception) {
                    notification.toError("Vero: Failed to parse login response", e.message)
                    return
                }
                val token = loginResponse.oauth.accessToken
                copyToClipboard(token, showNotification = false)
                notification.toInfo("Vero: Access token copied to clipboard!", token)
            } else {
                notification.toError("Vero: Login failed (${response.statusCode})", response.body)
            }
        } catch (e: Exception) {
            notification.toError("Vero: Login request failed", e.message ?: "Unknown error")
        }
    }

    if (settings.vero.loginData.size == 1) {
        downloadToken(settings.vero.loginData.first())
        return
    }
    val principals = settings.vero.loginData.map { it.principal }
    openCompletionDialog("Vero: Select principal", principals) {
        Globals.coroutineScope.launch(Dispatchers.IO) {
            downloadToken(settings.vero.loginData[it.index!!])
        }
    }
}
