package dima.weather

import dima.apps.networkActivity.getLogged
import dima.apps.notifications.showLoadingNotification
import dima.globalState.GlobalState
import dima.settings
import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.request.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlin.time.Duration.Companion.minutes

private val json = Json {
    ignoreUnknownKeys = true
}

private const val limitCelsius = 1.0

class ColdTemperatureWeatherThread : Thread() {
    override fun run() {
        runBlocking {
            val sleepDuration = 30.minutes.inWholeMilliseconds
            while (true) {
                val result = downloadFromApi() ?: return@runBlocking
                updateGlobalColdWeather(result.hourly.temperatures)
                sleep(sleepDuration)
            }
        }
    }
}

private fun updateGlobalColdWeather(temperatures: List<Double>) {
    val lowest = temperatures.sorted().min()
    if (lowest <= limitCelsius) {
        GlobalState.coldWeather = lowest
    } else {
        GlobalState.coldWeather = null
    }
}

/**
 * @return null on any network error
 */
private suspend fun downloadFromApi(): ApiResponse? {
    createHttpClientWithLocalhostProxy().apply {
        val r = getLogged(
            "https://api.open-meteo.com/v1/forecast"
        ) {
            parameter("latitude", settings.weather.latitude)
            parameter("longitude", settings.weather.longitude)
            parameter("hourly", "temperature_2m")
            parameter("forecast_days", "2")
            parameter("format", "json")
            parameter("timeformat", "unixtime")
        }
        if (r.statusCode != 200) {
            return null
        }
        val parsed = json.decodeFromString<ApiResponse>(r.body)
        return parsed
    }
}

fun checkColdWeather() {
    val notification = showLoadingNotification("Checking weather for today and tomorrow...")
    CoroutineScope(Dispatchers.IO).launch {
        val result = downloadFromApi()
        if (result == null) {
            notification.toError("Failed to download weather", "api.open-meteo.com/v1/forecast")
            return@launch
        }
        updateGlobalColdWeather(result.hourly.temperatures)
        notification.toInfo(
            if (GlobalState.coldWeather == null) {
                "No cold weather (${limitCelsius}°C) today or tomorrow"
            } else {
                "Cold weather (${GlobalState.coldWeather}°C) today or tomorrow"
            }
        )
    }
}

@Serializable
private data class ApiResponse(
    val latitude: Double,
    val longitude: Double,
    val timezone: String,
    val elevation: Double,
    val hourly: ApiResponseHourly,
)

@Serializable
private data class ApiResponseHourly(
    val time: List<Long>,
    @SerialName("temperature_2m")
    val temperatures: List<Double>,
)