package dima.apps.amazonVine

/*
Deletion is not implemented because it modifies data.db and one needs to commit and push changes to it.
And it is such a rare occurrence, better to just keep all entries and delete in bulk via stand-alone scripts in the
vine repo.

AppKey(Key.D, "Delete article from database") {
                if (entries.isEmpty()) {
                    return@AppKey
                }
                val entry = entries.getOrNull(selected ?: -1)
                if (entry == null) {
                    return@AppKey
                }
                openConfirmationDialog("Delete article from Vine database?", entry.title) {
                    AmazonVine.deleteArticle(entry.link)
                    entries = AmazonVine.fetchArticles(query, freeOnly, category)
                    selected = Lists.getInitialSelected(entries, GlobalState.vine.selectedArticleLink) { it.link }
                    if (selected == null) {
                        selected = 0
                    }
                    showNotification("Deleted from Amazon Vine database", entry.title)
                    CoroutineScope(Dispatchers.Main).launch {
                        globalEvent = GlobalEvent.Reload
                    }
                }
            }*/
