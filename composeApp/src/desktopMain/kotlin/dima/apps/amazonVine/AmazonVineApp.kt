package dima.apps.amazonVine

import GlobalEvent
import Globals
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dialogs
import dima.apps.notifications.NotificationTypeFromLoading
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.confirmation.openConfirmationDialog
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.os.Hammerspoon
import dima.os.openUrl
import dima.settings
import dima.telegram.vine.sendToTelegramGroup
import dima.utils.AppKey
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.Lists
import dima.utils.SimpleResult
import globalEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File

internal var cachedDbCategories: List<String>? = null

internal fun openCategoryDialog(onCategoryChange: (String?) -> Unit) {
    if (cachedDbCategories == null) {
        cachedDbCategories = AmazonVine.fetchCategories()
    }
    val categories = listOf("Alle") + cachedDbCategories!!
    openCompletionDialog("Neue Kategorie", categories) {
        val category = if (it.text == "Alle") {
            null
        } else {
            it.text
        }
        onCategoryChange(category)
    }
}

internal fun findInVine(article: AmazonVine.Article) {
    Hammerspoon.showAlert("Opening Google Chrome...")
    AmazonVine.findArticleByWebDriver(article, maxPagesToCheck = ((article.pageNumber ?: 0) * 2) + 30)
}

@Composable
fun AmazonVineApp() {
    remember {
        AmazonVine.setupDatabase()
    }
    var query by remember { mutableStateOf(GlobalState.vine.query) }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }
    var freeOnly by remember { mutableStateOf(GlobalState.vine.freeOnly) }
    var entries by remember { mutableStateOf<List<AmazonVine.Article>>(emptyList()) }
    var selected by remember { mutableStateOf<Int?>(null) }
    val coroutineScope = rememberCoroutineScope()
    var category by remember { mutableStateOf(GlobalState.vine.category) }
    val searchFocusRequester = remember { FocusRequester() }
    val appFocusRequester = remember { FocusRequester() }
    val listState = rememberLazyListState()

    suspend fun scrollTo() {
        if (selected == null) {
            return
        }
        if (entries.isNotEmpty()) {
            listState.animateScrollToItem(selected!!, scrollOffset = -200)
            GlobalState.vine = GlobalState.vine.copy(selectedArticleLink = entries.getOrNull(selected!!)?.link)
        } else {
            GlobalState.vine = GlobalState.vine.copy(selectedArticleLink = null)
        }
    }

    remember {
        appKeys = listOf(
            // use KeyEventType.KeyUp to not insert the s on focusing,
            // also an issue with the AudioDialog S key handling
            AppKey(Key.B, "Focus search", onKeyUp = {
                searchFocusRequester.requestFocus()
                queryState = TextFieldValue(text = query, selection = TextRange(query.length))
            }),
            AppKey(Key.A, "Find article in Vine page") {
                if (entries.isNotEmpty()) {
                    val entry = entries.getOrNull(selected ?: -1)
                    if (entry == null) {
                        return@AppKey
                    }
                    openConfirmationDialog("Find article in browser?", entry.title) {
                        Globals.coroutineScope.launch(Dispatchers.IO) {
                            findInVine(entry)
                        }
                    }
                }
            },
            AppKey(Key.U, "Change category") {
                openCategoryDialog {
                    category = it
                }
            },
            AppKey(Key.F, "Toggle free only") {
                freeOnly = !freeOnly
            },
            AppKey(Key.Enter, "Open selected article in browser") {
                if (entries.isNotEmpty()) {
                    val link = entries.getOrNull(selected ?: -1)?.link
                    if (link != null) {
                        openUrl(link)
                    }
                }
            },
            AppKey(Key.T, "Go one article down") {
                if (selected != null && selected!! + 1 < entries.size) {
                    selected = selected!! + 1
                }
            },
            AppKey(Key.M, "Go 6 articles down") {
                if (selected == null) {
                    return@AppKey
                }
                val newIndex = selected!! + 6
                selected = newIndex.coerceAtMost(entries.size - 1)
            },
            AppKey(Key.V, "Go 6 articles up") {
                if (selected == null) {
                    return@AppKey
                }
                val newIndex = selected!! - 6
                selected = newIndex.coerceAtLeast(0)
            },
            AppKey(Key.C, "Go one article up") {
                if (selected != null && selected!! >= 1) {
                    selected = selected!! - 1
                }
            },
            AppKey(Key.Z, "Send current article to Telegram") {
                if (selected == null) {
                    return@AppKey
                }
                val articleToSend = entries.getOrNull(selected!!) ?: return@AppKey
                openConfirmationDialog("Send to Telegram?", articleToSend.title) {
                    val result = articleToSend.sendToTelegramGroup()
                    when (result) {
                        is SimpleResult.Error -> showErrorNotification("Failed to send to Telegram", result.error)
                        is SimpleResult.Success -> showNotification("Sent to Telegram group", articleToSend.title)
                    }
                }
            }
        )
    }

    Ui(
        appFocusRequester = appFocusRequester,
        entries = entries,
        queryState = queryState,
        query = query,
        selected = selected,
        searchFocusRequester = searchFocusRequester,
        category = category,
        freeOnly = freeOnly,
        listState = listState,
        onCategoryChange = { category = it },
        changeQueryState = { queryState = it },
        changeQuery = { query = it },
        changeSelected = { selected = it },
        changeFreeOnly = { freeOnly = it }
    )

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    var ignoreFirstSelectedArticle by remember { mutableStateOf(true) }
    LaunchedEffect(query, freeOnly, category) {
        launch {
            entries = AmazonVine.fetchArticles(query, freeOnly, category)
            if (ignoreFirstSelectedArticle) {
                selected = Lists.getInitialSelected(entries, GlobalState.vine.selectedArticleLink) { it.link }
                if (selected == null) {
                    selected = 0
                }
                ignoreFirstSelectedArticle = false
            } else {
                selected = 0
                // scroll here since the LaunchedEffect(selectedArticle) is not triggered here
                listState.animateScrollToItem(0, scrollOffset = -200)
            }
            GlobalState.vine = if (entries.isEmpty()) {
                GlobalState.vine.copy(query = query, freeOnly = freeOnly, category = category)
            } else {
                GlobalState.vine.copy(
                    query = query,
                    freeOnly = freeOnly,
                    category = category,
                    selectedArticleLink = entries.getOrNull(selected ?: -1)?.link
                )
            }
        }
    }
    LaunchedEffect(selected) {
        scrollTo()
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> selected = 0
            GlobalEvent.ScrollToBottom -> selected = entries.size - 1
            GlobalEvent.Reload -> {
                if (!File(settings.vineDatabasePath).exists()) {
                    showErrorNotification(
                        "Amazon Vine database file does not exist!",
                        settings.vineDatabasePath
                    )
                    return@LaunchedEffectGlobalEventForApps
                }
                val notification = showLoadingNotification("git pull...")
                var gitPullOutput: String
                withContext(Dispatchers.Default) {
                    var didGitProcessFail = false
                    runBlocking {
                        var lines = listOf<String>()
                        val gitPull = process(
                            "git", "pull", "--progress",
                            directory = File(settings.vineDatabasePath).parentFile,
                            stdout = Redirect.CAPTURE,
                            stderr = Redirect.CAPTURE,
                            consumer = {
                                lines = lines + it
                                coroutineScope.launch {
                                    notification.update(message = lines.joinToString("\n").trim())
                                }
                            }
                        )
                        gitPullOutput = gitPull.output.joinToString(" ").trim()
                        didGitProcessFail = gitPull.resultCode != 0
                    }
                    entries = AmazonVine.fetchArticles(query, freeOnly, category)
                    scrollTo()
                    coroutineScope.launch {
                        notification.toType(
                            title = "Reloaded articles and pulled",
                            message = gitPullOutput,
                            durationMillis = 3000,
                            type = if (didGitProcessFail) NotificationTypeFromLoading.Error else NotificationTypeFromLoading.Info
                        )
                    }
                }
            }

            else -> {}
        }
    }
}