package dima.apps.amazonVine

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.notifications.showErrorNotification
import dima.dateTime.DateTimeFormat
import dima.os.Hammerspoon
import dima.os.Osascript
import dima.settings
import dima.utils.breakWithNewlines
import kotlinx.coroutines.runBlocking
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.transactions.transaction
import org.openqa.selenium.By
import org.openqa.selenium.WebDriver
import org.openqa.selenium.WebDriverException
import org.openqa.selenium.chrome.ChromeDriver
import org.openqa.selenium.chrome.ChromeOptions
import java.io.File
import java.time.LocalDateTime
import kotlin.time.Duration.Companion.seconds

private const val amazonVineBaseUrl = "https://www.amazon.de/vine/vine-items?queue=encore"

/**
 * @param name the name of the category which can be mapped to the database data
 * @url the suffix after [amazonVineBaseUrl]
 */
internal data class Category(val url: String, val name: String)

internal val categories = listOf(
    Category("&pn=355007011", "Baby"),
    Category("&pn=12950651", "Spielzeug"),
    Category("&pn=84230031", "Kosmetik"),
    Category("&pn=340846031", "Lebensmittel & Getränke"),
    Category("&pn=64187031", "Drogerie & Körperpflege"),
    Category("&pn=11961464031", "Fashion"),
    Category("&pn=908823031", "Elektro-Großgeräte"),
    Category("&pn=340849031", "Musikinstrumente & DJ-Equipment"),
    Category("&pn=16435051", "Sport & Freizeit"),
    Category("&pn=10925031", "Garten"),
    Category("&pn=5866098031", "Gewerbe, Industrie & Wissenschaft"),
    Category("&pn=213083031", "Beleuchtung"),
    Category("&pn=562066", "Elektronik & Foto"),
    Category("&pn=80084031", "Baumarkt"),
    Category("&pn=300992", "Games"),
    Category("&pn=72921031", "Sonstiges"),
    Category("&pn=78191031", "Auto & Motorrad"),
    Category("&pn=192416031", "Bürobedarf & Schreibwaren"),
    Category("&pn=3167641", "Küche, Haushalt & Wohnen"),
    Category("&pn=340843031", "Computer & Zubehör"),
    Category("&pn=340852031", "Haustier"),
)

object AmazonVine {

    internal const val RELATIVE_CACHE_DIRECTORY = "cache/vine"
    internal val cacheDirectory = File(RELATIVE_CACHE_DIRECTORY)
    private const val MAX_VARCHAR_LENGTH = 9999999
    private var database by mutableStateOf<Database?>(null)

    fun setupDatabase() {
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        if (database != null) {
            return
        }
        val dbFile = settings.vineDatabasePath
        if (!File(dbFile).exists()) {
            showErrorNotification(
                "Amazon Vine database file does not exist!",
                settings.vineDatabasePath
            )
            return
        }
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
    }

    fun findArticleByWebDriver(article: Article, maxPagesToCheck: Int) {

        /**
         * @return a list with 3 elements
         */
        fun getCountParts(driver: WebDriver): List<Int> {
            val container = driver.findElement(By.id("vvp-items-grid-container"))
            val rawText = container.findElement(By.tagName("p")).text
            val regex = "(\\d+)".toRegex()
            // "Anzeige 2.809–2.821 von 2.821 Ergebnissen"
            val text = rawText.replace(".", "")
            return regex.findAll(text).toList().map {
                it.value.toInt()
            }
        }

        fun isOnLastPage(driver: WebDriver): Boolean {
            val parts = getCountParts(driver)
            try {
                return parts[1] == parts[2]
            } catch (_: IndexOutOfBoundsException) {
                val currentUrl = driver.currentUrl ?: throw IllegalStateException("Driver currentUrl is null?")
                // this is a workaround in case the modal does not close
                driver.get(currentUrl)
                return false
            }
        }

        val options = ChromeOptions()
        options.addArguments("user-data-dir=selenium")
        var driver: WebDriver? = null
        try {
            driver = ChromeDriver(options)
            driver.manage().window().maximize()
            val suffixUrl = categories.find { it.name == article.category }!!.url
            driver.get(amazonVineBaseUrl + suffixUrl)
            var pageNumber = 0
            var found = false
            while (true) {
                pageNumber++
                if (pageNumber >= maxPagesToCheck) {
                    break
                }
                val all = driver.findElements(By.className("vvp-item-tile"))
                all.forEach {
                    val anchor = it.findElement(By.className("a-link-normal"))
                    var articleLink = anchor.getDomAttribute("href")
                    articleLink = "https://www.amazon.de$articleLink"
                    if (articleLink == article.link) {
                        found = true
                        Thread.sleep(1.seconds.inWholeMilliseconds)
                        driver.executeScript(
                            "arguments[0].scrollIntoView({behavior: 'auto', block: 'center', inline: 'center'});",
                            it
                        )
                        // click "Weitere Details" to open the popup to request the article
                        it.findElement(By.className("a-button-primary")).click()
                        return@forEach
                    }
                }
                if (found) {
                    break
                }
                Thread.sleep(1.seconds.inWholeMilliseconds)
                if (isOnLastPage(driver)) {
                    break
                }
                try {
                    driver.findElement(By.className("a-last")).click()
                } catch (_: NoSuchElementException) {
                    val currentUrl = driver.currentUrl ?: throw IllegalStateException("WebDriver currentUrl is null?")
                    driver.get(currentUrl)
                }
            }
            if (found) {
                runBlocking {
                    Osascript.showDismissableDialog(
                        "Found Vine article! 'Produkt anfordern' in browser, then dismiss this dialog:\n\n" + article.title
                    )
                }
            } else {
                Hammerspoon.showAlert("Not found in $maxPagesToCheck pages:\n\n" + article.title.breakWithNewlines())
            }
        } catch (e: WebDriverException) {
            showErrorNotification("WebDriver error on finding article", e.message ?: "Unknown error")
        } finally {
            driver?.quit()
        }
    }

    fun fetchCategories(): List<String> {
        if (database == null) {
            return emptyList()
        }
        return transaction(database) {
            return@transaction Table.selectAll()
                .map { it[Table.category] }
                .filter { it.isNotBlank() }
                .distinct()
        }
    }

    fun fetchArticles(search: String, freeOnly: Boolean, category: String?): List<Article> {
        if (database == null) {
            return emptyList()
        }
        return transaction(database) {
            val query = Table.selectAll()
            if (search.isNotBlank()) {
                search.trim().split(' ').forEach {
                    if (it.startsWith("-")) {
                        query.andWhere { Table.title notLike "%${it.drop(1)}%" }
                    } else {
                        query.andWhere { Table.title like "%$it%" }
                    }
                }
            }
            if (category != null) {
                query.andWhere { Table.category eq category }
            }
            if (freeOnly) {
                query.andWhere { Table.taxPrice eq "€0.00" }
            }
            val articles = query.map {
                Article(
                    title = it[Table.title],
                    link = it[Table.link],
                    taxPrice = it[Table.taxPrice],
                    category = it[Table.category],
                    imageUrl = it[Table.imageUrl],
                    dateAdded = LocalDateTime.parse(it[Table.dateAdded], DateTimeFormat.dateTimeSpaceSeparated),
                    pageNumber = it[Table.pageNumber],
                    ratingStar = it[Table.ratingStar],
                    ratingTotal = it[Table.ratingTotal],
                )
            }
            articles.sortedByDescending { it.dateAdded }
        }
    }

    private object Table : org.jetbrains.exposed.sql.Table("Articles") {
        val title = text("title")
        val taxPrice = text("tax_price")
        val link = varchar("link", MAX_VARCHAR_LENGTH).uniqueIndex()
        val category = text("category")
        val imageUrl = text("image_url")
        val dateAdded = text("date_added")
        val pageNumber = integer("page_number").nullable()
        val ratingStar = text("rating_star").nullable()
        val ratingTotal = integer("rating_total").nullable()
    }

    data class Article(
        val title: String,
        val taxPrice: String,
        val link: String,
        val category: String,
        val imageUrl: String,
        val dateAdded: LocalDateTime,
        /**
         * Starts at 1.
         */
        val pageNumber: Int? = null,
        val ratingStar: String? = null,
        val ratingTotal: Int? = null,
    )

}
