package dima.apps.amazonVine

import GlobalEvent
import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.FileWarning
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.RefreshCw
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.color.TailwindCssColors
import dima.dateTime.formatDateTimeRelative
import dima.dateTime.isToday
import dima.images.CachedInternetImage
import dima.os.openUrl
import dima.telegram.vine.sendToTelegramGroup
import dima.text.TextMarked
import dima.utils.*
import globalEvent
import isTextFieldFocused
import handleLostFocus

@Composable
internal fun Ui(
    appFocusRequester: FocusRequester,
    entries: List<AmazonVine.Article>,
    queryState: TextFieldValue,
    changeQueryState: (TextFieldValue) -> Unit,
    query: String,
    changeQuery: (String) -> Unit,
    selected: Int?,
    changeSelected: (Int) -> Unit,
    searchFocusRequester: FocusRequester,
    category: String?,
    freeOnly: Boolean,
    changeFreeOnly: (Boolean) -> Unit,
    listState: LazyListState,
    onCategoryChange: (String?) -> Unit,
) {
    Column(
        modifier = Modifier
            .padding(PaddingValues(start = 12.dp, top = 12.dp))
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    if ((it.key == Key.Escape || it.key == Key.Enter) && it.type == KeyEventType.KeyDown) {
                        appFocusRequester.requestFocus()
                        return@onPreviewKeyEvent true
                    }
                    return@onPreviewKeyEvent false
                }
                if (it.key == Key.Spacebar) {
                    // fix that hitting space after clicking on toggle free with the mouse, toggles the checkbox
                    return@onPreviewKeyEvent true
                }
                return@onPreviewKeyEvent it.handleAppMap()
            },
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 6.dp)
        )
        {
            Text(
                "Amazon Vine (${entries.size} Artikel)",
                color = GlobalStyling.getTextColor(),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
                style = TextStyle(fontSize = 18.sp),
                modifier = Modifier
                    .padding(end = 12.dp)
                    .focusable(),
            )
            Icon(
                Lucide.RefreshCw,
                contentDescription = null,
                tint = GlobalStyling.getTextColor(),
                modifier = Modifier
                    .clickableWithoutBackgroundRipple {
                        globalEvent = GlobalEvent.Reload
                    }
                    .size(18.dp))
        }
        Row(
            horizontalArrangement = Arrangement.spacedBy(30.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(bottom = 12.dp)
                .fillMaxWidth()
        ) {
            TextFieldWithCandidateCountWithoutAnimation(
                value = queryState,
                onValueChange = {
                    changeQueryState(it)
                    changeQuery(it.text)
                    changeSelected(0)
                },
                topLabel = "Suche",
                singleLine = true, 
                modifier = Modifier
                    .onFocusChanged {
                        isTextFieldFocused = it.isFocused
                    }
                    .focusRequester(searchFocusRequester)
            )
            Text(
                category ?: "Alle Kategorien",
                color = GlobalStyling.getTextColor(),
                modifier = Modifier
                    .clickableWithoutBackgroundRipple {
                        openCategoryDialog(onCategoryChange = onCategoryChange)
                    }
            )
            CheckboxContainer("Nur kostenlose", freeOnly, onCheckedChange = {
                changeFreeOnly(it)
            })
        }
        Box(modifier = Modifier.fillMaxSize()) {
            LazyColumn(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.fillMaxSize().padding(end = GlobalStyling.ScrollBar.outerPadding),
                state = listState,
            ) {
                val selectedLink = if (selected == null) "" else entries.getOrNull(selected)?.link
                items(entries, key = { it.link }) { entry ->
                    val isSelected = entry.link == selectedLink
                    var itemModifier = Modifier
                        .fillMaxWidth(0.7f)
                    if (entry.link == entries.last().link) {
                        itemModifier = itemModifier.padding(bottom = 12.dp)
                    }
                    itemModifier = itemModifier
                        .background(
                            color = if (isSelected) {
                                GlobalStyling.getSelectedBackgroundColor()
                            } else {
                                GlobalStyling.getWindowBackgroundColor()
                            },
                            shape = RoundedCornerShape(5.dp)
                        )
                        .clickableWithoutBackgroundRipple {
                            changeSelected(entries.indexOf(entry))
                            openUrl(entry.link)
                        }
                    Column(modifier = itemModifier.padding(12.dp)) {
                        Row {
                            val size = 120.dp
                            // handle case when Amazon provides a broken image
                            if (entry.imageUrl.contains("undefined")) {
                                Icon(
                                    Lucide.FileWarning,
                                    contentDescription = null,
                                    tint = TailwindCssColors.red600,
                                    modifier = Modifier.padding(12.dp).size(size)
                                )
                            } else {
                                Column(modifier = Modifier.size(size)) {
                                    CachedInternetImage(
                                        imageUrl = entry.imageUrl,
                                        cacheDir = AmazonVine.cacheDirectory,
                                        logCacheDir = AmazonVine.RELATIVE_CACHE_DIRECTORY,
                                        modifier = Modifier
                                            .size(size)
                                            .shadow(if (isSelected) 1.dp else 0.dp, RoundedCornerShape(4.dp))
                                    )
                                }
                            }
                            Column(modifier = Modifier.padding(start = 12.dp)) {
                                Row(
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    when (entry.taxPrice) {
                                        "€0.00" -> {
                                            Text(
                                                "kostenlos", color = GlobalStyling.getBoldTextColor(),
                                                fontWeight = FontWeight.Bold,
                                            )
                                        }

                                        "" -> {
                                            Text("???", color = TailwindCssColors.red700)
                                        }

                                        else -> {
                                            Text(entry.taxPrice, color = GlobalStyling.getRedTextColor())
                                        }
                                    }
                                    Row(horizontalArrangement = Arrangement.spacedBy(24.dp)) {
                                        Text(
                                            "In Vine öffnen",
                                            fontSize = 12.sp,
                                            color = GlobalStyling.getGrayColor(),
                                            modifier = Modifier
                                                .clip(RoundedCornerShape(5.dp))
                                                .clickableWithoutBackgroundRipple {
                                                    findInVine(entry)
                                                }
                                                .padding(4.dp)
                                        )
                                        Text(
                                            "An Telegram-Gruppe senden",
                                            fontSize = 12.sp,
                                            color = GlobalStyling.getGrayColor(),
                                            modifier = Modifier
                                                .clip(RoundedCornerShape(5.dp))
                                                .clickableWithoutBackgroundRipple {
                                                    val result = entry.sendToTelegramGroup()
                                                    when (result) {
                                                        is SimpleResult.Error -> showErrorNotification(
                                                            "Failed to send to Telegram",
                                                            result.error
                                                        )

                                                        is SimpleResult.Success -> showNotification(
                                                            "Sent to Telegram group",
                                                            entry.title
                                                        )
                                                    }
                                                }
                                                .padding(4.dp)
                                        )
                                    }
                                }
                                if (entry.ratingStar != null && entry.ratingTotal != null) {
                                    val text = if (entry.ratingTotal == 1) {
                                        "${entry.ratingStar} / 5.0   ${entry.ratingTotal} Bewertung"
                                    } else {
                                        "${entry.ratingStar} / 5.0   ${entry.ratingTotal} Bewertungen"
                                    }
                                    Text(text, color = GlobalStyling.getGrayColor(), modifier = Modifier.padding(top = 8.dp))
                                }
                                TextMarked(entry.title, query, modifier = Modifier.padding(vertical = 8.dp), color = GlobalStyling.getTextColor())
                                val isToday = entry.dateAdded.isToday()
                                Text(
                                    entry.category + ", Seite ${entry.pageNumber}, ${entry.dateAdded.formatDateTimeRelative()}",
                                    color = if (isToday) TailwindCssColors.white else GlobalStyling.getGrayColor(),
                                    fontSize = if (isToday) 14.sp else 12.sp,
                                    modifier = if (isToday) {
                                        Modifier
                                            .background(
                                                color = TailwindCssColors.red600,
                                                shape = RoundedCornerShape(4.dp)
                                            )
                                            .padding(horizontal = 6.dp, vertical = 2.dp)
                                    } else {
                                        Modifier
                                    }
                                )
                            }
                        }
                    }
                }
            }
            VerticalScrollbar(
                modifier = Modifier.align(Alignment.CenterEnd).fillMaxHeight().padding(end = GlobalStyling.ScrollBar.outerPadding, bottom = 12.dp),
                style = scrollbarStyleThemed(),
                adapter = rememberScrollbarAdapter(scrollState = listState)
            )
        }
    }
}
