package dima.apps.textEditor

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.treeSitter.IntellijDarculaTheme

/**
 * Shared styling for:
 * - [dima.dialogs.readOnlyTextEditor.ReadOnlyTextEditorDialogDialog]
 * - [dima.apps.textEditor.TextEditorApp]
 */
object TextEditorStyling {

    val fontSize = 18.sp
    val borderWidth = 2.dp
    val roundedShape = RoundedCornerShape(5.dp)

    val greyBorderColor = TailwindCssColors.gray300
    val selectedCharLineBackgroundColor = TailwindCssColors.blue600
    val jumpCharBorderColor = TailwindCssColors.green500
    val insertModeBorderColor = TailwindCssColors.orange400
    val visualModeBorderColor = TailwindCssColors.blue400

    fun getVisualHightlingBackgroundColor(): Color {
        return if (GlobalState.isDarkMode) {
            IntellijDarculaTheme.visualBackground
        } else {
            TailwindCssColors.blue300
        }
    }

}