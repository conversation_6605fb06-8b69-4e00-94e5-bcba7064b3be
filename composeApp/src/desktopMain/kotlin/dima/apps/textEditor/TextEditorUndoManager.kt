package dima.apps.textEditor

internal data class TextEditorUndoState(val text: String, val position: Position)

/**
 * Note that history has no capacity management, since it would break the initial empty string state.
 */
internal class TextEditorUndoManager {

    private val history = mutableListOf<TextEditorUndoState>()
    private var index = 0
    private var lastText = ""

    /**
     * Added so the changed text which was undone, it not again added to the history.
     */
    private var inUndoSequence = false

    fun resetIndex() {
        inUndoSequence = false
        index = history.size - 1
    }

    fun undo(): TextEditorUndoState {
        inUndoSequence = true
        if (index == 0) {
            return TextEditorUndoState("", Position())
        }
        if (index > 0) {
            index--
            return history[index]
        }
        return TextEditorUndoState("", Position())
    }

    fun redo(): TextEditorUndoState {
        inUndoSequence = true
        if (index < history.size - 1) {
            index++
            return history[index]
        }
        if (history.size == 0) {
            return TextEditorUndoState("", Position())
        }
        return history.last()
    }

    fun remember(text: String, position: Position) {
        if (inUndoSequence) {
            return
        }
        inUndoSequence = false
        if (text == lastText) {
            if (history.isEmpty()) {
                return
            }
            if (position == history.last().position) {
                return
            }
            history[history.lastIndex] = TextEditorUndoState(lastText, position)
        } else {
            history.add(TextEditorUndoState(text, position))
            index = history.size - 1
            lastText = text
        }
    }

}
