package dima.apps.textEditor

import androidx.compose.foundation.background
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import dima.dialogs.readOnlyTextEditor.isLineMarkedInVisualMode
import dima.utils.getBoundingBoxSafe

/**
 * Set a background color for the entire line when in [TextEditorMode.VisualLine] and on the current line.
 */
fun Modifier.entireVisualLineSelectionBackground(
    textEditorMode: TextEditorMode,
    isLineMarkedInVisualMode: Boolean
): Modifier {
    return then(
        if (textEditorMode == TextEditorMode.VisualLine && isLineMarkedInVisualMode) {
            Modifier.background(TextEditorStyling.getVisualHightlingBackgroundColor())
        } else {
            Modifier
        }
    )
}

/**
 * Draw the partial background when in [TextEditorMode.Visual] for the characters selected.
 */
fun Modifier.visualModeBackground(
    textEditorMode: TextEditorMode,
    line: Line,
    lines: List<Line>,
    position: Position,
    visualStartAnchor: Position?,
    textWidth: Dp,
    isMonospace: Boolean,
    fontSize: TextUnit
): Modifier {
    return then(
        if (textEditorMode == TextEditorMode.Visual
            && isLineMarkedInVisualMode(line.lineNumber, visualStartAnchor, position)
            && visualStartAnchor != null
        ) {
            var startPosition = position
            var endPosition = visualStartAnchor
            val startIndex = point(lines, position)
            val endIndex = point(lines, visualStartAnchor)
            // skip rendering since the cursor background overrides this
            if (startIndex == endIndex || startIndex == (endIndex - 1)) {
                Modifier
            } else {
                if (startIndex > endIndex) {
                    val temp2 = startPosition
                    startPosition = endPosition
                    endPosition = temp2
                }
                val paragraph = getCachedMultiParagraph(
                    line = line.text,
                    textWidth = textWidth,
                    isMonospace = isMonospace,
                    fontSize = fontSize
                )
                if (visualStartAnchor.lineNumber == position.lineNumber) {
                    val startChar = paragraph.getBoundingBoxSafe(startPosition.column)
                    val endChar = paragraph.getBoundingBoxSafe(endPosition.column)
                    Modifier.drawBehind {
                        if (startChar.top == endChar.top) {
                            drawVisualRectangle(
                                TextEditorStyling.getVisualHightlingBackgroundColor(),
                                x = startChar.left,
                                y = startChar.top,
                                // use right to display the last character before the newline
                                width = endChar.right - startChar.left,
                                height = startChar.height
                            )
                        } else {
                            // same line number, but different virtual lines
                            val startLineNumber = paragraph.getLineForOffset(startPosition.column)
                            drawVisualRectangle(
                                TextEditorStyling.getVisualHightlingBackgroundColor(),
                                x = startChar.left,
                                y = startChar.top,
                                width = size.width - startChar.left,
                                height = startChar.height
                            )
                            val endLineNumber = paragraph.getLineForOffset(endPosition.column)
                            drawVisualRectangle(
                                TextEditorStyling.getVisualHightlingBackgroundColor(),
                                x = 0f,
                                y = endChar.top,
                                width = endChar.right,
                                height = endChar.height
                            )
                            if (startLineNumber + 1 != endLineNumber) {
                                drawVisualRectangle(
                                    TextEditorStyling.getVisualHightlingBackgroundColor(),
                                    x = 0f,
                                    y = startChar.bottom,
                                    width = size.width,
                                    height = endChar.top - startChar.bottom
                                )
                            }
                        }
                    }
                } else {
                    // visual marker is on different line
                    Modifier.drawBehind {
                        if (line.lineNumber == startPosition.lineNumber) {
                            // first line
                            val startChar = paragraph.getBoundingBoxSafe(startPosition.column)
                            val lastChar = paragraph.getBoundingBoxSafe(lines[startPosition.lineNumber].text.length - 1)
                            drawVisualRectangle(
                                TextEditorStyling.getVisualHightlingBackgroundColor(),
                                x = startChar.left,
                                y = startChar.top,
                                width = size.width - startChar.left,
                                height = startChar.height
                            )
                            if (startChar.top != lastChar.top) {
                                drawVisualRectangle(
                                    TextEditorStyling.getVisualHightlingBackgroundColor(),
                                    x = 0f,
                                    y = startChar.bottom,
                                    width = size.width,
                                    height = lastChar.bottom - startChar.bottom
                                )
                            }
                        } else if (line.lineNumber == endPosition.lineNumber) {
                            // last line
                            val startChar = paragraph.getBoundingBoxSafe(0)
                            val endChar = paragraph.getBoundingBoxSafe(endPosition.column)
                            drawVisualRectangle(
                                TextEditorStyling.getVisualHightlingBackgroundColor(),
                                x = 0f,
                                y = endChar.top,
                                width = endChar.right,
                                height = endChar.height
                            )
                            if (startChar.top != endChar.top) {
                                drawVisualRectangle(
                                    TextEditorStyling.getVisualHightlingBackgroundColor(),
                                    x = 0f,
                                    y = 0f,
                                    width = size.width,
                                    height = endChar.top
                                )
                            }
                        } else {
                            // middle line
                            drawVisualRectangle(
                                TextEditorStyling.getVisualHightlingBackgroundColor(),
                                x = 0f,
                                y = 0f,
                                width = size.width,
                                height = size.height
                            )
                        }
                    }
                }
            }
        } else {
            Modifier
        }
    )
}