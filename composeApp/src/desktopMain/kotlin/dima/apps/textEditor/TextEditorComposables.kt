package dima.apps.textEditor

import Globals
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.MultiParagraph
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import dima.utils.Ring

internal data class LineMeasurement(
    val line: String,
    val width: Dp,
    val result: MultiParagraph,
    val isMonospace: Boolean,
    val fontSize: TextUnit
)

/**
 * Cache since measuring a really long line takes a lot of CPU. Unfortunately, on editing the line this cache
 * always misses, but at least this is a good solution for viewing content.
 */
internal val lineMeasureCache = Ring<LineMeasurement>(100)

internal fun measureTextLine(
    line: String,
    textWidth: Dp,
    column: Int,
    isMonospace: <PERSON>olean,
    fontSize: TextUnit
): Rect {
    fun atChar(paragraph: MultiParagraph): Rect {
        return try {
            paragraph.getBoundingBox(column)
        } catch (e: IllegalArgumentException) {
            val measurer = TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
            val pxWidth = with(Globals.density) {
                textWidth.roundToPx()
            }
            val constraints = Constraints(maxWidth = pxWidth)
            // this happens when the line is empty
            measurer.measure(
                // use lowercase L which takes up a minimal amount of space for command mode
                line + "l",
                style = TextStyle(
                    fontFamily = if (isMonospace) FontFamily.Monospace else FontFamily.Default,
                    fontSize = fontSize,
                ),
                constraints = constraints
            ).getBoundingBox(line.length)
        }
    }

    val cached = lineMeasureCache.find {
        it.line == line &&
                it.width == textWidth &&
                it.isMonospace == isMonospace &&
                it.fontSize == fontSize
    }
    return if (cached == null) {
        val measurer = TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
        val pxWidth = with(Globals.density) {
            textWidth.roundToPx()
        }
        val constraints = Constraints(maxWidth = pxWidth)
        val measure =
            measurer.measure(
                line,
                style = TextStyle(
                    fontSize = fontSize,
                    fontFamily = if (isMonospace) FontFamily.Monospace else FontFamily.Default,
                ),
                constraints = constraints
            )
        lineMeasureCache.add(LineMeasurement(line, textWidth, measure.multiParagraph, isMonospace, fontSize))
        atChar(measure.multiParagraph)
    } else {
        atChar(cached.result)
    }
}

internal fun getCachedMultiParagraph(
    line: String,
    textWidth: Dp,
    isMonospace: Boolean,
    fontSize: TextUnit
): MultiParagraph {
    val cached = lineMeasureCache.find {
        it.line == line &&
                it.width == textWidth &&
                it.isMonospace == isMonospace &&
                it.fontSize == fontSize
    }
    return if (cached == null) {
        val measurer = TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
        val pxWidth = with(Globals.density) {
            textWidth.roundToPx()
        }
        val constraints = Constraints(maxWidth = pxWidth)
        val paragraph = measurer.measure(
            line,
            style = TextStyle(
                fontSize = fontSize,
                fontFamily = if (isMonospace) FontFamily.Monospace else FontFamily.Default,
            ),
            constraints = constraints
        ).multiParagraph
        lineMeasureCache.add(LineMeasurement(line, textWidth, paragraph, isMonospace, fontSize))
        paragraph
    } else {
        cached.result
    }
}

internal fun DrawScope.drawVisualRectangle(color: Color, x: Float, y: Float, width: Float, height: Float) {
    drawRect(
        color = color,
        topLeft = Offset(x, y),
        size = Size(width, height)
    )
}
