package dima.apps.textEditor

import dima.apps.AppType
import dima.globalState.GlobalState
import kotlin.time.Duration.Companion.seconds

class TextEditorSaveTextToDatabaseThread : Thread() {
    override fun run() {
        val sleepDuration = 3.seconds.inWholeMilliseconds
        while (true) {
            sleep(sleepDuration)
            if (GlobalState.app == AppType.TextEditor) {
                TextEditor.saveCurrentContentToDatabase()
            }
        }
    }
}
