package dima.apps.textEditor

import GlobalEvent
import TextEditorCharJumpMode
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.MultiParagraph
import androidx.compose.ui.unit.dp
import dev.snipme.highlights.internal.isNewLine
import dima.ai.singlePromptApp.launchAiSinglePromptApp
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.clipboard.readClipboard
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dialogs
import dima.dialogs.help.appKeys
import dima.dialogs.readOnlyTextEditor.isLineMarkedInVisualMode
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.os.homeWithoutSlash
import dima.utils.*
import globalEvent
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import textEditorJumpCharMode
import textEditorMode
import java.io.File
import kotlin.time.Duration.Companion.seconds

/**
 * The text measurement in Compose is very slow, so if a single line has about 2 MB of text, inserting any character
 * lags too much since the measurement take place in a single [Text]. I tried splitting up the text, but it leads to crashes.
 * One could also split the long line into multiple [Text] calls, but maybe crashes might still occur, and it complicates the rendering
 * logic a lot, so I am not doing that.
 * So STOP investigating performance improvements for extremely long line scenarios, just use Emacs for that.
 * It crashes like this:
 *
 * Or did it only crash because of my messed up Modifier.drawBehind?
 *
 * Text(line.text.substring(
 * (position.column - 9999).coerceAtLeast(0),
 * (position.column + 9999).coerceAtMost(line.text.length))
 *
 * If the 2 MB is split with newlines, performance is definitely fine!
 */
@Suppress("GrazieInspection")
@Composable
fun TextEditorApp() {
    remember {
        TextEditor.setupDatabase()
    }
    val file = remember {
//        File("$homeWithoutSlash/Downloads/8mb.json")
//        File("$homeWithoutSlash/Developer/kotlin-emacs/composeApp/src/desktopMain/kotlin/dima/apps/texteditor/TextEditorApp.kt")
//        File("$homeWithoutSlash/Downloads/2mb.json")
//        File("$homeWithoutSlash/Downloads/2mb-no-newlines.json")
        val f = File("$homeWithoutSlash/Downloads/small.txt")
        if (f.exists()) {
            f
        } else {
            val tmpFile = File.createTempFile("kotlin-emacs", "txt")
            tmpFile.writeText("tets\n1230\n".repeat(100))
            tmpFile
        }
    }
    val undoManager = remember {
        TextEditorUndoManager()
    }
    var text by remember {
        mutableStateOf(
            file
                .readText()
                .replace("\r\n", "\n")
        )
    }
    var lines: List<Line> by remember {
        mutableStateOf(text.splitToSequence("\n").toList().mapIndexed { index, line ->
            Line(line, index)
        })
    }
    var visualStartAnchor by remember { mutableStateOf<Position?>(null) }
    var hasLongLines by remember { mutableStateOf(TextEditor.hasLongLines(lines)) }
    var position by remember { mutableStateOf(Position()) }
    val scrollState = rememberLazyListState()
    val appFocusRequester = remember { FocusRequester() }
    // used for the line numbers width
    var maxLineNumberCharCount by remember { mutableStateOf(lines.size.toString().length) }
    var doSmoothScrollNext by remember { mutableStateOf(false) }
    var textWidth by remember { mutableStateOf(800.dp) }
    var lastKey by remember { mutableStateOf<Key?>(null) }
    var isMonospace by remember { mutableStateOf(false) }
    var selectedJumpToChars by remember { mutableStateOf(emptyList<Key>()) }

    /**
     * This is not [Position.column], but the X offset.
     */
    var columnGoal by remember { mutableStateOf(0) }
    var columnGoalChanged by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    /**
     * Always check [textEditorJumpCharMode] first.
     */
    var charJumpers by remember { mutableStateOf<List<CharJumperEntry>>(emptyList()) }

    /**
     * When scrolling inside [LaunchedEffect] (checking for position), the performance is not as good and leads to visual janks.
     * So always call this to modify the position.
     */
    fun setPositionAndScroll(newPosition: Position) {
        position = newPosition
        // store value since the launch block below is async and will lose the state for Key.M and Key.V
        val doSmooth = doSmoothScrollNext
        coroutineScope.launch {
            val line = lines[position.lineNumber]
            val paragraph = getCachedMultiParagraph(line.text, textWidth, isMonospace, TextEditorStyling.fontSize)
            val lineCount = paragraph.lineCount
            val defaultOffset = -500
            val scrollOffset = if (lineCount == 1) {
                defaultOffset
            } else {
                try {
                    (paragraph.getBoundingBox(position.column).top + defaultOffset).toInt()
                } catch (_: IllegalArgumentException) {
                    (paragraph.getBoundingBox(position.column - 1).top + defaultOffset).toInt()
                }
            }
            if (doSmooth) {
                doSmoothScrollNext = false
                scrollState.animateScrollToItem(position.lineNumber, scrollOffset)
            } else {
                scrollState.scrollToItem(position.lineNumber, scrollOffset)
            }
        }
    }

    fun goToCharacterLeft() {
        if (position.column == 0) {
            if (position.lineNumber >= 1) {
                columnGoalChanged = true
                setPositionAndScroll(
                    Position((position.lineNumber - 1).coerceAtLeast(0), lines[position.lineNumber - 1].text.length)
                )
            }
        } else {
            columnGoalChanged = true
            setPositionAndScroll(Position(position.lineNumber, (position.column - 1)))
        }
    }

    fun goToCharacterRight() {
        if (position.column == lines[position.lineNumber].text.length) {
            if (position.lineNumber < lines.size - 1) {
                columnGoalChanged = true
                setPositionAndScroll(Position((position.lineNumber + 1).coerceAtMost(lines.size - 1), 0))
            }
        } else {
            columnGoalChanged = true
            setPositionAndScroll(Position(position.lineNumber, (position.column + 1)))
        }
    }

    fun getCharacterBoxForLineMovement(lineIndex: Int): Pair<MultiParagraph, Float> {
        val line = lines[lineIndex]
        val previousParagraph = getCachedMultiParagraph(line.text, textWidth, isMonospace, TextEditorStyling.fontSize)
        val currentLine = lines[position.lineNumber]
        val xOffset = if (currentLine.text.isEmpty()) {
            0f
        } else {
            val currentParagraph = getCachedMultiParagraph(
                lines[position.lineNumber].text,
                textWidth,
                isMonospace,
                TextEditorStyling.fontSize
            )
            val currentBox = currentParagraph.getBoundingBoxSafe(position.column)
            currentBox.left
        }
        return Pair(previousParagraph, xOffset)
    }

    fun goToLineUp() {

        fun toPreviousRealLine() {
            val newLine = (position.lineNumber - 1).coerceAtLeast(0)
            if (newLine == position.lineNumber) {
                return
            }
            val (previousParagraph, xOffset) = getCharacterBoxForLineMovement(newLine)
            val yOffset = previousParagraph.getLineTop(previousParagraph.lineCount - 1)
            val newOffset = previousParagraph.getOffsetForPosition(Offset(x = xOffset, y = yOffset))
            setPositionAndScroll(
                position.copy(
                    lineNumber = newLine,
                    column = newOffset
                )
            )
        }

        if (textEditorMode == TextEditorMode.VisualLine) {
            if (position.lineNumber >= 1) {
                setPositionAndScroll(
                    position.copy(
                        lineNumber = position.lineNumber - 1,
                        column = position.column.coerceAtMost(lines[position.lineNumber - 1].text.length)
                    )
                )
            }
            return
        }
        val paragraph =
            getCachedMultiParagraph(lines[position.lineNumber].text, textWidth, isMonospace, TextEditorStyling.fontSize)
        val lineCount = paragraph.lineCount
        if (lineCount == 1) {
            toPreviousRealLine()
        } else {
            val currentLine = paragraph.getLineForOffset(position.column)
            if (currentLine == 0) {
                toPreviousRealLine()
            } else {
                val box = try {
                    paragraph.getBoundingBox(position.column)
                } catch (_: IllegalArgumentException) {
                    // handle when on newline
                    paragraph.getBoundingBox(position.column - 1)
                }
                val newOffset = paragraph.getOffsetForPosition(Offset(x = box.left, y = box.top - 1f))
                setPositionAndScroll(
                    position.copy(
                        column = newOffset
                    )
                )
            }
        }
    }

    fun goToLineDown() {

        fun toNextRealLine() {
            val newLine = (position.lineNumber + 1).coerceAtMost(lines.size - 1)
            if (newLine == position.lineNumber) {
                return
            }
            val (nextParagraph, xOffset) = getCharacterBoxForLineMovement(newLine)
            val yOffset = nextParagraph.getLineTop(0)
            val newOffset = nextParagraph.getOffsetForPosition(Offset(x = xOffset, y = yOffset))
            setPositionAndScroll(
                position.copy(
                    lineNumber = newLine,
                    column = newOffset
                )
            )
        }

        if (textEditorMode == TextEditorMode.VisualLine) {
            if (position.lineNumber < lines.size - 1) {
                setPositionAndScroll(
                    position.copy(
                        lineNumber = position.lineNumber + 1,
                        column = position.column.coerceAtMost(lines[position.lineNumber + 1].text.length)
                    )
                )
            }
            return
        }
        val paragraph =
            getCachedMultiParagraph(lines[position.lineNumber].text, textWidth, isMonospace, TextEditorStyling.fontSize)
        val lineCount = paragraph.lineCount
        if (lineCount == 1) {
            toNextRealLine()
        } else {
            val currentLine = paragraph.getLineForOffset(position.column)
            val isLastLine = currentLine + 1 == lineCount
            if (isLastLine) {
                toNextRealLine()
            } else {
                val box = paragraph.getBoundingBox(position.column)
                val newOffset = paragraph.getOffsetForPosition(Offset(x = box.left, y = box.bottom + 1f))
                setPositionAndScroll(
                    position.copy(
                        column = newOffset
                    )
                )
            }
        }
    }

    /**
     * Note that this is terribly slow and should rarely be used.
     */
    fun updateLinesByText() {
        lines = text.splitToSequence("\n").toList().mapIndexed { index, line ->
            Line(line, index)
        }
    }

    fun getAllMarkedLinesInVisualMode(): List<Line> {
        return lines.filter { isLineMarkedInVisualMode(it.lineNumber, visualStartAnchor, position) }
    }

    /**
     * Inverse of findPositionByTextIndex().
     */
    fun point(pos: Position = position, ignoreColumn: Boolean = false): Int {
        return point(lines, pos, ignoreColumn)
    }

    /**
     * Inverse of point().
     */
    fun findPositionByTextIndex(index: Int): Position {
        return findPositionByTextIndex(lines, index)
    }

    fun onEscape() {
        if (textEditorJumpCharMode != TextEditorCharJumpMode.NotActive) {
            if (selectedJumpToChars.isNotEmpty()) {
                selectedJumpToChars = emptyList()
                return
            }
            textEditorJumpCharMode = TextEditorCharJumpMode.NotActive
            return
        }
        visualStartAnchor = null
        textEditorMode = TextEditorMode.Command
    }

    fun maybeNextPositionChangeWithSmoothScroll() {
        doSmoothScrollNext = !hasLongLines
    }

    fun jumpTo(to: CharJumperEntry) {
        undoManager.resetIndex()
        val newLine = lines[to.line]
        val paragraph = getCachedMultiParagraph(newLine.text, textWidth, isMonospace, TextEditorStyling.fontSize)
        // apply minimal Y offset to not end up in one virtual line above - not sure why this is needed
        val offset = paragraph.getOffsetForPosition(Offset(x = to.x, y = to.y + 1f))
        maybeNextPositionChangeWithSmoothScroll()
        setPositionAndScroll(position.copy(lineNumber = newLine.lineNumber, column = offset))
    }

    fun onBackspace() {
        if (position.lineNumber == 0 && position.column == 0) {
            return
        }
        position = if (position.column == 0) {
            position.copy(
                lineNumber = position.lineNumber - 1,
                column = lines[position.lineNumber - 1].text.length
            )
        } else {
            position.copy(column = (position.column - 1).coerceAtLeast(0))
        }
        val index = point() + 1
        text = text.substring(0, index - 1) + text.substring(index)
        updateLinesByText()
    }

    /**
     * Note that this does not work for [TextEditorMode.VisualLine].
     */
    fun getVisualModeText(): String {
        var start = point()
        var end = point(visualStartAnchor!!)
        if (start > end) {
            val temp = start
            start = end
            end = temp
        }
        val txt = text.substring(start, end)
        return txt
    }

    fun deleteMarkedVisualLines() {
        val isVisualLine = textEditorMode == TextEditorMode.VisualLine
        if (isVisualLine || textEditorMode == TextEditorMode.Command) {
            val newLines = mutableListOf<Line>()
            var linesDeleted = 0
            var newLineIndex = 0
            text = buildString {
                if (textEditorMode == TextEditorMode.Command) {
                    lines.forEachIndexed { index, line ->
                        if (position.lineNumber == index) {
                            linesDeleted++
                        } else {
                            newLines.add(line.copy(lineNumber = newLineIndex++))
                            append(line.text)
                            if (line.text != "") {
                                append("\n")
                            }
                        }
                    }
                } else {
                    lines.forEachIndexed { index, line ->
                        if (isLineMarkedInVisualMode(index, visualStartAnchor, position)) {
                            linesDeleted++
                        } else {
                            newLines.add(line.copy(lineNumber = newLineIndex++))
                            append(line.text)
                            if (line.text != "") {
                                append("\n")
                            }
                        }
                    }
                }
            }
            if (newLines.isEmpty()) {
                lines = listOf(Line("", 0))
                setPositionAndScroll(Position())
                visualStartAnchor = null
                textEditorMode = TextEditorMode.Command
                return
            }
            lines = newLines
            if (isVisualLine && visualStartAnchor != null && visualStartAnchor!!.lineNumber <= position.lineNumber) {
                val newLine = (position.lineNumber - linesDeleted + 1)
                    .coerceAtLeast(0)
                    .coerceAtMost(lines.size - 1)
                val newColumn = position.column.coerceAtMost(lines[newLine].text.length)
                setPositionAndScroll(position.copy(lineNumber = newLine, column = newColumn))
                visualStartAnchor = null
                textEditorMode = TextEditorMode.Command
            } else {
                val newLine = (position.lineNumber - linesDeleted + 1)
                    .coerceAtLeast(0)
                    .coerceAtMost(lines.size - 1)
                val newColumn = position.column.coerceAtMost(lines[newLine].text.length)
                setPositionAndScroll(position.copy(lineNumber = newLine, column = newColumn))
                if (isVisualLine) {
                    visualStartAnchor = null
                    textEditorMode = TextEditorMode.Command
                }
            }
        } else if (textEditorMode == TextEditorMode.Visual) {
            var start = point()
            var end = point(visualStartAnchor!!)
            var newStart = position
            if (start > end) {
                val temp = start
                start = end
                end = temp
                newStart = visualStartAnchor!!
            }
            val visualText = text.substring(start, end)
            if (visualText.isEmpty()) {
                return
            }
            text = text.replaceRange(
                start,
                start + visualText.length,
                ""
            )
            updateLinesByText()
            setPositionAndScroll(newStart)
            visualStartAnchor = null
            textEditorMode = TextEditorMode.Command
        }
    }

    fun updateUndoState() {
        undoManager.remember(text, position)
    }

    fun insertAtPoint(s: String) {
        undoManager.resetIndex()
        val index = point()
        text = buildString(text.length + s.length) {
            append(text, 0, index)
            append(s)
            append(text, index, text.length)
        }
        updateLinesByText()
        setPositionAndScroll(findPositionByTextIndex(index + s.length))
    }

    remember {
        // this is not used for Insert mode
        appKeys = listOf(
            AppKey(Key.F, "Redo", isShift = true) {
                val state = undoManager.redo()
                text = state.text
                updateLinesByText()
                setPositionAndScroll(state.position)
            },
            AppKey(Key.F, "Undo") {
                val state = undoManager.undo()
                text = state.text
                updateLinesByText()
                setPositionAndScroll(state.position)
            },
            AppKey(Key.K, "Paste") {
                val clipboard = readClipboard()
                if (clipboard == null) {
                    val fromHistory = TextEditorTextHistoryRing.getHistory().lastOrNull()
                    if (fromHistory == null) {
                        showErrorNotification("No text to paste")
                    } else {
                        insertAtPoint(fromHistory)
                    }
                } else {
                    insertAtPoint(clipboard)
                }
            },
            AppKey(Key.U, "Toggle monospace font", isCmd = true) {
                isMonospace = !isMonospace
                maybeNextPositionChangeWithSmoothScroll()
                setPositionAndScroll(position)
            },
            AppKey(Key.DirectionLeft, "Go to left character") {
                goToCharacterLeft()
            },
            AppKey(Key.DirectionRight, "Go to right character") {
                goToCharacterRight()
            },
            AppKey(Key.DirectionUp, "Move line or selection up") {
                val markedLines = if (textEditorMode == TextEditorMode.Command) {
                    listOf(lines[position.lineNumber])
                } else {
                    getAllMarkedLinesInVisualMode()
                }

                fun swapLines(lines: MutableList<Line>, lineIndex: Int) {
                    if (lineIndex > 0) {
                        val temp = lines[lineIndex - 1]
                        lines[lineIndex - 1] = lines[lineIndex].copy(lineNumber = lineIndex - 1)
                        lines[lineIndex] = temp.copy(lineNumber = lineIndex)
                    }
                }

                if (markedLines.isNotEmpty()) {
                    val newLines = lines.toMutableList()
                    for (line in markedLines) {
                        val currentIndex = line.lineNumber
                        if (currentIndex > 0) {
                            swapLines(newLines, currentIndex)
                        }
                    }
                    lines = newLines
                    text = buildString {
                        lines.forEach {
                            append(it.text)
                            append("\n")
                        }
                    }
                    setPositionAndScroll(position.copy(lineNumber = position.lineNumber - markedLines.size))
                }
            },
            AppKey(Key.DirectionDown, "Move line or selection down") {
            },
            AppKey(Key.Backspace, "Delete character to the left") {
                onBackspace()
            },
            AppKey(Key.A, "Open dialog for more functionality") {
                val point = point()
                val candidates = buildList {
                    add(
                        CompletionDialogCandidate(
                            "Buffer name: " + GlobalState.textEditor.activeBufferName!!,
                            additionalSearchString = "bufferName"
                        )
                    )
                    add(
                        CompletionDialogCandidate(
                            "Point: $point, Line number: ${position.lineNumber + 1}, Column: ${position.column}",
                            additionalSearchString = "point"
                        )
                    )
                }
                openCompletionDialog(GlobalState.textEditor.activeBufferName!!, candidates) {
                    val choice = candidates[it.index!!]
                    if (choice.additionalSearchString == "point") {
                        copyToClipboard(it.text)
                    } else if (choice.additionalSearchString == "bufferName") {
                        copyToClipboard(GlobalState.textEditor.activeBufferName!!)
                    }
                }
            },
            AppKey(Key.Y, "Enter visual mode") {
                visualStartAnchor = position
                textEditorMode = TextEditorMode.Visual
            },
            AppKey(Key.U, "Enter insert mode") {
                if (textEditorMode == TextEditorMode.VisualLine) {
                    deleteMarkedVisualLines()
                }
                textEditorMode = TextEditorMode.Insert
            },
            AppKey(Key.Escape, "Exit out of insert or visual mode") { onEscape() },
            AppKey(Key.L, "Enter char to jump to") {
                textEditorJumpCharMode = TextEditorCharJumpMode.WaitingForFirstCharToJumpTo
                selectedJumpToChars = emptyList()
            },
            AppKey(Key.O, "Insert newline below") {
                val newText = buildString(text.length + 1) {
                    lines.forEach {
                        if (it.lineNumber == position.lineNumber) {
                            append(it.text)
                            append("\n\n")
                        } else {
                            append(it.text)
                            append("\n")
                        }
                    }
                }
                text = newText
                val tmpLines = lines.toMutableList()
                tmpLines.add(
                    position.lineNumber + 1,
                    Line(text = "", lineNumber = position.lineNumber + 1)
                )
                lines =
                    tmpLines.subList(0, position.lineNumber + 2) + tmpLines.drop(position.lineNumber + 2).map {
                        it.copy(lineNumber = it.lineNumber + 1)
                    }
                doSmoothScrollNext = false
                setPositionAndScroll(position.copy(lineNumber = position.lineNumber + 1, column = 0))
            },
            AppKey(Key.Nine, "Go to next whitespace right") {
                columnGoalChanged = true
                val index = point()
                val nextIndex = if (text[index].isWhitespace()) {
                    val wordStart = text.substring(index).indexOfFirst { !it.isWhitespace() }
                    if (wordStart == -1) {
                        -1
                    } else {
                        text.substring(index + wordStart).indexOfFirst { it.isWhitespace() } + wordStart
                    }
                } else {
                    text.substring(index).indexOfFirst { it.isWhitespace() }
                }
                position = if (nextIndex == -1) {
                    position.copy(
                        lineNumber = lines.size - 1,
                        column = lines[lines.size - 1].text.length
                    )
                } else {
                    findPositionByTextIndex(index + nextIndex)
                }
            },
            AppKey(Key.Seven, "Go to next whitespace left") {
                columnGoalChanged = true
                val index = point()
                val nextIndex = if (text[index].isWhitespace()) {
                    val wordEnd = text.substring(0, index).indexOfLast { !it.isWhitespace() }
                    if (wordEnd == -1) {
                        -1
                    } else {
                        text.substring(0, wordEnd).indexOfLast { it.isWhitespace() }
                    }
                } else {
                    text.substring(0, index).indexOfLast { it.isWhitespace() }
                }
                position = if (nextIndex == -1) {
                    Position()
                } else {
                    findPositionByTextIndex(nextIndex)
                }
            },
            AppKey(Key.G, "Go to word left") {
                columnGoalChanged = true
                var index = point()
                val current = TextEditor.getWordBoundaryAt(text, index)
                if (current != null && index <= current.startInclusive) {
                    index = current.startInclusive
                }
                var offset = 1
                while (true) {
                    if (index - offset <= 0) {
                        globalEvent = GlobalEvent.ScrollToTop
                        break
                    }
                    val previousWord = TextEditor.getWordBoundaryAt(text, index - offset)
                    if (previousWord != null) {
                        position = findPositionByTextIndex(previousWord.startInclusive)
                        break
                    }
                    offset++
                }
            },
            AppKey(Key.P, "Delete word to the right") {
                val index = point()
                val current = TextEditor.getWordBoundaryAt(text, index)
                if (current == null) {
                    var offset = 1
                    while (true) {
                        if (index + offset >= text.length) {
                            text = text.substring(0, index) + text.substring((index + offset).coerceAtMost(text.length))
                            updateLinesByText()
                            break
                        }
                        val nextWord = TextEditor.getWordBoundaryAt(text, index + offset)
                        if (nextWord != null) {
                            text = text.substring(0, index) + text.substring(nextWord.endInclusive + 1)
                            updateLinesByText()
                            break
                        }
                        offset++
                    }
                } else {
                    text = text.substring(0, index) + text.substring(current.endInclusive + 1)
                    updateLinesByText()
                }
            },
            AppKey(Key.R, "Go to word right") {
                columnGoalChanged = true
                var index = point()
                val current = TextEditor.getWordBoundaryAt(text, index)
                if (current != null) {
                    index = current.endInclusive
                }
                var offset = 1
                while (true) {
                    if (index + offset >= text.length) {
                        globalEvent = GlobalEvent.ScrollToBottom
                        break
                    }
                    val nextWord = TextEditor.getWordBoundaryAt(text, index + offset)
                    if (nextWord != null) {
                        position = findPositionByTextIndex(nextWord.startInclusive)
                        break
                    }
                    offset++
                }
            },
            AppKey(Key.H, "Go to character left") {
                goToCharacterLeft()
            },
            AppKey(Key.D, "Go to line start; on second move to real start") {
                columnGoalChanged = true
                if (lastKey == Key.D) {
                    position = position.copy(column = 0)
                } else {
                    val paragraph = getCachedMultiParagraph(
                        lines[position.lineNumber].text,
                        textWidth,
                        isMonospace,
                        TextEditorStyling.fontSize
                    )
                    val lineCount = paragraph.lineCount
                    if (lineCount == 1) {
                        position = position.copy(column = 0)
                    } else {
                        val box = try {
                            paragraph.getBoundingBox(position.column)
                        } catch (_: IllegalArgumentException) {
                            paragraph.getBoundingBox(position.column - 1)
                        }
                        val newOffset = paragraph.getOffsetForPosition(Offset(x = 0f, y = box.top))
                        position = position.copy(
                            column = newOffset
                        )
                    }
                }
            },
            AppKey(Key.S, "Go to line end; on second move to real end") {
                columnGoalChanged = true
                if (lastKey == Key.S) {
                    position = position.copy(column = lines[position.lineNumber].text.length)
                } else {
                    val paragraph = getCachedMultiParagraph(
                        lines[position.lineNumber].text,
                        textWidth,
                        isMonospace,
                        TextEditorStyling.fontSize
                    )
                    val lineCount = paragraph.lineCount
                    if (lineCount == 1) {
                        position = position.copy(column = lines[position.lineNumber].text.length)
                    } else {
                        val box = try {
                            paragraph.getBoundingBox(position.column)
                        } catch (_: IllegalArgumentException) {
                            paragraph.getBoundingBox(position.column - 1)
                        }
                        val newOffset = paragraph.getOffsetForPosition(Offset(x = 9999999f, y = box.top))
                        position = position.copy(
                            column = newOffset
                        )
                    }
                }
            },
            AppKey(Key.NumPadLeftParenthesis, "Delete char to the right of cursor") {
                if (textEditorMode == TextEditorMode.VisualLine) {
                    deleteMarkedVisualLines()
                    visualStartAnchor = null
                    textEditorMode = TextEditorMode.Command
                } else {
                    goToCharacterRight()
                    onBackspace()
                }
            },
            AppKey(Key.X, "Toggle case") {
                if (textEditorMode == TextEditorMode.VisualLine) {
                    val newLines = mutableListOf<Line>()
                    text = buildString {
                        lines.forEachIndexed { index, line ->
                            if (isLineMarkedInVisualMode(index, visualStartAnchor, position)) {
                                val newLine = line.text.map {
                                    if (it.isLowerCase()) {
                                        it.uppercase()
                                    } else if (it.isUpperCase()) {
                                        it.lowercase()
                                    } else {
                                        it
                                    }
                                }.joinToString(separator = "")
                                newLines.add(line.copy(text = newLine))
                                append(newLine)
                                append("\n")
                            } else {
                                newLines.add(line)
                                append(line.text)
                                append("\n")
                            }
                        }
                    }
                    lines = newLines
                    visualStartAnchor = null
                    textEditorMode = TextEditorMode.Command
                } else {
                    val index = point()
                    val char = text[index]
                    if (char.isLowerCase()) {
                        text = text.replaceRange(index, index + 1, char.uppercaseChar().toString())
                        updateLinesByText()
                    } else if (char.isUpperCase()) {
                        text = text.replaceRange(index, index + 1, char.lowercaseChar().toString())
                        updateLinesByText()
                    }
                    goToCharacterRight()
                }
            },
            AppKey(Key.J, "Copy selection") {
                when (textEditorMode) {
                    TextEditorMode.VisualLine -> {
                        val toCopy = buildString {
                            lines.forEach {
                                if (isLineMarkedInVisualMode(it.lineNumber, visualStartAnchor, position)) {
                                    append(it.text)
                                    append("\n")
                                }
                            }
                        }
                        TextEditorTextHistoryRing.add(toCopy)
                        visualStartAnchor = null
                        textEditorMode = TextEditorMode.Command
                    }

                    TextEditorMode.Visual -> {
                        val selectedText = getVisualModeText()
                        TextEditorTextHistoryRing.add(selectedText)
                        visualStartAnchor = null
                        textEditorMode = TextEditorMode.Command
                    }

                    else -> {
                        val line = lines[position.lineNumber].text
                        TextEditorTextHistoryRing.add(line, notificationSubstringTitle = "line")
                    }
                }
            },
            AppKey(Key.Q, "Delete current line or selection") {
                deleteMarkedVisualLines()
            },
            AppKey(Key.N, "Go to character right") {
                goToCharacterRight()
            },
            AppKey(Key.C, "Go to line up") {
                goToLineUp()
            },
            AppKey(Key.T, "Go to line down") {
                goToLineDown()
            },
            AppKey(Key.V, "Go to a few lines above") {
                repeat(TextEditor.LINES_TO_JUMP_FOR_M_AND_V) {
                    maybeNextPositionChangeWithSmoothScroll()
                    goToLineUp()
                }
            },
            AppKey(Key.M, "Go to a few lines below") {
                repeat(TextEditor.LINES_TO_JUMP_FOR_M_AND_V) {
                    maybeNextPositionChangeWithSmoothScroll()
                    goToLineDown()
                }
            },
            AppKey(Key.Enter, "Insert newline") {
                undoManager.resetIndex()
                val point = point()
                text = buildString(text.length + 1) {
                    append(text, 0, point)
                    append("\n")
                    append(text, point, text.length)
                }
                val tmpLines = lines.toMutableList()
                val currentLine = lines[position.lineNumber].text
                tmpLines[position.lineNumber] =
                    tmpLines[position.lineNumber].copy(text = currentLine.substring(0, position.column))
                tmpLines.add(
                    position.lineNumber + 1,
                    Line(text = currentLine.substring(position.column), lineNumber = position.lineNumber + 1)
                )
                lines =
                    tmpLines.subList(0, position.lineNumber + 2) + tmpLines.drop(position.lineNumber + 2).map {
                        it.copy(lineNumber = it.lineNumber + 1)
                    }
                doSmoothScrollNext = false
                setPositionAndScroll(position.copy(lineNumber = position.lineNumber + 1, column = 0))
            },
            AppKey(Key.Enter, "Send buffer to ChatGPT", isCmd = true) {
                val t = text.trim()
                TextEditorTextHistoryRing.add(t, showNotification = false)
                launchAiSinglePromptApp(t)
            }
        )
    }

    fun onWaitingForFirstCharToJumpToHint(it: KeyEvent) {
        handleWaitingForFirstCharToJumpToHint(
            it = it,
            lines = lines,
            scrollState = scrollState,
            textWidth = textWidth,
            isMonospace = isMonospace,
            position = position,
            onCharJumpersUpdate = { newCharJumpers ->
                charJumpers = newCharJumpers
                if (newCharJumpers.size == 1) {
                    jumpTo(newCharJumpers.first())
                }
            },
            onJumpModeChange = { textEditorJumpCharMode = it },
            textSize = TextEditorStyling.fontSize
        )
    }

    fun onHintsVisibleAndWaitingForSelection(it: KeyEvent): Boolean {
        selectedJumpToChars = selectedJumpToChars + it.key
        val found = charJumpers.find { charJumperEntry ->
            charJumperEntry.keyToTrigger == selectedJumpToChars
        }
        if (found == null) {
            val first = charJumpers.find { charJumperEntry ->
                charJumperEntry.keyToTrigger.take(1) == selectedJumpToChars.take(1)
            }
            if (first == null) {
                showErrorNotification("No such candidate: ${it.key}")
                selectedJumpToChars = emptyList()
            }
        } else {
            doSmoothScrollNext = true
            jumpTo(found)
            textEditorJumpCharMode = TextEditorCharJumpMode.NotActive
        }
        return true
    }

    fun onKeyEvent(it: KeyEvent): Boolean {
        val isKeyDown = it.type == KeyEventType.KeyDown
        try {
            if (isKeyDown && it.key == Key.Escape) {
                onEscape()
                return true
            }
            if (isKeyDown && textEditorJumpCharMode == TextEditorCharJumpMode.HintsVisibleAndWaitingForSelection) {
                return onHintsVisibleAndWaitingForSelection(it)
            }
            if (isKeyDown && textEditorJumpCharMode == TextEditorCharJumpMode.WaitingForFirstCharToJumpTo) {
                onWaitingForFirstCharToJumpToHint(it)
                return true
            }
            if (textEditorMode == TextEditorMode.VisualLine ||
                textEditorMode == TextEditorMode.Visual ||
                textEditorMode == TextEditorMode.Command
            ) {
                return it.handleAppMap()
            }
            // handle insert mode
            if (isKeyDown) {
                when {
                    it.key == Key.DirectionLeft -> {
                        goToCharacterLeft()
                    }

                    it.key == Key.DirectionRight -> {
                        goToCharacterRight()
                    }

                    it.key == Key.DirectionUp -> {
                        goToLineUp()
                    }

                    it.key == Key.DirectionDown -> {
                        goToLineDown()
                    }

                    it.key == Key.Backspace -> {
                        onBackspace()
                    }

                    it.key == Key.Enter -> {
                        undoManager.resetIndex()
                        val point = point()
                        text = buildString(text.length + 1) {
                            append(text, 0, point)
                            append("\n")
                            append(text, point, text.length)
                        }
                        val tmpLines = lines.toMutableList()
                        val currentLine = lines[position.lineNumber].text
                        tmpLines[position.lineNumber] =
                            tmpLines[position.lineNumber].copy(text = currentLine.substring(0, position.column))
                        tmpLines.add(
                            position.lineNumber + 1,
                            Line(text = currentLine.substring(position.column), lineNumber = position.lineNumber + 1)
                        )
                        lines =
                            tmpLines.subList(0, position.lineNumber + 2) + tmpLines.drop(position.lineNumber + 2).map {
                                it.copy(lineNumber = it.lineNumber + 1)
                            }
                        doSmoothScrollNext = false
                        setPositionAndScroll(position.copy(lineNumber = position.lineNumber + 1, column = 0))
                    }

                    it.utf16CodePoint != 65535 -> {
                        undoManager.resetIndex()
                        // do not split on newlines here since it is terribly slow on lots of key inputs
                        val newChar = it.utf16CodePointToString()
                        val index = point()
                        text = buildString(text.length + newChar.length) {
                            append(text, 0, index)
                            append(newChar)
                            append(text, index, text.length)
                        }
                        val tmpLines = lines.toMutableList()
                        val currentLine = lines[position.lineNumber].text
                        val newLine = buildString {
                            append(currentLine, 0, position.column)
                            append(newChar)
                            append(currentLine, position.column, currentLine.length)
                        }
                        tmpLines[position.lineNumber] = tmpLines[position.lineNumber].copy(text = newLine)
                        lines = tmpLines
                        position = position.copy(column = position.column + 1)
                    }
                }
            }
        } finally {
            if (isKeyDown) {
                lastKey = it.key
            }
        }
        return false
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .padding(12.dp)
            .focusRequester(appFocusRequester)
            .focusable()
            .fillMaxWidth()
            .onPreviewKeyEvent(::onKeyEvent)
    ) {
        TextEditorTabs()
        TextEditorContent(
            textEditorMode = textEditorMode,
            textEditorJumpCharMode = textEditorJumpCharMode,
            lines = lines,
            position = position,
            maxLineNumberCharCount = maxLineNumberCharCount,
            isMonospace = isMonospace,
            charJumpers = charJumpers,
            selectedJumpToChars = selectedJumpToChars,
            textWidth = textWidth,
            visualStartAnchor = visualStartAnchor,
            scrollState = scrollState,
            onTextWidthChange = { textWidth = it },
            onDoSmoothScrollNext = { doSmoothScrollNext = true },
            onPositionChange = ::setPositionAndScroll
        )
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
        textEditorMode = TextEditorMode.Command
        launch {
            while (true) {
                delay((0.3).seconds)
                updateUndoState()
            }
        }
    }
    LaunchedEffect(visualStartAnchor, position, textEditorMode, text) {
        when (textEditorMode) {
            TextEditorMode.Visual -> {
                val visualText = getVisualModeText()
                val lineCount = visualText.count { it.isNewLine() }
                TextEditor.state.visualSelectedCharacters =
                    TextEditorState.VisualSelection(visualText.length, lineCount)
            }

            TextEditorMode.VisualLine -> {
                val visualText = buildString {
                    lines.forEach {
                        if (isLineMarkedInVisualMode(it.lineNumber, visualStartAnchor, position)) {
                            append(it.text)
                            append("\n")
                        }
                    }
                }
                val lineCount = visualText.count { it.isNewLine() }
                TextEditor.state.visualSelectedCharacters =
                    TextEditorState.VisualSelection(visualText.length, lineCount)
            }

            else -> {
                TextEditor.state.visualSelectedCharacters = null
            }
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(text) {
        hasLongLines = TextEditor.hasLongLines(lines)
        maxLineNumberCharCount = lines.size.toString().length
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.Reload -> {
                text = file
                    .readText()
                    .replace("\r\n", "\n")
                updateLinesByText()
                setPositionAndScroll(Position())
                showNotification("Reloaded file", file.name)
            }

            GlobalEvent.ScrollToTop -> {
                maybeNextPositionChangeWithSmoothScroll()
                setPositionAndScroll(Position())
            }

            GlobalEvent.ScrollToBottom -> {
                maybeNextPositionChangeWithSmoothScroll()
                setPositionAndScroll(
                    position.copy(
                        lineNumber = lines.size - 1,
                        column = lines[lines.size - 1].text.length
                    )
                )
            }

            GlobalEvent.KillAll -> {
                TextEditorTextHistoryRing.add(text)
                text = ""
                lines = listOf(Line("", 0))
                position = Position()
            }

            GlobalEvent.CopyAll -> {
                TextEditorTextHistoryRing.add(text, notificationSubstringTitle = "buffer")
            }

            GlobalEvent.EnterVisualLineMode -> {
                visualStartAnchor = position
                textEditorMode = TextEditorMode.VisualLine
            }

            GlobalEvent.KillToEndOfLine -> {
                text = TextEditor.killToEndOfLine(position, lines)
                updateLinesByText()
            }

            GlobalEvent.JoinLineToPrevious -> {
                if (position.lineNumber == 0) {
                    if (lines.size >= 2) {
                        var newText = lines[0].text + lines[1].text.trimStart() + "\n"
                        val newColumn = lines[0].text.length
                        lines.drop(2).forEach {
                            newText += it.text + "\n"
                        }
                        if (newText.endsWith("\n")) {
                            newText = newText.substring(0, newText.length - 1)
                        }
                        text = newText
                        updateLinesByText()
                        setPositionAndScroll(position.copy(lineNumber = 0, column = newColumn))
                    }
                } else {
                    var newText = ""
                    var newColumn = 0
                    lines.forEachIndexed { index, line ->
                        if (index == position.lineNumber) {
                            newText = newText.substring(0, newText.length - 1) + line.text.trimStart() + "\n"
                            newColumn = lines[index - 1].text.length
                        } else {
                            newText += line.text + "\n"
                        }
                    }
                    if (newText.endsWith("\n")) {
                        newText = newText.substring(0, newText.length - 1)
                    }
                    text = newText
                    updateLinesByText()
                    setPositionAndScroll(position.copy(lineNumber = position.lineNumber - 1, column = newColumn))
                }
            }

            GlobalEvent.DuplicateLine -> {
                var newText = ""
                lines.forEachIndexed { index, line ->
                    if (index == position.lineNumber) {
                        newText += line.text + "\n"
                    }
                    newText += line.text + "\n"
                }
                text = newText
                updateLinesByText()
                setPositionAndScroll(position.copy(lineNumber = position.lineNumber + 1))
            }

            GlobalEvent.SaveBuffer -> {
                file.writeText(text)
                showNotification("Saved buffer", file.name)
            }

            GlobalEvent.InsertTextFromHistory -> {
                val history = TextEditorTextHistoryRing.getHistory()
                if (history.isEmpty()) {
                    showErrorNotification("No history to insert")
                } else {
                    openCompletionDialog("Insert from history", history) {
                        insertAtPoint(it.text)
                    }
                }
            }

            else -> {}
        }
    }
}
