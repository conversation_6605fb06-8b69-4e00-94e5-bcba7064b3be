package dima.apps.textEditor

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.selection.DisableSelection
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors

/**
 * @param lineHeight should be same as the font size of the text row
 */
@Composable
internal fun LineNumber(lineNumber: String, maxLineNumberCharCount: Int, isCurrentLine: Boolean, lineHeight: TextUnit) {
    DisableSelection {
        Box(
            contentAlignment = Alignment.CenterEnd,
            modifier = Modifier.padding(end = 15.dp)
        ) {
            Text(
                lineNumber,
                fontFamily = FontFamily.Monospace,
                fontSize = 14.sp,
                lineHeight = lineHeight,
                color = if (isCurrentLine) TailwindCssColors.gray600 else TailwindCssColors.gray400,
                textAlign = TextAlign.Right,
                // add padding to center
                modifier = Modifier.padding(top = 1.dp)
            )
            Text(
                "5".repeat(maxLineNumberCharCount),
                color = TailwindCssColors.transparent
            )
        }
    }
}
