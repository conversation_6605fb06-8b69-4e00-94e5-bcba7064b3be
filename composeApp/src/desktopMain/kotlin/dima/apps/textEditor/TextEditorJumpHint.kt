package dima.apps.textEditor

import Globals
import TextEditorCharJumpMode
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEvent
import androidx.compose.ui.input.key.key
import androidx.compose.ui.text.*
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import dima.apps.notifications.showErrorNotification
import dima.color.TailwindCssColors
import dima.utils.asUpperCase
import dima.utils.toLowerKey
import textEditorJumpCharMode

/**
 * Represents a jump target in the text editor with its position and trigger keys.
 */
data class CharJumperEntry(val x: Float, val y: Float, val line: Int, val keyToTrigger: List<Key>)

/**
 * Handles character jump functionality for both TextEditor and ReadOnlyTextEditor.
 * Returns true if the event was handled.
 */
fun handleWaitingForFirstCharToJumpToHint(
    it: KeyEvent,
    lines: List<Line>,
    scrollState: LazyListState,
    textWidth: Dp,
    isMonospace: Boolean,
    position: Position,
    textSize: TextUnit,
    onCharJumpersUpdate: (List<CharJumperEntry>) -> Unit,
    onJumpModeChange: (TextEditorCharJumpMode) -> Unit
) {
    // ignore shift so ? works which requires shift to be held
    if (it.key == Key.ShiftLeft || it.key == Key.ShiftRight) {
        println("Ignoring shift key")
        return
    }
    val charToHighlight = it.toLowerKey()
    println("Looking for character: $charToHighlight")
    val layout = scrollState.layoutInfo
    var newCharJumpers = mutableListOf<CharJumperEntry>()
    if (layout.visibleItemsInfo.isEmpty()) {
        onJumpModeChange(TextEditorCharJumpMode.NotActive)
        showErrorNotification("No visible lines?!")
        return
    }
    println("handleWaitingForFirstCharToJumpToHint: mode = $textEditorJumpCharMode")
    layout.visibleItemsInfo.forEach { l ->
        val visibleLine = lines[l.index]
        val paragraph = getCachedMultiParagraph(
            line = visibleLine.text, textWidth = textWidth, isMonospace = isMonospace, fontSize = textSize
        )
        val startY = if (l.offset < 0) {
            -l.offset.toFloat()
        } else {
            0f
        }
        val startOffset = paragraph.getOffsetForPosition(
            Offset(
                x = 0f,
                y = startY
            )
        )
        val endOffset = paragraph.getOffsetForPosition(
            Offset(
                x = 9999999f,
                y = startY + layout.viewportEndOffset
            )
        )
        val subText = visibleLine.text.substring(startOffset, endOffset)
        println("Searching in text: $subText for line ${visibleLine.lineNumber}")
        var lastBox: Rect? = null
        subText.forEachIndexed { index, c ->
            // exclude when point is on same line
            println("Comparing character '${c}' (${c.code}) with '$charToHighlight'")
            if (c.lowercase() == charToHighlight && !(visibleLine.lineNumber == position.lineNumber && index + startOffset == position.column)) {
                println("  -> Found match at line ${visibleLine.lineNumber}, column ${index + startOffset}")
                val box = paragraph.getBoundingBox(index + startOffset)
                // use horizontal offset to never have overlapping boxes
                val shouldAdd =
                    lastBox == null ||
                            lastBox!!.top.toInt() != box.top.toInt() ||
                            (box.left - lastBox!!.left >= textSize.value * 3)
                if (shouldAdd) {
                    newCharJumpers.add(
                        CharJumperEntry(
                            x = box.left,
                            y = box.top,
                            line = l.index,
                            // has to be set below once we know the total amount of hints
                            keyToTrigger = emptyList()
                        )
                    )
                    lastBox = box
                }
            }
        }
    }
    newCharJumpers = newCharJumpers.mapIndexed { index, jumper ->
        jumper.copy(keyToTrigger = TextEditor.mapHintChars(index, newCharJumpers.size))
    }.toMutableList()
    when (newCharJumpers.size) {
        0 -> {
            onJumpModeChange(TextEditorCharJumpMode.NotActive)
            showErrorNotification("There is no $charToHighlight in the text")
        }

        1 -> {
            onJumpModeChange(TextEditorCharJumpMode.NotActive)
            onCharJumpersUpdate(newCharJumpers) // Let the caller handle the jump
        }

        else -> {
            onCharJumpersUpdate(newCharJumpers)
            onJumpModeChange(TextEditorCharJumpMode.HintsVisibleAndWaitingForSelection)
        }
    }
}

fun Modifier.renderJumpCharHints(
    charJumpers: List<CharJumperEntry>, selectedKeys: List<Key>, line: Line, textWidth: Dp
): Modifier {
    return if (
        textEditorJumpCharMode == TextEditorCharJumpMode.NotActive ||
        textEditorJumpCharMode == TextEditorCharJumpMode.WaitingForFirstCharToJumpTo
    ) {
        this
    } else {
        this.then(
            Modifier.drawWithContent {
                drawContent()
                drawRect(
                    color = TailwindCssColors.white,
                    alpha = 0.5f,
                    blendMode = BlendMode.SrcOver
                )
                val pxWidth = with(Globals.density) {
                    textWidth.roundToPx()
                }
                val constraints = Constraints(maxWidth = pxWidth)
                val style = TextStyle(
                    color = TailwindCssColors.white,
                    fontSize = TextEditorStyling.fontSize,
                )
                charJumpers
                    .filter {
                        val first = it.keyToTrigger.first()
                        return@filter if (selectedKeys.isEmpty()) {
                            true
                        } else if (selectedKeys.first() == first) {
                            true
                        } else {
                            false
                        }
                    }
                    .forEach {
                        if (it.line == line.lineNumber) {
                            drawText(
                                Globals.textMeasurer.measure(
                                    buildAnnotatedString {
                                        val first = it.keyToTrigger.first()
                                        if (selectedKeys.isEmpty()) {
                                            append(
                                                AnnotatedString(
                                                    first.asUpperCase(),
                                                    SpanStyle(
                                                        background = TailwindCssColors.black
                                                    )
                                                )
                                            )
                                        } else {
                                            append(
                                                AnnotatedString(
                                                    first.asUpperCase(),
                                                    SpanStyle(
                                                        color = TailwindCssColors.transparent,
                                                    )
                                                )
                                            )
                                        }
                                        if (it.keyToTrigger.size >= 2) {
                                            append(
                                                AnnotatedString(
                                                    it.keyToTrigger[1].asUpperCase(),
                                                    SpanStyle(
                                                        color = TailwindCssColors.green500,
                                                        background = TailwindCssColors.black
                                                    )
                                                )
                                            )
                                        }
                                    },
                                    constraints = constraints,
                                    style = style
                                ),
                                topLeft = Offset(it.x, it.y),
                            )
                        }
                    }
            }
        )
    }
}
