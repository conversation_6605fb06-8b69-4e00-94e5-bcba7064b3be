package dima.apps.textEditor

import dima.database.transactionToAvoidBusySqlite
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File

class TextEditorRepository {

    private var database: Database? = null
    private val cacheDirectory = File("cache/texteditor")
    private val table = BufferTable()

    fun setupDatabase() {
        if (database != null) return
        
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        
        val dbDir = File(cacheDirectory, "db")
        if (!dbDir.exists()) {
            dbDir.mkdirs()
        }
        
        val dbFile = File(dbDir, "texteditor.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
        
        transaction(database) {
            SchemaUtils.create(table)
        }
    }

    fun createBuffer(name: String, text: String = "", fileName: String? = null): String {
        return transactionToAvoidBusySqlite(database) {
            val bufferName = getUniqueBufferName(name)
            table.insert {
                it[table.name] = bufferName
                it[table.text] = text
                it[table.fileName] = fileName
            }
            bufferName
        }
    }

    fun deleteBuffer(name: String) {
        transactionToAvoidBusySqlite(database) {
            table.deleteWhere { table.name eq name }
        }
    }

    fun saveBuffer(name: String, text: String, position: Int) {
        transactionToAvoidBusySqlite(database) {
            table.update({ table.name eq name }) {
                it[table.text] = text
                it[table.position] = position
            }
        }
    }

    fun getBufferNames(): List<String> {
        return transactionToAvoidBusySqlite(database) {
            table.selectAll().map { it[table.name] }
        }
    }

    fun getUniqueBufferName(name: String): String {
        val existingNames = getBufferNames()
        if (!existingNames.contains(name)) return name
        
        var counter = 2
        var uniqueName: String
        do {
            uniqueName = "$name<$counter>"
            counter++
        } while (uniqueName in existingNames)
        
        return uniqueName
    }

    private inner class BufferTable : Table("Buffer") {
        val name = text("name").uniqueIndex()
        val text = text("text").default("")
        val fileName = text("file_name").nullable().default(null)
        val position = integer("position").default(0)
    }
}