package dima.apps.textEditor

import GlobalStyling
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FileOpen
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.utils.clickableWithoutBackgroundRipple

@Composable
fun TextEditorTabs() {
    var tabs by remember { mutableStateOf(TextEditor.getBufferTabsFromDatabase()) }
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterHorizontally),
        modifier = Modifier.fillMaxWidth()
    ) {
        items(tabs, key = { it.name }) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .animateItem(
                        fadeInSpec = spring(stiffness = Spring.StiffnessHigh),
                        fadeOutSpec = spring(stiffness = Spring.StiffnessHigh),
                    )
            ) {
                if (it.isFilename) {
                    Icon(
                        Icons.Default.FileOpen,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                }
                val isActive = (it.name == GlobalState.textEditor.activeBufferName)
                Text(
                    it.name,
                    modifier = Modifier
                        .padding(4.dp)
                        .alpha(if (isActive) 1f else 0.7f)
                        .then(
                            if (isActive) {
                                Modifier.background(
                                    GlobalStyling.getSelectedBackgroundColor(),
                                    shape = GlobalStyling.smallRoundedCorners
                                )
                            } else {
                                Modifier.background(TailwindCssColors.white, shape = GlobalStyling.smallRoundedCorners)
                            }
                        )
                        .then(
                            if (isActive) {
                                Modifier
                            } else {
                                Modifier.clickableWithoutBackgroundRipple {
                                    TextEditor.switchToBuffer(it.name)
                                }
                            }
                        )
                        .padding(horizontal = 6.dp, vertical = 4.dp)
                )
            }
        }
    }

    LaunchedEffect(TextEditor.state.bufferNames) {
        tabs = TextEditor.getBufferTabsFromDatabase()
    }
}