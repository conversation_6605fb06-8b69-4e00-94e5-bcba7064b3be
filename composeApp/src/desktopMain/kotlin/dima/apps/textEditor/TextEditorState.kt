package dima.apps.textEditor

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.globalState.GlobalState

class TextEditorState {
    var bufferNames by mutableStateOf(listOf<String>())
    var visualSelectedCharacters by mutableStateOf<VisualSelection?>(null)
    
    var activeBufferName: String?
        get() = GlobalState.textEditor.activeBufferName
        set(value) {
            GlobalState.textEditor = GlobalState.textEditor.copy(activeBufferName = value)
        }

    data class VisualSelection(val chars: Int, val lines: Int)
}