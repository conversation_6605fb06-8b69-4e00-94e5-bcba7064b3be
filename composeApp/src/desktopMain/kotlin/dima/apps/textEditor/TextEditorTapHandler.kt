package dima.apps.textEditor

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit

fun Modifier.setTapOnLine(
    line: Line,
    position: Position,
    textWidth: Dp,
    isMonospace: Boolean,
    onSmoothScrollNext: () -> Unit,
    onNewPosition: (Position) -> Unit,
    fontSize: TextUnit
): Modifier {
    return pointerInput(Unit) {
        detectTapGestures { offset ->
            val paragraph = getCachedMultiParagraph(line.text, textWidth, isMonospace, fontSize)
            val newColumn = paragraph.getOffsetForPosition(offset)
            onSmoothScrollNext()
            onNewPosition(position.copy(lineNumber = line.lineNumber, column = newColumn))
        }
    }
}