package dima.apps.textEditor

import dima.os.copyToClipboard

object TextEditorTextHistoryRing {

    private val history = mutableListOf<String>()

    fun getHistory(): List<String> {
        return history.toList()
    }

    /**
     * Skip blank strings.
     *
     * @param notificationSubstringTitle if null, no notification is shown
     */
    fun add(text: String, showNotification: Boolean = true, notificationSubstringTitle: String? = null) {
        if (text.isBlank()) {
            return
        }
        history.add(text)
        if (notificationSubstringTitle == null) {
            copyToClipboard(text, showNotification = showNotification)
        } else {
            copyToClipboard(
                text,
                showNotification = showNotification,
                notificationTitleSubstring = notificationSubstringTitle
            )
        }
    }

}