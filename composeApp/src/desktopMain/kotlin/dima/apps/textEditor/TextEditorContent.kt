package dima.apps.textEditor

import Globals
import TextEditorCharJumpMode
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.dialogs.readOnlyTextEditor.isLineMarkedInVisualMode

@Composable
internal fun TextEditorContent(
    textEditorMode: TextEditorMode,
    textEditorJumpCharMode: TextEditorCharJumpMode,
    lines: List<Line>,
    position: Position,
    maxLineNumberCharCount: Int,
    isMonospace: Boolean,
    charJumpers: List<CharJumperEntry>,
    selectedJumpToChars: List<Key>,
    textWidth: Dp,
    visualStartAnchor: Position?,
    scrollState: LazyListState,
    onTextWidthChange: (Dp) -> Unit,
    onDoSmoothScrollNext: () -> Unit,
    onPositionChange: (Position) -> Unit,
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            textEditorMode.toString(),
            textAlign = TextAlign.Center,
            fontSize = 24.sp,
            modifier = Modifier
                .padding(12.dp)
                .fillMaxWidth()
        )
        Box(
            contentAlignment = Alignment.TopCenter,
            modifier = Modifier
                .then(
                    when {
                        textEditorMode == TextEditorMode.Insert ->
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                TextEditorStyling.insertModeBorderColor,
                                shape = TextEditorStyling.roundedShape
                            )

                        textEditorMode in listOf(TextEditorMode.Visual, TextEditorMode.VisualLine) ->
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                TextEditorStyling.visualModeBorderColor,
                                shape = TextEditorStyling.roundedShape
                            )

                        textEditorJumpCharMode != TextEditorCharJumpMode.NotActive ->
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                TextEditorStyling.jumpCharBorderColor,
                                shape = TextEditorStyling.roundedShape
                            )

                        else ->
                            Modifier.border(
                                TextEditorStyling.borderWidth,
                                TextEditorStyling.greyBorderColor,
                                shape = TextEditorStyling.roundedShape
                            )
                    }
                )
                .padding(TextEditorStyling.borderWidth)
                .fillMaxWidth(0.7f)
        ) {
            SelectionContainer {
                LazyColumn(
                    state = scrollState,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp)
                ) {
                    items(lines, key = { it.lineNumber }) { line ->
                        val lineNumber = (line.lineNumber + 1).toString().padStart(maxLineNumberCharCount)
                        val isCursorOnCurrentLine = (line.lineNumber == position.lineNumber)
                        Row(modifier = Modifier.fillMaxWidth()) {
                            LineNumber(
                                lineNumber = lineNumber,
                                maxLineNumberCharCount = maxLineNumberCharCount,
                                isCurrentLine = isCursorOnCurrentLine,
                                lineHeight = TextEditorStyling.fontSize
                            )
                            Text(
                                line.text,
                                fontSize = TextEditorStyling.fontSize,
                                fontFamily = if (isMonospace) FontFamily.Monospace else FontFamily.Default,
                                modifier = Modifier
                                    .renderJumpCharHints(charJumpers, selectedJumpToChars, line, textWidth)
                                    .fillMaxWidth()
                                    .onSizeChanged {
                                        with(Globals.density) {
                                            onTextWidthChange(it.width.toDp())
                                        }
                                    }
                                    .setTapOnLine(
                                        line = line,
                                        position = position,
                                        textWidth = textWidth,
                                        isMonospace = isMonospace,
                                        onSmoothScrollNext = onDoSmoothScrollNext,
                                        onNewPosition = onPositionChange,
                                        fontSize = TextEditorStyling.fontSize
                                    )
                                    .entireVisualLineSelectionBackground(
                                        textEditorMode = textEditorMode,
                                        isLineMarkedInVisualMode = isLineMarkedInVisualMode(
                                            line.lineNumber,
                                            visualStartAnchor,
                                            position
                                        )
                                    )
                                    .visualModeBackground(
                                        textEditorMode = textEditorMode,
                                        line = line,
                                        lines = lines,
                                        position = position,
                                        visualStartAnchor = visualStartAnchor,
                                        textWidth = textWidth,
                                        isMonospace = isMonospace,
                                        fontSize = TextEditorStyling.fontSize
                                    )
                                    .cursorBackground(
                                        isCursorOnCurrentLine = isCursorOnCurrentLine,
                                        line = line,
                                        textWidth = textWidth,
                                        position = position,
                                        isMonospace = isMonospace,
                                        textEditorMode = textEditorMode
                                    )
                            )
                        }
                    }
                }
            }
            VerticalScrollbar(
                modifier = Modifier.align(Alignment.CenterEnd),
                adapter = rememberScrollbarAdapter(scrollState = scrollState)
            )
        }
    }
}