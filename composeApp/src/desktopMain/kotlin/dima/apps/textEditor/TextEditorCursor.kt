package dima.apps.textEditor

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.unit.Dp
import dima.color.TailwindCssColors

fun Modifier.cursorBackground(
    isCursorOnCurrentLine: <PERSON>ole<PERSON>,
    line: Line,
    textWidth: Dp,
    position: Position,
    isMonospace: <PERSON>olean,
    textEditorMode: TextEditorMode
): Modifier {
    return then(
        if (isCursorOnCurrentLine) {
            Modifier.drawBehind {
                val box = measureTextLine(
                    line = line.text,
                    textWidth = textWidth,
                    column = position.column,
                    isMonospace = isMonospace,
                    fontSize = TextEditorStyling.fontSize
                )
                if (textEditorMode == TextEditorMode.Insert) {
                    drawLine(
                        color = TailwindCssColors.red600,
                        start = Offset(x = box.left, y = box.top),
                        end = Offset(x = box.left, y = box.bottom),
                        strokeWidth = 3f
                    )
                } else {
                    drawRect(
                        color = TailwindCssColors.gray400,
                        topLeft = Offset(x = box.left, y = box.top),
                        size = Size(width = box.width, height = box.height)
                    )
                }
            }
        } else {
            Modifier
        }
    )
}