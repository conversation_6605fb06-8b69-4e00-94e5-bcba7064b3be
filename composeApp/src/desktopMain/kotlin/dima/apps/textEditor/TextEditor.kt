package dima.apps.textEditor

import androidx.compose.ui.input.key.Key
import dima.clipboard.readClipboard
import dima.globalState.GlobalState
import java.io.File

data class Range(val startInclusive: Int, val endInclusive: Int)

/**
 * @param lineNumber starts from 0
 * @param column starts from 0. If a line has 3 chars, this can be 0, 1, 2 or 3 where 3 means after the last char, the newline
 */
data class Position(val lineNumber: Int = 0, val column: Int = 0)

data class Line(val text: String, val lineNumber: Int)

/**
 * Inverse of [findPositionByTextIndex].
 */
fun point(lines: List<Line>, pos: Position, ignoreColumn: Boolean = false): Int {
    var point = 0
    for (lineIndex in 0 until pos.lineNumber) {
        // add + 1 for each newline
        point += lines[lineIndex].text.length + 1
    }
    if (ignoreColumn) {
        return point
    }
    return point + pos.column
}

/**
 * Inverse of [point].
 */
fun findPositionByTextIndex(lines: List<Line>, index: Int): Position {
    var totalIndex = index
    var line = 0
    while (line < lines.size) {
        val lineLength = lines[line].text.length + 1
        if (totalIndex < lineLength) {
            return Position(line, totalIndex)
        }
        totalIndex -= lineLength
        line++
    }
    throw IndexOutOfBoundsException("Index $index is out of bounds.")
}

enum class TextEditorMode {
    Insert,
    Command,
    Visual,
    VisualLine,
}

object TextEditor {

    const val LINES_TO_JUMP_FOR_M_AND_V = 10
    private const val LONG_LINE_THRESHOLD = 10000

    data class VisualSelection(val chars: Int, val lines: Int)

    /**
     * Note that this always needs to be updated on any buffer changes for proper tab display.
     */
    val state = TextEditorState()
    private const val RELATIVE_CACHE_DIRECTORY = "cache/texteditor"
    private val cacheDirectory = File(RELATIVE_CACHE_DIRECTORY)
    private val wordRegex = Regex("""[a-zA-Z0-9_]+""")
    private val punctuationRegex = Regex("""\p{Punct}+""")
    private val repository = TextEditorRepository()

    fun setupDatabase() {
        repository.setupDatabase()
        val dbBufferNames = repository.getBufferNames()
        if (dbBufferNames.isEmpty()) {
            val bufferName = repository.createBuffer("scratch")
            state.bufferNames = listOf(bufferName)
            state.activeBufferName = bufferName
        } else {
            if (GlobalState.textEditor.activeBufferName == null
                || GlobalState.textEditor.activeBufferName !in dbBufferNames
            ) {
                GlobalState.textEditor = GlobalState.textEditor.copy(activeBufferName = dbBufferNames.first())
            }
            state.bufferNames = dbBufferNames
        }
    }

    fun switchToBuffer(name: String) {
        GlobalState.textEditor = GlobalState.textEditor.copy(activeBufferName = name)
    }

    data class BufferTab(val name: String, val isFilename: Boolean)

    fun getBufferTabsFromDatabase(): List<BufferTab> {
        return repository.getBufferNames().map { BufferTab(it, false) }
    }

    fun getUniqueBufferName(name: String): String {
        return repository.getUniqueBufferName(name)
    }

    /**
     * Use same keys as in Firefox Vimium.
     */
    @Suppress("SpellCheckingInspection")
    val hintCharacters = listOf(
        Key.C,
        Key.D,
        Key.E,
        Key.G,
        Key.H,
        Key.J,
        Key.K,
        Key.M,
        Key.N,
        Key.O,
        Key.P,
        Key.Q,
        Key.R,
        Key.T,
        Key.U,
        Key.V,
        Key.W,
        Key.X,
    )

    /**
     * Can crash when index is too big.
     */
    internal fun mapHintChars(index: Int, hintCount: Int): List<Key> {
        val hintCharCount = hintCharacters.size
        val intHintCount = index / hintCharCount
        val remainingHintIndex = index % hintCharCount
        if (hintCount <= hintCharCount) {
            if (intHintCount == 0) {
                return listOf(hintCharacters[remainingHintIndex])
            }
            return listOf(hintCharacters[intHintCount - 1], hintCharacters[remainingHintIndex])
        } else {
            return listOf(hintCharacters[intHintCount], hintCharacters[remainingHintIndex])
        }
    }

    /**
     * Takes the content from the current clipboard.
     */
    fun openNewScratchBuffer() {
        setupDatabase()
        var clipboard = readClipboard()
        if (clipboard.isNullOrEmpty()) {
            clipboard = ""
        }
        val bufferName = repository.createBuffer("scratch", text = clipboard)
        state.bufferNames = repository.getBufferNames()
        state.activeBufferName = bufferName
    }

    fun closeCurrentBufferWithoutSaving() {
        val activeBufferName = state.activeBufferName
        if (activeBufferName == null) {
            val bufferName = repository.createBuffer("scratch")
            state.bufferNames = repository.getBufferNames()
            state.activeBufferName = bufferName
            return
        }

        val bufferNamesBeforeDeletion = repository.getBufferNames()
        repository.deleteBuffer(activeBufferName)

        if (bufferNamesBeforeDeletion.size == 1) {
            val bufferName = repository.createBuffer("scratch")
            state.bufferNames = repository.getBufferNames()
            state.activeBufferName = bufferName
        } else {
            val index = bufferNamesBeforeDeletion.indexOf(activeBufferName)
            val newIndex = (index + 1).coerceAtMost(bufferNamesBeforeDeletion.size - 2)
            state.activeBufferName = repository.getBufferNames()[newIndex]
        }
    }

    fun killToEndOfLine(position: Position, lines: List<Line>): String {
        return lines.mapIndexed { index, line ->
            if (index == position.lineNumber) {
                line.text.substring(0, position.column)
            } else {
                line.text
            }
        }.joinToString("\n")
    }

    /**
     * @return null if not on a word
     */
    fun getWordBoundaryAt(buffer: String, point: Int): Range? {

        fun doForRegex(regex: Regex, point: Int, buffer: String): Range? {
            var start = point
            var end = point
            while (start > 0 && regex.matches(buffer[start - 1].toString())) {
                start--
            }
            while (end < buffer.length - 1 && regex.matches(buffer[end + 1].toString())) {
                end++
            }
            return if (regex.matches(buffer.substring(start, end + 1))) {
                Range(start, end)
            } else {
                null
            }
        }

        if (point !in buffer.indices) {
            return null
        }
        if (punctuationRegex.matches(buffer[point].toString())) {
            return doForRegex(punctuationRegex, point, buffer)
        }
        return doForRegex(wordRegex, point, buffer)
    }

    fun saveCurrentContentToDatabase() {
        val activeBufferName = GlobalState.textEditor.activeBufferName ?: return
        /*
                transactionToAvoidBusySqlite(database) {
                    val currentBuffer = Table.select { Table.name eq activeBufferName }.firstOrNull()
                    // Only save scratch buffers (ones without associated files)
                    if (currentBuffer != null && currentBuffer[Table.fileName] == null) {
                        Table.update({ Table.name eq activeBufferName }) {
                            it[text] = GlobalState.textEditor.text
                            it[position] = GlobalState.textEditor.position?.toInt() ?: 0
                        }
                    }
                }
        */
    }

    /**
     * @return true if there is at least one line which is very long which causes smooth scrolling issues
     */
    fun hasLongLines(lines: List<Line>): Boolean = lines.any { it.text.length > LONG_LINE_THRESHOLD }
}
