package dima.apps.notifications

import GlobalEvent
import GlobalStyling
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dialogs
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.globalState.PaneState
import dima.utils.AppKey
import dima.utils.LaunchedEffectForAppKeys
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import dima.utils.handleAppMap
import globalEvent
import handleLostFocus

private var notifications by mutableStateOf(
    listOf<Notification>()
)

private var isRightSideSelected by mutableStateOf(false)

internal fun addDismissedToNotificationHistory(vararg dismissedNotifications: Notification) {
    dismissedNotifications.forEach {
        if (it !in notifications) {
            notifications = listOf(it) + notifications
        }
    }
}

@Composable
fun LastNotificationsApp(paneState: dima.globalState.PaneState) {
    val leftScrollState = rememberLazyListState()
    val rightScrollState = rememberScrollState()
    val rightScrollVelocity = remember { ScrollVelocity(rightScrollState) }
    var selected by remember { mutableStateOf<Int?>(null) }
    val appFocusRequester = remember { FocusRequester() }
    val prevNotificationsSize = remember { mutableStateOf(notifications.size) }

    val localAppKeys = remember {
        listOf(
            AppKey(Key.Escape, "Deselect notification text") {
                isRightSideSelected = false
            },
            AppKey(Key.Enter, "Select notification text") {
                isRightSideSelected = true
            },
            AppKey(
                Key.C, "Go one notification up or scroll up",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollUpKeyPressed()
                    } else {
                        if (selected != null && selected!! >= 1) {
                            selected = selected!! - 1
                        }
                    }
                }),
            AppKey(
                Key.T, "Go one notification down or scroll down",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollDownKeyPressed()
                    } else {
                        if (selected != null && selected!! + 1 < notifications.size) {
                            selected = selected!! + 1
                        }
                    }
                }),
            AppKey(
                Key.M, "Go 6 notification down or scroll down more",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollDownMoreKeyPressed()
                    } else {
                        if (selected == null) {
                            return@AppKey
                        }
                        val newIndex = selected!! + 6
                        selected = newIndex.coerceAtMost(notifications.size - 1)
                    }
                }),
            AppKey(
                Key.V, "Go 6 notification up or scroll up more",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollUpMoreKeyPressed()
                    } else {
                        if (selected == null) {
                            return@AppKey
                        }
                        val newIndex = selected!! - 6
                        selected = newIndex.coerceAtLeast(0)
                    }
                }),
            AppKey(Key.J, "Copy notification content") {
                notifications.getOrNull(selected ?: -1)?.let { notification ->
                    openCopyNotificationPartDialog(notification)
                }
            }
        )
    }
    LaunchedEffectForAppKeys(paneState, localAppKeys)

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxSize()
            .padding(start = 12.dp, end = 12.dp, top = 12.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        Text(
            "Dismissed notifications",
            fontSize = 20.sp,
            textAlign = TextAlign.Center,
            color = GlobalStyling.getTextColor(),
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier
                .focusable()
                .fillMaxWidth()
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .fillMaxSize()
        ) {
            val notification = notifications.getOrNull((selected ?: -1))
            val selectedId = notification?.id
            NotificationAppUiList(
                notifications = notifications,
                selectedId = selectedId?.toInt(),
                isRightSideSelected = isRightSideSelected,
                scrollState = leftScrollState,
                modifier = Modifier
                    .weight(1f)
            )
            if (notification != null) {
                NotificationAppUiDetail(
                    notification = notification,
                    isRightSideSelected = isRightSideSelected,
                    scrollState = rightScrollState,
                    modifier = Modifier
                        .weight(1f)
                )
            }
        }
    }

    suspend fun scrollTo(smooth: Boolean) {
        if (selected == null || selected!! < 0 || selected!! >= notifications.size) return // Guard against invalid index

        val offset = (-GlobalState.mainWindow.heightInDp / 2).toInt()
        if (smooth) {
            leftScrollState.animateScrollToItem(selected!!, scrollOffset = offset)
        } else {
            leftScrollState.scrollToItem(selected!!, scrollOffset = offset)
        }
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
        if (notifications.isNotEmpty() && selected == null) { // Ensure selection on first open
            selected = 0
        }
        rightScrollVelocity.loopForeverAndTick()
    }

    LaunchedEffect(notifications) {
        val newSize = notifications.size
        val oldSize = prevNotificationsSize.value
        if (newSize > oldSize && selected != null) {
            val itemsAdded = newSize - oldSize
            selected = (selected!! + itemsAdded).coerceAtMost(newSize - 1)
        } else {
            // List shrank, stayed same size, or nothing was selected.
            // Or if "Open to most recent" is desired when selection is lost or on fresh open.
            if (notifications.isNotEmpty()) {
                // If selected item is now out of bounds (e.g. deleted), or if nothing was selected,
                // select the first item (most recent).
                if (selected == null || selected!! >= newSize) {
                    selected = 0
                }
                // If selected item is still valid, `selected` will retain its value, no change needed here.
            } else {
                selected = null // No items, nothing to select
            }
        }
        prevNotificationsSize.value = newSize
    }


    LaunchedEffect(selected) {
        if (selected == null) {
            return@LaunchedEffect
        }
        // Ensure selected is within bounds before scrolling.
        if (notifications.isNotEmpty() && selected!! >= 0 && selected!! < notifications.size) {
            scrollTo(smooth = true)
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                if (isRightSideSelected) {
                    rightScrollVelocity.resetVelocity()
                    rightScrollState.animateScrollTo(0)
                } else {
                    if (notifications.isNotEmpty()) selected = 0
                }
            }

            GlobalEvent.ScrollToBottom -> {
                if (isRightSideSelected) {
                    rightScrollVelocity.resetVelocity()
                    rightScrollState.animateScrollTo(rightScrollState.maxValue)
                } else {
                    if (notifications.isNotEmpty()) selected = notifications.size - 1
                }
            }

            else -> {}
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
}