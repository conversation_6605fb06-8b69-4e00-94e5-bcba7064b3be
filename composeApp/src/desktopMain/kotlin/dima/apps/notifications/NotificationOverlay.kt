package dima.apps.notifications

import GlobalStyling
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.utils.clickableWithoutBackgroundRipple
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Shows notifications in an overlay popup in the top-right corner of the screen.
 * Manages the lifecycle and display of notifications.
 */
@Composable
fun NotificationOverlay() {
    var notifications by remember { mutableStateOf(listOf<Notification>()) }

    Popup(alignment = Alignment.TopEnd, offset = IntOffset(-12, 12)) {
        Column(
            modifier = Modifier
                .width(300.dp)
        ) {
            notifications.forEach { notification ->
                var visible by remember { mutableStateOf(true) }
                AnimatedVisibility(visible) {
                    Column(
                        verticalArrangement = Arrangement.Center,
                        modifier = Modifier
                            .width(300.dp)
                            .padding(4.dp)
                            .clip(RoundedCornerShape(5.dp))
                            .then(
                                when (notification.type) {
                                    NotificationType.Info, null -> {
                                        if (GlobalState.isDarkMode) {
                                            Modifier
                                                .background(TailwindCssColors.gray700)
                                                .border(
                                                    1.dp,
                                                    GlobalStyling.Dialog.darkThemeBorderColor,
                                                    GlobalStyling.Dialog.smallRoundedCorners
                                                )
                                        } else {
                                            Modifier.background(TailwindCssColors.gray700)
                                        }
                                    }

                                    NotificationType.Loading -> Modifier.background(loadingNotificationBackgroundBrush)
                                    NotificationType.Error -> Modifier.background(GlobalStyling.getRedBackgroundColor())
                                }
                            )
                            .clickableWithoutBackgroundRipple {
                                visible = false
                            }
                            .padding(8.dp),
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column {
                                Text(
                                    notification.title ?: "",
                                    style = TextStyle(fontWeight = FontWeight.Medium),
                                    color = Color.White,
                                    overflow = TextOverflow.Ellipsis,
                                    maxLines = 2
                                )
                                if (notification.hasMessage()) {
                                    Text(
                                        notification.message!!,
                                        style = TextStyle(fontSize = 12.sp),
                                        color = Color.White,
                                        modifier = Modifier.padding(top = 4.dp)
                                    )
                                }
                            }
                        }
                    }
                }
                LaunchedEffect(Unit) {
                    launch {
                        while (true) {
                            if (!notificationDurationMutex.isLocked) {
                                notificationDurationMutex.lock()
                            }
                            if (notification.durationMillis == 0L || notification.durationMillis == null) {
                                delay(250)
                                if (notificationDurationMutex.isLocked) {
                                    notificationDurationMutex.unlock()
                                }
                                continue
                            }
                            if (!visible) {
                                delay(750)
                                if (notificationDurationMutex.isLocked) {
                                    notificationDurationMutex.unlock()
                                }
                                break
                            }
                            delay(250)
                            if (notification.startTime!! + notification.durationMillis!! < System.currentTimeMillis()) {
                                delay(750)
                                visible = false
                                addDismissedToNotificationHistory(notification)
                                if (notificationDurationMutex.isLocked) {
                                    notificationDurationMutex.unlock()
                                }
                                break
                            }
                            if (notificationDurationMutex.isLocked) {
                                notificationDurationMutex.unlock()
                            }
                        }
                    }
                }
                // hopefully this will prevent notificationDurationSemaphore from locking up
                DisposableEffect(Unit) {
                    onDispose {
                        if (notificationDurationMutex.isLocked) {
                            notificationDurationMutex.unlock()
                        }
                    }
                }
            }
        }
    }
    // This should hopefully avoid when notifications are dangling and are always visible.
    // Regardless, I think notificationDurationSemaphore locks up and then no notifications are dismissed.
    LaunchedEffect(Unit) {
        launch {
            while (true) {
                delay(1000)
                for (notification in notifications) {
                    if (notification.startTime == null || notification.durationMillis == null || notification.isVisible == false) {
                        continue
                    }
                    if (notification.startTime!! + (notification.durationMillis!! + (3000 * notifications.size)) < System.currentTimeMillis()) {
                        dismissNotification(notification.id)
                    }
                }
            }
        }
    }
    LaunchedEffect(globalNotificationEvents) {
        for (event in globalNotificationEvents) {
            when (event.event) {
                NotificationEvent.Create -> {
                    val new = notifications.toMutableList()
                    new.add(event.notification!!)
                    notifications = new
                }

                NotificationEvent.Update -> {
                    val new = notifications.toMutableList()
                    new.map {
                        if (it.id == event.id) {
                            val notification = event.notification!!
                            if (notification.title != null) {
                                it.title = notification.title
                            }
                            if (notification.type != null) {
                                it.type = notification.type
                            }
                            if (notification.message == "") {
                                it.message = null
                            } else if (notification.message != null) {
                                it.message = notification.message
                            }
                            if (notification.startTime != null) {
                                it.startTime = notification.startTime
                            }
                            if (notification.durationMillis != null) {
                                it.durationMillis = notification.durationMillis
                            }
                            it
                        } else {
                            it
                        }
                    }
                    notifications = new
                }

                NotificationEvent.Dismiss -> {
                    val new = notifications.toMutableList()
                    // ignore when not found, in most cases, it was already dismissed
                    val notification = notifications.firstOrNull { it.id == event.id }
                    if (notification != null) {
                        addDismissedToNotificationHistory(notification)
                        new.removeAll { it.id == event.id }
                        notifications = new
                    }
                }

                NotificationEvent.DismissAll -> {
                    addDismissedToNotificationHistory(*notifications.toTypedArray())
                    notifications = listOf()
                }
            }
        }
        globalNotificationEvents = listOf()
    }
}
