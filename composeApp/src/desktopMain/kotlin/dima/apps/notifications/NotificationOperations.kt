package dima.apps.notifications

class LoadingNotification internal constructor(val id: Long) {

    fun toInfo(title: String? = null, message: String? = null, durationMillis: Long = 3000) {
        updateNotificationType(title, message, durationMillis, NotificationTypeFromLoading.Info)
    }

    fun toError(title: String? = null, message: String? = null, durationMillis: Long = 3000) {
        updateNotificationType(title, message, durationMillis, NotificationTypeFromLoading.Error)
    }

    fun toType(
        title: String? = null,
        message: String? = null,
        durationMillis: Long = 3000,
        type: NotificationTypeFromLoading
    ) {
        updateNotificationType(title, message, durationMillis, type)
    }

    fun update(title: String? = null, message: String? = null) {
        updateNotification(id, title = title, message = message)
    }

    private fun updateNotificationType(
        title: String? = null,
        message: String? = null,
        durationMillis: Long = 3000,
        type: NotificationTypeFromLoading
    ) {
        updateNotification(
            id,
            title = title,
            message = message,
            durationMillis = durationMillis,
            type = type.toNotificationType()
        )
    }

    fun dismiss() {
        dismissNotification(id)
    }
}

fun showLoadingNotification(
    title: String,
    message: String? = null,
): LoadingNotification {
    notificationModificationSemaphore.acquire()
    val id = lastId++
    globalNotificationEvents = globalNotificationEvents + NotificationGlobalEvent(
        event = NotificationEvent.Create,
        Notification(
            id = id,
            title = title,
            type = NotificationType.Loading,
            message = message,
            startTime = System.currentTimeMillis(),
            durationMillis = null,
            isVisible = true
        )
    )
    notificationModificationSemaphore.release()
    return LoadingNotification(id)
}

fun showNotification(
    title: String,
    message: String? = null,
    durationMillis: Long? = 3000,
    type: NotificationType = NotificationType.Info
): Long {
    notificationModificationSemaphore.acquire()
    val id = lastId++
    globalNotificationEvents = globalNotificationEvents + NotificationGlobalEvent(
        event = NotificationEvent.Create,
        Notification(
            id = id,
            title = title,
            type = type,
            message = message,
            startTime = System.currentTimeMillis(),
            durationMillis = if (type == NotificationType.Loading) null else durationMillis,
            isVisible = true
        )
    )
    notificationModificationSemaphore.release()
    return id
}

fun showErrorNotification(title: String, message: String? = null, durationMillis: Long? = 3000): Long {
    return showNotification(title, message, durationMillis = durationMillis, type = NotificationType.Error)
}

fun updateNotification(
    id: Long,
    title: String? = null,
    message: String? = null,
    type: NotificationType? = null,
    durationMillis: Long? = null
) {
    notificationModificationSemaphore.acquire()
    globalNotificationEvents = globalNotificationEvents + NotificationGlobalEvent(
        event = NotificationEvent.Update,
        Notification(
            id = id,
            title = title,
            type = type,
            message = message,
            startTime = System.currentTimeMillis(),
            durationMillis = durationMillis,
            isVisible = true
        ),
        id
    )
    notificationModificationSemaphore.release()
}

fun dismissNotification(id: Long) {
    notificationModificationSemaphore.acquire()
    globalNotificationEvents = globalNotificationEvents + NotificationGlobalEvent(
        event = NotificationEvent.Dismiss,
        id = id
    )
    notificationModificationSemaphore.release()
}

fun dismissAllNotifications() {
    globalNotificationEvents = globalNotificationEvents + NotificationGlobalEvent(
        event = NotificationEvent.DismissAll
    )
}
