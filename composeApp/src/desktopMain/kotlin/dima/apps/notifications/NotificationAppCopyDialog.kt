package dima.apps.notifications

import GlobalStyling
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import dima.utils.truncateWithEllipsis

private const val truncationLength = 60

internal fun openCopyNotificationPartDialog(notification: Notification) {
    if (notification.title == null && notification.message == null) {
        showErrorNotification("No content to copy")
        return
    }

    val prefixTextWidth = 80.dp // Adjusted for potentially longer prefixes
    val textColor = GlobalStyling.getTextColor()

    val candidates = buildList {
        if (notification.title != null && notification.message != null) {
            add(
                CompletionDialogCandidate(
                    text = notification.title!!.truncateWithEllipsis(truncationLength) + "\n\n" +
                            notification.message!!.truncateWithEllipsis(truncationLength),
                    prefixView = {
                        Text(
                            "All",
                            color = textColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixTextWidth)
                        )
                    },
                    additionalSearchString = "All"
                )
            )
        }
        if (notification.title != null) {
            add(
                CompletionDialogCandidate(
                    text = notification.title!!.truncateWithEllipsis(truncationLength),
                    prefixView = {
                        Text(
                            "Title",
                            color = textColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixTextWidth)
                        )
                    },
                    additionalSearchString = "Title"
                )
            )
        }
        if (notification.message != null) {
            add(
                CompletionDialogCandidate(
                    text = notification.message!!.truncateWithEllipsis(truncationLength),
                    prefixView = {
                        Text(
                            "Subtitle",
                            color = textColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixTextWidth)
                        )
                    },
                    additionalSearchString = "Subtitle"
                )
            )
        }
    }

    if (candidates.size == 1) {
        val singleCandidate = candidates.first()
        val textToCopy = when (singleCandidate.additionalSearchString) {
            "Title" -> notification.title!!
            "Subtitle" -> notification.message!!
            else -> notification.title ?: notification.message!! // Should not happen with size 1 logic
        }
        copyToClipboard(textToCopy)
        return
    }

    openCompletionDialog(
        title = "Copy from notification",
        candidates = candidates,
        hideCopyCmdActionInBottomBar = true,
        onAccept = { accepted ->
            val candidate = candidates[accepted.index!!] // Get the CompletionDialogCandidate
            val textToCopy: String = when (candidate.additionalSearchString) {
                "All" -> {
                    buildString {
                        append(notification.title ?: "")
                        if (!notification.message.isNullOrBlank()) {
                            append("\n\n")
                            append(notification.message)
                        }
                    }
                }

                "Title" -> notification.title!!
                "Subtitle" -> notification.message!!
                else -> throw Exception("Unhandled candidate: ${candidate.additionalSearchString}")
            }
            copyToClipboard(textToCopy)
        }
    )
}
