package dima.apps.notifications

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import dima.color.TailwindCssColors
import kotlinx.coroutines.sync.Mutex
import java.util.concurrent.Semaphore
import kotlin.math.sin

enum class NotificationType {
    Info,
    Error,
    Loading
}

enum class NotificationTypeFromLoading {
    Info,
    Error;

    fun toNotificationType(): NotificationType {
        return when (this) {
            Info -> NotificationType.Info
            Error -> NotificationType.Error
        }
    }
}

data class Notification(
    val id: Long,
    var title: String?,
    var type: NotificationType?,
    var startTime: Long?,
    var message: String?,
    var durationMillis: Long?,
    var isVisible: Boolean? = true,
) {
    fun hasMessage(): Boolean = message != null && message!!.isNotBlank()
}

internal enum class NotificationEvent {
    Create,
    Update,
    Dism<PERSON>,
    DismissAll,
}

internal data class NotificationGlobalEvent(
    val event: NotificationEvent,
    val notification: Notification? = null,
    val id: Long? = null
)

internal val loadingNotificationBackgroundBrush =
    Brush.sweepGradient(
        0f to TailwindCssColors.fuchsia700,
        0.5f to TailwindCssColors.violet600,
        1f to TailwindCssColors.fuchsia700,
        center = Offset(
            (-100 + (500f * sin((1).toDouble())).toFloat()),
            -30f
        ),
    )

internal var globalNotificationEvents by mutableStateOf(listOf<NotificationGlobalEvent>())
internal val notificationDurationMutex = Mutex()
internal val notificationModificationSemaphore = Semaphore(1)
internal var lastId = 0L
