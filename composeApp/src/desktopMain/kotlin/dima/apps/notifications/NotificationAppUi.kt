package dima.apps.notifications

import GlobalStyling
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.X
import dima.apps.dired.addSelectedBorder
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.utils.scrollbarStyleThemed

@Composable
internal fun NotificationItem(
    notification: Notification,
    isSelected: Boolean,
    isRightSideSelected: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .alpha(if (!isSelected && isRightSideSelected) GlobalStyling.DISABLED_ALPHA else 1f)
            .then(
                if (isSelected && isRightSideSelected) {
                    Modifier.background(
                        GlobalStyling.getSelectedInactiveBackgroundColor(),
                        GlobalStyling.smallRoundedCorners
                    )
                } else if (isSelected) {
                    Modifier.background(GlobalStyling.getSelectedBackgroundColor(), GlobalStyling.smallRoundedCorners)
                } else {
                    Modifier
                }
            )
            .padding(12.dp)
    ) {
        val textColor = GlobalStyling.getTextColor()
        SelectionContainer {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (notification.type == NotificationType.Error) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .background(TailwindCssColors.red600, CircleShape)
                            .size(18.dp)
                    ) {
                        Icon(
                            Lucide.X,
                            contentDescription = "Error",
                            tint = TailwindCssColors.white,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    Spacer(Modifier.width(6.dp))
                }
                Text(
                    text = notification.title ?: "[no title]",
                    color = if (GlobalState.isDarkMode) TailwindCssColors.white else textColor,
                    maxLines = 3,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
        if (!notification.message.isNullOrBlank()) {
            SelectionContainer {
                Text(
                    notification.message!!,
                    color = textColor,
                    maxLines = 6,
                    modifier = Modifier.padding(top = if (notification.type == NotificationType.Error) 4.dp else 2.dp)
                )
            }
        }
    }
}

@Composable
internal fun NotificationAppUiList(
    notifications: List<Notification>,
    selectedId: Int?,
    isRightSideSelected: Boolean,
    scrollState: LazyListState,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        LazyColumn(
            contentPadding = PaddingValues(bottom = 12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            state = scrollState,
            modifier = Modifier
        ) {
            items(notifications, key = { it.id }) { notification ->
                NotificationItem(
                    notification = notification,
                    isSelected = notification.id == selectedId?.toLong(),
                    isRightSideSelected = isRightSideSelected,
                    modifier = Modifier
                        .padding(start = 12.dp)
                        .fillMaxWidth()
                )
            }
        }
        VerticalScrollbar(
            style = scrollbarStyleThemed(),
            adapter = rememberScrollbarAdapter(scrollState = scrollState),
            modifier = Modifier
                .padding(bottom = 12.dp)
                .align(Alignment.TopStart),
        )
    }
}

@Composable
internal fun NotificationAppUiDetail(
    notification: Notification,
    isRightSideSelected: Boolean,
    scrollState: ScrollState,
    modifier: Modifier = Modifier
) {
    val color = GlobalStyling.getTextColor()
    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 12.dp)
                .addSelectedBorder(isRightSideSelected)
                .verticalScroll(state = scrollState)
                .padding(12.dp)
        ) {
            SelectionContainer {
                Text(
                    notification.title!! + "\n",
                    color = if (GlobalState.isDarkMode) TailwindCssColors.white else color,
                    fontWeight = FontWeight.SemiBold
                )
            }
            if (notification.hasMessage()) {
                SelectionContainer {
                    Text(
                        notification.message!!,
                        color = color,
                    )
                }
            }
        }
        VerticalScrollbar(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(end = if (isRightSideSelected) 5.dp else 0.dp),
            adapter = rememberScrollbarAdapter(scrollState = scrollState)
        )
    }
}

