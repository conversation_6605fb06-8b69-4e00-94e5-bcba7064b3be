package dima.apps.calendar

import dima.utils.Result
import org.jsoup.internal.StringUtil.isNumeric
import java.time.DateTimeException
import java.time.LocalDate
import java.util.regex.Pattern

private val months: Map<String, Int> = mapOf(
    "jan" to 1, "januar" to 1,
    "feb" to 2, "februar" to 2,
    "mar" to 3, "maerz" to 3, "marz" to 3, "march" to 3, "märz" to 3,
    "apr" to 4, "april" to 4,
    "mai" to 5, "may" to 5,
    "jun" to 6, "juni" to 6, "june" to 6,
    "jul" to 7, "juli" to 7, "july" to 7,
    "aug" to 8, "august" to 8,
    "sep" to 9, "sept" to 9, "september" to 9,
    "oct" to 10, "okt" to 10, "oktober" to 10, "october" to 10,
    "nov" to 11, "november" to 11,
    "dec" to 12, "dez" to 12, "dezember" to 12, "december" to 12
)

data class DateParseResult(
    var foundDay: Int? = null,
    var foundMonth: Int? = null,
    var foundYear: Int? = null
)

/**
 * Parses a string to extract a date, inspired by Emacs (org-time-stamp) functionality.
 *
 * @param input String input for parsing.
 * @param startDate Optional start date for reference. Defaults to today.
 * @return Parsed date as a LocalDate instance.
 */
fun parseStringToDate(input: String, startDate: LocalDate? = null): Result<LocalDate> { 
    val trimmedInput = input.trim().lowercase()
    if (trimmedInput.isEmpty()) {
        return Result.Error("Empty input")
    }

    // ISO date support (yyyy-MM-dd)
    if (trimmedInput.contains('-')) {
        val iso = runCatching { java.time.LocalDate.parse(trimmedInput) }.getOrNull()
        if (iso != null) return Result.Success(iso)
    }

    val referenceDate = startDate ?: LocalDate.now()

    // Handle relative dates like +1, +3m, +90
    val relativeDatePattern = Pattern.compile("^\\+(\\d+)([dym]?)$")
    val matcher = relativeDatePattern.matcher(trimmedInput)
    if (matcher.matches()) {
        val amount = matcher.group(1)?.toLongOrNull()
        val unit = matcher.group(2)

        if (amount == null) {
            return Result.Error("Invalid relative date format")
        }

        return try {
            val newDate = when (unit) {
                "d", "" -> referenceDate.plusDays(amount)
                "m" -> referenceDate.plusMonths(amount)
                "y" -> referenceDate.plusYears(amount)
                else -> return Result.Error("Unknown relative date unit: $unit")
            }
            Result.Success(newDate)
        } catch (e: DateTimeException) {
            Result.Error("Error calculating relative date: ${e.message}")
        }
    }

    val parts = trimmedInput.split(Regex("[ .]+")).filter { it.isNotEmpty() }
    if (parts.size >= 4) {
        return Result.Error("Too many parts in input")
    }
    val parseState = DateParseResult()
    var parsedMonthByText = false // Renamed from parsedMonth to be clearer

    for (part in parts) {
        if (isNumeric(part)) {
            val number = part.toInt()
            when {
                parseState.foundDay == null && number in 1..31 -> parseState.foundDay = number
                parseState.foundMonth == null && number in 1..12 -> parseState.foundMonth = number
                parseState.foundYear == null -> parseState.foundYear = if (number in 1..99) 2000 + number else number
                // If number doesn't fit, it's ignored or could be an error for too many numeric parts,
                // but parts.size >= 4 already checks overall part count.
            }
        } else {
            if (parseState.foundMonth == null) {
                val month = months[part]
                if (month != null) {
                    parseState.foundMonth = month
                    parsedMonthByText = true
                }
            }
        }
    }

    // If no date components were found at all
    if (parseState.foundDay == null && parseState.foundMonth == null && parseState.foundYear == null) {
        return Result.Error("No date found")
    }

    // Specific validation for three numeric parts (e.g. "10 10 23" expects D M Y)
    // This check is about the raw parts if no text month was parsed.
    if (!parsedMonthByText && parts.size == 3) {
        val dayCandidate = parts[0].toIntOrNull()
        val monthCandidate = parts[1].toIntOrNull()
        // parts[2] is assumed to be year, its validity will be checked by LocalDate.of

        if (dayCandidate == null || dayCandidate !in 1..31 ||
            monthCandidate == null || monthCandidate !in 1..12
        ) {
            return Result.Error("Invalid date format for 3 numeric parts: Expected D M Y structure where D is 1-31 and M is 1-12.")
        }
        // If this check passes, it implies that parts[0] was parsed as foundDay
        // and parts[1] as foundMonth by the main loop if they were valid.
        // If parts[0] or parts[1] were out of typical day/month range but assigned to year/other by flexible parser,
        // this rule enforces the D M Y for 3 numeric parts.
    }


    var finalYear = referenceDate.year
    var finalMonth = referenceDate.monthValue
    var finalDay = referenceDate.dayOfMonth

    // Apply found year first
    if (parseState.foundYear != null) {
        finalYear = parseState.foundYear!!
    }

    // Apply found month
    if (parseState.foundMonth != null) {
        finalMonth = parseState.foundMonth!!
    }

    // Apply found day and handle "only day" logic
    if (parseState.foundDay != null) {
        finalDay = parseState.foundDay!! // Use the day from input

        // If only the day was provided in the input string (no month or year)
        if (parseState.foundMonth == null && parseState.foundYear == null) {
            // Base off the referenceDate's month and year initially for this logic
            var tempMonth = referenceDate.monthValue
            var tempYear = referenceDate.year

            // If the specified day is on or before the reference day in the current month, advance.
            if (referenceDate.dayOfMonth >= finalDay) {
                // Advance to the next month, ensuring day is valid by setting to 1 first
                val advancedDate = referenceDate.withDayOfMonth(1).plusMonths(1)
                tempMonth = advancedDate.monthValue
                tempYear = advancedDate.year
            }
            // Update finalMonth and finalYear based on this "only day" logic
            finalMonth = tempMonth
            finalYear = tempYear
        }
        // If day and month were provided, year might be from referenceDate (if not in parseState.foundYear)
        // If day and year were provided, month might be from referenceDate (if not in parseState.foundMonth)
        // finalDay is already set from parseState.foundDay.
    }
    // If no day was explicitly provided in input (parseState.foundDay == null),
    // finalDay remains referenceDate.dayOfMonth (defaulted above). This is correct.

    return try {
        Result.Success(LocalDate.of(finalYear, finalMonth, finalDay))
    } catch (e: DateTimeException) {
        Result.Error("Invalid date combination: Day $finalDay for month $finalMonth/$finalYear. Details: ${e.message}")
    }
}
