package dima.apps.calendar

import GlobalEvent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.focus.FocusRequester
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dialogs
import dima.globalState.GlobalState
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.Result
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.time.LocalDate

private fun List<CalendarEvent>.sortedForDisplay(): List<CalendarEvent> {
    return this.sortedWith(
        compareBy<CalendarEvent>(
            // 1. Start date/time
            { event ->
                when (event) {
                    is CalendarEvent.AllDay -> event.startDate.atStartOfDay()
                    is CalendarEvent.WithTime -> event.startDateTime.toLocalDateTime()
                }
            },
            // 2. All-day events before timed events
            { event ->
                when (event) {
                    is CalendarEvent.AllDay -> 0
                    is CalendarEvent.WithTime -> 1
                }
            }
        ).thenComparing { event -> // 3. For AllDay events starting on the same day, sort by endDate descending (longer events first)
            if (event is CalendarEvent.AllDay) {
                // endDate is exclusive. A larger epochDay for endDate means the event ends later or spans longer.
                // Sorting by -endDate.toEpochDay() achieves descending order by endDate.
                -event.endDate.toEpochDay()
            } else {
                0 // This comparator does not apply further sorting for TimedEvents at this stage.
            }
        }.thenComparing { event -> // 4. Fallback to summary for tie-breaking
            when (event) {
                is CalendarEvent.AllDay -> event.summary
                is CalendarEvent.WithTime -> event.summary
            }
        }
    )
}

private suspend fun handleReloadEvents(changeEvents: (List<CalendarEvent>) -> Unit) {
    val currentYear = GlobalState.calendar.selectedDate.year
    val yearsToLoad = listOf(currentYear - 1, currentYear, currentYear + 1)
    var phaseErrorOccurred = false

    val loadingNotification = showLoadingNotification("Reloading calendar events...")
    // Phase 1 for RELOAD: Fetch from API, update DB for all relevant years
    yearsToLoad.forEach { year ->
        val apiResult = GoogleCalendarService.downloadEventsForYearOrLoadFromDatabase(
            year,
            forceDownload = true // Force API fetch
        )
        if (apiResult is Result.Error) {
            // Avoid logging the specific "scope left" error as it's handled by isActive checks
            if (apiResult.error != "The coroutine scope left the composition") {
                showErrorNotification("Error updating calendar for $year (Reload)", apiResult.error)
            }
            phaseErrorOccurred = true
        }
    }

    if (!phaseErrorOccurred) {
        // Phase 2 for RELOAD: After all API updates, reload all from DB for UI
        val refreshedEventsFromDb = mutableSetOf<CalendarEvent>()
        yearsToLoad.forEach { year ->
            GoogleCalendarService.downloadEventsForYearOrLoadFromDatabase(
                year,
                forceDownload = false // Load from DB (which is now updated)
            ).let { dbLoadResult ->
                if (dbLoadResult is Result.Success) {
                    val newEvents = dbLoadResult.value.events
                    val newEventIds = newEvents.map {
                        when (it) {
                            is CalendarEvent.AllDay -> it.id
                            is CalendarEvent.WithTime -> it.id
                        }
                    }.toSet()
                    refreshedEventsFromDb.removeAll { existingEvent ->
                        val existingId = when (existingEvent) {
                            is CalendarEvent.AllDay -> existingEvent.id
                            is CalendarEvent.WithTime -> existingEvent.id
                        }
                        existingId in newEventIds
                    }
                    refreshedEventsFromDb.addAll(newEvents)
                } else if (dbLoadResult is Result.Error && dbLoadResult.error != "The coroutine scope left the composition") {
                    showErrorNotification(
                        "Error reloading from DB for $year after API fetch",
                        dbLoadResult.error
                    )
                }
            }
        }
        changeEvents(refreshedEventsFromDb.toList().sortedForDisplay())
        loadingNotification.toInfo("Calendar events reloaded", durationMillis = 1000L)
    } else { // Errors occurred during API fetch
        loadingNotification.toError("Calendar reload finished with errors.")
    }
}


@Composable
internal fun CalendarAppEffects(
    appFocusRequester: FocusRequester,
    previousYearForInitialLoad: Int?,
    isInitialLoadForYear: Boolean,
    changeIsInitialLoadForYear: (Boolean) -> Unit,
    changePreviousYearForInitialLoad: (Int?) -> Unit,
    changeEvents: (List<CalendarEvent>) -> Unit,
    scope: CoroutineScope, // Use the one passed from CalendarApp
    scrollToCurrentDay: (animateScroll: Boolean) -> Unit,
    tryUpdateDate: (newDate: LocalDate, allowYearChange: Boolean) -> Unit,
    events: List<CalendarEvent>,
    currentFocusArea: CalendarFocusArea,
    changeSelectedEventIndexInRightSide: (Int?) -> Unit,
) {
    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }
    LaunchedEffect(GlobalState.calendar.selectedDate, events, currentFocusArea) {
        if (currentFocusArea == CalendarFocusArea.RightSide) {
            val eventsOnDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
            changeSelectedEventIndexInRightSide(if (eventsOnDate.isEmpty()) null else 0)
        } else {
            changeSelectedEventIndexInRightSide(null)
        }
    }
    LaunchedEffect(GlobalState.calendar.selectedDate.year) {
        val currentYear = GlobalState.calendar.selectedDate.year
        var shouldProcessEvents = false
        if (previousYearForInitialLoad != currentYear) {
            changeIsInitialLoadForYear(true) // This specific state change might be fine here as it's at the start.
            changePreviousYearForInitialLoad(currentYear) // Same here.
            shouldProcessEvents = true
        } else if (isInitialLoadForYear) {
            shouldProcessEvents = true
        }

        if (!shouldProcessEvents) {
            return@LaunchedEffect
        }

        // Initial Load or Year Change - load events from cache/DB
        val yearsToLoad = listOf(currentYear - 1, currentYear, currentYear + 1)
        val displayEvents = mutableSetOf<CalendarEvent>()
        var phaseErrorOccurred = false

        yearsToLoad.forEach { year ->
            if (!isActive) return@forEach
            val result = GoogleCalendarService.downloadEventsForYearOrLoadFromDatabase(
                year,
                forceDownload = false // Use cache for initial load
            )
            when (result) {
                is Result.Success -> displayEvents.addAll(result.value.events)
                is Result.Error -> {
                    if (result.error != "The coroutine scope left the composition") {
                        showErrorNotification("Error loading calendar events for $year", result.error)
                        phaseErrorOccurred = true
                    }
                }
            }
        }

        if (isActive && (displayEvents.isNotEmpty() || !phaseErrorOccurred)) {
            changeEvents(displayEvents.toList().sortedForDisplay())
        }

        // Background API updates for Initial Load or Year Change (if display came from cache)
        if (isActive) {
            yearsToLoad.forEach { yearToUpdateInBackground ->
                scope.launch(Dispatchers.IO) { // Background task
                    if (!isActive) return@launch // Check parent scope before starting
                    val apiFetchResult = GoogleCalendarService.downloadEventsForYearOrLoadFromDatabase(
                        yearToUpdateInBackground,
                        forceDownload = true // Force API for background update
                    )
                    if (apiFetchResult is Result.Success && isActive) { // Check child scope (this launch)
                        val refreshedEventsFromDb = mutableSetOf<CalendarEvent>()
                        yearsToLoad.forEach { yearToReloadFromDb ->
                            if (!isActive) return@forEach
                            GoogleCalendarService.downloadEventsForYearOrLoadFromDatabase(
                                yearToReloadFromDb,
                                forceDownload = false // Load from DB
                            ).let { dbReloadResult ->
                                if (dbReloadResult is Result.Success) {
                                    val newEvents = dbReloadResult.value.events
                                    val newEventIds = newEvents.map {
                                        when (it) {
                                            is CalendarEvent.AllDay -> it.id
                                            is CalendarEvent.WithTime -> it.id
                                        }
                                    }.toSet()
                                    refreshedEventsFromDb.removeAll { existingEvent ->
                                        val existingId = when (existingEvent) {
                                            is CalendarEvent.AllDay -> existingEvent.id
                                            is CalendarEvent.WithTime -> existingEvent.id
                                        }
                                        existingId in newEventIds
                                    }
                                    refreshedEventsFromDb.addAll(newEvents)
                                }
                            }
                        }
                        if (isActive) changeEvents(refreshedEventsFromDb.toList().sortedForDisplay())
                    } else if (apiFetchResult is Result.Error && isActive && apiFetchResult.error != "The coroutine scope left the composition") {
                        showErrorNotification(
                            "Background calendar update failed for $yearToUpdateInBackground",
                            apiFetchResult.error
                        )
                    }
                }
            }
        }

        if (isInitialLoadForYear) {
            if (isActive) scrollToCurrentDay(false)
        }
        if (isActive) { // Guard this state change
            changeIsInitialLoadForYear(false)
        }
    }
    LaunchedEffect(GlobalState.calendar.selectedDate) {
        if (!isInitialLoadForYear && isActive) {
            scrollToCurrentDay(true)
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty() && isActive) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffectGlobalEventForApps {
        if (!isActive) {
            return@LaunchedEffectGlobalEventForApps
        }
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> tryUpdateDate(LocalDate.of(GlobalState.calendar.selectedDate.year, 1, 1), true)

            GlobalEvent.ScrollToBottom -> tryUpdateDate(
                LocalDate.of(GlobalState.calendar.selectedDate.year, 12, 31),
                true
            )

            GlobalEvent.Reload -> handleReloadEvents(changeEvents)
            else -> {}
        }
    }
}