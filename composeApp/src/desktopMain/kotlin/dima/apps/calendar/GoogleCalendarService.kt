package dima.apps.calendar

import androidx.compose.ui.graphics.Color
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.client.util.DateTime
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.EventDateTime
import dima.apps.calendar.ui.CalendarAppUiStyling
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.NetworkActivity.updateFaked
import dima.apps.notifications.showErrorNotification
import dima.dateTime.toLocalDate
import dima.dateTime.toZonedDateTime
import dima.utils.Result
import dima.utils.SimpleResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.UnknownHostException
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime

internal object GoogleCalendarService {
    const val TOKENS_DIRECTORY_PATH = "cache/calendar"
    internal const val APPLICATION_NAME = "Kotlin Emacs Calendar"
    internal val JSON_FACTORY = GsonFactory.getDefaultInstance()

    private fun createApiService(): Calendar {
        val httpTransport = GoogleNetHttpTransport.newTrustedTransport()
        return Calendar.Builder(
            httpTransport,
            JSON_FACTORY,
            getCredentials(httpTransport)
        ).setApplicationName(APPLICATION_NAME)
            .build()
    }

    data class EventsResult(
        val events: List<CalendarEvent>,
        val cached: Boolean
    )

    /**
     * @param forceDownload if true, the events are downloaded from the API, even if they are in the database
     */
    suspend fun downloadEventsForYearOrLoadFromDatabase(
        year: Int,
        forceDownload: Boolean = false
    ): Result<EventsResult> {
        val log = NetworkActivity.addFakedEntry(
            "Fetching calendar events for $year (force=$forceDownload)",
            FakedNetworkActivityStatus.InTransit
        )
        return try {
            withContext(Dispatchers.IO) {
                val nrwHolidaysForYear = getNrwHolidays(year)

                fun distinctEvents(events: List<CalendarEvent>): List<CalendarEvent> {
                    return events.distinctBy {
                        when (it) {
                            is CalendarEvent.AllDay -> it.startDate.toString() + it.summary
                            is CalendarEvent.WithTime -> it.startDateTime.toLocalDate().toString() + it.summary
                        }
                    }
                }

                if (!forceDownload) {
                    val dbEvents = CalendarDatabase.getEventsForYear(year)
                    if (dbEvents.isNotEmpty()) {
                        val combinedEvents = distinctEvents(dbEvents + nrwHolidaysForYear)
                        log.updateFaked(
                            text = "Loaded calendar events for $year from cache",
                            status = FakedNetworkActivityStatus.Success,
                            responseBody = "${dbEvents.size} events from DB, ${nrwHolidaysForYear.size} holidays"
                        )
                        return@withContext Result.Success(EventsResult(events = combinedEvents, cached = true))
                    }
                }
                val service = createApiService()
                // Fetch events from 7 days before the start of the year to 7 days after the end of the year
                val fetchStartDate = LocalDateTime.of(year, 1, 1, 0, 0).minusDays(7)
                val fetchEndDate = LocalDateTime.of(year, 12, 31, 23, 59).plusDays(7)

                val timeMin = DateTime(fetchStartDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())
                val timeMax = DateTime(fetchEndDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())

                val eventsResponse = service.events().list("primary")
                    .setTimeMin(timeMin)
                    .setTimeMax(timeMax)
                    .setOrderBy("startTime")
                    .setSingleEvents(true)
                    // set to the largest allowed value
                    .setMaxResults(2500)
                    .execute()

                val mappedEventsFromApi = if (eventsResponse.items == null) {
                    emptyList()
                } else {
                    if (eventsResponse.items.size >= 2450) {
                        showErrorNotification("Approaching Google Calendar API limit", "2500 events per year")
                    }
                    eventsResponse.items.mapNotNull { event ->
                        val summary = event.summary ?: "No title"
                        val color: Color = event.colorId?.let { colorMap[it] } ?: CalendarAppUiStyling.defaultEventColor

                        // Skip cancelled events
                        if (event.status == "cancelled") {
                            return@mapNotNull null
                        }

                        if (event.start.date == null) { // This means it's a timed event
                            val start = event.start.dateTime?.toZonedDateTime()
                            val end = event.end.dateTime?.toZonedDateTime()
                            if (start == null || end == null) {
                                // Log or handle missing dateTimes for timed events
                                return@mapNotNull null
                            }
                            CalendarEvent.WithTime(
                                id = event.id,
                                summary = summary,
                                color = color,
                                startDateTime = start,
                                endDateTime = end,
                                description = event.description
                            )
                        } else { // This means it's an all-day event
                            val start = event.start.date?.toLocalDate()
                            val end = event.end.date?.toLocalDate()
                            if (start == null || end == null) {
                                // Log or handle missing dates for all-day events
                                return@mapNotNull null
                            }
                            CalendarEvent.AllDay(
                                id = event.id,
                                summary = summary,
                                color = color,
                                startDate = start,
                                endDate = end, // This is the exclusive end date from Google
                                description = event.description
                            )
                        }
                    }
                }

                // Save ONLY API events to database
                CalendarDatabase.insertEventsForYear(year, mappedEventsFromApi)

                val combinedEventsForDisplay = distinctEvents(mappedEventsFromApi + nrwHolidaysForYear)
                log.updateFaked(
                    text = "Fetched calendar events for $year from API",
                    status = FakedNetworkActivityStatus.Success,
                    responseBody = "${mappedEventsFromApi.size} events from API, ${nrwHolidaysForYear.size} holidays"
                )
                return@withContext Result.Success(EventsResult(combinedEventsForDisplay, cached = false))
            }
        } catch (e: Exception) {
            val errorMsg = e.message ?: "Unknown error"
            log.updateFaked(
                text = "Failed to fetch calendar events for $year from API",
                status = FakedNetworkActivityStatus.Error,
                responseBody = errorMsg
            )
            if (e is UnknownHostException) {
                Result.Error("No internet")
            } else {
                Result.Error(errorMsg)
            }
        }
    }

    /**
     * Creates an all-day event, which can span multiple days.
     * The Google Calendar API expects the end date for all-day events to be exclusive.
     */
    fun createAllDayEvent(
        startDate: LocalDate,
        endDate: LocalDate,
        title: String,
        description: String? = null,
    ): SimpleResult {
        val logText = "Creating all-day event: $title (All-day)"
        val requestDetails = "Start: $startDate, End: $endDate, Desc: $description"
        val log = NetworkActivity.addFakedEntry(logText, FakedNetworkActivityStatus.InTransit, requestBody = requestDetails)
        return try {
            val service = createApiService()
            val startEventDateTime = EventDateTime().setDate(DateTime(startDate.toString()))
            val apiExclusiveEndDate = endDate.plusDays(1)
            val endEventDateTime = EventDateTime().setDate(DateTime(apiExclusiveEndDate.toString()))

            val event = Event()
                .setSummary(title)
                .setDescription(description)
                .setStart(startEventDateTime)
                .setEnd(endEventDateTime)
            service.events().insert("primary", event).execute()
            log.updateFaked(status = FakedNetworkActivityStatus.Success)
            SimpleResult.Success
        } catch (e: Exception) {
            log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = e.message ?: "Unknown error")
            SimpleResult.Error(e.message ?: "Unknown error")
        }
    }

    /** Creates a timed event. */
    fun createTimedEvent(
        startDateTime: ZonedDateTime,
        endDateTime: ZonedDateTime,
        title: String,
        description: String? = null
    ): SimpleResult {
        val logText = "Creating timed event: $title (Timed)"
        val requestDetails = "Start: $startDateTime, End: $endDateTime, Desc: $description"
        val log = NetworkActivity.addFakedEntry(logText, FakedNetworkActivityStatus.InTransit, requestBody = requestDetails)
        return try {
            val service = createApiService()

            val start = EventDateTime()
                .setDateTime(DateTime(startDateTime.toInstant().toEpochMilli()))
                .setTimeZone(startDateTime.zone.id)

            val end = EventDateTime()
                .setDateTime(DateTime(endDateTime.toInstant().toEpochMilli()))
                .setTimeZone(endDateTime.zone.id)

            val event = Event()
                .setSummary(title)
                .setDescription(description)
                .setStart(start)
                .setEnd(end)

            service.events().insert("primary", event).execute()
            log.updateFaked(status = FakedNetworkActivityStatus.Success)
            SimpleResult.Success
        } catch (e: Exception) {
            log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = e.message ?: "Unknown error creating timed event")
            SimpleResult.Error(e.message ?: "Unknown error creating timed event")
        }
    }

    fun updateAllDayEvent(
        id: String,
        startDate: LocalDate,
        endDate: LocalDate,
        title: String,
        description: String?
    ): SimpleResult {
        val log = NetworkActivity.addFakedEntry("Updating all-day event $id", FakedNetworkActivityStatus.InTransit)
        return try {
            val service = createApiService()
            val event = service.events().get("primary", id).execute()
            event.summary = title
            event.description = description
            event.start = EventDateTime().setDate(DateTime(startDate.toString()))
            event.end = EventDateTime().setDate(DateTime(endDate.plusDays(1).toString()))
            service.events().update("primary", id, event).execute()
            log.updateFaked(status = FakedNetworkActivityStatus.Success)
            SimpleResult.Success
        } catch (e: Exception) {
            log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = e.message ?: "Unknown error updating all-day event")
            SimpleResult.Error(e.message ?: "Unknown error updating all-day event")
        }
    }

    fun updateTimedEvent(
        id: String,
        startDateTime: ZonedDateTime,
        endDateTime: ZonedDateTime,
        title: String,
        description: String?
    ): SimpleResult {
        val log = NetworkActivity.addFakedEntry("Updating timed event $id", FakedNetworkActivityStatus.InTransit)
        return try {
            val service = createApiService()
            val event = service.events().get("primary", id).execute()
            event.summary = title
            event.description = description
            event.start = EventDateTime()
                .setDateTime(DateTime(startDateTime.toInstant().toEpochMilli()))
                .setTimeZone(startDateTime.zone.id)
            event.end = EventDateTime()
                .setDateTime(DateTime(endDateTime.toInstant().toEpochMilli()))
                .setTimeZone(endDateTime.zone.id)
            service.events().update("primary", id, event).execute()
            log.updateFaked(status = FakedNetworkActivityStatus.Success)
            SimpleResult.Success
        } catch (e: Exception) {
            log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = e.message ?: "Unknown error updating timed event")
            SimpleResult.Error(e.message ?: "Unknown error updating timed event")
        }
    }

    fun deleteEvent(eventId: String): SimpleResult {
        val log = NetworkActivity.addFakedEntry("Deleting event ID: $eventId", FakedNetworkActivityStatus.InTransit)
        return try {
            val service = createApiService()
            service.events().delete("primary", eventId).execute()
            log.updateFaked(status = FakedNetworkActivityStatus.Success)
            SimpleResult.Success
        } catch (e: Exception) {
            log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = e.message ?: "Unknown error deleting event")
            SimpleResult.Error(e.message ?: "Unknown error deleting event")
        }
    }
}