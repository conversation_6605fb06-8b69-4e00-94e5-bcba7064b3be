package dima.apps.calendar

import dima.color.TailwindCssColors

/**
 * Add color mapping based on Google Calendar's color IDs.
 * Google Calendar default color names:
 * 1: Lavender
 * 2: <PERSON>
 * 3: <PERSON>rape
 * 4: <PERSON>lamingo
 * 5: <PERSON><PERSON>
 * 6: <PERSON><PERSON><PERSON>
 * 7: <PERSON>
 * 8: <PERSON><PERSON><PERSON><PERSON>
 * 9: <PERSON><PERSON>
 * 10: <PERSON>
 * 11: <PERSON><PERSON>
 *
 * Mapped via https://find-nearest-tailwind-colour.netlify.app/
 */
internal val colorMap = mapOf(
    "1" to TailwindCssColors.slate400,   // Lavender
    "2" to TailwindCssColors.green500,  // Sage
    "3" to TailwindCssColors.fuchsia700, // Grape
    "4" to TailwindCssColors.red400,  // Flamingo
    "5" to TailwindCssColors.amber400, // Banana
    "6" to TailwindCssColors.orange600, // Tangerine
    "7" to TailwindCssColors.sky500,    // Peacock
    "8" to TailwindCssColors.zinc600,   // Graphite
    "9" to TailwindCssColors.indigo700,   // Blueberry
    "10" to TailwindCssColors.green700, // <PERSON>
    "11" to TailwindCssColors.red700    // Tomato
)
