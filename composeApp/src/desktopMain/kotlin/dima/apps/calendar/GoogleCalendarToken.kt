package dima.apps.calendar

import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.globalState.GlobalState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File

/**
 * Will either refresh the token or open up the Google login page.
 */
internal fun downloadNewGoogleCalendarToken(scope: CoroutineScope) {
    openConfirmationDialog(
        "Force new Google Calendar token?",
        "This will delete the existing token and request a new one.",
        confirmButtonText = "Force New Token"
    ) {
        scope.launch {
            try {
                File(GoogleCalendarService.TOKENS_DIRECTORY_PATH).deleteRecursively()
                GoogleCalendarService.downloadEventsForYearOrLoadFromDatabase(GlobalState.calendar.selectedDate.year)
                showNotification("Successfully obtained new Google Calendar token")
            } catch (e: Exception) {
                showErrorNotification("Failed to refresh token", e.message)
            }
        }
    }
}
