package dima.apps.calendar

import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.utils.AppKey
import kotlinx.coroutines.launch
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.ChronoUnit

// A fixed Monday in the past to act as a reference for week calculations.
private val REFERENCE_DATE_FOR_SCROLLING = LocalDate.of(1970, 1, 5) // A Monday

@Composable
fun CalendarApp() {
    var events by remember { mutableStateOf<List<CalendarEvent>>(emptyList()) }
    val scope = rememberCoroutineScope()
    val appFocusRequester = remember { FocusRequester() }
    val rightSideFocusRequester = remember { FocusRequester() }

    val initialScrollIndex = ChronoUnit.WEEKS.between(
        REFERENCE_DATE_FOR_SCROLLING,
        GlobalState.calendar.selectedDate.with(DayOfWeek.MONDAY)
    ).toInt()
    val lazyListState = rememberLazyListState(initialFirstVisibleItemIndex = initialScrollIndex)

    var isInitialLoadForYear by remember { mutableStateOf(true) }
    var previousYearForInitialLoad by remember { mutableStateOf<Int?>(null) }


    fun scrollToCurrentDay(animateScroll: Boolean) {
        val selectedDateMonday = GlobalState.calendar.selectedDate.with(DayOfWeek.MONDAY)
        val targetScrollIndex = ChronoUnit.WEEKS.between(REFERENCE_DATE_FOR_SCROLLING, selectedDateMonday).toInt()

        scope.launch {
            if (targetScrollIndex < 0) return@launch // Guard against invalid index

            if (animateScroll) {
                lazyListState.animateScrollToItem(targetScrollIndex, scrollOffset = -500)
            } else {
                lazyListState.scrollToItem(targetScrollIndex, scrollOffset = -500)
            }
        }
    }

    fun tryUpdateDate(newDate: LocalDate, allowYearChange: Boolean = true) {
        val oldSelectedDateYear = GlobalState.calendar.selectedDate.year
        val dateToSet: LocalDate = if (!allowYearChange && newDate.year != oldSelectedDateYear) {
            when {
                newDate.year < oldSelectedDateYear -> LocalDate.of(oldSelectedDateYear, 1, 1)
                newDate.year > oldSelectedDateYear -> LocalDate.of(oldSelectedDateYear, 12, 31)
                else -> newDate
            }
        } else {
            newDate
        }

        GlobalState.calendar.setSelectedDate(dateToSet)

        if (dateToSet.year == oldSelectedDateYear) {
            scrollToCurrentDay(animateScroll = true)
        }
        // If year changes, LaunchedEffect(selectedDate.year) handles event loading & initial scroll for new year
    }

    fun moveToPreviousDay() {
        tryUpdateDate(GlobalState.calendar.selectedDate.minusDays(1), allowYearChange = true)
    }

    fun moveToNextDay() {
        tryUpdateDate(GlobalState.calendar.selectedDate.plusDays(1), allowYearChange = true)
    }

    fun moveUpWeek() {
        tryUpdateDate(GlobalState.calendar.selectedDate.minusWeeks(1), allowYearChange = true)
    }

    fun moveDownWeek() {
        tryUpdateDate(GlobalState.calendar.selectedDate.plusWeeks(1), allowYearChange = true)
    }

    fun moveUpMultipleWeeks() {
        tryUpdateDate(GlobalState.calendar.selectedDate.minusWeeks(6), allowYearChange = true)
    }

    fun moveDownMultipleWeeks() {
        tryUpdateDate(GlobalState.calendar.selectedDate.plusWeeks(6), allowYearChange = true)
    }

    var currentFocusArea by remember { mutableStateOf(CalendarFocusArea.LeftSide) }
    var selectedEventIndexInRightSide by remember { mutableStateOf<Int?>(null) }

    remember {
        appKeys = listOf(
            AppKey(Key.F, "Open Google Calendar in browser") { openGoogleCalendarInBrowser() },
            AppKey(Key.H, "Move cursor left") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveToPreviousDay()
                    }

                    CalendarFocusArea.RightSide -> {
                        // Exit right side but still move the point as usual
                        currentFocusArea = CalendarFocusArea.LeftSide
                        selectedEventIndexInRightSide = null
                        appFocusRequester.requestFocus()
                        moveToPreviousDay()
                    }
                }
            },
            AppKey(Key.DirectionLeft, "Move cursor left") { moveToPreviousDay() },
            AppKey(Key.U, "Jump to date") { openJumpToDateDialog() },
            AppKey(Key.N, "Move cursor right") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveToNextDay()
                    }

                    CalendarFocusArea.RightSide -> {
                        // Exit right side but still move the point as usual
                        currentFocusArea = CalendarFocusArea.LeftSide
                        selectedEventIndexInRightSide = null
                        appFocusRequester.requestFocus()
                        moveToNextDay()
                    }
                }
            },
            AppKey(Key.DirectionRight, "Move cursor right") { moveToNextDay() },
            AppKey(Key.T, "Move cursor down") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveDownWeek()
                    }

                    CalendarFocusArea.RightSide -> {
                        val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                        if (eventsOnSelectedDate.isNotEmpty()) {
                            val currentIdx = selectedEventIndexInRightSide ?: -1
                            selectedEventIndexInRightSide = (currentIdx + 1).coerceAtMost(eventsOnSelectedDate.size - 1)
                        }
                    }
                }
            },
            AppKey(Key.DirectionDown, "Move cursor down") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveDownWeek()
                    }

                    CalendarFocusArea.RightSide -> {
                        val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                        if (eventsOnSelectedDate.isNotEmpty()) {
                            val currentIdx = selectedEventIndexInRightSide ?: -1
                            selectedEventIndexInRightSide = (currentIdx + 1).coerceAtMost(eventsOnSelectedDate.size - 1)
                        }
                    }
                }
            },
            // use onKeyUp to not bleed the key event down to the transient dialog
            AppKey(Key.Enter, "Select right side or create event on day", onKeyUp = {
                if (currentFocusArea == CalendarFocusArea.LeftSide) {
                    val eventsOnSelectedDate =
                        getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                    if (eventsOnSelectedDate.isEmpty()) {
                        selectedEventIndexInRightSide = null
                        openCreateEventDialog(GlobalState.calendar.selectedDate)
                    } else {
                        selectedEventIndexInRightSide = 0
                        currentFocusArea = CalendarFocusArea.RightSide
                        rightSideFocusRequester.requestFocus()
                    }
                } else {
                    currentFocusArea = CalendarFocusArea.LeftSide
                    appFocusRequester.requestFocus()
                }
            }),
            AppKey(Key.J, "Copy current date") {
                openCopyDateDialog(currentFocusArea, events, selectedEventIndexInRightSide)
            },
            AppKey(Key.C, "Move cursor up") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveUpWeek()
                    }

                    CalendarFocusArea.RightSide -> {
                        val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                        if (eventsOnSelectedDate.isNotEmpty()) {
                            val currentIdx = selectedEventIndexInRightSide ?: 0 // Start from top if null
                            selectedEventIndexInRightSide = (currentIdx - 1).coerceAtLeast(0)
                        }
                    }
                }
            },
            AppKey(Key.DirectionUp, "Move cursor week up") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveUpWeek()
                    }

                    CalendarFocusArea.RightSide -> {
                        val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                        if (eventsOnSelectedDate.isNotEmpty()) {
                            val currentIdx = selectedEventIndexInRightSide ?: 0 // Start from top if null
                            selectedEventIndexInRightSide = (currentIdx - 1).coerceAtLeast(0)
                        }
                    }
                }
            },
            AppKey(Key.M, "Move cursor 6 weeks down") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveDownMultipleWeeks()
                    }

                    CalendarFocusArea.RightSide -> {
                        val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                        if (eventsOnSelectedDate.isNotEmpty()) {
                            val currentIdx = selectedEventIndexInRightSide ?: -1
                            selectedEventIndexInRightSide = (currentIdx + 6).coerceAtMost(eventsOnSelectedDate.size - 1)
                        }
                    }
                }
            },
            AppKey(Key.V, "Move cursor 6 weeks up") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        moveUpMultipleWeeks()
                    }

                    CalendarFocusArea.RightSide -> {
                        val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                        if (eventsOnSelectedDate.isNotEmpty()) {
                            val currentIdx = selectedEventIndexInRightSide ?: 0
                            selectedEventIndexInRightSide = (currentIdx - 6).coerceAtLeast(0)
                        }
                    }
                }
            },
            AppKey(Key.Escape, "Exit right side") {
                currentFocusArea = CalendarFocusArea.LeftSide
                selectedEventIndexInRightSide = null
                appFocusRequester.requestFocus()
            },
            AppKey(Key.D, "Jump to today or delete event in right side") {
                when (currentFocusArea) {
                    CalendarFocusArea.LeftSide -> {
                        GlobalState.calendar.setSelectedDate(LocalDate.now())
                    }

                    CalendarFocusArea.RightSide -> {
                        if (selectedEventIndexInRightSide == null) {
                            return@AppKey
                        }
                        deleteEvent(
                            events = events,
                            selectedEventIndexInRightSide = selectedEventIndexInRightSide!!,
                            scope = scope,
                            appFocusRequester = appFocusRequester
                        )
                    }
                }
            },
            AppKey(Key.L, "Download new Google Calendar token/grant") {
                downloadNewGoogleCalendarToken(scope)
            },
            AppKey(Key.O, "Create new event", onKeyUp = { openCreateEventDialog(GlobalState.calendar.selectedDate) }),
            AppKey(Key.E, "Edit selected event") {
                if (currentFocusArea == CalendarFocusArea.RightSide) {
                    val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                    val idx = selectedEventIndexInRightSide
                    if (idx != null && idx in eventsOnSelectedDate.indices) {
                        val ev = eventsOnSelectedDate[idx]
                        when (ev) {
                            is CalendarEvent.AllDay -> openEditEventDialog(
                                id = ev.id,
                                initialTitle = ev.summary,
                                initialDescription = ev.description,
                                isAllDay = true,
                                startDate = ev.startDate,
                                // pass inclusive end for dialog preset
                                endDate = ev.endDate.minusDays(1),
                                startTime = null,
                                endTime = null
                            )
                            is CalendarEvent.WithTime -> openEditEventDialog(
                                id = ev.id,
                                initialTitle = ev.summary,
                                initialDescription = ev.description,
                                isAllDay = false,
                                startDate = ev.startDateTime.toLocalDate(),
                                endDate = ev.endDateTime.toLocalDate(),
                                startTime = ev.startDateTime.toLocalTime(),
                                endTime = ev.endDateTime.toLocalTime()
                            )
                        }
                    }
                }
            },
        )
    }

    CalenderAppUi(
        appFocusRequester = appFocusRequester,
        lazyListState = lazyListState,
        events = events,
        selectedEventIndexInRightSide = selectedEventIndexInRightSide,
        rightSideFocusRequester = rightSideFocusRequester,
        referenceDateForScrolling = REFERENCE_DATE_FOR_SCROLLING,
        currentFocusArea = currentFocusArea
    )

    CalendarAppEffects(
        appFocusRequester = appFocusRequester,
        previousYearForInitialLoad = previousYearForInitialLoad,
        isInitialLoadForYear = isInitialLoadForYear,
        changeEvents = { events = it },
        scrollToCurrentDay = ::scrollToCurrentDay,
        tryUpdateDate = ::tryUpdateDate,
        changePreviousYearForInitialLoad = { previousYearForInitialLoad = it },
        scope = scope,
        changeIsInitialLoadForYear = { isInitialLoadForYear = it },
        events = events,
        currentFocusArea = currentFocusArea,
        changeSelectedEventIndexInRightSide = { selectedEventIndexInRightSide = it }
    )
}
