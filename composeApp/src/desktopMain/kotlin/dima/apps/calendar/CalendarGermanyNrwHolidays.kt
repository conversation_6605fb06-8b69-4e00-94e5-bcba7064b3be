package dima.apps.calendar

import dima.color.TailwindCssColors
import java.time.LocalDate

private fun calculateEasterSunday(year: Int): LocalDate {
    val a = year % 19
    val b = year / 100
    val c = year % 100
    val d = b / 4
    val e = b % 4
    val f = (b + 8) / 25
    val g = (b - f + 1) / 3
    val h = (19 * a + b - d - g + 15) % 30
    val i = c / 4
    val k = c % 4
    val l = (32 + 2 * e + 2 * i - h - k) % 7
    val m = (a + 11 * h + 22 * l) / 451
    val month = (h + l - 7 * m + 114) / 31
    val day = ((h + l - 7 * m + 114) % 31) + 1
    return LocalDate.of(year, month, day)
}

internal fun getNrwHolidays(year: Int): List<CalendarEvent.AllDay> {
    val holidays = mutableListOf<CalendarEvent.AllDay>()
    val holidayColor = TailwindCssColors.green600

    fun addHoliday(date: LocalDate, name: String) {
        holidays.add(
            CalendarEvent.AllDay(
                // Create a synthetic ID for these generated holidays
                id = "nrw-$year-${name.hashCode()}",
                summary = name,
                color = holidayColor,
                startDate = date,
                endDate = date.plusDays(1) // Google Calendar API's end date is exclusive for all-day events
            )
        )
    }

    // Fixed holidays
    addHoliday(LocalDate.of(year, 1, 1), "Neujahr")
    addHoliday(LocalDate.of(year, 5, 1), "Tag der Arbeit")
    addHoliday(LocalDate.of(year, 10, 3), "Tag der Deutschen Einheit")
    addHoliday(LocalDate.of(year, 11, 1), "Allerheiligen")
    addHoliday(LocalDate.of(year, 12, 25), "1. Weihnachtstag")
    addHoliday(LocalDate.of(year, 12, 26), "2. Weihnachtstag")

    // Holidays relative to Easter
    val easterSunday = calculateEasterSunday(year)
    addHoliday(easterSunday.minusDays(2), "Karfreitag")
    addHoliday(easterSunday.plusDays(1), "Ostermontag")
    addHoliday(easterSunday.plusDays(39), "Christi Himmelfahrt")
    addHoliday(easterSunday.plusDays(50), "Pfingstmontag")
    addHoliday(easterSunday.plusDays(60), "Fronleichnam")

    return holidays
}
