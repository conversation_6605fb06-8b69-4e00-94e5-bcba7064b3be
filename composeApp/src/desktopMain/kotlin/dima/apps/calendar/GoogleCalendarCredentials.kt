package dima.apps.calendar

import com.google.api.client.auth.oauth2.Credential
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets
import com.google.api.client.util.store.FileDataStoreFactory
import com.google.api.services.calendar.CalendarScopes
import dima.apps.calendar.GoogleCalendarService.JSON_FACTORY
import dima.apps.calendar.GoogleCalendarService.TOKENS_DIRECTORY_PATH
import dima.settings
import java.io.File
import java.io.InputStreamReader

private val SCOPES = listOf(CalendarScopes.CALENDAR, CalendarScopes.CALENDAR_EVENTS)

internal fun getCredentials(httpTransport: com.google.api.client.http.HttpTransport): Credential {
    val secret = settings.googleCalendar!!.secretCredentialsJson
    val clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, InputStreamReader(secret.byteInputStream()))

    val flow = GoogleAuthorizationCodeFlow.Builder(
        /* transport = */ httpTransport,
        /* jsonFactory = */ JSON_FACTORY,
        /* clientSecrets = */ clientSecrets,
        /* scopes = */ SCOPES
    ).apply {
        setDataStoreFactory(FileDataStoreFactory(File(TOKENS_DIRECTORY_PATH)))
        setAccessType("offline")
    }.build()

    val receiver = LocalServerReceiver.Builder().setPort(8888).build()
    return AuthorizationCodeInstalledApp(flow, receiver).authorize("user")
}