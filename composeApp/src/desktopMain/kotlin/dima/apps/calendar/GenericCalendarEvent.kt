package dima.apps.calendar

import androidx.compose.ui.graphics.Color
import dima.utils.ColorAsLongSerializer
import dima.utils.LocalDateSerializer
import dima.utils.ZonedDateTimeSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.LocalDate
import java.time.ZonedDateTime

/**
 * Sealed to model how the Google calendar API returns events.
 */
@Serializable
internal sealed class CalendarEvent {
    @Serializable
    @SerialName("AllDay")
    data class AllDay(
        val id: String,
        val summary: String,
        @Serializable(with = ColorAsLongSerializer::class)
        val color: Color,
        @Serializable(with = LocalDateSerializer::class)
        val startDate: LocalDate,
        @Serializable(with = LocalDateSerializer::class)
        val endDate: LocalDate,
        val description: String? = null,
    ) : CalendarEvent()

    @Serializable
    @SerialName("WithTime")
    data class WithTime(
        val id: String,
        val summary: String,
        @Serializable(with = ColorAsLongSerializer::class)
        val color: Color,
        @Serializable(with = ZonedDateTimeSerializer::class)
        val startDateTime: ZonedDateTime,
        @Serializable(with = ZonedDateTimeSerializer::class)
        val endDateTime: ZonedDateTime,
        val description: String? = null,
    ) : CalendarEvent()

    /**
     * Assuming that the end date is exclusive for [AllDay].
     * An event from May 10 to May 12 (inclusive of May 12 for display) would have
     * startDate = May 10, endDate = May 13.
     * This method checks if the given 'date' falls within this range.
     */
    internal fun isOnDate(date: LocalDate): Boolean {
        return when (this) {
            is AllDay -> !date.isBefore(this.startDate) && date.isBefore(this.endDate)
            is WithTime -> this.startDateTime.toLocalDate() == date
        }
    }
}

internal fun getEventsForSelectedDate(allEvents: List<CalendarEvent>, date: LocalDate): List<CalendarEvent> {
    return allEvents.filter { event -> event.isOnDate(date) }
}

