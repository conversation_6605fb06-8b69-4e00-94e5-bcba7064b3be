package dima.apps.calendar

import dima.dateTime.DateTimeFormat
import dima.dialogs.completion.openCompletionDialog
import dima.globalState.GlobalState
import dima.os.copyToClipboard

internal fun openCopyDateDialog(
    currentFocusArea: CalendarFocusArea,
    events: List<CalendarEvent>,
    selectedEventIndexInRightSide: Int?
) {
    when (currentFocusArea) {
        CalendarFocusArea.LeftSide -> {
            val shortDateWithDots =
                GlobalState.calendar.selectedDate.format(DateTimeFormat.shortDateWithDots).removePrefix("0")
            val monthName = GlobalState.calendar.selectedDate.format(DateTimeFormat.germanDateWithMonth)
            openCompletionDialog("Copy date", listOf(shortDateWithDots, monthName)) {
                copyToClipboard(it.text)
            }
        }

        CalendarFocusArea.RightSide -> {
            val eventsOnSelectedDate =
                getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
            selectedEventIndexInRightSide?.let { index ->
                if (index >= 0 && index < eventsOnSelectedDate.size) {
                    val eventToCopy = eventsOnSelectedDate[index]
                    copyToClipboard(
                        if (eventToCopy is CalendarEvent.AllDay) {
                            eventToCopy.summary
                        } else {
                            (eventToCopy as CalendarEvent.WithTime).summary
                        }
                    )
                }
            }
        }
    }
}