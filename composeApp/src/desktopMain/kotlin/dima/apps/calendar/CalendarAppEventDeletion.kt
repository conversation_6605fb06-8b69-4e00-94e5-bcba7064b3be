package dima.apps.calendar

import GlobalEvent
import androidx.compose.ui.focus.FocusRequester
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.globalState.GlobalState
import dima.utils.SimpleResult
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal fun deleteEvent(
    events: List<CalendarEvent>,
    selectedEventIndexInRightSide: Int,
    scope: CoroutineScope,
    appFocusRequester: FocusRequester
) {
    val eventsOnSelectedDate = getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
    val eventToDelete = eventsOnSelectedDate.getOrNull(selectedEventIndexInRightSide)
    if (eventToDelete == null) {
        return
    }
    val summary = when (eventToDelete) {
        is CalendarEvent.AllDay -> eventToDelete.summary
        is CalendarEvent.WithTime -> eventToDelete.summary
    }
    openConfirmationDialog(
        title = "Delete event?",
        subTitle = summary,
        confirmButtonText = "Delete"
    ) {
        scope.launch(Dispatchers.IO) {
            val id = when (eventToDelete) {
                is CalendarEvent.AllDay -> eventToDelete.id
                is CalendarEvent.WithTime -> eventToDelete.id
            }
            val result = GoogleCalendarService.deleteEvent(id)
            when (result) {
                is SimpleResult.Success -> {
                    showNotification("Event deleted", summary, durationMillis = 1000)
                    globalEvent = GlobalEvent.Reload
                    appFocusRequester.requestFocus()
                }

                is SimpleResult.Error -> {
                    showErrorNotification("Failed to delete event", result.error)
                }
            }
        }
    }
}
