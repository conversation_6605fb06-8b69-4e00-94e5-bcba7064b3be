package dima.apps.calendar

import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

internal object CalendarDatabase {
    private val cacheDirectory = File("cache/calendar")
    private val json = Json { prettyPrint = true; classDiscriminator = "kind" }
    private val mutex = ReentrantLock()

    private fun fileForYear(year: Int) = File(cacheDirectory, "$year.json")

    fun getEventsForYear(year: Int): List<CalendarEvent> {
        return mutex.withLock {
            val file = fileForYear(year)
            if (!file.exists()) {
                return emptyList()
            }
            file.readText().let { json.decodeFromString<List<CalendarEvent>>(it) }
        }
    }

    fun insertEventsForYear(year: Int, events: List<CalendarEvent>) {
        mutex.withLock {
            if (!cacheDirectory.exists()) {
                cacheDirectory.mkdirs()
            }
            val file = fileForYear(year)
            if (file.exists()) {
                file.delete()
            }
            file.writeText(json.encodeToString(events))
        }
    }
}