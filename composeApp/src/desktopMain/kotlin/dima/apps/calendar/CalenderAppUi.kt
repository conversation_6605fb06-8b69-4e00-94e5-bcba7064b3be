package dima.apps.calendar

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.onPreviewKeyEvent
import dima.apps.calendar.ui.CalendarAppUiHeaderWithMonthAndWeekdays
import dima.apps.calendar.ui.CalendarAppUiLeftSide
import dima.apps.calendar.ui.CalendarAppUiRightSide
import dima.globalState.GlobalState
import dima.utils.DummyFocusable
import dima.utils.handleAppMap
import handleLostFocus
import java.time.LocalDate

@Composable
internal fun CalenderAppUi(
    appFocusRequester: FocusRequester,
    lazyListState: LazyListState,
    events: List<CalendarEvent>,
    selectedEventIndexInRightSide: Int?,
    rightSideFocusRequester: FocusRequester,
    referenceDateForScrolling: LocalDate,
    currentFocusArea: CalendarFocusArea
) {
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .focusable()
                .focusRequester(appFocusRequester)
                .handleLostFocus()
                .onPreviewKeyEvent {
                    return@onPreviewKeyEvent it.handleAppMap()
                }
        ) {
            DummyFocusable()
            CalendarAppUiHeaderWithMonthAndWeekdays(
                selectedDate = GlobalState.calendar.selectedDate,
                isRightSideSelected = currentFocusArea == CalendarFocusArea.RightSide
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    CalendarAppUiLeftSide(
                        lazyListState = lazyListState,
                        referenceDate = referenceDateForScrolling, // Pass the reference date
                        selectedDate = GlobalState.calendar.selectedDate,
                        events = events,
                        isRightSideSelected = currentFocusArea == CalendarFocusArea.RightSide
                    )
                    val eventsForRightSide = remember(GlobalState.calendar.selectedDate, events) {
                        getEventsForSelectedDate(events, GlobalState.calendar.selectedDate)
                    }
                    CalendarAppUiRightSide(
                        selectedDate = GlobalState.calendar.selectedDate,
                        eventsOnSelectedDate = eventsForRightSide,
                        selectedEventIndex = selectedEventIndexInRightSide,
                        rightSideFocusModifier = Modifier
                            .focusRequester(rightSideFocusRequester)
                            .focusable(),
                    )
                }
            }
        }
    }
}

