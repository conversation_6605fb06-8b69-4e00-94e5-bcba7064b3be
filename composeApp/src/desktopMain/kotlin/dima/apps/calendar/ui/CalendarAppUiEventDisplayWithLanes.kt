package dima.apps.calendar.ui

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import dima.apps.calendar.CalendarEvent
import dima.apps.calendar.EventWithLane
import dima.color.TailwindCssColors
import dima.color.toHashedComposeColor
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * Displays events with lane-based positioning to maintain horizontal continuity.
 * This replaces the simple event display with smart lane calculation.
 *
 * IMPORTANT: This properly handles empty lanes by reserving space for them,
 * ensuring visual continuity across days even when some lanes are empty.
 */
@Composable
internal fun CalendarAppUiEventDisplayWithLanes(
    eventsWithLanes: List<EventWithLane>,
    currentDate: LocalDate
) {
    if (eventsWithLanes.isEmpty()) {
        return
    }
    // Group events by lane and sort by lane number
    val eventsByLane = eventsWithLanes.groupBy { it.lane }.toSortedMap()
    val maxLane = eventsByLane.keys.maxOrNull() ?: 0
    // Display events in lane order, including empty lanes to maintain visual positioning
    for (lane in 0..maxLane) {
        val eventsInLane = eventsByLane[lane]
        if (eventsInLane == null) {
            EmptyLaneSpace()
        } else {
            // Render actual events in this lane
            eventsInLane.forEach { eventWithLane ->
                when (val event = eventWithLane.event) {
                    is CalendarEvent.AllDay -> AllDayEventWithLane(event, currentDate)
                    is CalendarEvent.WithTime -> TimedEventWithLane(event)
                }
            }
        }
    }
}

/**
 * Renders empty space for lanes that have no events on this date,
 * but need to maintain their visual position for continuity.
 */
@Composable
private fun EmptyLaneSpace() {
    // Create a spacer that exactly matches the height of an actual event
    // Event height = font size + vertical padding (2dp top + 2dp bottom) + 1dp spacing
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(CalendarAppUiStyling.leftEventFontSize.value.dp + 6.dp) // Match exact event height
    )
}

/**
 * Displays a single all-day event with proper lane-aware styling.
 */
@Composable
private fun AllDayEventWithLane(event: CalendarEvent.AllDay, currentDate: LocalDate) {
    val eventActualEndDate = event.endDate.minusDays(1) // Google's endDate is exclusive

    val weekStartForCurrentDate = currentDate.with(DayOfWeek.MONDAY)
    val weekEndForCurrentDate = currentDate.with(DayOfWeek.SUNDAY)

    // Determine if the event segment starts on this cell or continues from a previous cell in this week
    val isSegmentStartInCell = event.startDate == currentDate ||
            (currentDate == weekStartForCurrentDate && event.startDate.isBefore(currentDate))

    // Determine if the event segment ends on this cell or continues to a next cell in this week
    val isSegmentEndInCell = eventActualEndDate == currentDate ||
            (currentDate == weekEndForCurrentDate && eventActualEndDate.isAfter(currentDate))

    val shape = when {
        isSegmentStartInCell && isSegmentEndInCell -> RoundedCornerShape(4.dp) // Single day in week or full event fits week
        isSegmentStartInCell -> RoundedCornerShape(topStart = 4.dp, bottomStart = 4.dp, topEnd = 0.dp, bottomEnd = 0.dp)
        isSegmentEndInCell -> RoundedCornerShape(topStart = 0.dp, bottomStart = 0.dp, topEnd = 4.dp, bottomEnd = 4.dp)
        else -> RoundedCornerShape(0.dp) // Middle of a multi-day span
    }

    // Show text on the first day the event is visible in the current week.
    val displayStartDateInWeek =
        if (event.startDate.isBefore(weekStartForCurrentDate)) weekStartForCurrentDate else event.startDate
    val showText = currentDate == displayStartDateInWeek

    // Adjust horizontal padding based on whether it's a start, middle, or end segment.
    val outerHorizontalPadding = when {
        isSegmentStartInCell && isSegmentEndInCell -> PaddingValues(horizontal = 4.dp) // Self-contained
        isSegmentStartInCell -> PaddingValues(start = 4.dp, end = 0.dp)
        isSegmentEndInCell -> PaddingValues(start = 0.dp, end = 4.dp)
        else -> PaddingValues(0.dp) // Middle segment, no outer padding
    }
    val textHorizontalPadding = 4.dp // Inner padding for text content

    Text(
        text = if (showText) event.summary else "",
        fontSize = CalendarAppUiStyling.leftEventFontSize,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        color = TailwindCssColors.white,
        modifier = Modifier
            .fillMaxWidth()
            .padding(outerHorizontalPadding)
            .background(event.color, shape = shape)
            .padding(vertical = 2.dp, horizontal = textHorizontalPadding)
    )
}

/**
 * Displays a timed event with lane-aware styling but maintaining timed event visual identity.
 */
@Composable
private fun TimedEventWithLane(event: CalendarEvent.WithTime) {
    // Render timed events with their traditional styling (dot + time + text) but in lane position
    val timeText = event.startDateTime.format(DateTimeFormatter.ofPattern("HH:mm"))

    Row(verticalAlignment = Alignment.CenterVertically) {
        Spacer(
            modifier = Modifier
                .padding(start = 4.dp)
                .background(event.color, CircleShape)
                .size(8.dp)
        )
        Text(
            text = "$timeText ${event.summary}",
            fontSize = CalendarAppUiStyling.leftEventFontSize,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = GlobalStyling.getTextColor(),
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 2.dp, end = 4.dp)
                .padding(2.dp)
        )
    }
}

/**
 * Displays timed events (with specific start/end times) - unchanged from original.
 */
@Composable
internal fun CalendarAppUiTimedEventDisplay(event: CalendarEvent.WithTime) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Spacer(
            modifier = Modifier
                .padding(start = 4.dp)
                .background(event.summary.toHashedComposeColor(), CircleShape)
                .size(8.dp)
        )
        Text(
            text = "${event.startDateTime.format(DateTimeFormatter.ofPattern("HH:mm"))} ${event.summary}",
            fontSize = CalendarAppUiStyling.leftEventFontSize,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = GlobalStyling.getTextColor(),
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 2.dp, end = 4.dp)
                .padding(2.dp)
        )
    }
}