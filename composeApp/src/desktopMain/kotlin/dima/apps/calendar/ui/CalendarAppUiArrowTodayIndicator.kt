package dima.apps.calendar.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardDoubleArrowDown
import androidx.compose.material.icons.filled.KeyboardDoubleArrowUp
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import java.time.LocalDate

@Composable
private fun ArrowIconInstance(
    isUp: <PERSON>olean,
    isSelectedDateInCurrentYear: <PERSON><PERSON><PERSON>,
    isRightSideSelected: Boolean,
) {
    val iconVector = if (isSelectedDateInCurrentYear) {
        if (isUp) Icons.Filled.KeyboardArrowUp else Icons.Filled.KeyboardArrowDown
    } else {
        if (isUp) Icons.Filled.KeyboardDoubleArrowUp else Icons.Filled.KeyboardDoubleArrowDown
    }

    if (GlobalState.isDarkMode) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(36.dp)
                .alpha(if (isRightSideSelected) GlobalStyling.DISABLED_ALPHA else 1f)
                .background(TailwindCssColors.white, CircleShape)
        ) {
            Icon(
                imageVector = iconVector,
                contentDescription = "Scroll indicator",
                tint = TailwindCssColors.black,
                modifier = Modifier.size(28.dp)
            )
        }
    } else {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(36.dp)
                .alpha(if (isRightSideSelected) GlobalStyling.DISABLED_ALPHA else 1f)
                .shadow(
                    2.dp,
                    CircleShape,
                    ambientColor = CalendarAppUiStyling.lightTodayBorderColor,
                    spotColor = CalendarAppUiStyling.lightTodayBorderColor
                )
                .background(CalendarAppUiStyling.lightTodayBorderColor, CircleShape)
        ) {
            Icon(
                imageVector = iconVector,
                contentDescription = "Scroll indicator",
                tint = Color.White,
                modifier = Modifier.size(28.dp)
            )
        }
    }
}

@Composable
internal fun BoxScope.********************************(
    todayArrowDirection: ArrowDirection,
    isRightSideSelected: Boolean
) {
    val isUp = todayArrowDirection == ArrowDirection.UP
    val selectedDateYear = GlobalState.calendar.selectedDate.year
    val currentSystemYear = LocalDate.now().year
    val isSelectedDateInCurrentYear = selectedDateYear == currentSystemYear

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .align(if (isUp) Alignment.TopCenter else Alignment.BottomCenter)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Spacer(Modifier.weight(0.5f))
        ArrowIconInstance(
            isUp = isUp,
            isSelectedDateInCurrentYear = isSelectedDateInCurrentYear,
            isRightSideSelected = isRightSideSelected
        )
        Spacer(Modifier.weight(1f))
        ArrowIconInstance(
            isUp = isUp,
            isSelectedDateInCurrentYear = isSelectedDateInCurrentYear,
            isRightSideSelected = isRightSideSelected
        )
        Spacer(Modifier.weight(0.5f))
    }
}
