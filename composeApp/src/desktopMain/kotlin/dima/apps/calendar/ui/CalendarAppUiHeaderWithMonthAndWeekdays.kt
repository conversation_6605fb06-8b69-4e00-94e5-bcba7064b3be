package dima.apps.calendar.ui

import GlobalStyling
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.settings
import java.time.LocalDate

@Composable
fun CalendarAppUiHeaderWithMonthAndWeekdays(
    selectedDate: LocalDate,
    isRightSideSelected: Boolean = false
) {
    val textColor = GlobalStyling.getTextColor()
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 12.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
        ) {
            Text(
                text = selectedDate.year.toString(),
                textAlign = TextAlign.Center,
                fontSize = 24.sp,
                color = GlobalStyling.getBoldTextColor(),
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.8f)
                    .padding(bottom = 16.dp)
            )
            Text(
                settings.googleCalendar!!.emailAccount,
                textAlign = TextAlign.End,
                color = textColor,
                fontSize = 12.sp,
                modifier = Modifier
                    .padding(end = 8.dp)
                    .weight(0.2f)
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column(
                modifier = Modifier
                    .weight(0.8f)
                    // 40.dp is the size of the week day text + 4.dp horizontal spaced by arrangement
                    .padding(start = 44.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    listOf("Mo", "Di", "Mi", "Do", "Fr", "Sa", "So").forEachIndexed { index, day ->
                        val dayOfWeek = when (index) {
                            0 -> java.time.DayOfWeek.MONDAY
                            1 -> java.time.DayOfWeek.TUESDAY
                            2 -> java.time.DayOfWeek.WEDNESDAY
                            3 -> java.time.DayOfWeek.THURSDAY
                            4 -> java.time.DayOfWeek.FRIDAY
                            5 -> java.time.DayOfWeek.SATURDAY
                            6 -> java.time.DayOfWeek.SUNDAY
                            else -> java.time.DayOfWeek.MONDAY
                        }
                        val isCurrentDayOfWeek = selectedDate.dayOfWeek == dayOfWeek
                        val alpha = if (isRightSideSelected && !isCurrentDayOfWeek) GlobalStyling.DISABLED_ALPHA else 1f
                        
                        Text(
                            text = day,
                            textAlign = TextAlign.Center,
                            color = textColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier
                                .weight(1f)
                                .padding(bottom = 4.dp)
                                .alpha(alpha)
                        )
                    }
                }
            }
            Spacer(
                modifier = Modifier
                    .weight(0.2f)
            )
        }
    }
}
