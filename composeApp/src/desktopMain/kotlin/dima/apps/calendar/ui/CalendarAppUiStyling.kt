package dima.apps.calendar.ui

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors

object CalendarAppUiStyling {
    val leftEventFontSize = 14.sp
    val defaultEventColor: Color = TailwindCssColors.sky600
    val minDayCellHeight = 130.dp
    val monthHeaderBackgroundColor: Color = TailwindCssColors.orange600
    val lightTodayBorderColor = TailwindCssColors.black
    val darkTodayBorderColor = TailwindCssColors.orange400
}