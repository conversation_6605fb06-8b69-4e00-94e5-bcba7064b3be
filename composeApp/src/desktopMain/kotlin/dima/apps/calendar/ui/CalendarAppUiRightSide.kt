package dima.apps.calendar.ui

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.calendar.CalendarEvent
import dima.dateTime.DateTimeFormat
import java.time.LocalDate

@Composable
internal fun RowScope.CalendarAppUiRightSide(
    selectedDate: LocalDate,
    eventsOnSelectedDate: List<CalendarEvent>,
    selectedEventIndex: Int?,
    rightSideFocusModifier: Modifier
) {
    Box(
        modifier = Modifier
            .weight(0.2f)
            .fillMaxHeight()
            .padding(start = 6.dp, end = 2.dp, bottom = 2.dp)
            .padding(8.dp)
            .then(rightSideFocusModifier)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Text(
                text = selectedDate.format(DateTimeFormat.germanDateWithMonth),
                fontWeight = FontWeight.Bold,
                color = GlobalStyling.getBoldTextColor(),
                fontSize = 16.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(bottom = 8.dp)
                    .fillMaxWidth()
            )

            if (eventsOnSelectedDate.isEmpty()) {
                Text(
                    text = "No events",
                    color = GlobalStyling.getRedTextColor(),
                    fontSize = 14.sp
                )
            } else {
                Column(
                    modifier = Modifier.fillMaxSize(), // Allow Column to scroll if needed with LazyColumn
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    eventsOnSelectedDate.forEachIndexed { index, event ->
                        val isSelected = index == selectedEventIndex
                        val backgroundColor = when {
                            event is CalendarEvent.AllDay -> event.color
                            else -> Color.White
                        }
                        val textColor = when {
                            event is CalendarEvent.AllDay -> Color.White
                            else -> GlobalStyling.getTextColor()
                        }
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .then(
                                    if (isSelected) {
                                        Modifier.border(
                                            GlobalStyling.selectedBorderWidth,
                                            GlobalStyling.getSelectedBorderColor(),
                                            RoundedCornerShape(4.dp)
                                        )
                                    } else {
                                        Modifier
                                    }
                                )
                                .padding(8.dp)
                        ) {
                            when (event) {
                                is CalendarEvent.AllDay -> {
                                    Text(
                                        text = event.summary,
                                        fontSize = 14.sp,
                                        color = textColor,
                                        modifier = Modifier
                                            .padding(vertical = 4.dp)
                                            .background(
                                                backgroundColor,
                                                shape = GlobalStyling.smallRoundedCorners
                                            )
                                            .padding(horizontal = 4.dp, vertical = 2.dp)
                                            .fillMaxWidth()
                                    )
                                }

                                is CalendarEvent.WithTime -> {
                                    Text(
                                        text = event.startDateTime.format(DateTimeFormat.hourColonMinute),
                                        fontSize = 14.sp,
                                        color = textColor,
                                    )
                                    Text(
                                        text = event.summary,
                                        fontSize = 14.sp,
                                        color = textColor
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
