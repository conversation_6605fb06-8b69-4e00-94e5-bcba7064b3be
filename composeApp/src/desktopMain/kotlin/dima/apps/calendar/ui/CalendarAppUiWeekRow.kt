package dima.apps.calendar.ui

import GlobalStyling
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dima.apps.calendar.CalendarEvent
import dima.apps.calendar.EventLaneCalculator
import dima.apps.calendar.getSummary
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.WeekFields

@Composable
internal fun CalendarWeekRow(
    absoluteWeekIndex: Int,
    referenceDate: LocalDate,
    modifier: Modifier = Modifier,
    selectedDate: LocalDate,
    events: List<CalendarEvent>,
    isRightSideSelected: Boolean
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        modifier = modifier
            .fillMaxWidth()
            .width(40.dp)
            .height(IntrinsicSize.Min) // Ensure row takes minimum height of its children
    ) {
        val mondayOfThisWeek = referenceDate.plusWeeks(absoluteWeekIndex.toLong()).with(DayOfWeek.MONDAY)
        val weekNumber = mondayOfThisWeek.get(WeekFields.ISO.weekOfWeekBasedYear())

        Column(
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .height(30.dp) // Visually align to the day cell day text
        ) {
            // Check if this week contains the selected date
            val selectedDateWeekNumber = selectedDate.get(WeekFields.ISO.weekOfWeekBasedYear())
            val isCurrentWeek = weekNumber == selectedDateWeekNumber
            val alpha = if (isRightSideSelected && !isCurrentWeek) GlobalStyling.DISABLED_ALPHA else 1f

            Text(
                text = "W$weekNumber",
                fontSize = CalendarAppUiStyling.leftEventFontSize,
                color = GlobalStyling.getTextColor(),
                textAlign = TextAlign.End,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .width(40.dp)
                    .alpha(alpha)
            )
        }

        // Calculate lanes only for events that are visible in this specific week
        val weekStart = mondayOfThisWeek
        val weekEnd = mondayOfThisWeek.plusDays(6)

        val eventsInWeek = events.filter { event ->
            when (event) {
                is CalendarEvent.AllDay -> {
                    // Only include events that have at least one day visible in this week
                    val eventStart = event.startDate
                    val eventEnd = event.endDate.minusDays(1) // endDate is exclusive
                    !(eventEnd.isBefore(weekStart) || eventStart.isAfter(weekEnd))
                }

                is CalendarEvent.WithTime -> {
                    // Include timed events that occur within this week
                    val eventDate = event.startDateTime.toLocalDate()
                    !eventDate.isBefore(weekStart) && !eventDate.isAfter(weekEnd)
                }
            }
        }

        val laneCalculator = EventLaneCalculator()
        val weekLaneAssignments = if (eventsInWeek.isNotEmpty()) {
            // Debug output for week containing Aug 21
            if (mondayOfThisWeek.dayOfMonth <= 21 && mondayOfThisWeek.plusDays(6).dayOfMonth >= 21) {
                println("DEBUG WEEK: Week starting $mondayOfThisWeek")
                println("  All events in week: ${eventsInWeek.map { "${it.getSummary()}: ${it.javaClass.simpleName}" }}")
            }
            val assignments = laneCalculator.calculateLanes(eventsInWeek)

            if (mondayOfThisWeek.dayOfMonth <= 21 && mondayOfThisWeek.plusDays(6).dayOfMonth >= 21) {
                val aug21Events = assignments.getEventsForDate(LocalDate.of(2025, 8, 21))
                println("  Aug 21 lane assignments: ${aug21Events.map { "${it.getSummary()} -> lane ${it.lane} (${it.event.javaClass.simpleName})" }}")
            }
            assignments
        } else null

        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
        ) {
            for (dayOffset in 0..6) {
                val date: LocalDate = mondayOfThisWeek.plusDays(dayOffset.toLong())
                CalendarAppUiDayCellWithLanes(
                    date = date,
                    selectedDate = selectedDate,
                    events = events.filter { it.isOnDate(date) },
                    weekLaneAssignments = weekLaneAssignments,
                    isRightSideSelected = isRightSideSelected
                )
            }
        }
    }
}
