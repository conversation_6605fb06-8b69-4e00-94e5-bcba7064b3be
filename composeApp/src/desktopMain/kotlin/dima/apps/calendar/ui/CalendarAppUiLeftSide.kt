package dima.apps.calendar.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import dima.apps.calendar.CalendarEvent
import java.time.LocalDate

internal enum class ArrowDirection { UP, DOWN }

@Composable
internal fun RowScope.CalendarAppUiLeftSide(
    lazyListState: LazyListState,
    referenceDate: LocalDate,
    selectedDate: LocalDate,
    events: List<CalendarEvent>,
    isRightSideSelected: Boolean
) {
    Box(modifier = Modifier.weight(0.8f)) {
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
        ) {
            items(Int.MAX_VALUE) { absoluteWeekIndex ->
                CalendarWeekRow(
                    absoluteWeekIndex = absoluteWeekIndex,
                    referenceDate = referenceDate,
                    selectedDate = selectedDate,
                    events = events,
                    isRightSideSelected = isRightSideSelected
                )
            }
        }

        // Arrow Indicator Logic
        val today = LocalDate.now()
        val visibleItemsInfo = lazyListState.layoutInfo.visibleItemsInfo
        val isTodayVisible = remember(today, visibleItemsInfo, referenceDate) {
            if (visibleItemsInfo.isEmpty()) false
            else {
                visibleItemsInfo.any { itemInfo ->
                    val weekMonday = referenceDate.plusWeeks(itemInfo.index.toLong())
                    val weekSunday = weekMonday.plusDays(6) // Monday to Sunday
                    !today.isBefore(weekMonday) && !today.isAfter(weekSunday)
                }
            }
        }

        val todayArrowDirection: ArrowDirection? = remember(isTodayVisible, today, visibleItemsInfo, referenceDate) {
            if (isTodayVisible || visibleItemsInfo.isEmpty()) {
                null
            } else {
                val firstVisibleWeekMonday = referenceDate.plusWeeks(visibleItemsInfo.first().index.toLong())
                val lastVisibleItemIndex = visibleItemsInfo.last().index
                val lastVisibleWeekSunday = referenceDate.plusWeeks(lastVisibleItemIndex.toLong()).plusDays(6)

                when {
                    today.isBefore(firstVisibleWeekMonday) -> ArrowDirection.UP
                    today.isAfter(lastVisibleWeekSunday) -> ArrowDirection.DOWN
                    else -> null
                }
            }
        }

        if (todayArrowDirection != null) {
            CalendarAppUiArrowTodayIndicator(
                todayArrowDirection = todayArrowDirection,
                isRightSideSelected = isRightSideSelected
            )
        }
    }
}
