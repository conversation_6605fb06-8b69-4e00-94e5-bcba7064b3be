package dima.apps.calendar.ui

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import dima.apps.calendar.CalendarEvent
import dima.apps.calendar.LaneAssignments
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.TextStyle
import java.util.*

/**
 * Enhanced day cell that uses pre-calculated lane assignments for optimal performance
 * and consistent horizontal continuity across the entire week.
 */
@Composable
internal fun RowScope.CalendarAppUiDayCellWithLanes(
    date: LocalDate,
    selectedDate: LocalDate,
    events: List<CalendarEvent>,
    weekLaneAssignments: LaneAssignments?,
    isRightSideSelected: Boolean
) {
    val isSelected = date == selectedDate
    val isWeekend = date.dayOfWeek == DayOfWeek.SATURDAY || date.dayOfWeek == DayOfWeek.SUNDAY
    val isToday = date == LocalDate.now()

    val todayDayNumberTextColor: Color = GlobalStyling.getTextColor()
    Box(
        modifier = Modifier
            .weight(1f)
            .heightIn(min = CalendarAppUiStyling.minDayCellHeight)
            .fillMaxHeight() // Make cell fill height of the row
            .alpha(if (!isSelected && isRightSideSelected) GlobalStyling.DISABLED_ALPHA else 1f)
            .then(
                if (isToday && isSelected) {
                    val color = if (GlobalState.isDarkMode) {
                        CalendarAppUiStyling.darkTodayBorderColor
                    } else {
                        CalendarAppUiStyling.lightTodayBorderColor
                    }
                    val selectedBackgroundColor = if (isRightSideSelected) {
                        GlobalStyling.getSelectedInactiveBackgroundColor()
                    } else {
                        GlobalStyling.getSelectedBackgroundColor()
                    }
                    Modifier
                        .background(color, GlobalStyling.smallRoundedCorners)
                        .padding(2.dp)
                        .background(selectedBackgroundColor, GlobalStyling.smallRoundedCorners)
                } else if (isToday) {
                    val color = if (GlobalState.isDarkMode) {
                        CalendarAppUiStyling.darkTodayBorderColor
                    } else {
                        CalendarAppUiStyling.lightTodayBorderColor
                    }
                    Modifier.background(color, GlobalStyling.smallRoundedCorners)
                        .padding(2.dp)
                        .background(GlobalStyling.getWindowBackgroundColor(), GlobalStyling.smallRoundedCorners)
                } else if (isSelected) {
                    val selectedBackgroundColor = if (isRightSideSelected) {
                        GlobalStyling.getSelectedInactiveBackgroundColor()
                    } else {
                        GlobalStyling.getSelectedBackgroundColor()
                    }
                    Modifier.background(selectedBackgroundColor)
                } else if (isWeekend) {
                    Modifier.background(
                        if (GlobalState.isDarkMode) TailwindCssColors.neutral700 else TailwindCssColors.gray100,
                    )
                } else {
                    Modifier.background(GlobalStyling.getWindowBackgroundColor())
                }
            )
            .padding(bottom = 4.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
        ) {
            val dayTextModifier = Modifier
            if (date.dayOfMonth == 1) {
                Column(
                    modifier = Modifier
                        // Fixed height for month header text
                        .height(29.dp)
                ) {
                    Text(
                        text = "1. " + date.month.getDisplayName(
                            TextStyle.FULL,
                            Locale.GERMAN
                        ),
                        color = TailwindCssColors.white,
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        fontSize = 15.sp,
                        modifier = dayTextModifier
                            .padding(start = 8.dp, end = 8.dp, top = 6.dp)
                            .background(
                                CalendarAppUiStyling.monthHeaderBackgroundColor,
                                GlobalStyling.smallRoundedCorners
                            )
                            .padding(vertical = 2.dp, horizontal = 4.dp)
                    )
                }
            } else {
                // Box to ensure consistent height for day number (pulsating or static)
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .then(
                            if (isToday) {
                                // important because the today indicator has a border which takes away space
                                Modifier
                            } else {
                                Modifier.padding(top = 2.dp) // Align with month header position
                            }
                        )
                        .height(25.dp) // Fixed height for the day number area, including circle
                ) {
                    Text(
                        text = date.dayOfMonth.toString() + ". " + date.month.getDisplayName(
                            TextStyle.SHORT,
                            Locale.GERMAN
                        ),
                        fontSize = 15.sp,
                        textAlign = TextAlign.Center,
                        color = todayDayNumberTextColor, // Use the dynamically determined text color
                        modifier = if (isToday) {
                            dayTextModifier
                                .zIndex(999f)
                                .padding(3.dp)
                                .fillMaxWidth()
                        } else {
                            dayTextModifier
                                .padding(vertical = 3.dp) // Regular padding for non-today dates
                        }
                    )
                }
            }
            
            // Separate all-day events from timed events
            val allDayEvents = events.filterIsInstance<CalendarEvent.AllDay>()
            val timedEvents = events.filterIsInstance<CalendarEvent.WithTime>()

            // Render all events using pre-calculated lane assignments (both all-day and timed)
            if (weekLaneAssignments == null) {
                // Fallback: render events the old way if no lane assignments
                // Note: This fallback should rarely be used since we now calculate lanes for all events
                allDayEvents.forEach { event ->
                    CalendarAppUiEventDisplay(event, currentDate = date)
                }
                timedEvents.forEach { event ->
                    CalendarAppUiTimedEventDisplay(event)
                }
            } else {
                val eventsWithLanes = weekLaneAssignments.getEventsForDate(date)
                if (eventsWithLanes.isNotEmpty()) {
                    CalendarAppUiEventDisplayWithLanes(eventsWithLanes, currentDate = date)
                }
            }
        }
    }
}