package dima.apps.calendar

import java.time.LocalDate
import java.time.temporal.ChronoUnit

/**
 * Helper function to get the summary from any CalendarEvent type
 */
internal fun CalendarEvent.getSummary(): String = when (this) {
    is CalendarEvent.AllDay -> this.summary
    is CalendarEvent.WithTime -> this.summary
}

/**
 * Helper function to get the summary from EventWithLane
 */
internal fun EventWithLane.getSummary(): String = this.event.getSummary()

/**
 * Represents an event with its assigned lane for display purposes.
 */
internal data class EventWithLane(
    val event: CalendarEvent,
    val lane: Int
)

/**
 * Contains the lane assignments for all events, organized by date.
 */
internal class LaneAssignments {
    private val eventsByDate = mutableMapOf<LocalDate, MutableList<EventWithLane>>()

    fun addEvent(date: LocalDate, eventWithLane: EventWithLane) {
        eventsByDate.getOrPut(date) { mutableListOf() }.add(eventWithLane)
    }

    fun getEventsForDate(date: LocalDate): List<EventWithLane> {
        return eventsByDate[date]?.sortedBy { it.lane } ?: emptyList()
    }

    @Suppress("unused")
    fun getAllDates(): Set<LocalDate> = eventsByDate.keys
}

// Helper for LocalDate.datesUntil (Java 9+)
// If not available, implement manually:
// fun LocalDate.datesUntil(endExclusive: LocalDate): List<LocalDate> {
//     val dates = mutableListOf<LocalDate>()
//     var current = this
//     while (current.isBefore(endExclusive)) {
//         dates.add(current)
//         current = current.plusDays(1)
//     }
//     return dates
// }

// Helper to get event ID consistently
internal val CalendarEvent.id: String
    get() = when (this) {
        is CalendarEvent.AllDay -> this.id
        is CalendarEvent.WithTime -> this.id
    }


/**
 * Calculates optimal lane assignments for calendar events.
 *
 * The algorithm prioritizes:
 * 1. Continuity for multi-day all-day events (they maintain the same lane).
 * 2. All-day events are placed in lower-numbered lanes first.
 * 3. Timed events fill gaps within the lanes used by all-day events, or are placed
 *    in the next available lanes after all-day events.
 */
internal class EventLaneCalculator {
    fun calculateLanes(events: List<CalendarEvent>): LaneAssignments {
        val laneAssignments = LaneAssignments()
        // Tracks the assigned lane for multi-day all-day events to ensure continuity
        val allDayEventLaneContinuity = mutableMapOf<String, Int>()

        if (events.isEmpty()) return laneAssignments

        // Get all unique dates spanned by any event, sorted chronologically
        val allDates = events.flatMap { event ->
            when (event) {
                is CalendarEvent.AllDay -> event.startDate.datesUntil(event.endDate).toList() // Assuming datesUntil is exclusive for endDate
                is CalendarEvent.WithTime -> listOf(event.startDateTime.toLocalDate())
            }
        }.distinct().sorted()

        for (currentDate in allDates) {
            val eventsOnThisDate = events.filter { it.isOnDate(currentDate) }
            val allDayEventsOnThisDate = eventsOnThisDate.filterIsInstance<CalendarEvent.AllDay>()
            val timedEventsOnThisDate = eventsOnThisDate.filterIsInstance<CalendarEvent.WithTime>()

            // Stores lane -> eventId for all-day events active on this date
            val lanesOccupiedByAllDayThisDate = mutableMapOf<Int, String>()
            // Stores all lanes used by any event (all-day or timed) on this date
            val allUsedLanesToday = mutableSetOf<Int>()


            // 1. Place CONTINUING multi-day all-day events
            // Sort all-day events for consistent processing
            val sortedAllDayEvents = allDayEventsOnThisDate
                .sortedWith(compareBy<CalendarEvent.AllDay> { it.startDate } // Start date
                    .thenByDescending { ChronoUnit.DAYS.between(it.startDate, it.endDate) } // Duration (longer first)
                    .thenBy { it.summary }) // Summary for tie-breaking

            for (event in sortedAllDayEvents) {
                if (allDayEventLaneContinuity.containsKey(event.id)) {
                    val lane = allDayEventLaneContinuity[event.id]!!
                    laneAssignments.addEvent(currentDate, EventWithLane(event, lane))
                    lanesOccupiedByAllDayThisDate[lane] = event.id
                    allUsedLanesToday.add(lane)
                }
            }

            // 2. Place NEW or SINGLE-DAY all-day events for this date
            for (event in sortedAllDayEvents) {
                // If not already placed (i.e., it's not a continuing event already handled)
                if (laneAssignments.getEventsForDate(currentDate).none { it.event.id == event.id }) {
                    var lane = 0
                    // Find the first available lane not yet occupied by another all-day event
                    while (lanesOccupiedByAllDayThisDate.containsKey(lane)) {
                        lane++
                    }
                    laneAssignments.addEvent(currentDate, EventWithLane(event, lane))
                    lanesOccupiedByAllDayThisDate[lane] = event.id
                    allUsedLanesToday.add(lane)

                    // If it's a multi-day event, record its lane for continuity
                    if (event.endDate.isAfter(event.startDate.plusDays(1))) {
                        allDayEventLaneContinuity[event.id] = lane
                    }
                }
            }

            // Determine the maximum lane used by any all-day event today.
            // This helps timed events find gaps within the "all-day event zone".
            val maxLaneEffectivelyUsedByAllDay = allUsedLanesToday.maxOrNull() ?: -1

            // 3. Place TIMED events
            val sortedTimedEvents = timedEventsOnThisDate.sortedBy { it.startDateTime }
            for (event in sortedTimedEvents) {
                var assignedLane: Int? = null

                // Attempt to fill gaps within the range of lanes used by all-day events
                for (laneCandidate in 0..maxLaneEffectivelyUsedByAllDay) {
                    if (!allUsedLanesToday.contains(laneCandidate)) {
                        assignedLane = laneCandidate
                        break
                    }
                }

                // If no gap was found within the all-day event lanes,
                // place it in the next available lane after all current events (all-day and timed).
                if (assignedLane == null) {
                    assignedLane = 0
                    while (allUsedLanesToday.contains(assignedLane)) {
                        assignedLane++
                    }
                }

                laneAssignments.addEvent(currentDate, EventWithLane(event, assignedLane))
                allUsedLanesToday.add(assignedLane) // Mark this lane as used for the day
            }

            // 4. Clean up continuity map for all-day events that END today
            // An all-day event from Aug 4 to Aug 5 has endDate Aug 6 (exclusive), so it truly ends on Aug 5.
            val endedAllDayEventIds = allDayEventLaneContinuity.keys.filter { eventId ->
                val adEvent = events.find { it.id == eventId } as? CalendarEvent.AllDay
                adEvent?.endDate?.minusDays(1) == currentDate
            }
            endedAllDayEventIds.forEach { allDayEventLaneContinuity.remove(it) }
        }
        return laneAssignments
    }
}
