package dima.apps.calendar

import GlobalEvent
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dateTime.DateTimeFormat
import dima.dialogs.generic.*
import dima.globalState.GlobalState
import dima.utils.Result
import dima.utils.SimpleResult
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime

private fun GenericDateValidator(content: String): SimpleResult {
    if (content.isBlank()) return SimpleResult.Success // Optional field
    return when (val result =
        parseStringToDate(content, GlobalState.calendar.selectedDate)) { // Use current selected date as reference
        is Result.Success -> SimpleResult.Success
        is Result.Error -> SimpleResult.Error(result.error)
    }
}

internal fun openCreateEventDialog(initialDate: LocalDate) {
    openGenericDialog(
        "Create Calendar Event for ${initialDate.format(DateTimeFormat.germanDateWithMonth)}",
        layout = listOf(
            GenericDialogRow.TextInputSingleLine("Event Name", id = "eventName", isRequired = true),
            GenericDialogRow.TextInputSingleLine("Description", id = "eventDescription"),
            GenericDialogRow.Checkbox("All-day event", id = "isAllDay", isChecked = true),
            GenericDialogRow.TextInputSingleLine(
                "Start Time (e.g., 14 or 14:30)",
                id = "startTime",
                validator = ::GenericDialogTimeValidator,
                isDisabled = { it.isCheckboxTicked("isAllDay") }
            ),
            GenericDialogRow.TextInputSingleLine(
                "End Date (optional, e.g., 2023-12-25 or 25.12)",
                id = "endDate",
                validator = ::GenericDateValidator
            ),
            GenericDialogRow.TextInputSingleLine(
                "End Time (optional, e.g., 16 or 16:00)",
                id = "endTime",
                validator = ::GenericDialogTimeValidator,
                isDisabled = { it.isCheckboxTicked("isAllDay") }
            )
        )
    ) { dialogResult ->
        val name = dialogResult.getTextInputSingleLineContent("eventName")
        val description = dialogResult.getTextInputSingleLineContent("eventDescription").ifBlank { null }
        val isAllDay = dialogResult.isCheckboxTicked("isAllDay")
        val endDateStr = dialogResult.getTextInputSingleLineContent("endDate")

        if (isAllDay) {
            val eventStartDate = initialDate
            var eventEndDate = initialDate // Default to single day event initially

            if (endDateStr.isNotBlank()) {
                when (val parsedEndDateResult = parseStringToDate(endDateStr, initialDate)) {
                    is Result.Success -> {
                        if (parsedEndDateResult.value.isBefore(initialDate)) {
                            showErrorNotification("End date cannot be before start date.")
                            return@openGenericDialog
                        }
                        eventEndDate = parsedEndDateResult.value
                    }

                    is Result.Error -> {
                        showErrorNotification("Invalid end date format: ${parsedEndDateResult.error}")
                        return@openGenericDialog
                    }
                }
            }

            // Call the updated GoogleCalendarService.createAllDayEvent
            // It now handles both single-day and multi-day all-day events correctly.
            // The service method itself will add .plusDays(1) to eventEndDate for the API.
            CoroutineScope(Dispatchers.IO).launch {
                val result = GoogleCalendarService.createAllDayEvent(
                    startDate = eventStartDate,
                    endDate = eventEndDate, // Pass inclusive end date
                    title = name,
                    description = description
                )
                when (result) {
                    is SimpleResult.Success -> {
                        showNotification("Created all-day event", name, durationMillis = 1000)
                        globalEvent = GlobalEvent.Reload
                    }

                    is SimpleResult.Error -> showErrorNotification("Failed to create event", result.error)
                }
            }

        } else { // With Time
            val startTimeStr = dialogResult.getTextInputSingleLineContent("startTime")
            val endTimeStr = dialogResult.getTextInputSingleLineContent("endTime")

            if (startTimeStr.isBlank()) {
                showErrorNotification("Start time is required for timed events.")
                return@openGenericDialog
            }
            val parsedStartTime = parseStringIntoTime(startTimeStr)
            if (parsedStartTime == null) {
                showErrorNotification("Invalid start time format. Use HH or HH:MM.")
                return@openGenericDialog
            }

            val startDateTime = ZonedDateTime.of(
                initialDate,
                LocalTime.of(parsedStartTime.hour, parsedStartTime.minute),
                ZoneId.systemDefault()
            )

            var effectiveEndDate = initialDate
            if (endDateStr.isNotBlank()) {
                when (val parsedEndDateResult = parseStringToDate(endDateStr, initialDate)) {
                    is Result.Success -> effectiveEndDate = parsedEndDateResult.value
                    is Result.Error -> {
                        showErrorNotification("Invalid end date format for timed event: ${parsedEndDateResult.error}")
                        return@openGenericDialog
                    }
                }
            }

            var endDateTime: ZonedDateTime?
            if (endTimeStr.isNotBlank()) {
                val parsedEndTime = parseStringIntoTime(endTimeStr)
                if (parsedEndTime == null) {
                    showErrorNotification("Invalid end time format. Use HH or HH:MM.")
                    return@openGenericDialog
                }
                endDateTime = ZonedDateTime.of(
                    effectiveEndDate,
                    LocalTime.of(parsedEndTime.hour, parsedEndTime.minute),
                    ZoneId.systemDefault()
                )

                if (endDateTime.isBefore(startDateTime)) {
                    showErrorNotification("End date/time cannot be before start date/time.")
                    return@openGenericDialog
                }
            } else {
                // If no end time is provided, default to a 1-hour duration from start time
                // Adjust this default duration as needed
                endDateTime = startDateTime.plusHours(1)
            }

            CoroutineScope(Dispatchers.IO).launch {
                val result = GoogleCalendarService.createTimedEvent(startDateTime, endDateTime, name, description)
                when (result) {
                    is SimpleResult.Success -> {
                        showNotification("Created timed event", name, durationMillis = 1000)
                        globalEvent = GlobalEvent.Reload
                    }

                    is SimpleResult.Error -> showErrorNotification("Failed to create event", result.error)
                }
            }
        }
    }
}

