package dima.apps.calendar

import dima.dateTime.DateTimeFormat
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.utils.Result
import dima.utils.StringResult

fun openJumpToDateDialog() {
    openTextInputDialog(
        "Jump to date",
        showValidationSuccess = true,
        validator = {
            val result = parseStringToDate(it)
            when (result) {
                is Result.Error -> StringResult.Error(result.error)
                is Result.Success -> StringResult.Success(result.value.format(DateTimeFormat.germanDateWithMonth))
            }
        }
    ) {
        val date = parseStringToDate(it) as Result.Success
        GlobalState.calendar = GlobalState.calendar.copy(selectedDate = date.value)
        return@openTextInputDialog TextInputDialogConfirmAction.Close
    }
}