package dima.apps.calendar

import GlobalEvent
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dateTime.DateTimeFormat
import dima.dialogs.generic.*
import dima.utils.Result
import dima.globalState.GlobalState
import dima.utils.SimpleResult
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime

internal fun openEditEventDialog(
    id: String,
    initialTitle: String,
    initialDescription: String?,
    isAllDay: Boolean,
    startDate: LocalDate,
    endDate: LocalDate,
    startTime: LocalTime?,
    endTime: LocalTime?
) {
    val title = "Edit Event • ${startDate.format(DateTimeFormat.germanDateWithMonth)}"
    openGenericDialog(
        title,
        layout = listOf(
            GenericDialogRow.TextInputSingleLine("Event Name", id = "eventName", isRequired = true, content = initialTitle),
            GenericDialogRow.TextInputSingleLine("Description", id = "eventDescription", content = initialDescription ?: ""),
            GenericDialogRow.Checkbox("All-day event", id = "isAllDay", isChecked = isAllDay),
            GenericDialogRow.TextInputSingleLine(
                "Start Time (e.g., 14 or 14:30)",
                id = "startTime",
                validator = ::GenericDialogTimeValidator,
                isDisabled = { it.isCheckboxTicked("isAllDay") },
                content = startTime?.let { String.format("%02d:%02d", it.hour, it.minute) } ?: ""
            ),
            GenericDialogRow.TextInputSingleLine(
                "Start Date (e.g., 2025-08-03, 2023-12-25 or 25.12)",
                id = "startDate",
                validator = { content ->
                    if (content.isBlank()) SimpleResult.Success else when (val res = parseStringToDate(content, GlobalState.calendar.selectedDate)) {
                        is Result.Success -> SimpleResult.Success
                        is Result.Error -> SimpleResult.Error(res.error)
                    }
                },
                content = startDate.format(DateTimeFormat.isoDate)
            ),
            GenericDialogRow.TextInputSingleLine(
                "End Date (optional, e.g., 2025-08-03, 2023-12-25 or 25.12)",
                id = "endDate",
                validator = { content ->
                    if (content.isBlank()) SimpleResult.Success else when (val res = parseStringToDate(content, GlobalState.calendar.selectedDate)) {
                        is Result.Success -> SimpleResult.Success
                        is Result.Error -> SimpleResult.Error(res.error)
                    }
                },
                content = endDate.format(DateTimeFormat.isoDate)
            ),
            GenericDialogRow.TextInputSingleLine(
                "End Time (optional, e.g., 16 or 16:00)",
                id = "endTime",
                validator = ::GenericDialogTimeValidator,
                isDisabled = { it.isCheckboxTicked("isAllDay") },
                content = endTime?.let { String.format("%02d:%02d", it.hour, it.minute) } ?: ""
            )
        )
    ) { dialogResult ->

        val name = dialogResult.getTextInputSingleLineContent("eventName")
        val description = dialogResult.getTextInputSingleLineContent("eventDescription").ifBlank { null }
        val allDay = dialogResult.isCheckboxTicked("isAllDay")
        val startDateStr = dialogResult.getTextInputSingleLineContent("startDate").trim()
        val endDateStr = dialogResult.getTextInputSingleLineContent("endDate").trim()

        if (allDay) {
            val parsedStartDate = if (startDateStr.isBlank()) startDate else when (val res = parseStringToDate(startDateStr, startDate)) {
                is Result.Success -> res.value
                is Result.Error -> {
                    showErrorNotification("Invalid start date format: ${res.error}")
                    return@openGenericDialog
                }
            }
            val parsedEndDate = if (endDateStr.isBlank()) parsedStartDate else when (val res = parseStringToDate(endDateStr, parsedStartDate)) {
                is Result.Success -> res.value
                is Result.Error -> {
                    showErrorNotification("Invalid end date format: ${res.error}")
                    return@openGenericDialog
                }
            }
            CoroutineScope(Dispatchers.IO).launch {
                val result = GoogleCalendarService.updateAllDayEvent(
                    id = id,
                    startDate = parsedStartDate,
                    endDate = parsedEndDate,
                    title = name,
                    description = description
                )
                when (result) {
                    is SimpleResult.Success -> {
                        showNotification("Updated event", name, durationMillis = 1000)
                        globalEvent = GlobalEvent.Reload
                    }
                    is SimpleResult.Error -> showErrorNotification("Failed to update event", result.error)
                }
            }
        } else {
            val startTimeStr = dialogResult.getTextInputSingleLineContent("startTime")
            val endTimeStr = dialogResult.getTextInputSingleLineContent("endTime")

            val startLocalTime: LocalTime? = if (startTimeStr.isNotBlank()) {
                val t = parseStringIntoTime(startTimeStr)
                if (t == null) {
                    showErrorNotification("Invalid start time format. Use HH or HH:MM.")
                    return@openGenericDialog
                }
                LocalTime.of(t.hour, t.minute)
            } else {
                startTime
            }
            if (startLocalTime == null) {
                showErrorNotification("Start time is required for timed events.")
                return@openGenericDialog
            }

            val parsedStartDate = if (startDateStr.isBlank()) startDate else when (val res = parseStringToDate(startDateStr, startDate)) {
                is Result.Success -> res.value
                is Result.Error -> {
                    showErrorNotification("Invalid start date format: ${res.error}")
                    return@openGenericDialog
                }
            }

            val parsedEndDate = if (endDateStr.isBlank()) parsedStartDate else when (val res = parseStringToDate(endDateStr, parsedStartDate)) {
                is Result.Success -> res.value
                is Result.Error -> {
                    showErrorNotification("Invalid end date format: ${res.error}")
                    return@openGenericDialog
                }
            }

            val endLocalTime: LocalTime? = if (endTimeStr.isNotBlank()) {
                val t = parseStringIntoTime(endTimeStr)
                if (t == null) {
                    showErrorNotification("Invalid end time format. Use HH or HH:MM.")
                    return@openGenericDialog
                }
                LocalTime.of(t.hour, t.minute)
            } else {
                endTime
            }

            val startDateTime = ZonedDateTime.of(parsedStartDate, startLocalTime, ZoneId.systemDefault())
            val endDateTime = endLocalTime?.let { ZonedDateTime.of(parsedEndDate, it, ZoneId.systemDefault()) }
                ?: startDateTime.plusHours(1)
            if (endDateTime.isBefore(startDateTime)) {
                showErrorNotification("End date/time cannot be before start date/time.")
                return@openGenericDialog
            }
            CoroutineScope(Dispatchers.IO).launch {
                val result = GoogleCalendarService.updateTimedEvent(
                    id = id,
                    startDateTime = startDateTime,
                    endDateTime = endDateTime,
                    title = name,
                    description = description
                )
                when (result) {
                    is SimpleResult.Success -> {
                        showNotification("Updated event", name, durationMillis = 1000)
                        globalEvent = GlobalEvent.Reload
                    }
                    is SimpleResult.Error -> showErrorNotification("Failed to update event", result.error)
                }
            }
        }
    }
}