package dima.apps.calendar

import dima.apps.notifications.showNotification
import dima.globalState.GlobalState
import dima.os.openUrl
import dima.settings

internal fun openGoogleCalendarInBrowser() {
    val year = GlobalState.calendar.selectedDate.year
    val month = GlobalState.calendar.selectedDate.monthValue
    val url = "https://calendar.google.com/calendar/r/month/$year/$month/1" +
            "?authuser=${settings.googleCalendar!!.emailAccount}"
    showNotification("Opening Google Calendar in browser", url)
    openUrl(url)
}
