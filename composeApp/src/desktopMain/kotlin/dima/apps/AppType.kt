package dima.apps

import androidx.compose.runtime.Composable
import dima.ai.AiChatApp
import dima.ai.singlePromptApp.AiSinglePromptApp
import dima.ai.transcribe.app.TranscribeApp
import dima.apps.amazonVine.AmazonVineApp
import dima.apps.calculator.CalculatorApp
import dima.apps.calendar.CalendarApp
import dima.apps.dired.DiredApp
import dima.apps.dummy.DummyApp
import dima.apps.email.EmailApp
import dima.apps.loader.LoaderApp
import dima.apps.networkActivity.NetworkActivityApp
import dima.apps.notifications.LastNotificationsApp
import dima.apps.tailwindCssColorContrast.TailwindCssColorContrasterApp
import dima.apps.tests.MarkdownTestApp
import dima.apps.tests.ScrollVelocityTestApp
import dima.apps.tests.TreeSitterAnnotatedStringTestApp
import dima.apps.textEditor.TextEditorApp
import dima.apps.todoist.TodoistApp
import dima.apps.veroLog.VeroLogApp
import dima.feeds.FeedsApp
import dima.globalState.EmailAppState
import dima.globalState.PaneState
import dima.globalState.TailwindContrasterAppState
import dima.globalState.TelegramAppState
import dima.process.app.ProcessListApp
import dima.process.app.tester.ProcessTesterApp
import dima.telegram.app.TelegramApp
import dima.treeSitter.app.TreeSitterApp

enum class AppType(val displayName: String) {
    Dummy("Dummy"),
    Loader("Loader"),
    AmazonVine("Amazon Vine"),
    AiSinglePrompt("AI Single Prompt"),
    AiChat("LLM Chatter"),
    Calculator("Calculator"),
    Calendar("Calendar"),
    Feeds("Feeds"),
    Email("Email"),
    Dired("Dired"),
    Todoist("Todoist"),
    TreeSitter("TreeSitter"),
    TextEditor("Text Editor"),
    LastNotifications("Last Notifications"),
    NetworkActivity("Network Activity"),
    TailwindContraster("Tailwind Contraster"),
    Transcribe("Transcribe"),
    ProcessList("Process List"),
    ProcessTester("Process Tester"),
    Telegram("Telegram"),
    VeroLog("Vero Log"),

    // tests
    ScrollVelocityTest("ScrollVelocityTest"),
    TreeSitterAnnotatedStringTest("TreeSitterAnnotatedStringTest"),
    MarkdownTestApp("MarkdownTestApp"),
}

@Composable
internal fun AppPane(paneState: PaneState) {
    val appType = paneState.appType
    when (appType) {
        AppType.TailwindContraster -> TailwindCssColorContrasterApp(
            paneState.getCurrentAppState() as TailwindContrasterAppState,
            paneState
        )

        AppType.Email -> EmailApp(paneState.getCurrentAppState() as EmailAppState, paneState)
        AppType.Telegram -> TelegramApp(paneState.getCurrentAppState() as TelegramAppState, paneState)
        AppType.Dired -> DiredApp(paneState)
        AppType.LastNotifications -> LastNotificationsApp(paneState)
        AppType.AmazonVine -> AmazonVineApp()
        AppType.Dummy -> DummyApp()
        AppType.Loader -> LoaderApp()
        AppType.AiSinglePrompt -> AiSinglePromptApp()
        AppType.AiChat -> AiChatApp()
        AppType.Feeds -> FeedsApp(paneState)
        AppType.Calculator -> CalculatorApp()
        AppType.Calendar -> CalendarApp()
        AppType.Todoist -> TodoistApp()
        AppType.TreeSitter -> TreeSitterApp()
        AppType.TextEditor -> TextEditorApp()
        AppType.NetworkActivity -> NetworkActivityApp()
        AppType.Transcribe -> TranscribeApp()
        AppType.ProcessList -> ProcessListApp()
        AppType.ProcessTester -> ProcessTesterApp()
        AppType.VeroLog -> VeroLogApp()
        AppType.ScrollVelocityTest -> ScrollVelocityTestApp()
        AppType.TreeSitterAnnotatedStringTest -> TreeSitterAnnotatedStringTestApp()
        AppType.MarkdownTestApp -> MarkdownTestApp()
    }
}
