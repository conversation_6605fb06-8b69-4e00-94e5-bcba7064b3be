package dima.apps.todoist

import GlobalEvent
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.notifications.NotificationType
import dima.apps.notifications.dismissNotification
import dima.apps.notifications.showNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dialogs
import dima.dialogs.help.appKeys
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.os.openUrl
import dima.utils.*
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

@Composable
fun TodoistApp() {
    remember {
        TodoistDatabase.setup()
    }
    var entries by remember { mutableStateOf(Todoist.getFromDatabase()) }
    var selected by remember {
        mutableStateOf<Int?>(
            Lists.getInitialSelected(
                entries,
                GlobalState.todoist.selectedId
            ) { it.id } ?: 0)
    }
    val listState = rememberLazyListState()
    val appFocusRequester = remember { FocusRequester() }
    val coroutineScope = rememberCoroutineScope()

    fun getEntriesFromDatabaseAndUpdateSelected() {
        entries = Todoist.getFromDatabase()
        selected = Lists.getSelectedOrNext(
            selected,
            entries,
            GlobalState.todoist.selectedId
        ) { it.id }
    }

    remember {
        appKeys = listOf(
            AppKey(Key.D, "Delete task") {
                if (entries.isNotEmpty()) {
                    val task = entries.getOrNull(selected ?: -1)
                    if (task != null) {
                        openConfirmationDialog("Delete task?", task.name, confirmButtonText = "Delete") {
                            coroutineScope.launch(Dispatchers.Default) {
                                Todoist.deleteTask(task, onDatabaseUpdate = {
                                    getEntriesFromDatabaseAndUpdateSelected()
                                })
                            }
                        }
                    }
                }
            },
            AppKey(Key.N, "Create new task") {
                openTextInputDialog("Create new task in Todoist") {
                    CoroutineScope(Dispatchers.IO).launch {
                        Todoist.createNewTask(it)
                    }
                    return@openTextInputDialog TextInputDialogConfirmAction.Close
                }
            },
            AppKey(Key.E, "Edit/reword/rename task") {
                if (entries.isNotEmpty()) {
                    val task = entries.getOrNull(selected ?: -1)
                    if (task != null) {
                        openTextInputDialog("Edit task", task.name, initialText = task.name, minLines = 2) {
                            coroutineScope.launch(Dispatchers.Default) {
                                Todoist.rewordTask(task, it, onDatabaseUpdate = {
                                    getEntriesFromDatabaseAndUpdateSelected()
                                })
                            }
                            return@openTextInputDialog TextInputDialogConfirmAction.Close
                        }
                    }
                }
            },
            AppKey(Key.Z, "Open selected task in browser") {
                if (entries.isNotEmpty()) {
                    val task = entries.getOrNull(selected ?: -1)
                    if (task != null) {
                        openConfirmationDialog("Remove due date?", task.name, confirmButtonText = "Remove") {
                            coroutineScope.launch(Dispatchers.Default) {
                                Todoist.removeDueDate(task, onDatabaseUpdate = {
                                    getEntriesFromDatabaseAndUpdateSelected()
                                })
                            }
                        }
                    }
                }
            },
            AppKey(Key.U, "Open selected task in browser") {
                if (entries.isNotEmpty()) {
                    val task = entries.getOrNull(selected ?: -1)
                    if (task != null) {
                        openUrl(task.url)
                    }
                }
            },
            AppKey(Key.Enter, "Mark task as completed") {
                if (entries.isNotEmpty()) {
                    val task = entries.getOrNull(selected ?: -1)
                    if (task != null) {
                        openConfirmationDialog("Mark task as completed?", task.name) {
                            coroutineScope.launch(Dispatchers.Default) {
                                runBlocking {
                                    Todoist.markCompleted(task)
                                    entries = Todoist.getFromDatabase()
                                    selected =
                                        Lists.getSelectedOrNext(
                                            selected,
                                            entries,
                                            GlobalState.todoist.selectedId
                                        ) { it.id }
                                }
                            }
                        }
                    }
                }
            },
            AppKey(Key.T, "Go one article down") {
                if (selected != null && selected!! + 1 < entries.size) {
                    selected = selected!! + 1
                }
            },
            AppKey(Key.M, "Go 6 articles down") {
                if (selected == null) {
                    return@AppKey
                }
                val newIndex = selected!! + 6
                selected = newIndex.coerceAtMost(entries.size - 1)
            },
            AppKey(Key.V, "Go 6 articles up") {
                if (selected == null) {
                    return@AppKey
                }
                val newIndex = selected!! - 6
                selected = newIndex.coerceAtLeast(0)
            },
            AppKey(Key.C, "Go one article up") {
                if (selected != null && selected!! >= 1) {
                    selected = selected!! - 1
                }
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .focusable()
            .focusRequester(appFocusRequester)
            .onPreviewKeyEvent {
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        DummyFocusable()
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxSize()
        ) {
            Text(
                "Todoist",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 12.dp)
            )
            Box(
                contentAlignment = Alignment.TopCenter,
                modifier = Modifier.fillMaxSize()
            ) {
                TodoistList(listState, selected, entries)
                VerticalScrollbar(
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = listState),
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(start = 12.dp),
                )
            }
        }
    }

    suspend fun scrollTo(smooth: Boolean) {
        val offset = (-GlobalState.mainWindow.heightInDp / 2).toInt()
        if (smooth) {
            listState.animateScrollToItem(selected!!, scrollOffset = offset)
        } else {
            listState.scrollToItem(selected!!, scrollOffset = offset)
        }
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
        launch(Dispatchers.Default) {
            runBlocking {
                entries = Todoist.downloadToDatabase(showNotifications = true)
            }
        }
    }
    LaunchedEffect(selected) {
        if (selected == null) {
            return@LaunchedEffect
        }
        GlobalState.todoist = if (entries.isNotEmpty() && selected!! >= 0) {
            scrollTo(smooth = true)
            GlobalState.todoist.copy(selectedId = entries.getOrNull(selected!!)?.id)
        } else {
            GlobalState.todoist.copy(selectedId = null)
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                selected = 0
                scrollTo(smooth = true)
            }

            GlobalEvent.ScrollToBottom -> {
                selected = entries.size - 1
                scrollTo(smooth = true)
            }

            GlobalEvent.Reload -> {
                coroutineScope.launch(Dispatchers.Default) {
                    val notification = showNotification("Updating Tasks...", type = NotificationType.Loading)
                    entries = runBlocking {
                        Todoist.downloadToDatabase(showNotifications = true)
                    }
                    selected = Lists.getInitialSelected(entries, GlobalState.todoist.selectedId) { it.id }
                    coroutineScope.launch {
                        selected = Lists.getInitialSelected(entries, GlobalState.todoist.selectedId) { it.id }
                        if (selected == null) {
                            selected = 0
                            scrollTo(smooth = false)
                        } else {
                            scrollTo(smooth = true)
                        }
                        dismissNotification(notification)
                    }
                }
            }

            else -> {}
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
}
