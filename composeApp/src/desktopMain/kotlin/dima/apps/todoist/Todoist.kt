package dima.apps.todoist

import GlobalEvent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.AppType
import dima.apps.networkActivity.deleteLogged
import dima.apps.networkActivity.getLogged
import dima.apps.networkActivity.postLogged
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dateTime.DateTimeUtils
import dima.globalState.GlobalState
import dima.settings
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.setJsonBody
import globalEvent
import io.ktor.client.request.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction

/**
 * https://developer.todoist.com/rest/v2
 */
object Todoist {

    var randomTask by mutableStateOf<String?>(null)
    private val client = createHttpClientWithLocalhostProxy()

    private val json = Json {
        ignoreUnknownKeys = true
    }

    suspend fun createNewTask(title: String) {
        val notification = showLoadingNotification("Creating task via Rest API...", title)
        val response = client.postLogged("https://api.todoist.com/rest/v2/tasks") {
            setJsonBody("content" to title)
            bearerAuth(settings.todoistToken)
        }
        if (response.statusCode != 200) {
            notification.toError("Failed to create new task", response.body)
            return
        }
        notification.toInfo("Created task", durationMillis = 500)
        downloadToDatabase(showNotifications = true)
        if (GlobalState.app == AppType.Todoist) {
            globalEvent = GlobalEvent.Reload
        }
    }

    fun updateRandomTask() {
        val today = DateTimeUtils.nowAsIsoDate()
        transaction(database) {
            val all = TodoistDatabase.Table.selectAll().filter {
                val due = it[TodoistDatabase.Table.due]
                due == null || (today >= due)
            }.map {
                it[TodoistDatabase.Table.name]
            }
            randomTask = all.randomOrNull()
        }
    }

    internal suspend fun deleteTask(task: Task, onDatabaseUpdate: () -> Unit) {
        val notification = showLoadingNotification("Deleting task...", task.name)
        val response = client.deleteLogged("https://api.todoist.com/rest/v2/tasks/${task.id}") {
            bearerAuth(settings.todoistToken)
        }
        if (response.statusCode != 204) {
            notification.toError("Failed to delete task", message = response.body)
            return
        }
        notification.toInfo("Deleted task", durationMillis = 1000L)
        transaction(database) {
            TodoistDatabase.Table.deleteWhere { id eq task.id }
        }
        onDatabaseUpdate()
        updateRandomTask()
    }

    internal suspend fun rewordTask(task: Task, newText: String, onDatabaseUpdate: () -> Unit) {
        val notification = showLoadingNotification("Rewording task", task.name)
        val response = client.postLogged("https://api.todoist.com/rest/v2/tasks/${task.id}") {
            setJsonBody("content" to newText)
            bearerAuth(settings.todoistToken)
        }
        if (response.statusCode != 200) {
            notification.toError("Failed to reword task", message = response.body)
            return
        }
        notification.toInfo("Reworded task", durationMillis = 1000L)
        transaction(database) {
            TodoistDatabase.Table.update({ TodoistDatabase.Table.id eq task.id }) {
                it[name] = newText
            }
        }
        onDatabaseUpdate()
    }

    internal suspend fun removeDueDate(task: Task, onDatabaseUpdate: () -> Unit) {
        val notification = showLoadingNotification("Removing due date", task.name)
        val response = client.postLogged("https://api.todoist.com/rest/v2/tasks/${task.id}") {
            setJsonBody("due_string" to "no due date")
            bearerAuth(settings.todoistToken)
        }
        if (response.statusCode != 200) {
            notification.toError("Failed to remove due date", message = response.body)
            return
        }
        notification.toInfo("Removed due date", durationMillis = 1000L)
        transaction(database) {
            TodoistDatabase.Table.update({ TodoistDatabase.Table.id eq task.id }) {
                it[due] = null
            }
        }
        onDatabaseUpdate()
        updateRandomTask()
    }

    internal suspend fun markCompleted(task: Task) {
        val notification = showLoadingNotification("Completing task", task.name)
        // requires a POST without a body
        val response = client.postLogged("https://api.todoist.com/rest/v2/tasks/${task.id}/close") {
            bearerAuth(settings.todoistToken)
        }
        if (response.statusCode != 204) {
            notification.toError(
                "Failed to mark task as completed",
                response.statusCode.toString() + ": " + response.body
            )
            return
        }
        notification.toInfo("Completed task", durationMillis = 1000L)
        transaction(database) {
            TodoistDatabase.Table.deleteWhere { id eq task.id }
        }
        updateRandomTask()
    }

    internal suspend fun downloadToDatabase(showNotifications: Boolean): List<Task> {
        val response = client.getLogged("https://api.todoist.com/rest/v2/tasks") {
            headers {
                bearerAuth(settings.todoistToken)
            }
        }
        /*
                } catch (e: UnknownHostException) {
                    if (showNotifications) {
                        showErrorNotification("GET api.todoist.com/rest/v2/tasks", "UnknownHostException")
                    }
                    return emptyList()
                }
        */
        if (response.statusCode != 200) {
            if (showNotifications) {
                showErrorNotification(
                    "Failed to GET Todoist tasks",
                    buildList {
                        if (response.statusCode == 0) {
                            add("No internet?")
                        } else {
                            add(response.statusCode.toString())
                        }
                        if (response.body.isNotBlank()) {
                            add(response.body)
                        }
                    }.joinToString(": ")
                )
            }
            return getFromDatabase()
        }
        val tasks = json.decodeFromString<List<Task>>(response.body)
        transaction(database) {
            TodoistDatabase.Table.deleteAll()
            TodoistDatabase.Table.batchInsert(tasks) { task ->
                this[TodoistDatabase.Table.id] = task.id
                this[TodoistDatabase.Table.name] = task.name
                this[TodoistDatabase.Table.description] = task.description
                this[TodoistDatabase.Table.labels] = task.labels.joinToString("\n")
                this[TodoistDatabase.Table.due] = task.due?.let { Json.encodeToString(it) }
                this[TodoistDatabase.Table.url] = task.url
            }
        }
        updateRandomTask()
        return sortTasks(tasks)
    }

    private fun sortTasks(tasks: List<Task>): List<Task> {
        val (noDue, withDue) = tasks.partition {
            it.due == null
        }
        val withDueSorted = withDue.sortedBy {
            it.due!!.date
        }.mapIndexed { index, task ->
            if (index == 0) {
                task.copy(showHeader = true)
            } else {
                task
            }
        }
        return noDue.sortedBy {
            it.name.lowercase()
        } + withDueSorted
    }

    internal fun getFromDatabase(): List<Task> {
        return sortTasks(transaction(database) {
            TodoistDatabase.Table.selectAll().map { row ->
                val due = row[TodoistDatabase.Table.due]
                Task(
                    id = row[TodoistDatabase.Table.id],
                    name = row[TodoistDatabase.Table.name],
                    description = row[TodoistDatabase.Table.description],
                    labels = row[TodoistDatabase.Table.labels].split("\n").filter { it.isNotBlank() },
                    due = if (due == null) null else Json.decodeFromString<Due>(due),
                    url = row[TodoistDatabase.Table.url]
                )
            }
        })
    }

}
