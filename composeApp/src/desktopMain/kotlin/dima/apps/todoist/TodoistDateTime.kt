package dima.apps.todoist

import dima.dateTime.DateTimeFormat
import dima.dateTime.DateTimeUtils
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Period


/**
 * Assume that [Task.due] is not null.
 */
internal fun Task.formatDueHuman(): String {
    val due = this.due!!
    val date: LocalDate = LocalDate.parse(due.date)
    val today = LocalDateTime.now()

    fun formatDate(): String = date.format(DateTimeFormat.germanDateWithMonth)

    return when {
        DateTimeUtils.isSameDay(date, today) -> "heute"
        DateTimeUtils.isSameDay(date, today.toLocalDate().minusDays(1)) -> "gestern"
        DateTimeUtils.isSameDay(date, today.toLocalDate().minusDays(2)) -> "vorgestern"
        else -> handlePeriod(date, today, ::formatDate)
    }
}

private fun handlePeriod(date: LocalDate, today: LocalDateTime, formatDate: () -> String): String {
    val period = Period.between(date, today.toLocalDate())
    return when {
        period.years > 0 -> formatYears(period, formatDate)
        period.months > 0 -> formatMonths(period, formatDate)
        period.days > 0 -> "vor ${period.days} Tagen • ${formatDate()}"
        else -> {
            val futurePeriod = Period.between(today.toLocalDate(), date)
            when {
                futurePeriod.years > 0 -> formatFutureYears(futurePeriod, formatDate)
                futurePeriod.months > 0 -> formatFutureMonths(futurePeriod, formatDate)
                else -> "in ${futurePeriod.days} Tagen • ${formatDate()}"
            }
        }
    }
}

private fun formatYears(period: Period, formatDate: () -> String): String {
    return when (period.years) {
        1 -> "vor 1 Jahr • ${formatDate()}"
        else -> "vor ${period.years} Jahren • ${formatDate()}"
    }
}

private fun formatMonths(period: Period, formatDate: () -> String): String {
    return when (period.months) {
        1 -> "vor 1 Monat • ${formatDate()}"
        else -> "vor ${period.months} Monaten • ${formatDate()}"
    }
}

private fun formatFutureYears(period: Period, formatDate: () -> String): String {
    return when (period.years) {
        1 -> "in 1 Jahr • ${formatDate()}"
        else -> "in ${period.years} Jahren • ${formatDate()}"
    }
}

private fun formatFutureMonths(period: Period, formatDate: () -> String): String {
    return when (period.months) {
        1 -> "in 1 Monat • ${formatDate()}"
        else -> "in ${period.months} Monaten • ${formatDate()}"
    }
}
