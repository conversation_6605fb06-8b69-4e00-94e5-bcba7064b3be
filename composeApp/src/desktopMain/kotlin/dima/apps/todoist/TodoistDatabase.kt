package dima.apps.todoist

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SchemaUtils
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File
import java.util.concurrent.Semaphore

internal var database by mutableStateOf<Database?>(null)

internal object TodoistDatabase {

    private val cacheDirectory = File("cache/todoist")
    private val databaseSetupSemaphore = Semaphore(1)

    internal object Table : org.jetbrains.exposed.sql.Table("Entries") {
        val id = text("id")

        /** The content, the main title. */
        val name = text("name")
        val description = text("description")

        /** Newline separated. */
        val labels = text("labels")

        /** JSON string. */
        val due = text("due").nullable()
        val url = text("url")
    }

    fun setup() {
        databaseSetupSemaphore.acquire()
        if (database != null) {
            databaseSetupSemaphore.release()
            return
        }
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        val dbFile = File(cacheDirectory, "todoist.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
        transaction(database) {
            SchemaUtils.create(Table)
        }
        databaseSetupSemaphore.release()
    }

}
