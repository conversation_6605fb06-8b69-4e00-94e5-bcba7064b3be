package dima.apps.todoist

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
internal fun TodoistList(
    listState: LazyListState,
    selected: Int?,
    entries: List<Task>
) {
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxWidth(0.7f)
            .fillMaxHeight()
            .padding(bottom = 24.dp),
        state = listState,
    ) {
        val selectedId = if (selected == null) "" else entries.getOrNull(selected)?.id
        items(entries, key = { it.id }) {
            Column {
                if (it.showHeader) {
                    Row(
                        modifier = Modifier
                            .padding(top = 36.dp)
                    ) {}
                }
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = if (it.id == selectedId) {
                                GlobalStyling.getSelectedBackgroundColor()
                            } else {
                                GlobalStyling.getWindowBackgroundColor()
                            },
                            shape = GlobalStyling.smallRoundedCorners
                        )
                        .animateItem(fadeInSpec = null, fadeOutSpec = null)
                        .padding(12.dp)
                ) {
                    Text(
                        it.name,
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 18.sp,
                    )
                    if (it.description.isNotBlank()) {
                        Text(
                            it.description,
                            fontSize = 14.sp,
                        )
                    }
                    if (it.due != null) {
                        Row {
                            Text(it.formatDueHuman())
                            if (it.due.isRecurring) {
                                Text("Wiederholt sich", modifier = Modifier.padding(start = 8.dp))
                            }
                        }
                    }
                    if (it.labels.isNotEmpty()) {
                        Row {
                            it.labels.forEach { label ->
                                Text(label)
                            }
                        }
                    }
                }
            }
        }
    }
}
