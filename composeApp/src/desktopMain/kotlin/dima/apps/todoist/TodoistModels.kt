package dima.apps.todoist

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
internal data class Due(
    /** yyyy-MM-dd */
    val date: String,
    /** 28 Okt. */
    val string: String,
    /** de */
    val lang: String,
    @SerialName("is_recurring") val isRecurring: Boolean
)

@Serializable
internal data class Task(
    val id: String,
    @SerialName("content")
    val name: String,
    val url: String,
    val labels: List<String>,
    /** Can be an empty string. */
    val description: String,
    val due: Due?,
    /**
     * This is true for the first task with a set due date.
     * This is initially false and will be set in [Todoist.sortTasks].
     *
     * At first, I did not have this inside data class, but calculated it inside the LazyColumn block which led to
     * incorrect behavior depending on position.
     */
    val showHeader: Boolean = false,
)
