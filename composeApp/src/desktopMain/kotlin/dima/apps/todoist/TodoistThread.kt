package dima.apps.todoist

import kotlinx.coroutines.runBlocking
import kotlin.time.Duration.Companion.minutes

internal class TasksModelineThread : Thread() {
    override fun run() {
        TodoistDatabase.setup()
        Todoist.updateRandomTask()
        val sleepDuration = 15.minutes.inWholeMilliseconds
        while (true) {
            sleep(sleepDuration)
            runBlocking {
                Todoist.downloadToDatabase(showNotifications = false)
            }
        }
    }
}
