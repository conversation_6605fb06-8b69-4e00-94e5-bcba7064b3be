package dima.apps.tailwindCssColorContrast

import androidx.compose.ui.graphics.Color
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow

internal fun sRGBtoLin(channel: Float): Float {
    val c = channel // Already in 0..1 range from Color
    return if (c <= 0.04045f) c / 12.92f else ((c + 0.055f) / 1.055f).pow(2.4f)
}

internal fun getLuminance(color: Color): Float {
    val r = sRGBtoLin(color.red)
    val g = sRGBtoLin(color.green)
    val b = sRGBtoLin(color.blue)
    return 0.2126f * r + 0.7152f * g + 0.0722f * b
}

internal fun getContrast(color1: Color, color2: Color): Float {
    val l1 = getLuminance(color1)
    val l2 = getLuminance(color2)
    return (max(l1, l2) + 0.05f) / (min(l1, l2) + 0.05f)
}

internal fun isWcagConstrastGood(contrastRatio: Float): Boolean {
    return contrastRatio >= 7.0f
}