package dima.apps.tailwindCssColorContrast

import dima.color.TailwindCssColors
import dima.color.toHexRGB

object TailwindPaletteData {
    val tailwindColors: Map<String, Map<String, String>> = mapOf(
        "slate" to mapOf(
            "50" to TailwindCssColors.slate50.toHexRGB(),
            "100" to TailwindCssColors.slate100.toHexRGB(),
            "200" to TailwindCssColors.slate200.toHexRGB(),
            "300" to TailwindCssColors.slate300.toHexRGB(),
            "400" to TailwindCssColors.slate400.toHexRGB(),
            "500" to TailwindCssColors.slate500.toHexRGB(),
            "600" to TailwindCssColors.slate600.toHexRGB(),
            "700" to TailwindCssColors.slate700.toHexRGB(),
            "800" to TailwindCssColors.slate800.toHexRGB(),
            "900" to TailwindCssColors.slate900.toHexRGB(),
            "950" to TailwindCssColors.slate950.toHexRGB()
        ),
        "gray" to mapOf(
            "50" to TailwindCssColors.gray50.toHexRGB(),
            "100" to TailwindCssColors.gray100.toHexRGB(),
            "200" to TailwindCssColors.gray200.toHexRGB(),
            "300" to TailwindCssColors.gray300.toHexRGB(),
            "400" to TailwindCssColors.gray400.toHexRGB(),
            "500" to TailwindCssColors.gray500.toHexRGB(),
            "600" to TailwindCssColors.gray600.toHexRGB(),
            "700" to TailwindCssColors.gray700.toHexRGB(),
            "800" to TailwindCssColors.gray800.toHexRGB(),
            "900" to TailwindCssColors.gray900.toHexRGB(),
            "950" to TailwindCssColors.gray950.toHexRGB()
        ),
        "zinc" to mapOf(
            "50" to TailwindCssColors.zinc50.toHexRGB(),
            "100" to TailwindCssColors.zinc100.toHexRGB(),
            "200" to TailwindCssColors.zinc200.toHexRGB(),
            "300" to TailwindCssColors.zinc300.toHexRGB(),
            "400" to TailwindCssColors.zinc400.toHexRGB(),
            "500" to TailwindCssColors.zinc500.toHexRGB(),
            "600" to TailwindCssColors.zinc600.toHexRGB(),
            "700" to TailwindCssColors.zinc700.toHexRGB(),
            "800" to TailwindCssColors.zinc800.toHexRGB(),
            "900" to TailwindCssColors.zinc900.toHexRGB(),
            "950" to TailwindCssColors.zinc950.toHexRGB()
        ),
        "neutral" to mapOf(
            "50" to TailwindCssColors.neutral50.toHexRGB(),
            "100" to TailwindCssColors.neutral100.toHexRGB(),
            "200" to TailwindCssColors.neutral200.toHexRGB(),
            "300" to TailwindCssColors.neutral300.toHexRGB(),
            "400" to TailwindCssColors.neutral400.toHexRGB(),
            "500" to TailwindCssColors.neutral500.toHexRGB(),
            "600" to TailwindCssColors.neutral600.toHexRGB(),
            "700" to TailwindCssColors.neutral700.toHexRGB(),
            "800" to TailwindCssColors.neutral800.toHexRGB(),
            "900" to TailwindCssColors.neutral900.toHexRGB(),
            "950" to TailwindCssColors.neutral950.toHexRGB()
        ),
        "stone" to mapOf(
            "50" to TailwindCssColors.stone50.toHexRGB(),
            "100" to TailwindCssColors.stone100.toHexRGB(),
            "200" to TailwindCssColors.stone200.toHexRGB(),
            "300" to TailwindCssColors.stone300.toHexRGB(),
            "400" to TailwindCssColors.stone400.toHexRGB(),
            "500" to TailwindCssColors.stone500.toHexRGB(),
            "600" to TailwindCssColors.stone600.toHexRGB(),
            "700" to TailwindCssColors.stone700.toHexRGB(),
            "800" to TailwindCssColors.stone800.toHexRGB(),
            "900" to TailwindCssColors.stone900.toHexRGB(),
            "950" to TailwindCssColors.stone950.toHexRGB()
        ),
        "red" to mapOf(
            "50" to TailwindCssColors.red50.toHexRGB(),
            "100" to TailwindCssColors.red100.toHexRGB(),
            "200" to TailwindCssColors.red200.toHexRGB(),
            "300" to TailwindCssColors.red300.toHexRGB(),
            "400" to TailwindCssColors.red400.toHexRGB(),
            "500" to TailwindCssColors.red500.toHexRGB(),
            "600" to TailwindCssColors.red600.toHexRGB(),
            "700" to TailwindCssColors.red700.toHexRGB(),
            "800" to TailwindCssColors.red800.toHexRGB(),
            "900" to TailwindCssColors.red900.toHexRGB(),
            "950" to TailwindCssColors.red950.toHexRGB()
        ),
        "orange" to mapOf(
            "50" to TailwindCssColors.orange50.toHexRGB(),
            "100" to TailwindCssColors.orange100.toHexRGB(),
            "200" to TailwindCssColors.orange200.toHexRGB(),
            "300" to TailwindCssColors.orange300.toHexRGB(),
            "400" to TailwindCssColors.orange400.toHexRGB(),
            "500" to TailwindCssColors.orange500.toHexRGB(),
            "600" to TailwindCssColors.orange600.toHexRGB(),
            "700" to TailwindCssColors.orange700.toHexRGB(),
            "800" to TailwindCssColors.orange800.toHexRGB(),
            "900" to TailwindCssColors.orange900.toHexRGB(),
            "950" to TailwindCssColors.orange950.toHexRGB()
        ),
        "amber" to mapOf(
            "50" to TailwindCssColors.amber50.toHexRGB(),
            "100" to TailwindCssColors.amber100.toHexRGB(),
            "200" to TailwindCssColors.amber200.toHexRGB(),
            "300" to TailwindCssColors.amber300.toHexRGB(),
            "400" to TailwindCssColors.amber400.toHexRGB(),
            "500" to TailwindCssColors.amber500.toHexRGB(),
            "600" to TailwindCssColors.amber600.toHexRGB(),
            "700" to TailwindCssColors.amber700.toHexRGB(),
            "800" to TailwindCssColors.amber800.toHexRGB(),
            "900" to TailwindCssColors.amber900.toHexRGB(),
            "950" to TailwindCssColors.amber950.toHexRGB()
        ),
        "yellow" to mapOf(
            "50" to TailwindCssColors.yellow50.toHexRGB(),
            "100" to TailwindCssColors.yellow100.toHexRGB(),
            "200" to TailwindCssColors.yellow200.toHexRGB(),
            "300" to TailwindCssColors.yellow300.toHexRGB(),
            "400" to TailwindCssColors.yellow400.toHexRGB(),
            "500" to TailwindCssColors.yellow500.toHexRGB(),
            "600" to TailwindCssColors.yellow600.toHexRGB(),
            "700" to TailwindCssColors.yellow700.toHexRGB(),
            "800" to TailwindCssColors.yellow800.toHexRGB(),
            "900" to TailwindCssColors.yellow900.toHexRGB(),
            "950" to TailwindCssColors.yellow950.toHexRGB()
        ),
        "lime" to mapOf(
            "50" to TailwindCssColors.lime50.toHexRGB(),
            "100" to TailwindCssColors.lime100.toHexRGB(),
            "200" to TailwindCssColors.lime200.toHexRGB(),
            "300" to TailwindCssColors.lime300.toHexRGB(),
            "400" to TailwindCssColors.lime400.toHexRGB(),
            "500" to TailwindCssColors.lime500.toHexRGB(),
            "600" to TailwindCssColors.lime600.toHexRGB(),
            "700" to TailwindCssColors.lime700.toHexRGB(),
            "800" to TailwindCssColors.lime800.toHexRGB(),
            "900" to TailwindCssColors.lime900.toHexRGB(),
            "950" to TailwindCssColors.lime950.toHexRGB()
        ),
        "green" to mapOf(
            "50" to TailwindCssColors.green50.toHexRGB(),
            "100" to TailwindCssColors.green100.toHexRGB(),
            "200" to TailwindCssColors.green200.toHexRGB(),
            "300" to TailwindCssColors.green300.toHexRGB(),
            "400" to TailwindCssColors.green400.toHexRGB(),
            "500" to TailwindCssColors.green500.toHexRGB(),
            "600" to TailwindCssColors.green600.toHexRGB(),
            "700" to TailwindCssColors.green700.toHexRGB(),
            "800" to TailwindCssColors.green800.toHexRGB(),
            "900" to TailwindCssColors.green900.toHexRGB(),
            "950" to TailwindCssColors.green950.toHexRGB()
        ),
        "emerald" to mapOf(
            "50" to TailwindCssColors.emerald50.toHexRGB(),
            "100" to TailwindCssColors.emerald100.toHexRGB(),
            "200" to TailwindCssColors.emerald200.toHexRGB(),
            "300" to TailwindCssColors.emerald300.toHexRGB(),
            "400" to TailwindCssColors.emerald400.toHexRGB(),
            "500" to TailwindCssColors.emerald500.toHexRGB(),
            "600" to TailwindCssColors.emerald600.toHexRGB(),
            "700" to TailwindCssColors.emerald700.toHexRGB(),
            "800" to TailwindCssColors.emerald800.toHexRGB(),
            "900" to TailwindCssColors.emerald900.toHexRGB(),
            "950" to TailwindCssColors.emerald950.toHexRGB()
        ),
        "teal" to mapOf(
            "50" to TailwindCssColors.teal50.toHexRGB(),
            "100" to TailwindCssColors.teal100.toHexRGB(),
            "200" to TailwindCssColors.teal200.toHexRGB(),
            "300" to TailwindCssColors.teal300.toHexRGB(),
            "400" to TailwindCssColors.teal400.toHexRGB(),
            "500" to TailwindCssColors.teal500.toHexRGB(),
            "600" to TailwindCssColors.teal600.toHexRGB(),
            "700" to TailwindCssColors.teal700.toHexRGB(),
            "800" to TailwindCssColors.teal800.toHexRGB(),
            "900" to TailwindCssColors.teal900.toHexRGB(),
            "950" to TailwindCssColors.teal950.toHexRGB()
        ),
        "cyan" to mapOf(
            "50" to TailwindCssColors.cyan50.toHexRGB(),
            "100" to TailwindCssColors.cyan100.toHexRGB(),
            "200" to TailwindCssColors.cyan200.toHexRGB(),
            "300" to TailwindCssColors.cyan300.toHexRGB(),
            "400" to TailwindCssColors.cyan400.toHexRGB(),
            "500" to TailwindCssColors.cyan500.toHexRGB(),
            "600" to TailwindCssColors.cyan600.toHexRGB(),
            "700" to TailwindCssColors.cyan700.toHexRGB(),
            "800" to TailwindCssColors.cyan800.toHexRGB(),
            "900" to TailwindCssColors.cyan900.toHexRGB(),
            "950" to TailwindCssColors.cyan950.toHexRGB()
        ),
        "sky" to mapOf(
            "50" to TailwindCssColors.sky50.toHexRGB(),
            "100" to TailwindCssColors.sky100.toHexRGB(),
            "200" to TailwindCssColors.sky200.toHexRGB(),
            "300" to TailwindCssColors.sky300.toHexRGB(),
            "400" to TailwindCssColors.sky400.toHexRGB(),
            "500" to TailwindCssColors.sky500.toHexRGB(),
            "600" to TailwindCssColors.sky600.toHexRGB(),
            "700" to TailwindCssColors.sky700.toHexRGB(),
            "800" to TailwindCssColors.sky800.toHexRGB(),
            "900" to TailwindCssColors.sky900.toHexRGB(),
            "950" to TailwindCssColors.sky950.toHexRGB()
        ),
        "blue" to mapOf(
            "50" to TailwindCssColors.blue50.toHexRGB(),
            "100" to TailwindCssColors.blue100.toHexRGB(),
            "200" to TailwindCssColors.blue200.toHexRGB(),
            "300" to TailwindCssColors.blue300.toHexRGB(),
            "400" to TailwindCssColors.blue400.toHexRGB(),
            "500" to TailwindCssColors.blue500.toHexRGB(),
            "600" to TailwindCssColors.blue600.toHexRGB(),
            "700" to TailwindCssColors.blue700.toHexRGB(),
            "800" to TailwindCssColors.blue800.toHexRGB(),
            "900" to TailwindCssColors.blue900.toHexRGB(),
            "950" to TailwindCssColors.blue950.toHexRGB()
        ),
        "indigo" to mapOf(
            "50" to TailwindCssColors.indigo50.toHexRGB(),
            "100" to TailwindCssColors.indigo100.toHexRGB(),
            "200" to TailwindCssColors.indigo200.toHexRGB(),
            "300" to TailwindCssColors.indigo300.toHexRGB(),
            "400" to TailwindCssColors.indigo400.toHexRGB(),
            "500" to TailwindCssColors.indigo500.toHexRGB(),
            "600" to TailwindCssColors.indigo600.toHexRGB(),
            "700" to TailwindCssColors.indigo700.toHexRGB(),
            "800" to TailwindCssColors.indigo800.toHexRGB(),
            "900" to TailwindCssColors.indigo900.toHexRGB(),
            "950" to TailwindCssColors.indigo950.toHexRGB()
        ),
        "violet" to mapOf(
            "50" to TailwindCssColors.violet50.toHexRGB(),
            "100" to TailwindCssColors.violet100.toHexRGB(),
            "200" to TailwindCssColors.violet200.toHexRGB(),
            "300" to TailwindCssColors.violet300.toHexRGB(),
            "400" to TailwindCssColors.violet400.toHexRGB(),
            "500" to TailwindCssColors.violet500.toHexRGB(),
            "600" to TailwindCssColors.violet600.toHexRGB(),
            "700" to TailwindCssColors.violet700.toHexRGB(),
            "800" to TailwindCssColors.violet800.toHexRGB(),
            "900" to TailwindCssColors.violet900.toHexRGB(),
            "950" to TailwindCssColors.violet950.toHexRGB()
        ),
        "purple" to mapOf(
            "50" to TailwindCssColors.purple50.toHexRGB(),
            "100" to TailwindCssColors.purple100.toHexRGB(),
            "200" to TailwindCssColors.purple200.toHexRGB(),
            "300" to TailwindCssColors.purple300.toHexRGB(),
            "400" to TailwindCssColors.purple400.toHexRGB(),
            "500" to TailwindCssColors.purple500.toHexRGB(),
            "600" to TailwindCssColors.purple600.toHexRGB(),
            "700" to TailwindCssColors.purple700.toHexRGB(),
            "800" to TailwindCssColors.purple800.toHexRGB(),
            "900" to TailwindCssColors.purple900.toHexRGB(),
            "950" to TailwindCssColors.purple950.toHexRGB()
        ),
        "fuchsia" to mapOf(
            "50" to TailwindCssColors.fuchsia50.toHexRGB(),
            "100" to TailwindCssColors.fuchsia100.toHexRGB(),
            "200" to TailwindCssColors.fuchsia200.toHexRGB(),
            "300" to TailwindCssColors.fuchsia300.toHexRGB(),
            "400" to TailwindCssColors.fuchsia400.toHexRGB(),
            "500" to TailwindCssColors.fuchsia500.toHexRGB(),
            "600" to TailwindCssColors.fuchsia600.toHexRGB(),
            "700" to TailwindCssColors.fuchsia700.toHexRGB(),
            "800" to TailwindCssColors.fuchsia800.toHexRGB(),
            "900" to TailwindCssColors.fuchsia900.toHexRGB(),
            "950" to TailwindCssColors.fuchsia950.toHexRGB()
        ),
        "pink" to mapOf(
            "50" to TailwindCssColors.pink50.toHexRGB(),
            "100" to TailwindCssColors.pink100.toHexRGB(),
            "200" to TailwindCssColors.pink200.toHexRGB(),
            "300" to TailwindCssColors.pink300.toHexRGB(),
            "400" to TailwindCssColors.pink400.toHexRGB(),
            "500" to TailwindCssColors.pink500.toHexRGB(),
            "600" to TailwindCssColors.pink600.toHexRGB(),
            "700" to TailwindCssColors.pink700.toHexRGB(),
            "800" to TailwindCssColors.pink800.toHexRGB(),
            "900" to TailwindCssColors.pink900.toHexRGB(),
            "950" to TailwindCssColors.pink950.toHexRGB()
        ),
        "rose" to mapOf(
            "50" to TailwindCssColors.rose50.toHexRGB(),
            "100" to TailwindCssColors.rose100.toHexRGB(),
            "200" to TailwindCssColors.rose200.toHexRGB(),
            "300" to TailwindCssColors.rose300.toHexRGB(),
            "400" to TailwindCssColors.rose400.toHexRGB(),
            "500" to TailwindCssColors.rose500.toHexRGB(),
            "600" to TailwindCssColors.rose600.toHexRGB(),
            "700" to TailwindCssColors.rose700.toHexRGB(),
            "800" to TailwindCssColors.rose800.toHexRGB(),
            "900" to TailwindCssColors.rose900.toHexRGB(),
            "950" to TailwindCssColors.rose950.toHexRGB()
        ),
        // "black" and "white" are special cases, typically handled outside the main palette map in Tailwind.
        // But the HTML example implies they might be in the select dropdown, derived from `tailwindColors`.
        // I'll add them directly to the dropdown population logic.
    )
}