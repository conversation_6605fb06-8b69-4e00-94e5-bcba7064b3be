package dima.apps.tailwindCssColorContrast

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import dima.color.toHexRGB

@Composable
internal fun HeaderControls(
    currentBg: Color,
    activeUiTheme: ContrasterUiTheme,
    availableBackgroundColors: List<Pair<String, String>>
) {
    val selectedBgDisplayName = remember(currentBg, availableBackgroundColors) {
        availableBackgroundColors.find { it.second == currentBg.toHexRGB() }?.first ?: currentBg.toHexRGB()
    }
    Text(
        "Tailwind CSS color contrasts for $selectedBgDisplayName",
        color = activeUiTheme.bodyTextColor,
        fontSize = 18.sp,
        textAlign = TextAlign.Center,
        modifier = Modifier.fillMaxWidth()
    )
}