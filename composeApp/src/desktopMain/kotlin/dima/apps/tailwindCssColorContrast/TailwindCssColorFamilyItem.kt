package dima.apps.tailwindCssColorContrast

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import dima.color.parseToColor

@Composable
internal fun TailwindCssColorFamilyItem(
    familyName: String,
    shades: Map<String, String>,
    currentBg: Color
) {
    val visibleShades = remember(shades, currentBg) {
        shades.filter { (_, fgColorHex) ->
            val fgColor = fgColorHex.parseToColor().expect()
            val contrastRatio = getContrast(fgColor, currentBg)
            contrastRatio >= 4.5f
        }
    }

    if (visibleShades.isEmpty()) {
        return
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(9),
        verticalArrangement = Arrangement.spacedBy(4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        modifier = Modifier
            .fillMaxWidth()
            // set max height not crash due to scroll bar
            .heightIn(max = 500.dp),
    ) {
        items(visibleShades.entries.toList().sortedBy { it.key.toIntOrNull() ?: 1000 }) { (shadeName, fgColorHex) ->
            TailwindCssColorShadeCard(
                shadeName = shadeName,
                familyName = familyName,
                foregroundColor = fgColorHex.parseToColor().expect(),
                backgroundColor = currentBg
            )
        }
    }
}
