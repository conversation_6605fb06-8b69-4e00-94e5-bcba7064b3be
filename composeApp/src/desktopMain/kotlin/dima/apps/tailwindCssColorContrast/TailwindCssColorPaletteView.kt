package dima.apps.tailwindCssColorContrast

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import dima.color.TailwindCssColors
import dima.color.toHexRGB

@Composable
internal fun TailwindCssColorPaletteView(
    currentBg: Color,
    activeUiTheme: ContrasterUiTheme,
    scrollState: ScrollState
) {
    Box {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {
            TailwindPaletteData.tailwindColors.entries.forEach { (familyName, shades) ->
                TailwindCssColorFamilyItem(
                    familyName = familyName,
                    shades = shades,
                    currentBg = currentBg
                )
            }
            TailwindCssColorFamilyItem(
                familyName = "black",
                shades = mapOf("" to TailwindCssColors.black.toHexRGB()),
                currentBg = currentBg
            )
            TailwindCssColorFamilyItem(
                familyName = "white",
                shades = mapOf("" to TailwindCssColors.white.toHexRGB()),
                currentBg = currentBg
            )
        }
        VerticalScrollbar(
            adapter = rememberScrollbarAdapter(scrollState),
            style = LocalScrollbarStyle.current.copy(
                unhoverColor = activeUiTheme.scrollbarThumbColor,
                hoverColor = activeUiTheme.scrollbarThumbHoverColor
            ),
            modifier = Modifier.align(Alignment.CenterEnd).fillMaxHeight(),
        )
    }
}
