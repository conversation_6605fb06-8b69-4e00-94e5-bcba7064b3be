package dima.apps.tailwindCssColorContrast

import androidx.compose.ui.graphics.Color
import dima.color.TailwindCssColors

internal data class ContrasterUiTheme(
    val bodyTextColor: Color,
    val selectBackgroundColor: Color,
    val selectBorderColor: Color,
    val selectTextColor: Color,
    val selectFocusRingColor: Color,
    val checkboxBorderColor: Color,
    val checkboxFocusRingColor: Color,
    val groupTitleBorderColor: Color,
    val scrollbarThumbColor: Color,
    val scrollbarThumbHoverColor: Color,
    val headerActualBackgroundColor: Color
)

internal val UI_THEME_LIGHT_ON_DARK = ContrasterUiTheme(
    bodyTextColor = TailwindCssColors.slate200,
    selectBackgroundColor = TailwindCssColors.slate700,
    selectBorderColor = TailwindCssColors.slate500,
    selectTextColor = TailwindCssColors.slate100,
    selectFocusRingColor = TailwindCssColors.indigo400,
    checkboxBorderColor = TailwindCssColors.slate500,
    checkboxFocusRingColor = TailwindCssColors.indigo400,
    groupTitleBorderColor = TailwindCssColors.slate600.copy(alpha = 0.5f),
    scrollbarThumbColor = Color(0x4DFFFFFF), // rgba(200,200,200,0.3) approx
    scrollbarThumbHoverColor = Color(0x66FFFFFF), // rgba(200,200,200,0.4) approx
    headerActualBackgroundColor = TailwindCssColors.slate800 // Example, will be dynamic
)

internal val UI_THEME_DARK_ON_LIGHT = ContrasterUiTheme(
    bodyTextColor = TailwindCssColors.slate800,
    selectBackgroundColor = TailwindCssColors.slate50,
    selectBorderColor = TailwindCssColors.slate300,
    selectTextColor = TailwindCssColors.slate900,
    selectFocusRingColor = TailwindCssColors.indigo600,
    checkboxBorderColor = TailwindCssColors.slate400,
    checkboxFocusRingColor = TailwindCssColors.indigo600,
    groupTitleBorderColor = TailwindCssColors.slate300.copy(alpha = 0.5f),
    scrollbarThumbColor = Color(0x4D000000), // rgba(0,0,0,0.3) approx
    scrollbarThumbHoverColor = Color(0x66000000), // rgba(0,0,0,0.4) approx
    headerActualBackgroundColor = TailwindCssColors.white // Example, will be dynamic
)
