package dima.apps.tailwindCssColorContrast

import GlobalStyling
import RestoreAppFocus
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.unit.dp
import dima.apps.notifications.showErrorNotification
import dima.color.TailwindCssColors
import dima.color.parseToColor
import dima.color.toHexRGB
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.events.collectEvent
import dima.globalState.PaneState
import dima.globalState.TailwindContrasterAppState
import dima.globalState.updatePaneState
import dima.utils.*
import handleLostFocus

private fun getStateBackgroundColor(state: TailwindContrasterAppState): Color {
    val result = state.selectedBgHex.parseToColor()
    return when (result) {
        is Result.Error -> {
            showErrorNotification("Failed to parse stored background color", result.error)
            TailwindCssColors.white
        }

        is Result.Success -> result.value
    }
}

@Composable
fun TailwindCssColorContrasterApp(state: TailwindContrasterAppState, paneState: PaneState) {
    // Derive current background color from state; source of truth is the AppState
    val currentBg: Color = remember(state.selectedBgHex) { getStateBackgroundColor(state) }
    val isDarkBackground = remember(currentBg) { getLuminance(currentBg) < 0.5 }
    val appFocusRequester = remember { FocusRequester() }
    val activeUiTheme = remember(isDarkBackground) {
        if (isDarkBackground) {
            UI_THEME_LIGHT_ON_DARK
        } else {
            UI_THEME_DARK_ON_LIGHT
        }
    }

    val scrollState = rememberScrollState()
    val scrollVelocity = remember { ScrollVelocity(scrollState) }

    val backgroundColorsForDialog = remember {
        val whiteHex = TailwindCssColors.white.toHexRGB()
        val blackHex = TailwindCssColors.black.toHexRGB()
        (listOf("White" to whiteHex, "Black" to blackHex) + TailwindPaletteData.tailwindColors
            .flatMap { (familyName, shades) ->
                shades.map { (shadeName, hex) ->
                    val displayName =
                        when {
                            familyName in listOf("black", "white") -> familyName.capitalizeWords()
                            else -> "${familyName.capitalizeWords()} $shadeName"
                        }
                    displayName to hex
                }
            }).sortedBy { it.first }
    }

    val localAppKeys = remember {
        listOf(
            AppKey(
                Key.C, "Scroll up",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollUpKeyPressed() }
            ),
            AppKey(
                Key.T, "Scroll down",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollDownKeyPressed() }
            ),
            AppKey(
                Key.M, "Scroll more down",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollDownMoreKeyPressed() }
            ),
            AppKey(
                Key.V, "Scroll more up",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollUpMoreKeyPressed() }
            ),
            AppKey(Key.G, "Toggle Light/Dark Window Background") {
                val lightHex = GlobalStyling.Light.windowBackgroundColor.toHexRGB()
                val darkHex = GlobalStyling.Dark.windowBackgroundColor.toHexRGB()
                val currHex = state.selectedBgHex
                val newHex = if (currHex.equals(lightHex, ignoreCase = true)) darkHex else lightHex
                showErrorNotification("[DEBUG] Key.G pressed", "curr=$currHex light=$lightHex dark=$darkHex -> new=$newHex")
                updatePaneState(paneState.id, state.copy(selectedBgHex = newHex))
            },
            AppKey(Key.U, "Change background color") {
                val candidates = backgroundColorsForDialog.map { (displayName, hex) ->
                    CompletionDialogCandidate(
                        text = displayName,
                        prefixView = {
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .background(hex.parseToColor().expect())
                                    .border(1.dp, activeUiTheme.bodyTextColor.copy(alpha = 0.5f))
                            )
                        }
                    )
                }
                openCompletionDialog(
                    title = "Select Background Color",
                    candidates = candidates,
                    maxLinesPerCandidate = 1 // Keep it compact
                ) { result ->
                    val selectedPair = backgroundColorsForDialog.find { it.first == result.text }
                    selectedPair?.let { (_, hex) ->
                        val parseResult = hex.parseToColor()
                        when (parseResult) {
                            is Result.Success -> updatePaneState(paneState.id, state.copy(selectedBgHex = parseResult.value.toHexRGB()))
                            is Result.Error -> showErrorNotification("Invalid color format", parseResult.error)
                        }
                    }
                }
            },
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(currentBg)
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent { it.handleAppMap() }
    ) {
        HeaderControls(
            currentBg = currentBg,
            activeUiTheme = activeUiTheme,
            availableBackgroundColors = backgroundColorsForDialog
        )
        Spacer(Modifier.height(12.dp))
        TailwindCssColorPaletteView(
            currentBg = currentBg,
            activeUiTheme = activeUiTheme,
            scrollState = scrollState
        )
    }

    LaunchedEffect(Unit) {
        if (paneState.isActive()) {
            appFocusRequester.requestFocus()
        }
        collectEvent<RestoreAppFocus> { event ->
            if (paneState.isActive()) {
                appFocusRequester.requestFocus()
            }
        }
        scrollVelocity.loopForeverAndTick()
    }
    LaunchedEffectForAppKeys(paneState, localAppKeys)
}