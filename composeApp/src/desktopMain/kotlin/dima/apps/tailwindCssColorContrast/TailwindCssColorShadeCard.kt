package dima.apps.tailwindCssColorContrast

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.os.copyToClipboard
import dima.utils.bottomBorder
import dima.utils.clickableWithoutBackgroundRipple

@Composable
internal fun TailwindCssColorShadeCard(
    shadeName: String,
    familyName: String,
    foregroundColor: Color,
    backgroundColor: Color
) {
    val contrastRatio = getContrast(foregroundColor, backgroundColor)
    val contrastGood = isWcagConstrastGood(contrastRatio)
    val shortDisplayName = shadeName

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .clickableWithoutBackgroundRipple {
                copyToClipboard("text-${familyName}-${shadeName}", showNotification = true)
            }
            .padding(vertical = 2.dp),
    ) {
        Text(
            text = "$familyName $shortDisplayName",
            color = foregroundColor,
            // works well on 1280x800px MacBook resolution while showing everything
            fontSize = 16.sp,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .weight(1f, fill = false)
                .padding(top = 2.dp)
                .bottomBorder(
                    2.dp,
                    if (contrastGood) {
                        TailwindCssColors.transparent
                    } else {
                        foregroundColor
                    }
                )
        )
    }
}
