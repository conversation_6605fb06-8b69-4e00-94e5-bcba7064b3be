package dima.apps.veroLog

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.rememberScrollbarAdapter

import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.utils.DummyFocusable
import dima.utils.handleAppMap
import dima.utils.scrollbarStyleThemed
import handleLostFocus

@Composable
internal fun VeroLogAppUi(
    appFocusRequester: FocusRequester,
    weekSummaries: List<WeekSummary>,
    scrollState: androidx.compose.foundation.ScrollState,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent { it.handleAppMap() }
    ) {
        DummyFocusable()
        Text(
            "Vero Log",
            fontSize = 20.sp,
            color = GlobalStyling.getTextColor(),
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp)
        )
        if (weekSummaries.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No Vero logs found!",
                    fontSize = 16.sp,
                    color = GlobalStyling.getRedTextColor()
                )
            }
        } else {
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(scrollState)
                ) {
                    for (index in weekSummaries.indices) {
                        val summary = weekSummaries[index]
                        VeroLogAppUiWeekSummaryCard(
                            summary = summary,
                        )
                    }
                }
                VerticalScrollbar(
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = scrollState),
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(top = 16.dp, end = 4.dp)
                )
            }
        }
    }
}
