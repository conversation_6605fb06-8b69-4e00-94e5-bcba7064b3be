package dima.apps.veroLog

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.utils.TextNumbersSameWidth
import dima.utils.bottomBorder

private fun formatHours(hours: Double): String {
    val wholeHours = hours.toInt()
    val minutes = ((hours - wholeHours) * 60).toInt()
    return if (minutes == 0) {
        "${wholeHours}h"
    } else {
        if (wholeHours == 0) {
            if (minutes <= 9) {
                " ${minutes}m"
            } else {
                "${minutes}m"
            }
        } else {
            if (minutes <= 9) {
                "${wholeHours}h  ${minutes}m"
            } else {
                "${wholeHours}h ${minutes}m"
            }
        }
    }
}

@Composable
internal fun VeroLogAppUiWeekSummaryCard(
    summary: WeekSummary,
) {
    val textColor = GlobalStyling.getTextColor()

    // German month names mapping
    val germanMonths = mapOf(
        1 to "Januar", 2 to "Februar", 3 to "März", 4 to "April",
        5 to "Mai", 6 to "Juni", 7 to "Juli", 8 to "August",
        9 to "September", 10 to "Oktober", 11 to "November", 12 to "Dezember"
    )

    // Format: "W25 16. Juni - 22. Juni 2025"
    val startMonth = germanMonths[summary.weekStart.monthValue] ?: summary.weekStart.month.name
    val endMonth = germanMonths[summary.weekEnd.monthValue] ?: summary.weekEnd.month.name
    val weekText =
        buildString {
            append("W")
            append(summary.weekNumber)
            append("    ")
            append(summary.weekEnd.dayOfMonth)
            append(". ")
            append(endMonth)
            if (summary.weekEnd.year != summary.weekStart.year) {
                append(" ")
                append(summary.weekEnd.year)
            }
            append(" - ")
            append(summary.weekStart.dayOfMonth)
            append(". ")
            append(startMonth)
            append(" ")
            append(summary.weekStart.year)
        }

    Box(
        modifier = Modifier
            .widthIn(max = 600.dp)
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        SelectionContainer {
            Column {
                // Week header with total hours
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val boldColor = GlobalStyling.getBoldTextColor()
                    Text(
                        text = weekText,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = boldColor,
                    )
                    TextNumbersSameWidth(
                        text = formatHours(summary.totalHours),
                        fontSize = 16.sp,
                        color = boldColor,
                        modifier = Modifier.bottomBorder(2.dp, boldColor)
                    )
                }

                // Daily breakdown
                if (summary.days.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    summary.days.forEach { day ->
                        Column {
                            // Day header
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .bottomBorder(1.dp, textColor), // Apply border to the Row
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = day.dayName,
                                    fontSize = VeroLogAppStyling.hourFontSize,
                                    fontWeight = FontWeight.Medium,
                                    color = textColor
                                )
                                TextNumbersSameWidth(
                                    text = formatHours(day.totalHours),
                                    fontSize = VeroLogAppStyling.hourFontSize,
                                    color = textColor,
                                    setAllCharactersToSameWidth = true,
                                )
                            }

                            // Projects for this day
                            if (day.projects.isNotEmpty()) {
                                Spacer(modifier = Modifier.height(4.dp))
                                day.projects.entries.sortedByDescending { it.value }.forEach { (projectName, hours) ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth().padding(start = 16.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text(
                                            text = projectName,
                                            fontSize = VeroLogAppStyling.hourFontSize,
                                            color = textColor,
                                            modifier = Modifier.weight(1f)
                                        )
                                        TextNumbersSameWidth(
                                            text = formatHours(hours),
                                            fontSize = VeroLogAppStyling.hourFontSize,
                                            color = textColor,
                                            setAllCharactersToSameWidth = true
                                        )
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }
            }
        }
    }
}
