package dima.apps.veroLog

import GlobalEvent
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import dialogs
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.utils.AppKey
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import dima.vero.VeroLog
import dima.vero.calculateWeeklySummaries
import globalEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.io.File
import java.time.LocalDate

@Serializable
data class VeroLogEntry(
    val timestamp: String,
    val projectId: Int,
    val projectName: String,
    val state: String // "WORK" or "END"
)

data class DaySummary(
    val date: LocalDate,
    val dayName: String, // Monday, Tuesday, etc.
    val totalHours: Double,
    val projects: Map<String, Double> // projectName to hours
)

data class WeekSummary(
    val weekStart: LocalDate,
    val weekEnd: LocalDate,
    val weekNumber: Int,
    val totalHours: Double,
    val days: List<DaySummary> // grouped by days
)

@Composable
fun VeroLogApp() {
    var weekSummaries by remember { mutableStateOf<List<WeekSummary>>(emptyList()) }
    val scrollState = rememberScrollState()
    val appFocusRequester = remember { FocusRequester() }
    val coroutineScope = rememberCoroutineScope()
    val scrollVelocity = remember { ScrollVelocity(scrollState) }

    // Start the scroll velocity loop
    LaunchedEffect(Unit) {
        scrollVelocity.loopForeverAndTick()
    }

    fun loadVeroLogs() {
        coroutineScope.launch(Dispatchers.Default) {
            try {
                val file = File(VeroLog.cacheDirectory, "hour-bookings.jsonl")
                if (!file.exists()) {
                    weekSummaries = emptyList()
                    return@launch
                }

                val entries = file.readLines()
                    .filter { it.isNotBlank() }
                    .mapNotNull { line ->
                        try {
                            Json.decodeFromString<VeroLogEntry>(line)
                        } catch (_: Exception) {
                            null
                        }
                    }
                    .sortedBy { it.timestamp }

                val summaries = calculateWeeklySummaries(entries)
                weekSummaries = summaries.sortedByDescending { it.weekStart }

                coroutineScope.launch {
                    if (weekSummaries.isNotEmpty()) {
                        scrollVelocity.scrollToTop(instantly = true)
                    }
                }
            } catch (_: Exception) {
                weekSummaries = emptyList()
            }
        }
    }

    remember {
        appKeys = listOf(
            AppKey(
                key = Key.T, 
                text = "Scroll down",
                onKeyDown = { scrollVelocity.onScrollDownKeyPressed() },
                onKeyUp = { scrollVelocity.onKeyReleased() }
            ),
            AppKey(
                key = Key.M, 
                text = "Scroll down more",
                onKeyDown = { scrollVelocity.onScrollDownMoreKeyPressed() },
                onKeyUp = { scrollVelocity.onKeyReleased() }
            ),
            AppKey(
                key = Key.V, 
                text = "Scroll up more",
                onKeyDown = { scrollVelocity.onScrollUpMoreKeyPressed() },
                onKeyUp = { scrollVelocity.onKeyReleased() }
            ),
            AppKey(
                key = Key.C, 
                text = "Scroll up",
                onKeyDown = { scrollVelocity.onScrollUpKeyPressed() },
                onKeyUp = { scrollVelocity.onKeyReleased() }
            )
        )
    }

    VeroLogAppUi(
        appFocusRequester = appFocusRequester,
        weekSummaries = weekSummaries,
        scrollState = scrollState,
    )

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
        loadVeroLogs()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                coroutineScope.launch {
                    scrollVelocity.scrollToTop()
                }
            }

            GlobalEvent.ScrollToBottom -> {
                coroutineScope.launch {
                    scrollVelocity.scrollToBottom()
                }
            }

            GlobalEvent.Reload -> loadVeroLogs()
            else -> {}
        }
    }
}