package dima.apps.calculator.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.calculator.Calculator.History
import dima.color.TailwindCssColors

@Composable
internal fun CalculatorDisplay(
    inputState: TextFieldValue,
    result: String?,
    error: String?,
    decimalPoints: Int,
    history: List<History>,
    selectedHistoryIndex: Int?,
    inputFocusRequester: FocusRequester,
    onInputChange: (TextFieldValue) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .padding(horizontal = 12.dp)
    ) {
        val textModifier = Modifier.fillMaxWidth()
        val fontSize = 30.sp
        val textAlign = TextAlign.Center
        if (error != null) {
            Text(
                error,
                textAlign = textAlign,
                fontSize = fontSize,
                color = TailwindCssColors.red500,
                modifier = textModifier
            )
        } else if (result != null) {
            Text(
                result,
                fontWeight = FontWeight.SemiBold,
                textAlign = textAlign,
                fontSize = fontSize,
                modifier = textModifier
            )
        } else {
            Text(
                "",
                textAlign = textAlign,
                fontSize = fontSize,
                modifier = textModifier
            )
        }
        Row(verticalAlignment = Alignment.CenterVertically) {
            TextField(
                value = inputState,
                onValueChange = onInputChange,
                textStyle = LocalTextStyle.current.copy(fontSize = 20.sp),
                colors = TextFieldDefaults.textFieldColors(
                    backgroundColor = TailwindCssColors.transparent
                ),
                modifier = Modifier
                    .fillMaxWidth(0.6f)
                    .padding(12.dp)
                    .focusRequester(inputFocusRequester)
            )
            Text(
                "Precision: $decimalPoints",
                modifier = Modifier.padding(12.dp)
            )
        }
        CalculatorHistory(history, selectedHistoryIndex)
    }
}
