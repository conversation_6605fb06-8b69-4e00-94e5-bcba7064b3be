package dima.apps.calculator.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.apps.calculator.Calculator.History
import dima.color.TailwindCssColors

@Composable
internal fun CalculatorHistory(
    history: List<History>,
    selectedHistoryIndex: Int?,
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(4.dp),
        modifier = Modifier
            .width(800.dp)
    ) {
        itemsIndexed(history, key = { index, _ -> index }) { index, it ->
            val flippedIndex = history.size - index - 1
            val selected = selectedHistoryIndex != null && selectedHistoryIndex == flippedIndex
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        if (selected) {
                            Modifier
                                .background(GlobalStyling.getSelectedBackgroundColor(), RoundedCornerShape(5.dp))
                        } else {
                            Modifier
                        }
                    )
                    .padding(vertical = 4.dp, horizontal = 6.dp)
            ) {
                Text(
                    "x$flippedIndex",
                    color = TailwindCssColors.orange800
                )
                if (it.name != null) {
                    Text(
                        it.name,
                        color = TailwindCssColors.white,
                        modifier = Modifier
                            .background(TailwindCssColors.blue500, RoundedCornerShape(5.dp))
                            .padding(horizontal = 4.dp, vertical = 2.dp)
                    )
                }
                val trimmedExpression = it.expression.trim()
                val asString = it.result.toString()
                val fontWeight = FontWeight.SemiBold
                if (trimmedExpression == asString) {
                    Text(
                        trimmedExpression,
                        fontWeight = fontWeight
                    )
                } else {
                    Row {
                        Text(it.expression.trim() + " = ")
                        Text(
                            asString,
                            fontWeight = fontWeight
                        )
                    }
                }
            }
        }
    }
}
