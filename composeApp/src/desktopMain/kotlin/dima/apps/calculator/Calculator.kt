package dima.apps.calculator

import com.github.keelar.exprk.ExpressionException
import com.github.keelar.exprk.Expressions
import dima.apps.calculator.Calculator.History
import dima.globalState.GlobalState
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encodeToString
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import java.io.File
import java.math.BigDecimal
import kotlin.math.cos
import kotlin.math.sin

/**
 * From https://github.com/Kotlin/kotlinx.serialization/issues/1051#issuecomment-1173599388
 */
object BigDecimalSerializer : KSerializer<BigDecimal> {
    override fun deserialize(decoder: Decoder): BigDecimal {
        return decoder.decodeString().toBigDecimal()
    }

    override fun serialize(encoder: Encoder, value: BigDecimal) {
        encoder.encodeString(value.toPlainString())
    }

    override val descriptor: SerialDescriptor
        get() = PrimitiveSerialDescriptor("BigDecimal", PrimitiveKind.STRING)
}

internal object Calculator {

    private val json = Json { isLenient = true }

    /**
     * Both can be null, when the input is not valid, like on an empty string.
     * Otherwise, either [result] or [error] is set.
     */
    data class Result(
        val result: BigDecimal? = null,
        val error: String? = null
    )

    @Serializable
    data class History(
        val name: String? = null,
        val expression: String,
        @Serializable(with = BigDecimalSerializer::class)
        val result: BigDecimal
    )

    private val relativeCacheDirectory = File("cache/calculator")
    private val historyFile = File(relativeCacheDirectory, "history.json")

    fun setupCache() {
        if (!relativeCacheDirectory.exists()) {
            relativeCacheDirectory.mkdirs()
        }
    }

    fun getHistory(): List<History> {
        return if (historyFile.exists()) {
            json.decodeFromString<List<History>>(historyFile.readText())
        } else {
            emptyList()
        }
    }

    fun addToHistory(entry: History): List<History> {
        val history = getHistory().toMutableList()
        if (history.contains(entry)) {
            history.remove(entry)
        }
        history.add(entry)
        historyFile.writeText(json.encodeToString(history))
        return history
    }

    fun removeHistoryEntry(entry: History): List<History> {
        val history = getHistory().toMutableList()
        history.remove(entry)
        historyFile.writeText(json.encodeToString(history))
        return history
    }

    fun clearHistory() {
        historyFile.delete()
    }

    /**
     * Eval with x{historyIndex} variables defined.
     */
    fun eval(inputText: String, attemptFix: Boolean = true): Result {
        return try {
            val input = inputText.trim()
            if (input == "") {
                Result()
            } else {
                val newInput = input.trimEnd('+', '-', '*', '/', '(', '^')
                if (newInput == "") {
                    Result()
                } else {
                    val history = getHistory()
                    val expression = Expressions().setPrecision(GlobalState.calculator.decimalPoints)
                    expression.addFunction("sin") { arguments ->
                        if (arguments.size != 1) {
                            throw ExpressionException("sin() requires exactly one argument")
                        }
                        sin(arguments[0].toDouble()).toBigDecimal()
                    }
                    expression.addFunction("cos") { arguments ->
                        if (arguments.size != 1) {
                            throw ExpressionException("cos() requires exactly one argument")
                        }
                        cos(arguments[0].toDouble()).toBigDecimal()
                    }
                    history.forEachIndexed { index, it ->
                        expression.define("x$index", it.result)
                    }
                    try {
                        Result(expression.eval(newInput))
                    } catch (e: IndexOutOfBoundsException) {
                        Result(error = e.message)
                    } catch (e: NumberFormatException) {
                        // on having too many decimal points like "2.5.1"
                        Result(error = e.message)
                    }
                }
            }
        } catch (e: ArithmeticException) {
//            Result(error = "ArithmeticException: " + e.message)
            Result(error = e.message)
        } catch (e: ExpressionException) {
            if (attemptFix && e.message!!.startsWith("Expected ')' after ")) {
                // add parentheses at end to try to fix
                val nextTry = eval("$inputText)", attemptFix = false)
                if (nextTry.result == null) {
//                    Result(error = "ExpressionException: " + e.message)
                    Result(error = e.message)
                } else {
                    nextTry
                }
            } else {
//                Result(error = "ExpressionException: " + e.message)
                Result(error = e.message)
            }
        }
    }

}

internal fun hasHistory(history: List<History>): Boolean {
    if (history.isEmpty()) {
        GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = null)
        return false
    }
    if (GlobalState.calculator.selectedHistory == null) {
        GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = history.size - 1)
        return false
    }
    return true
}

internal fun getCurrentHistoryEntry(history: List<History>): History =
    history.reversed()[GlobalState.calculator.selectedHistory!!]