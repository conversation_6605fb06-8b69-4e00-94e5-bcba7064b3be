package dima.apps.calculator

import androidx.compose.ui.focus.FocusRequester
import dima.apps.notifications.showErrorNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.os.copyToClipboard

internal fun clearHistory(onHistoryClear: () -> Unit) {
    openConfirmationDialog("Clear all history entries?") {
        Calculator.clearHistory()
        onHistoryClear()
    }
}

internal fun onEscape(
    appFocusRequester: FocusRequester,
    inputFocusRequester: FocusRequester
) {
    if (GlobalState.calculator.inputFocused) {
        appFocusRequester.requestFocus()
        GlobalState.calculator = GlobalState.calculator.copy(inputFocused = false)
    } else {
        inputFocusRequester.requestFocus()
        GlobalState.calculator = GlobalState.calculator.copy(inputFocused = true)
    }
}

internal fun copyResult() {
    if (GlobalState.calculator.inputFocused) {
        if (lastResult == null) {
            showErrorNotification("Can not copy with error")
            return
        }
        copyToClipboard(lastResult.toString(), notificationTitleSubstring = "expression result")
        return
    }
}

internal fun changeResultPrecision(inputFocusRequester: FocusRequester) {
    openTextInputDialog("Change to precision", minLines = 1) {
        try {
            val number = it.toInt()
            GlobalState.calculator = GlobalState.calculator.copy(decimalPoints = number)
            inputFocusRequester.requestFocus()
        } catch (_: NumberFormatException) {
            showErrorNotification("Not a valid number: $it")
        }
        return@openTextInputDialog TextInputDialogConfirmAction.Close
    }
}

internal fun moveHistoryEntryToTop(
    history: List<Calculator.History>,
    onHistoryChange: (List<Calculator.History>) -> Unit
) {
    if (!hasHistory(history)) {
        return
    }
    val entry = getCurrentHistoryEntry(history)
    onHistoryChange(Calculator.addToHistory(entry).reversed())
    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = history.size - 1)
}