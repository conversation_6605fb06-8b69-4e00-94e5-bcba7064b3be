package dima.apps.calculator

import GlobalEvent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.focus.FocusRequester
import dima.apps.calculator.Calculator.History
import dialogs
import dima.globalState.GlobalState
import dima.utils.LaunchedEffectGlobalEventForApps
import globalEvent

@Composable
internal fun CalculatorEffects(
    inputFocusRequester: FocusRequester,
    appFocusRequester: FocusRequester,
    history: List<History>
) {
    LaunchedEffect(Unit) {
        if (GlobalState.calculator.inputFocused) {
            inputFocusRequester.requestFocus()
        } else {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            if (GlobalState.calculator.inputFocused) {
                inputFocusRequester.requestFocus()
            } else {
                appFocusRequester.requestFocus()
            }
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToBottom -> {
                if (history.isEmpty()) {
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = null)
                } else {
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = 0)
                }
            }

            GlobalEvent.ScrollToTop -> {
                if (history.isEmpty()) {
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = null)
                } else {
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = history.size - 1)
                }
            }

            else -> {}
        }
    }
}
