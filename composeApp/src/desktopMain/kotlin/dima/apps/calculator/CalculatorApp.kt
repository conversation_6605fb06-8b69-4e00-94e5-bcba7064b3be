package dima.apps.calculator

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.apps.calculator.Calculator.History
import dima.apps.calculator.ui.CalculatorDisplay
import dima.apps.notifications.showErrorNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.dialogs.help.appKeys
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.utils.AppKey
import dima.utils.DummyFocusable
import dima.utils.handleAppMap
import isTextFieldFocused
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.math.BigDecimal

internal var lastResult: BigDecimal? = null

@Composable
fun CalculatorApp() {
    remember {
        Calculator.setupCache()
        GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = 5)
    }

    val appFocusRequester = remember { FocusRequester() }
    val inputFocusRequester = remember { FocusRequester() }
    var history: List<History> by remember { mutableStateOf(Calculator.getHistory().reversed()) }
    var inputState by remember {
        mutableStateOf(
            TextFieldValue(
                text = GlobalState.calculator.input,
                selection = when {
                    GlobalState.calculator.input.isEmpty() -> TextRange.Zero
                    else -> TextRange(GlobalState.calculator.input.length, GlobalState.calculator.input.length)
                }
            )
        )
    }

    remember {
        appKeys = listOf(
            AppKey(Key.F, "Change result precision") {
                changeResultPrecision(inputFocusRequester)
            },
            AppKey(Key.R, "Move history entry to top") {
                moveHistoryEntryToTop(history = history, onHistoryChange = { history = it })
            },
            AppKey(Key.E, "Replace expression with history entry") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val entry = getCurrentHistoryEntry(history)
                inputState =
                    TextFieldValue(entry.expression, TextRange(entry.expression.length, entry.expression.length))
                GlobalState.calculator = GlobalState.calculator.copy(input = entry.expression, inputFocused = true)
                // use launch to add a slight delay, so that the input field is focused correctly with proper content
                CoroutineScope(Dispatchers.Main).launch {
                    inputFocusRequester.requestFocus()
                }
            },
            AppKey(Key.DirectionUp, "Focus history and select next") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                if (GlobalState.calculator.selectedHistory!! == history.size - 1) {
                    inputFocusRequester.requestFocus()
                    GlobalState.calculator = GlobalState.calculator.copy(inputFocused = true)
                } else {
                    val next = (GlobalState.calculator.selectedHistory!! + 1).coerceAtMost(history.size - 1)
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
                }
            },
            AppKey(Key.DirectionDown, "Focus history and select previous") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val next = if (GlobalState.calculator.inputFocused) {
                    appFocusRequester.requestFocus()
                    GlobalState.calculator = GlobalState.calculator.copy(inputFocused = false)
                    history.size - 1
                } else {
                    (GlobalState.calculator.selectedHistory!! - 1).coerceAtLeast(0)
                }
                GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
            },
            AppKey(Key.C, "Select previous in history") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val next = (GlobalState.calculator.selectedHistory!! + 1).coerceAtMost(history.size - 1)
                GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
            },
            AppKey(Key.T, "Select next in history") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val next = (GlobalState.calculator.selectedHistory!! - 1).coerceAtLeast(0)
                GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
            },
            AppKey(Key.M, "Select more next in history") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val next = (GlobalState.calculator.selectedHistory!! - 6).coerceAtLeast(0)
                GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
            },
            AppKey(Key.V, "Select more previous in history") {
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val next = (GlobalState.calculator.selectedHistory!! + 6).coerceAtMost(history.size - 1)
                GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
            },
            AppKey(Key.D, "Delete history entry or clear expression") {
                if (GlobalState.calculator.inputFocused) {
                    inputState = TextFieldValue()
                    GlobalState.calculator = GlobalState.calculator.copy(input = "")
                    return@AppKey
                }
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val entry = getCurrentHistoryEntry(history)
                val subTitle = buildString {
                    if (entry.name != null) {
                        append(entry.name + ": ")
                    }
                    append(entry.expression)
                }
                openConfirmationDialog("Delete history entry", subTitle) {
                    history = Calculator.removeHistoryEntry(entry).reversed()
                    if (history.isEmpty()) {
                        GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = null)
                        return@openConfirmationDialog
                    }
                    val next = GlobalState.calculator.selectedHistory!!.coerceIn(0, history.size - 1)
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = next)
                }
            },
            AppKey(Key.X, "Clear history") {
                clearHistory {
                    history = emptyList()
                }
            },
            AppKey(Key.N, "Save entered expression with name or rename history entry") {
                if (GlobalState.calculator.inputFocused) {
                    val trimmed = GlobalState.calculator.input.trim()
                    if (trimmed == "") {
                        showErrorNotification("No expression to remember")
                        return@AppKey
                    }
                    if (lastResult == null) {
                        showErrorNotification("Can not remember with error")
                        return@AppKey
                    }
                    openTextInputDialog("Remember expression with name", GlobalState.calculator.input) {
                        val trimmedName = it.trim()
                        val entry = History(
                            name = trimmedName.ifBlank { null },
                            expression = GlobalState.calculator.input,
                            result = lastResult!!
                        )
                        history = Calculator.addToHistory(entry).reversed()
                        inputState = TextFieldValue()
                        GlobalState.calculator = GlobalState.calculator.copy(input = "")
                        return@openTextInputDialog TextInputDialogConfirmAction.Close
                    }
                    return@AppKey
                }
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val entry = getCurrentHistoryEntry(history)
                val subTitle = buildString {
                    if (entry.name != null) {
                        append(entry.name + ": ")
                    }
                    append(entry.expression)
                }
                openTextInputDialog("Rename history entry", subTitle) {
                    Calculator.removeHistoryEntry(entry)
                    val trimmedName = it.trim()
                    history = Calculator.addToHistory(entry.copy(name = trimmedName.ifBlank { null })).reversed()
                    GlobalState.calculator = GlobalState.calculator.copy(selectedHistory = history.size - 1)
                    return@openTextInputDialog TextInputDialogConfirmAction.Close
                }
            },
            AppKey(Key.J, "Copy expression result while in text field") {
                copyResult()
            },
            AppKey(Key.Escape, "Toggle focus between input text field and history") {
                onEscape(appFocusRequester, inputFocusRequester)
            },
            AppKey(Key.Enter, "Remember input and result") {
                if (GlobalState.calculator.inputFocused) {
                    if (lastResult == null) {
                        showErrorNotification("Can not remember with error")
                        return@AppKey
                    }
                    val trimmed = GlobalState.calculator.input.trim()
                    if (trimmed == "") {
                        showErrorNotification("No expression to remember")
                        return@AppKey
                    }
                    val entry = History(
                        expression = GlobalState.calculator.input,
                        result = lastResult!!
                    )
                    copyToClipboard(entry.result.toString())
                    history = Calculator.addToHistory(entry).reversed()
                    inputState = TextFieldValue()
                    GlobalState.calculator = GlobalState.calculator.copy(input = "")
                    return@AppKey
                }
                if (!hasHistory(history)) {
                    return@AppKey
                }
                val entry = getCurrentHistoryEntry(history)
                copyToClipboard(entry.result.toString(), notificationTitleSubstring = "result")
            },
        )
    }

    val calculatorResult by derivedStateOf {
        Calculator.eval(inputState.text)
    }

    lastResult = calculatorResult.result

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxSize()
            .padding(12.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    val isKeyDown = it.type == KeyEventType.KeyDown
                    val isHandledKey =
                        (it.key == Key.Escape || it.key == Key.Enter || it.key == Key.N || it.key == Key.J) ||
                                it.key == Key.DirectionDown || it.key == Key.DirectionUp ||
                                // handle code points manually since this internally is the Swing key pressed event on the text field
                                // Key.J
                                (it.utf16CodePoint == 106)
                                // Key.N
                                || (it.utf16CodePoint == 110)
                                // Key.F
                                || (it.utf16CodePoint == 102)
                                // Key.D
                                || (it.utf16CodePoint == 100)
                    if (isKeyDown && isHandledKey) {
                        it.handleAppMap()
                        return@onPreviewKeyEvent true
                    }
                    if (isHandledKey) {
                        return@onPreviewKeyEvent true
                    }
                    return@onPreviewKeyEvent false
                }
                return@onPreviewKeyEvent it.handleAppMap()
            },
    ) {
        DummyFocusable()
        CalculatorDisplay(
            inputState = inputState,
            result = calculatorResult.result?.toString(),
            error = calculatorResult.error,
            decimalPoints = GlobalState.calculator.decimalPoints,
            history = history,
            selectedHistoryIndex = GlobalState.calculator.selectedHistory,
            inputFocusRequester = inputFocusRequester,
            onInputChange = {
                inputState = it
                GlobalState.calculator = GlobalState.calculator.copy(input = it.text)
            }
        )
    }
    CalculatorEffects(inputFocusRequester, appFocusRequester, history)
}
