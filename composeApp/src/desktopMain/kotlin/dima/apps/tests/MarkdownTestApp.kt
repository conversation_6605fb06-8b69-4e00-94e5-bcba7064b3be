package dima.apps.tests

import androidx.compose.foundation.layout.Column
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors

@Composable
fun MarkdownTestApp() {
    Column {
        // The tree-sitter CLI returns those lines. Those offsets include the quotes and the emoji
        // string 11 17
        // string 29 35
        val annotatedString = buildAnnotatedString {
            val s = "val test = \"😄\"\nval test = \"😄\""
            append(s)
            addStyle(
                SpanStyle(
                    fontSize = 24.sp,
                    color = TailwindCssColors.blue500,
                    background = TailwindCssColors.blue200,
                    fontWeight = FontWeight.Bold
                    // 0, 3 is to select val
                    // 11, 15 is to select "😄"
                ), 11, 15
            )
            addStyle(
                SpanStyle(
                    fontSize = 24.sp,
                    color = TailwindCssColors.blue500,
                    background = TailwindCssColors.blue200,
                    fontWeight = FontWeight.Bold
                    // 27, 31 is to select "😄"
                ), 27, 31
            )
        }
        Text(annotatedString)
    }
}
