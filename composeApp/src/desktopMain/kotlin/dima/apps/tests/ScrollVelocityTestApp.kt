package dima.apps.tests

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.unit.dp
import com.mikepenz.markdown.compose.components.markdownComponents
import com.mikepenz.markdown.m2.Markdown
import dima.color.TailwindCssColors
import dima.dialogs.help.appKeys
import dima.images.CustomAsyncImage
import dima.markdownRendererLibraryPatches.MyMarkdownOrderedList
import dima.utils.*

@Composable
fun ScrollVelocityTestApp() {
    val scrollState = rememberScrollState()
    val scrollVelocity = remember { ScrollVelocity(scrollState) }
    val appFocusRequester = remember { FocusRequester() }

    LaunchedEffect(Unit) {
        scrollVelocity.loopForeverAndTick()
    }

    remember {
        appKeys = listOf(
            AppKey(
                Key.C, "Scroll up",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollUpKeyPressed() }),
            AppKey(
                Key.T, "Scroll down",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollDownKeyPressed() }),
            AppKey(
                Key.M, "Scroll down more",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollDownMoreKeyPressed() }),
            AppKey(
                Key.V, "Scroll up more",
                onKeyUp = { scrollVelocity.onKeyReleased() },
                onKeyDown = { scrollVelocity.onScrollUpMoreKeyPressed() }),
        )
    }

    Box {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .padding(12.dp)
                .verticalScroll(scrollState)
                .onPreviewKeyEvent {
                    return@onPreviewKeyEvent it.handleAppMap()
                }
        ) {
            DummyFocusable(appFocusRequester)
            /*
                        Markdown(
                            """``` yepo ```""".trimIndent(),
                            modifier = Modifier
                                .background(TailwindCssColors.blue200)
                                .padding(vertical = 20.dp)
                        )
                        Markdown(
                            """```
                                yepo
                                ```
                        """.trimIndent(),
                            modifier = Modifier
                                .background(TailwindCssColors.orange200)
                                .padding(vertical = 20.dp)
                        )
            */
            Markdown(
                """
Vor der Erfindung von Glas und Plastik wurden verschiedene natürliche und traditionelle Materialien für Gewächshausdächer verwendet:

1. Geöltes Papier/Pergament
- Speziell behandeltes, transluzentes Papier
- Mit Öl getränkt für Wetterbeständigkeit
- Besonders in Ostasien verbreitet

2. Gewachstes Leinen/Stoff
- Mit Wachs oder Öl imprägniert
- Lichtdurchlässig und wasserabweisend
- Musste regelmäßig nachbehandelt werden

""".trimIndent(),
                components = markdownComponents(
                    orderedList = {
                        Column {
                            MyMarkdownOrderedList(it.content, it.node, style = it.typography.ordered)
                        }
                    },
                ),
                modifier = Modifier
                    .background(TailwindCssColors.gray200)
                    .padding(vertical = 20.dp)
            )
            for (i in 1..100) {
                Text("DummyApp")
                CustomAsyncImage("/Users/<USER>/Developer/dummy-files-for-upload/568x400-signature-mock.png")
            }
        }
        VerticalScrollbar(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .fillMaxHeight(),
            adapter = rememberScrollbarAdapter(scrollState = scrollState)
        )
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }
}
