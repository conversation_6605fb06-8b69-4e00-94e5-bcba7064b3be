package dima.apps.email

import dima.apps.email.models.EmailFolderType
import dima.apps.notifications.showErrorNotification
import dima.dialogs.confirmation.openConfirmationDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal fun downloadAllEmailsInCurrentFolderType(
    folderType: EmailFolderType?,
    coroutineScope: CoroutineScope,
    showConfirmationDialog: <PERSON>ole<PERSON>,
    maxKnownEmailsBeforeStoppingDownload: Int?,
    getEmailsFromDatabaseAndUpdateSelected: () -> Unit
) {
    if (folderType == null) {
        return
    }
    val serverFolderNames = folderType.serverFolderNames()
    if (serverFolderNames == null) {
        showErrorNotification("No server folder names defined")
        return
    }
    fun download() {
        coroutineScope.launch(Dispatchers.Default) {
            downloadFolders(
                showNotifications = true,
                serverFolderNames = serverFolderNames,
                maxKnownEmailsBeforeStoppingDownload = maxKnownEmailsBeforeStoppingDownload,
                onNew = {
                    getEmailsFromDatabaseAndUpdateSelected()
                })
        }
    }

    if (showConfirmationDialog) {
        openConfirmationDialog("Download all emails?") {
            download()
        }
    } else {
        download()
    }
}
