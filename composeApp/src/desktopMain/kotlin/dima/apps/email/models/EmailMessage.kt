package dima.apps.email.models

import dima.apps.email.Email
import kotlinx.serialization.Serializable
import java.time.LocalDateTime

internal data class EmailNewDataInfo(val currentIndex: Int, val total: Int, val subject: String)

@Serializable
internal data class EmailFrom(val personal: String?, val email: String)

internal data class EmailMessage(
    val uid: String,
    /**
     * The server folder name.
     */
    val folderName: String,
    val plainText: String?,
    val html: String?,
    /**
     * JSON encoded string of [dima.apps.email.EmailAttachment] list.
     *
     * Only parse to JSON on demand.
     */
    val attachments: String,
    val attachmentCount: Int,
    val subject: String,
    val headers: Map<String, String>,
    val date: LocalDateTime,
    val from: List<EmailFrom>,
    val to: List<EmailFrom>,
    val cc: List<EmailFrom>,
    val bcc: List<EmailFrom>,
    val isAnswered: Boolean,
    val isDeleted: Boolean,
    val isDraft: <PERSON>olean,
    val isFlagged: <PERSON><PERSON>an,
    val isRecent: <PERSON><PERSON><PERSON>,
    /**
     * Check this for false for a new unread email.
     */
    val isSeen: <PERSON><PERSON><PERSON>,
    val wasTrashed: Boolean,
    /**
     * The 'Message-ID' header from the email.
     */
    val messageId: String? = null,
    /**
     * The 'References' header from the email. It looks like this, newline separated:
     * "<<EMAIL>> ...".
     *
     * For thread grouping, the thread key for GitLab emails is 'references', otherwise fallback to subject.
     */
    val references: String? = null,
    val isChildInThread: Boolean = false,
) {
    fun getFullUid(): String = Email.getFullUid(uid, folderName)
}
