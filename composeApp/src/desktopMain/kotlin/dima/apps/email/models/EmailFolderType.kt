package dima.apps.email.models

import androidx.compose.ui.graphics.Color
import dima.dialogs.completion.openCompletionDialog
import dima.settings
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

typealias EmailFolderName = String

/**
 * For [dima.apps.email.EmailLeftCard] pill display.
 *
 * Maps to the server name.
 */
data class EmailFolderTypeInfoPill(val pillDisplayName: String, val color: Color)

/**
 * The serverFolderName is not required to be unique.
 * [MultiServerFolder] can contain the same from other types.
 *
 * Make sure that only one [EmailFolderType] has [switchToForNewEmailView] set to true.
 */
@Serializable
sealed class EmailFolderType(
    open val displayName: String,
    open val syncRegularly: Boolean = false,
    open val switchToForNewEmailView: Boolean = false
) {
    /**
     * Iterates over all unique server folders in the settings order.
     */
    @SerialName("All")
    @Serializable
    data class All(
        @SerialName("_displayName") override val displayName: String,
        @SerialName("_switchToForNewEmailView") override val switchToForNewEmailView: Boolean = false
    ) : EmailFolderType(displayName, switchToForNewEmailView)

    @Serializable
    @SerialName("Inbox")
    data class Inbox(
        @SerialName("_displayName") override val displayName: String,
        val serverFolderName: String,
        @SerialName("_syncRegularly") override val syncRegularly: Boolean = false,
        @SerialName("_switchToForNewEmailView") override val switchToForNewEmailView: Boolean = false,
    ) : EmailFolderType(displayName, syncRegularly, switchToForNewEmailView)

    @Serializable
    @SerialName("Trash")
    data class Trash(
        @SerialName("_displayName") override val displayName: String,
        val serverFolderName: String,
        @SerialName("_syncRegularly") override val syncRegularly: Boolean = false,
        @SerialName("_switchToForNewEmailView") override val switchToForNewEmailView: Boolean = false,
    ) : EmailFolderType(displayName, syncRegularly, switchToForNewEmailView)

    @Serializable
    @SerialName("SingleServerFolder")
    data class SingleServerFolder(
        @SerialName("_displayName") override val displayName: String,
        val serverFolderName: String,
        @SerialName("_syncRegularly") override val syncRegularly: Boolean = false,
        @SerialName("_switchToForNewEmailView") override val switchToForNewEmailView: Boolean = false,
    ) : EmailFolderType(displayName, syncRegularly, switchToForNewEmailView)

    @Serializable
    @SerialName("MultiServerFolder")
    data class MultiServerFolder(
        @SerialName("_displayName") override val displayName: String,
        val serverFolderNames: List<String>,
        @SerialName("_syncRegularly") override val syncRegularly: Boolean = false,
        @SerialName("_switchToForNewEmailView") override val switchToForNewEmailView: Boolean = false,
    ) : EmailFolderType(displayName, syncRegularly, switchToForNewEmailView)

    /**
     * @return null when [All] and the settings have no folder types defined
     */
    fun serverFolderNames(): List<EmailFolderName>? {
        return when (this) {
            is Inbox -> listOf(serverFolderName)
            is Trash -> listOf(serverFolderName)
            is SingleServerFolder -> listOf(serverFolderName)
            is MultiServerFolder -> serverFolderNames
            is All -> settings.email.folderTypes.allFolderNames()
        }
    }
}

internal fun List<EmailFolderType>.inbox(): EmailFolderType.Inbox? {
    return filterIsInstance<EmailFolderType.Inbox>().firstOrNull()
}

internal fun List<EmailFolderType>.trash(): EmailFolderType.Trash? {
    return filterIsInstance<EmailFolderType.Trash>().firstOrNull()
}

internal fun List<EmailFolderType>.switchToForNewEmailView(): EmailFolderType? {
    return firstOrNull { it.switchToForNewEmailView }
}

/**
 * @return null if none found with [EmailFolderType.syncRegularly] set to true
 */
internal fun List<EmailFolderType>.regularlySyncedFolderNames(): List<EmailFolderName>? {
    val filtered = filter { it.syncRegularly }
    if (filtered.isEmpty()) {
        return null
    }
    return filtered.flatMap { type ->
        when (type) {
            is EmailFolderType.Inbox -> listOf(type.serverFolderName)
            is EmailFolderType.Trash -> listOf(type.serverFolderName)
            is EmailFolderType.SingleServerFolder -> listOf(type.serverFolderName)
            is EmailFolderType.MultiServerFolder -> type.serverFolderNames
            is EmailFolderType.All -> emptyList()
        }
    }.distinct()
}

/**
 * @return null if the list is empty
 */
internal fun List<EmailFolderType>.allFolderNames(): List<EmailFolderName>? {
    if (isEmpty()) {
        return null
    }
    val filtered = filter { it !is EmailFolderType.All }
    return filtered.flatMap { type ->
        when (type) {
            is EmailFolderType.Inbox -> listOf(type.serverFolderName)
            is EmailFolderType.Trash -> listOf(type.serverFolderName)
            is EmailFolderType.SingleServerFolder -> listOf(type.serverFolderName)
            is EmailFolderType.MultiServerFolder -> type.serverFolderNames
            is EmailFolderType.All -> emptyList()
        }
    }.distinct()
}

internal fun openChangeFolderTypeDialog(onFolderChange: (EmailFolderType) -> Unit) {
    val names = settings.email.folderTypes.map { it.displayName }
    openCompletionDialog("Change email folder type", names) {
        onFolderChange(settings.email.folderTypes[it.index!!])
    }
}