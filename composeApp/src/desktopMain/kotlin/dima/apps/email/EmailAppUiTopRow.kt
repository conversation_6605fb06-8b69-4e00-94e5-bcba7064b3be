package dima.apps.email

import GlobalEvent
import GlobalStyling
import androidx.compose.foundation.layout.*
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.RefreshCw
import dima.apps.email.models.EmailFolderType
import dima.apps.email.models.EmailMessage
import dima.apps.email.models.openChangeFolderTypeDialog
import dima.dateTime.DateTimeUtils
import dima.dateTime.toHumanRelativeInPastShort
import dima.globalState.GlobalState
import dima.settings
import dima.utils.SmartTextFieldCandidateCount
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import dima.utils.clickableWithoutBackgroundRipple
import globalEvent
import isTextFieldFocused

@Composable
internal fun EmailAppUiTopRow(
    folders: EmailFolderType?,
    queryState: TextFieldValue,
    searchFocusRequester: FocusRequester,
    markedEmailsIds: List<String>,
    filteredEntries: List<EmailMessage>,
    entries: List<EmailMessage>,
    onFolderChange: (EmailFolderType) -> Unit,
    onQueryStateChange: (TextFieldValue) -> Unit,
    onQueryChange: (String) -> Unit,
    onSelectedChange: (Int) -> Unit,
) {
    val color = GlobalStyling.getTextColor()
    val query = queryState.text // Extract query text for logic

    val countForTextField = if (markedEmailsIds.isNotEmpty()) {
        SmartTextFieldCandidateCount(
            currentCount = markedEmailsIds.size,
            maxCount = null // To display only "X" (selected count)
        )
    } else {
        SmartTextFieldCandidateCount(
            currentCount = if (query.isNotBlank() || filteredEntries.size != entries.size) filteredEntries.size else null,
            maxCount = entries.size
        )
    }

    Row(
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .padding(bottom = 12.dp)
                .fillMaxWidth()
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(top = 12.dp)
            ) {
                Text(
                    settings.email.address,
                    color = color,
                    textAlign = TextAlign.Center,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                )
                Icon(
                    Lucide.RefreshCw,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier
                        .clickableWithoutBackgroundRipple {
                            globalEvent = GlobalEvent.Reload
                        }
                        .size(18.dp)
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    folders?.displayName ?: "No folders defined in settings!",
                    color = color,
                    modifier = Modifier
                        .clickableWithoutBackgroundRipple {
                            openChangeFolderTypeDialog {
                                onFolderChange(it)
                            }
                        }
//                        .padding(16.dp)
                        .weight(1f)
                )
                Row(modifier = Modifier.weight(1f)) {
                    TextFieldWithCandidateCountWithoutAnimation(
                        value = queryState,
                        onValueChange = {
                            onQueryStateChange(it)
                            onQueryChange(it.text)
                            onSelectedChange(0)
                        },
                        topLabel = "Suche",
                        singleLine = true,
                        focusRequester = searchFocusRequester,
                        count = countForTextField, // Pass the dynamic count here
                        modifier = Modifier
                            .onFocusChanged {
                                isTextFieldFocused = it.isFocused
                            }
                            .fillMaxWidth()
                    )
                }
                Row(
                    horizontalArrangement = Arrangement.spacedBy(24.dp, Alignment.End),
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .weight(1f)
                ) {
                    // Removed the separate "X selected" Text composable
                    val lastUpdateText = if (GlobalState.email.lastUpdate == null) {
                        "Reload for latest emails"
                    } else {
                        DateTimeUtils.fromIsoToLocalDateTime(GlobalState.email.lastUpdate!!)
                            .toHumanRelativeInPastShort()
                    }
                    Text(
                        lastUpdateText,
                        color = if (GlobalState.email.lastUpdate == null) {
                            GlobalStyling.getBlueColor()
                        } else {
                            color
                        },
                    )
                }
            }
        }
    }
}
