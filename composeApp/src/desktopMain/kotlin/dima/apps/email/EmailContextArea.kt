package dima.apps.email

import androidx.compose.foundation.ContextMenuArea
import androidx.compose.foundation.ContextMenuItem
import androidx.compose.runtime.Composable
import dima.apps.email.models.EmailMessage
import dima.apps.email.models.inbox
import dima.apps.email.models.trash
import dima.settings

@Composable
internal fun EmailContextArea(
    entry: EmailMessage,
    onTrash: () -> Unit,
    onMoveToInbox: () -> Unit,
    onDownloadAttachments: () -> Unit,
    content: @Composable () -> Unit
) {
    ContextMenuArea(
        items = {
            buildList {
                val trash = settings.email.folderTypes.trash()
                if (trash?.serverFolderName != entry.folderName) {
                    add(ContextMenuItem("Trash") {
                        onTrash()
                    })
                }
                val inbox = settings.email.folderTypes.inbox()
                if (inbox?.serverFolderName != entry.folderName) {
                    add(ContextMenuItem("Move to Inbox") {
                        onMoveToInbox()
                    })
                }
                add(ContextMenuItem("Download attachments") {
                    onDownloadAttachments()
                })
            }
        }
    ) {
        content()
    }
}