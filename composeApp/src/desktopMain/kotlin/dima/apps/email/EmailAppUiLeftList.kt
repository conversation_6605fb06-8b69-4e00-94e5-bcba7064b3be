package dima.apps.email

import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.apps.email.models.EmailMessage
import dima.utils.scrollbarStyleThemed

@Composable
internal fun EmailAppUiLeftList(
    filteredEntries: List<EmailMessage>,
    selected: Int?,
    isPreviewSelected: Boolean,
    markedEmailsIds: List<String>,
    query: String,
    leftScrollState: LazyListState,
    onTrash: (EmailMessage) -> Unit,
    onMoveToInbox: (EmailMessage) -> Unit,
    onDownloadAttachments: (EmailMessage) -> Unit,
    onSelectedChange: (Int) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth(0.5f)
    ) {
        LazyColumn(
            state = leftScrollState,
            contentPadding = PaddingValues(bottom = 12.dp),
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 12.dp)
        ) {
            val current = if (selected == null) "" else filteredEntries.getOrNull(selected)?.uid
            itemsIndexed(filteredEntries, key = { _, entry -> entry.uid }) { index, entry ->
                var rootEmailForComparison: EmailMessage? = null
                if (entry.isChildInThread) {
                    for (i in index - 1 downTo 0) {
                        val potentialRoot = filteredEntries[i]
                        if (!potentialRoot.isChildInThread) {
                            rootEmailForComparison = potentialRoot
                            break
                        }
                    }
                }
                EmailLeftCard(
                    entry,
                    rootEmailForComparison = rootEmailForComparison,
                    isSelected = current == entry.uid,
                    isPreviewSelected = isPreviewSelected,
                    isMarked = markedEmailsIds.contains(entry.getFullUid()),
                    query = query,
                    onTrash = { onTrash(entry) },
                    onMoveToInbox = { onMoveToInbox(entry) },
                    onDownloadAttachments = { onDownloadAttachments(entry) },
                    onClick = { onSelectedChange(filteredEntries.indexOf(entry)) }
                )
            }
        }
        VerticalScrollbar(
            style = scrollbarStyleThemed(),
            adapter = rememberScrollbarAdapter(scrollState = leftScrollState),
            modifier = Modifier
                .align(Alignment.CenterStart)
                .fillMaxHeight()
                .padding(bottom = 12.dp),
        )
    }
}