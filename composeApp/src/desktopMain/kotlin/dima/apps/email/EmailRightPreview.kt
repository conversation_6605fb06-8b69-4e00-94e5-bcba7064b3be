package dima.apps.email

import GlobalStyling
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import dima.apps.email.models.EmailFrom
import dima.text.TextMarked

@Composable
internal fun EmailHeaderRow(prefix: String, info: List<EmailFrom>, leftWidth: Dp, searchQuery: String?) {

    @Composable
    fun insertText(s: String) {
        if (searchQuery == null) {
            Text(s, color = GlobalStyling.getTextColor())
        } else {
            TextMarked(s, searchQuery, color = GlobalStyling.getTextColor())
        }
    }

    info.forEachIndexed { index, it ->
        Row {
            Text(
                if (index == 0) prefix else "",
                color = GlobalStyling.getGrayColor(),
                modifier = Modifier.width(leftWidth)
            )
            if (it.personal == null) {
                insertText(it.email)
            } else {
                val personalTrimmed = it.personal.trim()
                val emailTrimmed = it.email.trim()
                if (personalTrimmed == emailTrimmed || personalTrimmed == "'$emailTrimmed'") {
                    insertText(it.email)
                } else {
                    insertText(it.personal + " • " + it.email)
                }
            }
        }
    }
}
