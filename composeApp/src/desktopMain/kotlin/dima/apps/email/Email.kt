package dima.apps.email

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.sun.mail.imap.IMAPFolder
import com.sun.mail.util.MailConnectException
import dima.apps.email.database.EmailDatabase
import dima.apps.email.database.EmailDatabase.getAllUntrashedForFolderType
import dima.apps.email.database.EmailTable
import dima.apps.email.models.EmailMessage
import dima.apps.email.models.inbox
import dima.apps.email.models.trash
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.settings
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.transactions.transaction
import org.jetbrains.exposed.sql.update
import java.io.File
import java.util.*
import javax.mail.*

object Email {

    /**
     * Used for debugging.
     *
     * Null to download all emails.
     */
    internal val emailFullIdWhitelist: List<String>? = null
//    private val emailFullIdWhitelist: List<String> = listOf("26428@INBOX")

    internal fun getFullUid(uid: String, serverFolderName: String): String = "$uid@$serverFolderName"

    var hasUnreadInboxEmails by mutableStateOf(false)

    internal const val RELATIVE_CACHE_DIRECTORY = "cache/email"
    internal val cacheDirectory = File(RELATIVE_CACHE_DIRECTORY)

    /**
     * Move the given email to Inbox on the IMAP server and update the local database.
     */
    internal fun moveEmailToInbox(email: EmailMessage, onDatabaseUpdate: () -> Unit) {
        val inbox = settings.email.folderTypes.inbox()
        if (inbox == null) {
            showErrorNotification("No Inbox folder defined")
            return
        }
        val notification = showLoadingNotification("Moving email to Inbox...", email.subject)
        var store: Store? = null
        var imapFolder: IMAPFolder? = null
        var inboxFolder: IMAPFolder? = null
        try {
            store = loginAndGetStore(showErrorNotifications = true)
            val folderName = email.folderName
            imapFolder = store!!.getFolder(folderName) as IMAPFolder
            NetworkActivity.addFakedEntry(
                "Opening email folder $folderName as read/write",
                FakedNetworkActivityStatus.Info
            )
            imapFolder.open(Folder.READ_WRITE)
            val message = imapFolder.getMessageByUID(email.uid.toLong())
            if (message == null) {
                transaction(EmailDatabase.database) {
                    EmailTable.deleteWhere { uidAndFolderName eq email.getFullUid() }
                }
                onDatabaseUpdate()
                notification.toError(title = "Failed to move Email: Not found in ${email.folderName}")
                return
            }
            inboxFolder = store.getFolder(inbox.serverFolderName) as IMAPFolder
            NetworkActivity.addFakedEntry(
                "Opening email folder ${inbox.displayName} as read/write",
                FakedNetworkActivityStatus.Info
            )
            if (!inboxFolder.exists()) {
                notification.toError(title = "Failed to move Email: Inbox folder '${inbox.serverFolderName}' not found.")
                return
            }
            inboxFolder.open(Folder.READ_WRITE)

            val messagesToMove = arrayOf(message)
            val newUidArray = imapFolder.moveUIDMessages(messagesToMove, inboxFolder)

            if (newUidArray == null || newUidArray.isEmpty()) {
                // This could happen if the folder does not support UIDPLUS or the move operation failed in a way that didn't throw an exception.
                // A more robust solution might be to delete from DB and re-sync, but for now, log and potentially show an error.
                notification.toError(title = "Failed to move Email: Server did not confirm new UID.")
                // Attempt to close source folder without expunging, destination folder with expunging
                // to commit the copy if it happened. This is tricky.
                // For now, we'll assume if no exception, the message is in the destination,
                // but we can't update its UID reliably. Best to re-sync.
                onDatabaseUpdate() // Trigger a re-sync or UI update
                return
            }

            val newUidInInbox = newUidArray[0].uid

            val originalFullUid = email.getFullUid()
            val newFullUidInInbox = getFullUid(newUidInInbox.toString(), inbox.serverFolderName)

            transaction(EmailDatabase.database) {
                EmailTable.update({ EmailTable.uidAndFolderName eq originalFullUid }) {
                    it[this.uidAndFolderName] = newFullUidInInbox
                }
            }
            onDatabaseUpdate()
            notification.toInfo(title = "Email moved to Inbox")
        } catch (e: MailConnectException) {
            showErrorNotification("Failed to move email to Inbox", e.message ?: "Unknown error")
        } catch (e: FolderNotFoundException) {
            showErrorNotification("Failed to move email to Inbox. Folder not found?", e.message ?: "Unknown error")
        } catch (e: Exception) {
            showErrorNotification("Failed to move email to Inbox", e.message ?: "An unexpected error occurred")
        } finally {
            try {
                inboxFolder?.close(true) // true to expunge deleted messages if copy+delete happened
                imapFolder?.close(true) // true to expunge the original moved message
                store?.close()
            } catch (_: IllegalStateException) {
                // ignore "This operation is not allowed on a closed folder"
            }
        }
    }


    /**
     * Trash the given email on the IMAP server and update the local database.
     */
    internal fun trashEmail(email: EmailMessage, onTrashFail: () -> Unit) {
        val trash = settings.email.folderTypes.trash()
        if (trash == null) {
            showErrorNotification("No Trash folder defined")
            return
        }
        val notification = showLoadingNotification("Trashing email...", email.subject)
        var store: Store? = null
        var imapFolder: IMAPFolder? = null
        var trashFolder: IMAPFolder? = null
        var updatedNotification = false
        try {
            store = loginAndGetStore(showErrorNotifications = true)
            val folderName = email.folderName
            imapFolder = store!!.getFolder(folderName) as IMAPFolder
            NetworkActivity.addFakedEntry(
                "Opening email folder $folderName as read/write to trash ${email.subject}",
                FakedNetworkActivityStatus.Info
            )
            imapFolder.open(Folder.READ_WRITE)

            val message = imapFolder.getMessageByUID(email.uid.toLong())
            if (message == null) {
                transaction(EmailDatabase.database) {
                    EmailTable.deleteWhere { uidAndFolderName eq email.getFullUid() }
                }
                onTrashFail()
                notification.toError(title = "Failed to trash: Not found in ${email.folderName}")
                updatedNotification = true
                return
            }

            trashFolder = store.getFolder(trash.serverFolderName) as IMAPFolder
            NetworkActivity.addFakedEntry(
                "Opening email folder ${trash.serverFolderName} as read/write to trash ${email.subject}",
                FakedNetworkActivityStatus.Info
            )
            if (!trashFolder.exists()) {
                if (!trashFolder.create(Folder.HOLDS_MESSAGES)) {
                    notification.toError(title = "Failed to trash: Could not create Trash folder '${trash.serverFolderName}'.")
                    updatedNotification = true
                    return
                }
            }
            trashFolder.open(Folder.READ_WRITE) // Open after ensuring it exists

            imapFolder.moveMessages(arrayOf(message), trashFolder)

            transaction(EmailDatabase.database) {
                val fullUid = getFullUid(email.uid, email.folderName)
                EmailTable.update({ EmailTable.uidAndFolderName eq fullUid }) {
                    it[this.wasTrashed] = true
                }
            }
            updatedNotification = true
            notification.toInfo(title = "Email trashed", durationMillis = 500L)
        } catch (e: Exception) {
            notification.toError("Failed to trash email", e.message ?: "Unknown error")
            updatedNotification = true
            onTrashFail()
        } finally {
            try {
                trashFolder?.close(true)
                imapFolder?.close(true)
                store?.close()
            } catch (_: IllegalStateException) {
                // ignore 'This operation is not allowed on a closed folder'
            }
            if (!updatedNotification) {
                notification.dismiss()
            }
        }
    }


    /**
     * Remember to close the store once done.
     */
    internal fun loginAndGetStore(showErrorNotifications: Boolean): Store? {
        val user = settings.email.address
        val pass = settings.email.password
        val host = settings.email.imapServer
        val protocol = "imap"
        val port = "993"
        System.setProperty("mail.mime.ignoreunknownencoding", "true")
        val properties = Properties().apply {
            this["mail.store.protocol"] = "imap"
            this["mail.$protocol.host"] = host
            this["mail.$protocol.port"] = port
            this["mail.imap.socketFactory.class"] = "javax.net.ssl.SSLSocketFactory"
            setProperty("mail.imap.socketFactory.fallback", "false")
            setProperty("mail.imap.socketFactory.port", port)
            setProperty("mail.mime.parameters.strict", "false")
        }
        val store = Session.getDefaultInstance(properties).store
        try {
            store.connect(user, pass)
            return store
        } catch (_: AuthenticationFailedException) {
            if (showErrorNotifications) {
                showErrorNotification("Email login failed", "Incorrect username or password for $user")
            }
            store.close() // Ensure store is closed on failure
            return null
        } catch (e: MessagingException) {
            if (showErrorNotifications) {
                showErrorNotification("Email connection failed", e.message ?: "Unknown IMAP connection error")
            }
            store.close() // Ensure store is closed on failure
            return null
        }
    }

    fun updateHasUnreadInboxEmails() {
        val inbox = settings.email.folderTypes.inbox()
        if (inbox == null) {
            hasUnreadInboxEmails = false
            return
        }
        hasUnreadInboxEmails = getAllUntrashedForFolderType(inbox).any { !it.isSeen }
    }
}