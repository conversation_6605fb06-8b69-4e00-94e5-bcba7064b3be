package dima.apps.email

import GlobalEvent
import com.sun.mail.imap.IMAPFolder
import com.sun.mail.imap.SortTerm
import com.sun.mail.util.MailConnectException
import dima.apps.AppType
import dima.apps.email.Email.getFullUid
import dima.apps.email.Email.loginAndGetStore
import dima.apps.email.database.EmailDatabase
import dima.apps.email.database.EmailTable
import dima.apps.email.models.allFolderNames
import dima.apps.email.models.regularlySyncedFolderNames
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.NetworkActivity.updateFaked
import dima.apps.notifications.showErrorNotification
import dima.database.transactionToAvoidBusySqlite
import dima.dateTime.DateTimeUtils
import dima.globalState.GlobalState
import dima.settings
import dima.utils.ignoreAllExceptions
import globalEvent
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.transactions.transaction
import java.time.LocalDateTime
import javax.mail.Folder
import javax.mail.FolderClosedException
import javax.mail.Message
import javax.mail.Store
import javax.mail.UIDFolder
import kotlin.math.abs
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

class DownloadAllEmailFoldersAndDeleteUnreadEmailsLocallyThread : Thread() {
    override fun run() {
        val sleepDuration = 30.minutes.inWholeMilliseconds
        while (true) {
            sleep(sleepDuration)
            downloadAllFoldersAndDeleteUnreadEmailsLocally()
        }
    }
}

/**
 * This does not show any notifications, but logs issues to [NetworkActivity].
 *
 * This is relatively slow, depending on email count and should be called sparingly.
 */
private fun downloadAllFoldersAndDeleteUnreadEmailsLocally() {
    if (settings.email.folderTypes.isEmpty()) {
        return
    }
    val folderNames = settings.email.folderTypes.allFolderNames()
    if (folderNames == null) {
        return
    }
    val localFullUids: List<String> = transaction(EmailDatabase.database) {
        EmailTable.select(EmailTable.uidAndFolderName).toList().map { it[EmailTable.uidAndFolderName] }
    }
    folderNames.forEach { serverFolderName ->
        var imapFolder: IMAPFolder? = null
        var store: Store? = null
        try {
            store = loginAndGetStore(showErrorNotifications = false)
            imapFolder = store!!.getFolder(serverFolderName) as IMAPFolder
            val uf = imapFolder as UIDFolder
            if (!imapFolder.exists()) {
                showErrorNotification("Email folder does not exist?", serverFolderName)
                return@forEach
            }
            val log = NetworkActivity.addFakedEntry(
                "Opening email folder $serverFolderName as read only for local deletion",
                FakedNetworkActivityStatus.Info
            )
            imapFolder.open(Folder.READ_ONLY)
            val emails: List<Message> = imapFolder.getSortedMessages(arrayOf(SortTerm.ARRIVAL)).toList()
            val existingFullUids = mutableSetOf<String>()
            for (email in emails) {
                val uid = uf.getUID(email).toString()
                existingFullUids.add(getFullUid(uid, serverFolderName))
            }
            var deletionCount = 0
            transactionToAvoidBusySqlite(EmailDatabase.database) {
                localFullUids
                    .filter { it.endsWith("@$serverFolderName") }
                    .forEach { localFullUid ->
                        if (localFullUid !in existingFullUids) {
                            deletionCount++
                            EmailTable.deleteWhere { uidAndFolderName eq localFullUid }
                        }
                    }
            }
            if (deletionCount > 0) {
                log.updateFaked(
                    responseBody = "Deleted $deletionCount emails in $serverFolderName which exist locally, but not on the server"
                )
            }
        } catch (e: FolderClosedException) {
            NetworkActivity.addFakedEntry(
                "FolderClosedException on trying to check if emails need to be deleted locally, which do not " +
                        "exist on the server. On $serverFolderName",
                responseBody = e.message,
                status = FakedNetworkActivityStatus.Error
            )
        } catch (e: MailConnectException) {
            NetworkActivity.addFakedEntry(
                "MailConnectException on trying to check if emails need to be deleted locally, which do not " +
                        "exist on the server. On $serverFolderName",
                responseBody = e.message,
                status = FakedNetworkActivityStatus.Error
            )
        } finally {
            ignoreAllExceptions {
                imapFolder?.close(true)
            }
            ignoreAllExceptions {
                store?.close()
            }
        }
    }
}

class UnreadEmailsThread : Thread() {
    override fun run() {
        if (settings.email.folderTypes.isEmpty()) {
            return
        }
        val regularlySyncedFolderNames = settings.email.folderTypes.regularlySyncedFolderNames()
        if (regularlySyncedFolderNames == null) {
            return
        }

        fun download() {
            var hasAnyUnread = false
            downloadFolders(
                showNotifications = false,
                serverFolderNames = regularlySyncedFolderNames,
                onNew = { hasAnyUnread = true })
            if (hasAnyUnread && GlobalState.app == AppType.Email) {
                globalEvent = GlobalEvent.ReloadData
            }
        }

        EmailDatabase.setup()
        val sleepDuration = 30.seconds.inWholeMilliseconds
        while (true) {
            if (GlobalState.email.lastUpdate == null) {
                download()
            } else {
                val lastUpdate: LocalDateTime = DateTimeUtils.fromIsoToLocalDateTime(GlobalState.email.lastUpdate!!)
                val duration = DateTimeUtils.differenceToNowInIso(lastUpdate)
                if (abs(duration.toMinutes()) >= 10) {
                    download()
                } else {
                    Email.updateHasUnreadInboxEmails()
                }
            }
            sleep(sleepDuration)
        }
    }
}
