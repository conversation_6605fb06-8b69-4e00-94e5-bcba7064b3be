package dima.apps.email

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import dima.apps.notifications.showErrorNotification
import dima.os.openUrl
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node

internal data class NodeInfo(
    val size: TextUnit,
    val weight: FontWeight,
    val isHeading: Boolean
)

/**
 * Determine font weight and size by traversing upwards from the TextNode's immediate parent
 *
 * @return if no size or weight is found, returns default values
 */
internal fun extractFontWeightAndSize(parentNode: Node): NodeInfo {
    var determinedWeight: FontWeight? = null
    var determinedSize: TextUnit? = null
    var ancestorNode: Node? = parentNode
    var isHeading = false

    while (ancestorNode != null && ancestorNode is Element) {
        val element = ancestorNode
        val tag = element.normalName()
        val styleAttribute = element.attr("style")

        // Font Size from h1-h3 (innermost wins)
        if (determinedSize == null) { // Only set if not already determined by a closer (inner) H tag
            when (tag) {
                "h1" -> {
                    determinedSize = 24.sp
                    isHeading = true
                }

                "h2" -> {
                    determinedSize = 20.sp
                    isHeading = true
                }

                "h3" -> {
                    determinedSize = 18.sp
                    isHeading = true
                }
            }
        }

        // Font Weight from style, strong/b, or h-tag default (innermost wins)
        if (determinedWeight == null) { // Only set if not already determined by a closer (inner) style/tag
            val fontWeightFromStyle = parseFontWeightFromStyle(styleAttribute)
            if (fontWeightFromStyle != null) {
                determinedWeight = if (fontWeightFromStyle >= 500) FontWeight.Bold else FontWeight.Normal
            } else if (tag == "strong" || tag == "b") {
                determinedWeight = FontWeight.Bold
            } else if ((tag == "h1" || tag == "h2" || tag == "h3")) {
                // This is the default weight for an H tag if no more specific weight was found.
                determinedWeight = FontWeight.Bold
            }
        }
        ancestorNode = ancestorNode.parentNode()
    }

    return NodeInfo(
        size = determinedSize ?: 14.sp,
        weight = determinedWeight ?: FontWeight.Normal,
        isHeading = isHeading
    )
}

internal fun parseFontWeightFromStyle(styleString: String): Int? {
    if (styleString.isBlank()) return null
    val parts = styleString.split(';')
    for (part in parts) {
        val declaration = part.trim()
        if (declaration.startsWith("font-weight", ignoreCase = true)) {
            val value = declaration.substringAfter(':').trim().lowercase()
            return when (value) {
                "bold" -> 700
                "normal" -> 400
                // Handle numeric weights like 500, 600 etc.
                else -> value.toIntOrNull()
            }
        }
    }
    return null
}

internal fun AnnotatedString.trimTrailingNewlines(): AnnotatedString {
    if (this.text.isEmpty()) {
        return this
    }
    var i = this.text.length - 1
    while (i >= 0 && this.text[i] == '\n') {
        i--
    }
    // If i is (original length - 1 - number of trailing newlines),
    // then i + 1 is the new length.
    return if (i < this.text.length - 1) {
        // Ensure we don't create a negative range if the string was all newlines
        if (i < 0) AnnotatedString("") else this.subSequence(0, i + 1)
    } else {
        this // No newlines to trim, or string was empty/no trailing newlines
    }
}

internal fun openUrlHref(href: String) {
    if (href.startsWith("www.")) {
        openUrl("https://$href")
    } else if (href.startsWith("http://") || href.startsWith("https://")) {
        openUrl(href)
    } else {
        showErrorNotification("Can't open link", href)
    }
}

internal fun AnnotatedString.Builder.ensureLeadingNewlinesSmartly(desiredTotalNewlines: Int) {
    val currentText = this.toAnnotatedString().text
    if (currentText.isBlank()) {
        return
    }
    var existingNewlines = 0
    for (i in currentText.length - 1 downTo 0) {
        if (currentText[i] == '\n') {
            existingNewlines++
        } else {
            break
        }
    }
    val newlinesToAdd = desiredTotalNewlines - existingNewlines
    if (newlinesToAdd > 0) {
        repeat(newlinesToAdd) {
            this.append("\n")
        }
    }
}

internal fun Char.isWhitespaceOrPunctuation(): Boolean {
    return this.isWhitespace() || this.toString().matches(Regex("\\p{Punct}"))
}