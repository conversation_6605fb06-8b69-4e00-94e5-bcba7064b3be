package dima.apps.email.processing

import com.sun.mail.imap.IMAPBodyPart
import com.sun.mail.imap.IMAPInputStream
import com.sun.mail.util.BASE64DecoderStream
import com.sun.mail.util.QPDecoderStream
import dima.apps.notifications.showErrorNotification
import javax.mail.Message
import javax.mail.internet.MimeMultipart

/**
 * Data class to hold extracted email content.
 */
data class EmailContent(
    val plainText: String?,
    val html: String?
)

/**
 * Handles extraction and processing of email content (plain text and HTML).
 */
object EmailContentProcessor {

    /**
     * Extracts plain text and HTML content from an email message.
     *
     * @param email The email message to extract content from
     * @return EmailContent containing the extracted plain text and HTML
     */
    fun extractEmailContent(email: Message): EmailContent {
        var plainText: String? = null
        var html: String? = null

        fun fillPlainTextAndHtml(contentType: String, content: Any) {
            val lowerCaseContentType = contentType.lowercase()
            when (content) {
                is String -> {
                    processStringContent(lowerCaseContentType, content) { newPlainText, newHtml ->
                        plainText = handlePlainTextUpdate(plainText, newPlainText)
                        html = handleHtmlUpdate(html, newHtml, content, plainText)
                    }
                }

                is IMAPBodyPart -> {
                    fillPlainTextAndHtml(content.contentType, content.content)
                }

                is MimeMultipart -> {
                    processMimeMultipart(content, ::fillPlainTextAndHtml)
                }

                is BASE64DecoderStream, is IMAPInputStream -> {
                    // These streams don't contain text content
                }

                is QPDecoderStream -> {
                    processQPDecoderStream(lowerCaseContentType, content) { newPlainText, newHtml ->
                        plainText = handlePlainTextUpdate(plainText, newPlainText)
                        html = handleHtmlUpdate(html, newHtml, content.readAllBytes().decodeToString(), plainText)
                    }
                }

                else -> {
                    throw Exception("Unknown content class: $content on $this")
                }
            }
        }

        fillPlainTextAndHtml(email.contentType, email.content)
        return EmailContent(plainText, html)
    }

    /**
     * Processes string content and determines if it's plain text or HTML.
     */
    private fun processStringContent(
        lowerCaseContentType: String,
        content: String,
        onContentExtracted: (plainText: String?, html: String?) -> Unit
    ) {
        when {
            lowerCaseContentType.startsWith("text/plain") || lowerCaseContentType.startsWith("text/xml") -> {
                val trimmed = content.trim()
                if (trimmed.isNotBlank()) {
                    onContentExtracted(content, null)
                }
            }

            lowerCaseContentType.startsWith("text/html") -> {
                val trimmed = content.trim()
                if (trimmed.isNotBlank()) {
                    onContentExtracted(null, content)
                }
            }

            else -> {
                throw Exception("Unknown content type: $lowerCaseContentType")
            }
        }
    }

    /**
     * Processes MimeMultipart content by iterating through all parts.
     */
    private fun processMimeMultipart(
        content: MimeMultipart,
        fillPlainTextAndHtml: (String, Any) -> Unit
    ) {
        for (i in 0 until content.count) {
            val part = content.getBodyPart(i)
            fillPlainTextAndHtml(part.contentType, part.content)
        }
    }

    /**
     * Processes QPDecoderStream content for plain text or HTML.
     */
    private fun processQPDecoderStream(
        lowerCaseContentType: String,
        content: QPDecoderStream,
        onContentExtracted: (plainText: String?, html: String?) -> Unit
    ) {
        when {
            lowerCaseContentType.startsWith("text/plain") -> {
                val decodedContent = content.readAllBytes().decodeToString()
                val trimmed = decodedContent.trim()
                if (trimmed.isNotBlank()) {
                    onContentExtracted(decodedContent, null)
                }
            }

            lowerCaseContentType.startsWith("text/html") -> {
                val decodedContent = content.readAllBytes().decodeToString()
                val trimmed = decodedContent.trim()
                if (trimmed.isNotBlank()) {
                    onContentExtracted(null, decodedContent)
                }
            }
        }
    }

    /**
     * Handles updating plain text content, concatenating if there's already content.
     */
    private fun handlePlainTextUpdate(currentPlainText: String?, newPlainText: String?): String? {
        return when {
            newPlainText == null -> currentPlainText
            currentPlainText != null -> "$currentPlainText\n\n-----------------------------------\n\n$newPlainText"
            else -> newPlainText
        }
    }

    /**
     * Handles updating HTML content, showing error if there's already HTML content.
     */
    private fun handleHtmlUpdate(
        currentHtml: String?,
        newHtml: String?,
        originalContent: String,
        plainText: String?
    ): String? {
        return when {
            newHtml == null -> currentHtml
            currentHtml != null -> {
                showErrorNotification(
                    "Already filled HTML on String content",
                    "$originalContent\n\n$plainText\n\n$currentHtml"
                )
                currentHtml
            }

            else -> newHtml
        }
    }
}