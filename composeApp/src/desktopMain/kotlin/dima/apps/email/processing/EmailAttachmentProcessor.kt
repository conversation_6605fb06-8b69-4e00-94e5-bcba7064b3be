package dima.apps.email.processing

import com.sun.mail.util.BASE64DecoderStream
import com.sun.mail.util.DecodingException
import com.sun.mail.util.QPDecoderStream
import dima.apps.email.Email.cacheDirectory
import dima.apps.email.EmailAttachment
import dima.apps.email.decodeViaMimeUtility
import dima.apps.notifications.showErrorNotification
import dima.utils.FileSystem
import dima.utils.asSha256Hash
import dima.utils.decodeToBytes
import java.io.File
import javax.mail.Message
import javax.mail.internet.MimeMultipart

/**
 * Handles extraction and processing of email attachments, including inline images.
 */
object EmailAttachmentProcessor {

    /**
     * Extracts all attachments from an email message.
     * Disposition inline images are downloaded to the cache and are displayed inline in the Email preview.
     *
     * @param email The email message to extract attachments from
     * @param fullUid The full UID of the email for cache file naming
     * @return List of EmailAttachment objects
     */
    fun extractAttachments(email: Message, fullUid: String): List<EmailAttachment> {
        val attachments = mutableListOf<EmailAttachment>()

        fun processContent(content: Any, fileName: String?, disposition: String? = null) {
            when (content) {
                is MimeMultipart -> {
                    processMimeMultipart(content, ::processContent)
                }

                is BASE64DecoderStream -> {
                    val inlineImagePath = processBase64DecoderStream(content, fileName, disposition, fullUid)
                    attachments.add(
                        EmailAttachment(
                            decodeViaMimeUtility(fileName),
                            disposition,
                            inlineImagePath,
                        )
                    )
                }

                is com.sun.mail.imap.IMAPInputStream -> {
                    // ignore, this has bytes set via content.readAllBytes(), but no file name and disposition
                }

                is String, is com.sun.mail.imap.IMAPBodyPart -> {
                    // These content types don't represent attachments
                }

                is QPDecoderStream -> {
                    val inlineImagePath = processQPDecoderStream(content, fileName, disposition, fullUid)
                    attachments.add(
                        EmailAttachment(
                            decodeViaMimeUtility(fileName),
                            disposition,
                            inlineImagePath,
                        )
                    )
                }

                else -> {
                    throw Exception("Unknown content type: $content")
                }
            }
        }

        try {
            processContent(email.content, email.fileName, email.disposition)
        } catch (e: DecodingException) {
            showErrorNotification(
                "Warning: Skipping malformed BASE64 content in email attachment",
                e.message
            )
            processContent("[Malformed attachment content]", email.fileName, email.disposition)
        }

        return attachments
    }

    /**
     * Processes MimeMultipart content by iterating through all body parts.
     */
    private fun processMimeMultipart(
        content: MimeMultipart,
        processContent: (Any, String?, String?) -> Unit
    ) {
        for (i in 0 until content.count) {
            val part = content.getBodyPart(i)
            try {
                processContent(part.content, part.fileName, part.disposition)
            } catch (_: DecodingException) {
                processContent("DecodingException", part.fileName, part.disposition)
            }
        }
    }

    /**
     * Processes BASE64DecoderStream content and handles inline image caching.
     *
     * @return The file path if it's an inline image, null otherwise
     */
    private fun processBase64DecoderStream(
        content: BASE64DecoderStream,
        fileName: String?,
        disposition: String?,
        fullUid: String
    ): String? {
        return if (disposition == "inline") {
            processInlineImage(
                fileName = fileName,
                fullUid = fullUid,
                dataProvider = { content.decodeToBytes() }
            )
        } else {
            null
        }
    }

    /**
     * Processes QPDecoderStream content and handles inline image caching.
     *
     * @return The file path if it's an inline image, null otherwise
     */
    private fun processQPDecoderStream(
        content: QPDecoderStream,
        fileName: String?,
        disposition: String?,
        fullUid: String
    ): String? {
        return if (disposition == "inline") {
            processInlineImage(
                fileName = fileName,
                fullUid = fullUid,
                dataProvider = { content.readAllBytes() }
            )
        } else {
            null
        }
    }

    /**
     * Processes inline images by caching them to disk if they are supported image formats.
     *
     * @param fileName The original filename of the attachment
     * @param fullUid The full UID of the email
     * @param dataProvider A function that provides the binary data of the image
     * @return The absolute path to the cached image file, or null if not a supported image format
     */
    private fun processInlineImage(
        fileName: String?,
        fullUid: String,
        dataProvider: () -> ByteArray
    ): String? {
        val extension = if (fileName == null) {
            ""
        } else {
            "." + File(fileName).extension
        }

        val lowerExtension = extension.lowercase()

        return if (isSupportedImageFormat(lowerExtension)) {
            cacheInlineImage(fileName, fullUid, extension, dataProvider)
        } else {
            null
        }
    }

    /**
     * Checks if the given file extension represents a supported image format.
     */
    private fun isSupportedImageFormat(lowerExtension: String): Boolean {
        return lowerExtension in setOf(
            ".jpg", ".jpeg", ".png", ".gif",
            ".bmp", ".webp", ".svg"
        )
    }

    /**
     * Caches an inline image to disk and returns the file path.
     *
     * @param fileName The original filename
     * @param fullUid The full UID of the email
     * @param extension The file extension
     * @param dataProvider A function that provides the binary data
     * @return The absolute path to the cached file
     */
    private fun cacheInlineImage(
        fileName: String?,
        fullUid: String,
        extension: String,
        dataProvider: () -> ByteArray
    ): String {
        val hash = ("$fullUid $fileName").asSha256Hash()
        val baseFileName = "${fullUid.replace("@", "-")}-inline-image-$hash$extension"
        val firstFileName = File(cacheDirectory, baseFileName)

        return if (firstFileName.exists()) {
            firstFileName.absolutePath
        } else {
            val data = dataProvider()
            val newFile = FileSystem.getUniqueFileByFileName(cacheDirectory, baseFileName)
            newFile.writeBytes(data)
            newFile.absolutePath
        }
    }
}