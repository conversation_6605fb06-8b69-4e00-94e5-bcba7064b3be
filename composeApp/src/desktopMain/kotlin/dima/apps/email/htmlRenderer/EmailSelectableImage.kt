package dima.apps.email.htmlRenderer

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.unit.dp
import dima.apps.email.openUrlHref
import dima.apps.email.selectedLinkBackground
import dima.color.TailwindCssColors
import dima.utils.clickableWithoutBackgroundRipple

/**
 * The image modifier for images, either with a parent anchor tag or not.
 */
@Composable
internal fun createImageModifier(href: String?, isSelected: Boolean): Modifier {
    return Modifier
        .padding(bottom = 12.dp)
        .then(
            if (href == null) {
                Modifier
            } else {
                Modifier
                    .drawBehind {
                        val strokeWidth = 8f
                        if (isSelected) {
                            drawRoundRect(
                                selectedLinkBackground,
                                topLeft = Offset(-20f, -20f),
                                cornerRadius = CornerRadius(20f),
                                size = Size(this.size.width + 40f, this.size.height + 40f)
                            )
                        } else {
                            val xOffset = 15f
                            drawLine(
                                TailwindCssColors.orange500,
                                Offset(-xOffset, -xOffset),
                                Offset(-xOffset, size.height + xOffset),
                                strokeWidth
                            )
                        }
                    }
            }
        )
        .then(
            if (href == null) {
                Modifier
            } else {
                Modifier.clickableWithoutBackgroundRipple {
                    openUrlHref(href)
                }
            })
}
