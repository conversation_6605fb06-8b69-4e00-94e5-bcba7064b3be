package dima.apps.email

import java.nio.charset.Charset
import java.nio.charset.spi.CharsetProvider
import java.util.*

/**
 * Just a dummy for later encodings.
 */
@Suppress("RedundantVisibilityModifier") // public for java.nio.charset.spi.CharsetProvider
public class UnknownCharsetProvider : CharsetProvider() {

    override fun charsetForName(charset: String): Charset {
        if (charset.equals(BAD_CHARSET, ignoreCase = true)) {
            return Charset.forName(GOOD_CHARSET)
        }
        return Charset.forName(GOOD_CHARSET)
    }

    override fun charsets(): Iterator<Charset> {
        return Collections.emptyIterator()
    }

    companion object {
        private const val BAD_CHARSET = "x-unknown"
        private const val GOOD_CHARSET = "iso-8859-1"
    }
}
