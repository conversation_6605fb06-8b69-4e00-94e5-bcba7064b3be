package dima.apps.email

import dima.apps.email.models.EmailMessage
import dima.settings

/**
 * Logic works off of [EmailMessage.isChildInThread] mainly which maps to the UI.
 *
 * @param threadMarkingSenders list of email addresses that enable thread marking, made a parameter to be testable
 */
internal fun markEmailOrThread(
    selectedEmailIndex: Int?,
    filteredEmails: List<EmailMessage>,
    markedEmailsIds: List<String>,
    changeIsPreviewSelected: (Boolean) -> Unit,
    changeMarkedEmailsIds: (List<String>) -> Unit,
    changeSelected: (Int) -> Unit,
    threadMarkingSenders: List<String> = settings.email.emailAddressesThreadMarkingSenders,
) {
    if (selectedEmailIndex == null) {
        return
    }
    val email = filteredEmails.getOrNull(selectedEmailIndex)
    if (email == null) {
        return
    }

    // Determine if the special thread marking logic applies
    val from = email.from.firstOrNull()?.email
    val shouldThreadMark = from != null && threadMarkingSenders.any { it.equals(from, ignoreCase = true) }

    if (!shouldThreadMark) {
        // Normal marking (single email)
        changeIsPreviewSelected(false)
        changeMarkedEmailsIds(
            if (markedEmailsIds.contains(email.getFullUid())) {
                markedEmailsIds - email.getFullUid()
            } else {
                (markedEmailsIds + email.getFullUid()).distinct()
            }
        )
        if (selectedEmailIndex + 1 < filteredEmails.size) {
            changeSelected(selectedEmailIndex + 1)
        }
        return
    }

    val emailsInThread = mutableListOf(email)
    if (email.isChildInThread) {
        var startIndex = selectedEmailIndex - 1
        while (true) {
            val nextIndex = startIndex
            if (nextIndex < 0) {
                break
            }
            val currentEmail = filteredEmails[nextIndex] // Use a different variable name
            if (!currentEmail.isChildInThread) {
                emailsInThread.add(0, currentEmail)
                break
            }
            emailsInThread.add(0, currentEmail)
            startIndex--
        }
        startIndex = selectedEmailIndex + 1 // Start from the email *after* the selected child
        while (true) {
            if (startIndex >= filteredEmails.size) {
                break
            }
            val currentEmail = filteredEmails[startIndex] // Use a different variable name
            if (!currentEmail.isChildInThread) {
                break
            }
            emailsInThread.add(currentEmail)
            startIndex++
        }
    } else { // Selected email is a parent
        val nextIndex = selectedEmailIndex + 1
        var currentIndex = nextIndex
        while (currentIndex < filteredEmails.size) {
            val emailBelow = filteredEmails[currentIndex]
            if (!emailBelow.isChildInThread) {
                break
            }
            emailsInThread.add(emailBelow)
            currentIndex++
        }
    }

    val allMarked = emailsInThread.all { it.getFullUid() in markedEmailsIds }
    val newMarked = markedEmailsIds.toMutableList()
    if (allMarked) {
        newMarked.removeAll { markedUid -> emailsInThread.any { markedUid == it.getFullUid() } }
    } else {
        newMarked.addAll(emailsInThread.map { it.getFullUid() })
    }
    changeMarkedEmailsIds(newMarked.distinct()) // Ensure distinct after addAll
    changeIsPreviewSelected(false)

    var nextSelectionTargetIndex = selectedEmailIndex // Default to current

    if (shouldThreadMark && emailsInThread.isNotEmpty()) {
        // Thread marking case
        val lastEmailOfProcessedThread = emailsInThread.last() // Last email of the identified thread
        // Find in the list *before* potential filtering due to read status change
        val indexOfLastInThread = filteredEmails.indexOfFirst { it.getFullUid() == lastEmailOfProcessedThread.getFullUid() }

        if (indexOfLastInThread != -1) { // If last email of thread is found
            nextSelectionTargetIndex = if (indexOfLastInThread + 1 < filteredEmails.size) {
                indexOfLastInThread + 1 // Select email after thread
            } else {
                indexOfLastInThread // Thread is at the end, select its last email
            }
        }
        // If not found, nextSelectionTargetIndex remains selectedEmailIndex (will be clamped by caller if list shrinks)
    } else { // Single email marking case (or thread of one that wasn't expanded)
        if (selectedEmailIndex + 1 < filteredEmails.size) {
            nextSelectionTargetIndex = selectedEmailIndex + 1
        }
        // If it's the last email, nextSelectionTargetIndex remains selectedEmailIndex.
        // Clamping/adjustment will happen in EmailApp if the list shrinks.
    }

    // Pass the calculated (or original) index.
    // The caller (EmailApp) will handle if this index becomes invalid due to list changes (e.g. newOnly filter).
    if (filteredEmails.isNotEmpty()) {
        changeSelected(nextSelectionTargetIndex.coerceIn(0, filteredEmails.size - 1))
    } else if (selectedEmailIndex != 0) { // If list became empty, try to select 0
        changeSelected(0) // EmailApp will likely turn this into null for `selected`
    }
}