package dima.apps.email

import GlobalStyling
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.Paperclip
import dima.dateTime.DateTimeFormat
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * @param hasAttachments if the email has any attachments, regardless of disposition (behaves like Gmail)
 */
@Composable
fun EmailLeftCardDate(localDateTime: LocalDateTime, fontWeight: FontWeight?, hasAttachments: Boolean) {
    val today: LocalDate = LocalDate.now()
    val date = localDateTime.toLocalDate()
    var d = when (date) {
        today -> localDateTime.format(DateTimeFormat.hourColonMinute)
        today.minusDays(1) -> "Gestern"
        else -> localDateTime.format(DateTimeFormat.germanDateWithMonthAbbreviated)
    }
    if (today.year != date.year) {
        d = d + " " + date.year
    }
    val color = GlobalStyling.getTextColor()
    if (hasAttachments) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp),
            modifier = Modifier.padding(start = 12.dp)
        ) {
            Icon(
                Lucide.Paperclip,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(13.dp)
            )
            Text(
                d,
                color = color,
                textAlign = TextAlign.Right,
                fontWeight = fontWeight,
                fontSize = 12.sp,
            )
        }
    } else {
        Text(
            d,
            color = color,
            textAlign = TextAlign.Right,
            fontWeight = fontWeight,
            fontSize = 12.sp,
            modifier = Modifier.padding(start = 12.dp)
        )
    }
}