package dima.apps.email

import com.sun.mail.imap.IMAPFolder
import com.sun.mail.imap.SortTerm
import com.sun.mail.util.DecodingException
import dima.apps.email.Email.emailFullIdWhitelist
import dima.apps.email.Email.getFullUid
import dima.apps.email.connection.EmailFolderConnectionManager
import dima.apps.email.database.EmailDatabase
import dima.apps.email.database.EmailDatabaseOperations
import dima.apps.email.database.EmailTable
import dima.apps.email.models.EmailNewDataInfo
import dima.apps.email.processing.EmailAttachmentProcessor
import dima.apps.email.processing.EmailContent
import dima.apps.email.processing.EmailContentProcessor
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.notifications.showErrorNotification
import dima.database.transactionToAvoidBusySqlite
import org.jetbrains.exposed.sql.selectAll
import javax.mail.Flags
import javax.mail.Flags.Flag
import javax.mail.Folder
import javax.mail.Message
import javax.mail.UIDFolder

/**
 * @param maxKnownEmailsBeforeStoppingDownload stop downloading emails after this many known emails.
 *   If null, never abort
 * @param onNew called with the subject of the new email
 */
fun downloadFolders(
    serverFolderNames: List<String>,
    maxKnownEmailsBeforeStoppingDownload: Int? = 3,
    showNotifications: Boolean,
    onNew: (String) -> Unit
) {
    EmailFolderConnectionManager.downloadFolders(
        serverFolderNames = serverFolderNames,
        maxKnownEmailsBeforeStoppingDownload = maxKnownEmailsBeforeStoppingDownload,
        showNotifications = showNotifications,
        onNew = onNew
    )
}

/**
 * Disposition inline images are downloaded to the cache and are displayed inline in the Email preview.
 *
 * @param maxKnownEmailsBeforeStoppingDownload if null, never abort
 * @param onNew called with the subject of the new email
 */
internal fun downloadFolder(
    serverFolderName: String,
    imapFolder: IMAPFolder,
    maxKnownEmailsBeforeStoppingDownload: Int?,
    onNew: (EmailNewDataInfo) -> Unit
) {


    val uf = imapFolder as UIDFolder
    if (!imapFolder.exists()) {
        throw Exception("Folder: '$serverFolderName' not found")
    }
    NetworkActivity.addFakedEntry(
        "Opening email folder $serverFolderName as read/write",
        FakedNetworkActivityStatus.Info
    )
    imapFolder.open(Folder.READ_WRITE)
    // reverse so index 0 contains the latest message
    var knownEmails = 0
    imapFolder.getHighestModSeq() // This seems to be important for some servers to get correct UIDs or flags
    val emails: List<Message> = imapFolder.getSortedMessages(arrayOf(SortTerm.ARRIVAL)).reversed()
    val unreadEmails = mutableListOf<Message>()
    val total = emails.size
    var i = 0
    for (email in emails) {
        i++
        val uid = uf.getUID(email).toString()
        val fullUid = getFullUid(uid, serverFolderName)

        if (emailFullIdWhitelist != null && fullUid !in emailFullIdWhitelist) {
            continue
        }
        var isNew = false
        val flags = email.flags
        val isSeen = flags.contains(Flag.SEEN)
        if (!isSeen) {
            unreadEmails.add(email)
        }
        var subject: String? = null // Initialize subject here to ensure it's always set before use
        transactionToAvoidBusySqlite(EmailDatabase.database) {
            isNew = EmailTable
                .selectAll()
                .where { EmailTable.uidAndFolderName eq fullUid }
                .empty()
            subject = email.subject // Fetch subject, potentially null
            if (isNew) {
                fun extractAttachments(): List<EmailAttachment> {
                    return EmailAttachmentProcessor.extractAttachments(email, fullUid)
                }

                val emailContent = try {
                    EmailContentProcessor.extractEmailContent(email)
                } catch (e: DecodingException) {
                    showErrorNotification("Failed to decode email", (e.message ?: "Unknown error") + fullUid)
                    EmailContent(null, null)
                }

                val attachments = extractAttachments()
                EmailDatabaseOperations.insertEmailToDatabase(
                    email = email,
                    fullUid = fullUid,
                    emailContent = emailContent,
                    attachments = attachments,
                    subject = subject
                )
            }
        }
        if (isNew) {
            onNew(EmailNewDataInfo(i, total, subject ?: "No Subject"))
        } else {
            if (maxKnownEmailsBeforeStoppingDownload != null &&
                knownEmails >= maxKnownEmailsBeforeStoppingDownload
            ) {
                break
            }
            knownEmails++
        }
    }
    if (unreadEmails.isNotEmpty()) {
        imapFolder.setFlags(unreadEmails.toTypedArray(), Flags(Flag.SEEN), true)
    }
}
