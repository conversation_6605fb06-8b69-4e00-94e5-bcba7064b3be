package dima.apps.email

import com.sun.mail.imap.IMAPBodyPart
import com.sun.mail.imap.IMAPFolder
import com.sun.mail.imap.IMAPInputStream
import com.sun.mail.util.BASE64DecoderStream
import com.sun.mail.util.MailConnectException
import dima.apps.AppType
import dima.apps.email.Email.loginAndGetStore
import dima.apps.email.database.EmailDatabase
import dima.apps.email.database.EmailTable
import dima.apps.email.models.EmailMessage
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.globalState.GlobalState
import dima.os.homeWithoutSlash
import dima.utils.FileSystem
import dima.utils.abbreviatePath
import dima.utils.decodeToBytes
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.transactions.transaction
import setCurrentApp
import java.io.File
import javax.mail.Folder
import javax.mail.Store
import javax.mail.internet.MimeMultipart

/**
 * One big issue is that some files like PDFs still have disposition "inline", so I copied the behavior
 * of Gmail which considers everything an attachment, regardless of disposition.
 *
 * @param fileName is often (?) null for inline disposition
 * @param disposition can be "inline" when the file is not a regular attachment
 * @param inlineImagePath the file system path to the downloaded image which has disposition "inline"
 */
@Serializable
data class EmailAttachment(val fileName: String?, val disposition: String?, val inlineImagePath: String?)

internal fun decodeAttachments(email: EmailMessage): List<EmailAttachment> {
    return Json.decodeFromString<List<EmailAttachment>>(email.attachments)
}

/**
 * Ignore attachments with disposition "inline".
 *
 * @param onDatabaseUpdate called when the email is not found in the folder
 */
internal fun downloadAttachmentsAndOpenInDired(email: EmailMessage, onDatabaseUpdate: () -> Unit) {
    var downloadFolder: File? = null

    fun extractAttachments(content: Any, fileName: String?) {
        when (content) {

            is MimeMultipart -> {
                for (i in 0 until content.count) {
                    val part = content.getBodyPart(i)
                    extractAttachments(part.content, part.fileName)
                }
            }

            is IMAPInputStream -> {
                // ignore, see other IMAPInputStream usages in this file for doc
            }

            is BASE64DecoderStream -> {
                val data = content.decodeToBytes()
                if (downloadFolder == null) {
                    downloadFolder =
                        FileSystem.createUniqueDirectory(File(homeWithoutSlash, "/Downloads"), email.subject)
                }
                val attachmentFileName =
                    decodeViaMimeUtility(fileName) ?: FileSystem.getUniqueFileByFileName(
                        downloadFolder,
                        "attachment"
                    ).name
                try {
                    File(downloadFolder, attachmentFileName).writeBytes(data)
                } catch (_: Exception) {
                    showErrorNotification(
                        "Failed to write attachment with file name: $attachmentFileName",
                        File(downloadFolder, attachmentFileName).abbreviatePath()
                    )
                }
            }

            is String, is IMAPBodyPart -> {}

            else -> {
                throw Exception("Unknown content type: $content")
            }
        }
    }

    val notification = showLoadingNotification("Downloading email attachments...", email.subject)
    var store: Store? = null
    var imapFolder: IMAPFolder? = null
    try {
        store = loginAndGetStore(showErrorNotifications = true)
        val folderName = email.folderName
        imapFolder = store!!.getFolder(folderName) as IMAPFolder
        NetworkActivity.addFakedEntry("Opening email folder $folderName as read", FakedNetworkActivityStatus.Info)
        imapFolder.open(Folder.READ_ONLY)
        val message = imapFolder.getMessageByUID(email.uid.toLong())
        if (message == null) {
            transaction(EmailDatabase.database) {
                EmailTable.deleteWhere { uidAndFolderName eq email.getFullUid() }
            }
            onDatabaseUpdate()
            notification.toError(
                title = "Failed to download attachments to email",
                message = "Not found in ${email.folderName}",
            )
            return
        }
        extractAttachments(message.content, message.fileName)
        if (downloadFolder == null) {
            notification.toInfo(title = "No attachments found to download or save.")
        } else {
            GlobalState.dired.setDirectory(downloadFolder.absolutePath)
            setCurrentApp(AppType.Dired)
            notification.toInfo(title = "Downloaded email attachments to ${downloadFolder.name}")
        }
    } catch (e: MailConnectException) {
        showErrorNotification("Failed to download attachments to email", e.message ?: "Unknown error")
    } catch (e: Exception) {
        showErrorNotification("Failed to download attachments", e.message ?: "Unknown error")
    } finally {
        imapFolder?.close(true)
        store?.close()
    }
}