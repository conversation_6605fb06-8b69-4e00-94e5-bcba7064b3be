package dima.apps.email

import GlobalStyling
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.BaselineShift
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dima.apps.email.htmlRenderer.createImageModifier
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.images.CachedInternetImage
import dima.images.CustomAsyncImage
import dima.utils.hasParentTag
import dima.utils.isInAnchor
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node
import org.jsoup.nodes.TextNode

private const val circle = "-  "

/**
 * I tried many approaches of Gemini 2.5 Pro, but it seems to be impossible to have a row of small images next to each.
 * So, they are all displayed below each other.
 */
@Composable
fun EmailHtmlRenderer(
    nodes: MutableList<Node>,
    attachments: List<EmailAttachment>,
    selectedLink: Int?,
    onAnchorLinks: (List<Node>) -> Unit,
) {
    val anchors by remember { mutableStateOf(mutableListOf<Node>()) }
    var tmpAnnotatedString = AnnotatedString.Builder()
    anchors.clear()

    nodes.forEach { node ->
        when (node) {
            is TextNode -> {
                val rawText = node.wholeText
                val parentNode = node.parentNode() ?: return@forEach // Direct parent of the TextNode
                if (parentNode.attr("style").replace(" ", "").contains("display:none")) {
                    return@forEach
                }

                if (rawText.isBlank()) {
                    val currentBufferText = tmpAnnotatedString.toAnnotatedString().text
                    val endsWithBulletAndSpace = currentBufferText.endsWith(circle)
                    if (rawText.contains('\n') && currentBufferText.isNotEmpty() && !currentBufferText.endsWith('\n') && !endsWithBulletAndSpace) {
                        tmpAnnotatedString.append('\n')
                    }
                    return@forEach
                }
                val cleanedText = rawText
                    .replace("\u00AD", "") // Remove soft hyphens
                    .replace("\u00A0", " ") // Replace NO-BREAK SPACE with regular space
                    .replace("\u200C", "") // Remove ZERO WIDTH NON-JOINER
                    .replace(Regex("\\s*\n\\s*"), "\n") // Normalize newlines
                    .replace(Regex(" {2,}"), " ") // Normalize multiple spaces
                    .trim()
                if (cleanedText.isEmpty()) {
                    return@forEach
                }
                val nodeInfo = extractFontWeightAndSize(parentNode)
                val directParentTag = parentNode.normalName() // Using direct parent for these
                val isAnchor = (directParentTag == "a")
                if (isAnchor) {
                    if (!anchors.contains(parentNode)) {
                        anchors.add(parentNode)
                    }
                }
                val isCurrentLink = isAnchor && anchors.indexOf(parentNode) == selectedLink

                val color = if (isCurrentLink) {
                    TailwindCssColors.white
                } else if (isAnchor) {
                    GlobalStyling.getOrangeColor()
                } else {
                    if (nodeInfo.weight == FontWeight.Bold && GlobalState.isDarkMode) {
                        // make brighter since in dark mode bolder fonts are harder to read
                        TailwindCssColors.gray200
                    } else {
                        GlobalStyling.getTextColor()
                    }
                }

                val isPreOrBlockquote = parentNode.hasParentTag("pre") || parentNode.hasParentTag("blockquote")
                val backgroundColor =
                    if (isPreOrBlockquote) {
                        if (GlobalState.isDarkMode) {
                            TailwindCssColors.gray700
                        } else {
                            TailwindCssColors.gray200
                        }
                    } else {
                        if (isCurrentLink) {
                            selectedLinkBackground
                        } else {
                            Color.Unspecified
                        }
                    }
                // --- End of original logic for other style properties ---

                val spanStyle = SpanStyle(
                    fontWeight = nodeInfo.weight,
                    fontSize = nodeInfo.size,
                    background = backgroundColor,
                    color = color,
                    baselineShift = if (isPreOrBlockquote) BaselineShift(0.1f) else null,
                    textDecoration = if (isAnchor && !isCurrentLink) TextDecoration.Underline else TextDecoration.None
                )

                if (isAnchor) {
                    val href = parentNode.attr("href")
                    tmpAnnotatedString.pushLink(LinkAnnotation.Url(href))
                    if (isCurrentLink) {
                        tmpAnnotatedString.append(
                            buildAnnotatedString {
                                withAnnotation(selectedLinkTagName, annotation = "ignored") {
                                    withStyle(spanStyle) {
                                        if (nodeInfo.isHeading) {
                                            append("\n")
                                        }
                                        append(cleanedText)
                                    }
                                }
                            }
                        )
                    } else {
                        tmpAnnotatedString.append(
                            buildAnnotatedString {
                                withStyle(spanStyle) {
                                    if (nodeInfo.isHeading) {
                                        append("\n")
                                    }
                                    append(cleanedText)
                                }
                            }
                        )
                    }
                    tmpAnnotatedString.pop()
                    if (cleanedText.isNotEmpty() && !cleanedText.last().isWhitespaceOrPunctuation()) {
                        tmpAnnotatedString.append(" ")
                    }
                } else { // Not an anchor
                    tmpAnnotatedString.append(
                        buildAnnotatedString {
                            withStyle(spanStyle) {
                                if (nodeInfo.isHeading) {
                                    append("\n")
                                }
                                append(cleanedText)
                            }
                        }
                    )

                    if (cleanedText.isNotEmpty() && !cleanedText.last().isWhitespaceOrPunctuation()) {
                        tmpAnnotatedString.append(" ")
                    }
                }
            }

            is Element -> {
                val tag = node.normalName()
                when (tag) {
                    "br" -> {
                        val currentText = tmpAnnotatedString.toAnnotatedString().text
                        if (currentText.isNotEmpty()) {
                            if (!currentText.endsWith("\n")) {
                                tmpAnnotatedString.append("\n")
                            } else if (!currentText.endsWith("\n\n")) {
                                tmpAnnotatedString.append("\n")
                            }
                        }
                    }

                    "li" -> {
                        tmpAnnotatedString.ensureLeadingNewlinesSmartly(1)
                        val bulletStyle = SpanStyle(color = GlobalStyling.getTextColor())
                        tmpAnnotatedString.append(
                            AnnotatedString(circle, spanStyle = bulletStyle)
                        )
                    }

                    "p", "div", "ul", "ol", "table", "tr", "td", // Ensure td is treated as block if needed
                    "blockquote", "h1", "h2", "h3", "h4", "h5", "h6",
                    "header", "footer", "section", "article", "aside", "nav" -> {
                        tmpAnnotatedString.ensureLeadingNewlinesSmartly(2)
                        if (tag == "table" && node.attr("class").contains("code")) { // GitLab specific
                            tmpAnnotatedString.ensureLeadingNewlinesSmartly(2)
                        }
                    }

                    "img" -> {
                        if (node.attr("style").replace(" ", "").contains("display:none")) {
                            return@forEach
                        }
                        val imageDimensions = node.getImageDimensions()
                        if (imageDimensions.isTrackingPixelOrTooSmallToRender()) {
                            return@forEach
                        }

                        val parentAnchorHref = node.isInAnchor()
                        if (parentAnchorHref != null) {
                            if (!anchors.contains(node)) anchors.add(node)
                        }
                        val isCurrentLink = parentAnchorHref != null && anchors.indexOf(node) == selectedLink
                        val src = node.attr("src").trim()

                        val currentTextBeforeImage = tmpAnnotatedString.toAnnotatedString()
                        if (currentTextBeforeImage.text.isNotEmpty()) {
                            EmailAppUiTextSelectedLink(currentTextBeforeImage)
                            tmpAnnotatedString = AnnotatedString.Builder()
                        }

                        if (src.startsWith("cid:")) {
                            val withoutCid = src.removePrefix("cid:")
                            val attachment =
                                attachments.find { att -> att.fileName == withoutCid && att.inlineImagePath != null }
                            if (attachment?.inlineImagePath != null) {
                                CustomAsyncImage(
                                    attachment.inlineImagePath,
                                    modifier = createImageModifier(parentAnchorHref, isCurrentLink)
                                        .sizeIn(
                                            maxWidth = imageDimensions.width?.dp ?: Dp.Unspecified,
                                            maxHeight = imageDimensions.height?.dp ?: Dp.Unspecified,
                                        )
                                )
                            } else {
                                val placeholderBuilder = AnnotatedString.Builder()
                                placeholderBuilder.ensureLeadingNewlinesSmartly(2)
                                placeholderBuilder.append(
                                    AnnotatedString(
                                        "[Image: ${src.removePrefix("cid:")}]",
                                        SpanStyle(fontWeight = FontWeight.Bold, color = GlobalStyling.getTextColor())
                                    )
                                )
                                EmailAppUiTextSelectedLink(placeholderBuilder.toAnnotatedString())
                                tmpAnnotatedString = AnnotatedString.Builder()
                                tmpAnnotatedString.ensureLeadingNewlinesSmartly(2)
                            }
                        } else {
                            CachedInternetImage(
                                src,
                                cacheDir = Email.cacheDirectory,
                                logCacheDir = Email.RELATIVE_CACHE_DIRECTORY,
                                displayCheckerboardWhenCloseToWhite = true,
                                modifier = createImageModifier(parentAnchorHref, isCurrentLink)
                                    .sizeIn(
                                        maxWidth = imageDimensions.width?.dp ?: Dp.Unspecified,
                                        maxHeight = imageDimensions.height?.dp ?: Dp.Unspecified,
                                    )
                            )
                        }
                    }
                }
            }
        }
    }

    val finalText = tmpAnnotatedString.toAnnotatedString()
    if (finalText.text.isNotEmpty()) {
        EmailAppUiTextSelectedLink(finalText.trimTrailingNewlines())
    }
    onAnchorLinks(anchors)
}