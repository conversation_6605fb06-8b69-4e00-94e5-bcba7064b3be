package dima.apps.email

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.Trash2
import dima.apps.email.models.EmailFolderTypeInfoPill
import dima.apps.email.models.EmailMessage
import dima.apps.email.models.trash
import dima.color.ColorHelper
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.settings
import dima.text.TextMarked
import dima.utils.clickableWithoutBackgroundRipple

/**
 * @param onClick passes the clicked email
 */
@Composable
internal fun EmailLeftCard(
    entry: EmailMessage,
    rootEmailForComparison: EmailMessage? = null,
    isSelected: Boolean,
    isPreviewSelected: Boolean,
    isMarked: Boolean,
    query: String,
    onTrash: () -> Unit,
    onMoveToInbox: () -> Unit,
    onDownloadAttachments: () -> Unit,
    onClick: (EmailMessage) -> Unit
) {
    val hideSubjectAndSender = remember(entry, rootEmailForComparison) {
        entry.isChildInThread && rootEmailForComparison != null &&
                entry.subject == rootEmailForComparison.subject
    }
    val showSubjectAndSender = !hideSubjectAndSender

    fun getBackgroundColor(): Color {
        return when {
            isSelected && isPreviewSelected -> GlobalStyling.getSelectedInactiveBackgroundColor()
            isSelected -> GlobalStyling.getSelectedBackgroundColor()
            isMarked -> GlobalStyling.getMarkedBackgroundColor()
            else -> GlobalStyling.getWindowBackgroundColor()
        }
    }

    EmailContextArea(
        entry = entry,
        onTrash = onTrash,
        onMoveToInbox = onMoveToInbox,
        onDownloadAttachments = onDownloadAttachments
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .then(
                    if (entry.isChildInThread) {
                        Modifier.padding(start = 33.dp)
                    } else {
                        Modifier
                    }
                )
                .alpha(if (!isSelected && isPreviewSelected) GlobalStyling.DISABLED_ALPHA else 1f)
                .background(
                    color = getBackgroundColor(),
                    shape = RoundedCornerShape(5.dp)
                )
                .clickableWithoutBackgroundRipple {
                    onClick(entry)
                }
                .padding(top = 12.dp, bottom = 12.dp, end = 8.dp)
        ) {
            val textWeight = if (entry.isSeen) null else FontWeight.SemiBold
            val firstFrom = entry.from.firstOrNull()
            @Suppress("IfThenToElvis") val textForColor = if (firstFrom == null) {
                " "
            } else {
                if (firstFrom.personal == null) {
                    firstFrom.email
                } else {
                    firstFrom.personal
                }
            }
            if (isMarked) {
                EmailAppUiCardCircle(
                    "✓",
                    TailwindCssColors.white,
                    isSmallSize = !showSubjectAndSender,
                    isMarked = true
                )
            } else {
                EmailAppUiCardCircle(
                    textForColor.substring(0, 1).uppercase(),
                    ColorHelper.getRandomByString(textForColor, 0.5f),
                    isSmallSize = !showSubjectAndSender,
                    isMarked = false
                )
            }
            val isTrashed = entry.folderName == settings.email.folderTypes.trash()?.serverFolderName
            val textColor = if (isTrashed) {
                GlobalStyling.getGrayColor()
            } else {
                if (entry.isSeen) {
                    GlobalStyling.getTextColor()
                } else {
                    if (GlobalState.isDarkMode) {
                        GlobalStyling.Dark.boldTextColor
                    } else {
                        GlobalStyling.getTextColor()
                    }
                }
            }
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp),
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (entry.from.isNotEmpty()) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.weight(1f)
                        ) {
                            if (isTrashed) {
                                Icon(
                                    Lucide.Trash2,
                                    contentDescription = null,
                                    tint = if (GlobalState.isDarkMode) TailwindCssColors.white else TailwindCssColors.black,
                                    modifier = Modifier
                                        .padding(end = 8.dp)
                                        .size(18.dp)
                                )
                            }
                            val first = entry.from.first()
                            TextMarked(
                                first.personal ?: first.email,
                                searchQuery = query,
                                fontWeight = textWeight,
                                color = textColor,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }
                    }
                    val attachments = decodeAttachments(entry)
                    EmailLeftCardDate(
                        entry.date,
                        textWeight,
                        attachments.isNotEmpty()
                    )
                }
                if (showSubjectAndSender) {
                    TextMarked(
                        entry.subject,
                        searchQuery = query,
                        fontWeight = textWeight,
                        color = textColor,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                    val infoPillColor = settings.email.infoPillsForServerNames[entry.folderName]
                    if (infoPillColor != null || entry.isDraft || entry.isDeleted || entry.isFlagged || entry.isAnswered) {
                        InfoPills(infoPillColor, entry)
                    }
                }
            }
        }
    }
}

@Composable
private fun InfoPills(
    infoPillColor: EmailFolderTypeInfoPill?,
    entry: EmailMessage
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (infoPillColor != null) {
            InfoPill(infoPillColor.pillDisplayName, infoPillColor.color)
        }
        if (entry.isDraft) {
            InfoPill("Draft", TailwindCssColors.yellow600)
        }
        if (entry.isDeleted) {
            InfoPill("Deleted", TailwindCssColors.teal600)
        }
        if (entry.isFlagged) {
            InfoPill("Flagged", TailwindCssColors.violet600)
        }
        if (entry.isAnswered) {
            InfoPill("Replied", TailwindCssColors.blue600)
        }
    }
}

@Composable
private fun InfoPill(text: String, color: Color) {
    Text(
        text,
        color = TailwindCssColors.white,
        modifier = Modifier
            .background(color, GlobalStyling.smallRoundedCorners)
            .padding(horizontal = 6.dp, vertical = 3.dp)
    )

}