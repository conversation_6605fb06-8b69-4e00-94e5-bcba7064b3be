package dima.apps.email

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.dired.addSelectedBorder
import dima.apps.email.models.EmailFrom
import dima.apps.email.models.EmailMessage
import dima.dateTime.DateTimeFormat
import dima.text.TextMarked
import dima.utils.DarkLightDivider
import dima.utils.scrollbarStyleThemed
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Node

@Composable
internal fun EmailAppUiRightSide(
    email: EmailMessage,
    isPreviewSelected: <PERSON><PERSON><PERSON>,
    rightScrollState: ScrollState,
    query: String,
    selectedLink: Int?,
    onAnchorsChange: (List<Node>) -> Unit,
) {
    Box(
        modifier = Modifier
            .padding(start = 12.dp, bottom = 12.dp)
            .fillMaxSize()
            .addSelectedBorder(shouldDrawBorder = isPreviewSelected)
    ) {
        SelectionContainer {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 12.dp, end = 12.dp)
                    .verticalScroll(rightScrollState)
            ) {
                val infoRowLeftWidth = if (email.attachments.isEmpty()) 60.dp else 72.dp
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.padding(
                        top = 12.dp,
                        start = 8.dp,
                        end = 8.dp,
                        bottom = 12.dp
                    )
                ) {
                    TextMarked(
                        email.subject.trim(),
                        searchQuery = query,
                        color = GlobalStyling.getTextColor(),
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 18.sp,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    EmailHeaderRow(
                        "Von",
                        email.from,
                        infoRowLeftWidth,
                        searchQuery = query.ifBlank { "" })
                    EmailHeaderRow("An", email.to, infoRowLeftWidth, searchQuery = null)
                    EmailHeaderRow("CC", email.cc, infoRowLeftWidth, searchQuery = null)
                    EmailHeaderRow("BCC", email.bcc, infoRowLeftWidth, searchQuery = null)
                    Row {
                        Text(
                            "Datum",
                            color = GlobalStyling.getGrayColor(),
                            modifier = Modifier.width(infoRowLeftWidth)
                        )
                        Text(
                            email.date.format(DateTimeFormat.humanDateMonthYearTime),
                            color = GlobalStyling.getTextColor()
                        )
                    }
                    val decodedAttachments: List<EmailAttachment> = decodeAttachments(email)
                    val attachmentFileNames = decodedAttachments
                        .map {
                            it.fileName ?: "???"
                        }
                    if (attachmentFileNames.isNotEmpty()) {
                        EmailHeaderRow(
                            "Anhang",
                            listOf(
                                EmailFrom(
                                    email = attachmentFileNames.joinToString(" • ") + " (${attachmentFileNames.size})",
                                    personal = null
                                )
                            ),
                            infoRowLeftWidth,
                            searchQuery = null
                        )
                    }
                    DarkLightDivider(
                        modifier = Modifier.padding(vertical = 15.dp)
                    )
                    Column {
                        onAnchorsChange(mutableListOf()) // Resetting anchors
                        if (email.html != null) {
                            val doc: Document = Jsoup.parse(email.html)
                            val nodes = doc.body().nodeStream().toList()
                            EmailHtmlRenderer(nodes, decodedAttachments, selectedLink, onAnchorLinks = {
                                onAnchorsChange(it)
                            })
                        }
                    }
                    val plainText = (email.plainText ?: "").trim()
                    if (plainText.isNotBlank() && email.html == null) {
                        Text(
                            plainText,
                            color = GlobalStyling.getTextColor(),
                            fontSize = 14.sp,
                            modifier = Modifier.padding(end = 12.dp)
                        )
                    }
                }
            }
        }
        VerticalScrollbar(
            style = scrollbarStyleThemed(),
            adapter = rememberScrollbarAdapter(scrollState = rightScrollState),
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .padding(
                    top = GlobalStyling.ScrollBar.outerPadding,
                    bottom = GlobalStyling.ScrollBar.outerPadding,
                    end = if (isPreviewSelected) GlobalStyling.ScrollBar.outerPadding else 0.dp
                )
                .fillMaxHeight(),
        )
    }
}