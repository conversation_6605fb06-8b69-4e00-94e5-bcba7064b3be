package dima.apps.email

import org.jsoup.nodes.Node

internal data class ImageDimensions(
    val width: Int?,
    val height: Int?
) {
    fun isTrackingPixelOrTooSmallToRender(): Boolean {
        return width != null && height != null && width <= 2 && height <= 2
    }
}

/**
 * Helper function to parse a specific dimension (e.g., "width" or "height") from a style string.
 */
internal fun parseDimensionFromStyle(styleString: String, dimensionName: String): Int? {
    // Regex to find "dimensionName: 123px" or "dimensionName: 45" etc.
    // It captures the numeric value. Ignores case for property name.
    // Allows for common units like px, %, em, pt, cm, mm, in, vw, vh but only extracts the number.
    // \b ensures we match "width" and not "max-width".
    val regex = Regex(
        """\b${Regex.escape(dimensionName)}\s*:\s*(\d+)\s*(?:px|%|em|pt|cm|mm|in|vw|vh)?\b""",
        RegexOption.IGNORE_CASE
    )
    return regex.find(styleString)?.groupValues?.get(1)?.toIntOrNull()
}

internal fun Node.getImageDimensions(): ImageDimensions {
    val widthStr = attr("width")
    val heightStr = attr("height")
    if ((widthStr == "1" && heightStr == "1") || widthStr == "0" || heightStr == "0") {
        return ImageDimensions(width = 0, height = 0)
    }
    val width: Int? = widthStr.toIntOrNull()
    val height: Int? = heightStr.toIntOrNull()
    if (width != null && height != null) {
        return ImageDimensions(width, height)
    }
    val style = attr("style")
    if (style.isBlank()) {
        return ImageDimensions(width, height)
    }
    return ImageDimensions(
        width = width ?: parseDimensionFromStyle(style, "width"),
        height = height ?: parseDimensionFromStyle(style, "height")
    )
}