package dima.apps.email

import GlobalEvent
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dialogs
import dima.ai.copyExtractedCodeToClipboard
import dima.apps.AppType
import dima.apps.email.database.EmailDatabase
import dima.apps.email.models.*
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.color.TailwindCssColors
import dima.dialogs.confirmation.openConfirmationDialog
import dima.globalState.EmailAppState
import dima.globalState.GlobalState
import dima.globalState.PaneState
import dima.os.openUrl
import dima.settings
import dima.utils.*
import globalEvent
import handleLostFocus
import isTextFieldFocused
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jsoup.Jsoup
import org.jsoup.nodes.Node

internal val selectedLinkBackground = TailwindCssColors.orange500
internal const val selectedLinkTagName = "selectedLink"

@Composable
fun EmailApp(state: EmailAppState, paneState: PaneState) {
    remember {
        EmailDatabase.setup()
        if (GlobalState.email.folderType == null && settings.email.folderTypes.isNotEmpty()) {
            GlobalState.email = GlobalState.email.copy(folderType = settings.email.folderTypes.first())
        }
    }
    var entries by remember { mutableStateOf<List<EmailMessage>>(emptyList()) }
    var filteredEntries by remember { mutableStateOf<List<EmailMessage>>(emptyList()) }
    var selected by remember { mutableStateOf<Int?>(null) }
    var markedEmailsIds: List<String> by remember { mutableStateOf(GlobalState.email.markedEmails) }
    var isPreviewSelected by remember { mutableStateOf(GlobalState.email.isPreviewSelected) }
    var folderType: EmailFolderType? by remember { mutableStateOf(GlobalState.email.folderType) }
    val searchFocusRequester = remember { FocusRequester() }
    val appFocusRequester = remember { FocusRequester() }
    var lastSelectedEmail by remember { mutableStateOf<EmailMessage?>(null) }
    var nextScrollToEmailSmooth by remember { mutableStateOf(false) }
    var lastMarkedEmailUid by remember { mutableStateOf<String?>(null) } // For Y key flicker prevention
    val leftScrollState = rememberLazyListState()
    val rightScrollState = rememberScrollState()
    val previewScrollVelocity = remember { ScrollVelocity(rightScrollState) }
    val coroutineScope = rememberCoroutineScope()
    var selectedLink by remember { mutableStateOf<Int?>(null) }
    var query by remember { mutableStateOf(GlobalState.email.query) }
    var anchors by remember { mutableStateOf(emptyList<Node>()) }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }

    fun getEmailsFromDatabaseAndUpdateSelected() {
        if (folderType == null) {
            return
        }
        entries = EmailDatabase.getAllUntrashedForFolderType(folderType!!)
        filteredEntries = if (query.isBlank()) {
            entries
        } else {
            entries.filterHuman(query) {
                val from = it.from.firstOrNull()
                if (from == null) {
                    it.subject
                } else {
                    (it.subject + " " + (from.personal ?: "") + " " + from.email).trim()
                }
            }

        }
        selected = if (filteredEntries.isEmpty()) {
            null
        } else {
            Lists.getInitialSelected(filteredEntries, GlobalState.email.selectedUid) { it.uid } ?: 0
        }
    }

    fun trashEmail(email: EmailMessage, showConfirmationDialog: Boolean) {

        fun trash() {
            filteredEntries = filteredEntries.filter { it.uid != email.uid }
            selected = if (selected == null) {
                0
            } else {
                selected!!.coerceAtMost(filteredEntries.size - 1)
            }
            coroutineScope.launch {
                rightScrollState.scrollTo(0)
            }
            coroutineScope.launch(Dispatchers.IO) {
                Email.trashEmail(email, onTrashFail = {
                    getEmailsFromDatabaseAndUpdateSelected()
                })
            }
        }

        if (showConfirmationDialog) {
            openConfirmationDialog("Trash email?", email.subject, confirmButtonText = "Trash") {
                trash()
            }
        } else {
            trash()
        }
    }

    fun moveEmailToInbox(email: EmailMessage) {
        coroutineScope.launch(Dispatchers.Default) {
            Email.moveEmailToInbox(
                email,
                onDatabaseUpdate = {
                    getEmailsFromDatabaseAndUpdateSelected()
                })
        }
    }

    /**
     * Download all attachments, regardless of whether they are inline or not.
     */
    fun downloadAttachments(email: EmailMessage) {
        val decodedAttachments = decodeAttachments(email)
        if (decodedAttachments.isEmpty()) {
            showNotification("Email has no attachments", email.subject)
            return
        }
        val text = if (decodedAttachments.size == 1) {
            "Download 1 attachment to Downloads?"
        } else {
            "Download ${decodedAttachments.size} attachments to Downloads?"
        }
        openConfirmationDialog(text, email.subject, confirmButtonText = "Download") {
            coroutineScope.launch(Dispatchers.Default) {
                downloadAttachmentsAndOpenInDired(email, onDatabaseUpdate = {
                    getEmailsFromDatabaseAndUpdateSelected()
                })
            }
        }
    }

    val localAppKeys = remember {
        listOf(
            // use KeyEventType.KeyUp to not insert the s on focusing,
            // also an issue with the AudioDialog S key handling
            AppKey(Key.B, "Focus search", onKeyUp = {
                searchFocusRequester.requestFocus()
                queryState = TextFieldValue(text = query, selection = TextRange(query.length))
            }),
            AppKey(Key.S, "Download all emails in current folder") {
                downloadAllEmailsInCurrentFolder(
                    folderType = folderType,
                    coroutineScope = coroutineScope,
                    getEmailsFromDatabaseAndUpdateSelected = ::getEmailsFromDatabaseAndUpdateSelected
                )
            },
            AppKey(Key.H, "Copy email folder path from server") {
                openServerFolderDialog(coroutineScope)
            },
            AppKey(Key.X, "Clear marked emails") {
                markedEmailsIds = emptyList()
            },
            AppKey(Key.Q, "Clear query") {
                query = ""
                queryState = TextFieldValue()
            },
            AppKey(
                Key.Y, "Mark email",
                onKeyDown = {
                    if (selected == null) return@AppKey
                    val email = filteredEntries.getOrNull(selected!!) ?: return@AppKey
                    val currentEmailFullUid = email.getFullUid()

                    if (currentEmailFullUid == lastMarkedEmailUid) {
                        return@AppKey // Prevent flicker by not re-processing if key is held on same item
                    }

                    markEmailOrThread(
                        selectedEmailIndex = selected,
                        filteredEmails = filteredEntries,
                        markedEmailsIds = markedEmailsIds,
                        changeIsPreviewSelected = { isPreviewSelected = it },
                        changeMarkedEmailsIds = { markedEmailsIds = it },
                        changeSelected = { selected = it }
                    )
                    lastMarkedEmailUid = currentEmailFullUid
                },
                onKeyUp = {
                    lastMarkedEmailUid = null // Reset on key up
                }
            ),
            AppKey(Key.U, "Change folder") {
                openChangeFolderTypeDialog {
                    folderType = it
                }
            },
            AppKey(Key.J, "Copy from email") { openCopyInfoFromEmailDialog(selected, filteredEntries) },
            AppKey(Key.F, "Open selected email in browser") {
                if (selected != null) {
                    val email = filteredEntries.getOrNull(selected!!)
                    if (email != null) {
                        if (email.html == null) {
                            showNotification("Email has no HTML")
                        } else {
                            val file = createTemporaryFile("email", ".html")
                            file.writeText(email.html)
                            openUrl("file://" + file.absolutePath)
                        }
                    }
                }
            },
            AppKey(Key.A, "Download attachments to Downloads") {
                if (selected != null) {
                    val email = filteredEntries[selected!!]
                    downloadAttachments(email)
                }
            },
            AppKey(Key.DirectionUp, "Select link above in preview") {
                selectedLink = if (selectedLink == null) {
                    0
                } else {
                    (selectedLink!! - 1).coerceAtLeast(0)
                }
            },
            AppKey(Key.DirectionDown, "Select link below in preview") {
                selectedLink = if (selectedLink == null) {
                    0
                } else {
                    (selectedLink!! + 1).coerceAtMost(anchors.size - 1)
                }
            },
            AppKey(Key.Escape, "Leave email preview") {
                if (selectedLink != null) {
                    selectedLink = null
                } else {
                    isPreviewSelected = false
                    GlobalState.email = GlobalState.email.copy(isPreviewSelected = false)
                }
            },
            AppKey(Key.Enter, "Select email preview or open link") {
                if (selectedLink == null) {
                    isPreviewSelected = true
                    GlobalState.email = GlobalState.email.copy(isPreviewSelected = true)
                } else {
                    val tag = anchors[selectedLink!!]
                    val href = if (tag.normalName() == "a") {
                        tag.attr("href").trim()
                    } else {
                        tag.isInAnchor()!!
                    }
                    openUrlHref(href)
                }
            },
            AppKey(Key.K, "Extract verification code via AI") {
                copyExtractedCodeToClipboard(selected, filteredEntries, coroutineScope)
            },
            AppKey(Key.Z, "Move email to Inbox (like from Spam or Trash)") {
                val inbox = settings.email.folderTypes.inbox()
                if (inbox == null) {
                    showErrorNotification("No Inbox folder defined")
                    return@AppKey
                }
                if (markedEmailsIds.isNotEmpty()) {
                    showErrorNotification("Not implemented with marked emails")
                    return@AppKey
                }
                if (selected != null) {
                    val email = filteredEntries[selected!!]
                    if (email.folderName == inbox.serverFolderName) {
                        showNotification("Email is already in Inbox")
                        return@AppKey
                    }
                    openConfirmationDialog("Move email to Inbox?", email.subject, confirmButtonText = "Move") {
                        moveEmailToInbox(email)
                    }
                }
            },
            // use since when the email is missing in the server, but still exists in the database
            AppKey(Key.D, "Remove email from database", isShift = true) {
                if (markedEmailsIds.isNotEmpty()) {
                    showErrorNotification("Not implemented with marked emails")
                    return@AppKey
                }
                if (selected == null) {
                    return@AppKey
                }
                isPreviewSelected = false
                selectedLink = null
                val email = filteredEntries[selected!!]
                openConfirmationDialog("Delete email from database?", email.subject, confirmButtonText = "Delete") {
                    EmailDatabase.deleteEmail(email)
                    getEmailsFromDatabaseAndUpdateSelected()
                    showNotification("Deleted email from database", email.subject)
                    Email.updateHasUnreadInboxEmails()
                }
            },
            AppKey(Key.D, "Trash email") {
                if (selected == null) {
                    return@AppKey
                }
                isPreviewSelected = false
                selectedLink = null
                if (markedEmailsIds.isEmpty()) {
                    val email = filteredEntries[selected!!]
                    trashEmail(email, showConfirmationDialog = true)
                } else {
                    val emails: List<EmailMessage> = markedEmailsIds.mapNotNull { markedEmailsId ->
                        // on crashes, markedEmailsIds is not reset and when the emails are trashed, they only exist
                        // in markedEmailsIds, but are properly trashed, so we ignore those here
                        filteredEntries.find { it.getFullUid() == markedEmailsId }
                    }
                    val title = if (emails.size == 1) "Trash email" else "Trash ${emails.size} emails"
                    val subTitle = emails.joinToString("\n") {
                        "• " + it.subject
                    }
                    openConfirmationDialog(title, subTitle = subTitle, confirmButtonText = "Trash") {
                        // set selected to current email by subtracting deleted emails
                        selected = if (selected == null) {
                            0
                        } else {
                            (selected!! - emails.size).coerceAtLeast(0)
                        }
                        emails.forEach { email ->
                            trashEmail(email, showConfirmationDialog = false)
                        }
                        markedEmailsIds = emptyList()
                        Email.updateHasUnreadInboxEmails()
                    }
                }
            },
            AppKey(Key.G, "Open in GitLab") {
                if (selected == null) {
                    return@AppKey
                }
                val email = filteredEntries[selected!!]
                val gitlabEmail = "<EMAIL>"
                if (email.from.first().email == gitlabEmail) {
                    val extractedAnchors = Jsoup.parse(email.html!!).select("a")
                    extractedAnchors.forEach {
                        if (it.text() == "View it on GitLab") {
                            openUrl(it.attr("href"))
                        }
                    }
                } else {
                    showErrorNotification("Email is not from $gitlabEmail")
                }
            },
            AppKey(
                Key.T, "Go one email down or scroll down",
                onKeyUp = {
                    previewScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isPreviewSelected) {
                        previewScrollVelocity.onScrollDownKeyPressed()
                    } else {
                        if (selected != null && selected!! + 1 < filteredEntries.size) {
                            selected = selected!! + 1
                        }
                    }
                }),
            AppKey(
                Key.M, "Go 6 emails down or scroll down more",
                onKeyUp = {
                    previewScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isPreviewSelected) {
                        previewScrollVelocity.onScrollDownMoreKeyPressed()
                    } else {
                        if (selected == null) {
                            return@AppKey
                        }
                        val newIndex = selected!! + 6
                        selected = newIndex.coerceAtMost(filteredEntries.size - 1)
                    }
                }),
            AppKey(
                Key.V, "Go 6 emails up or scroll up more",
                onKeyUp = {
                    previewScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isPreviewSelected) {
                        previewScrollVelocity.onScrollUpMoreKeyPressed()
                    } else {
                        if (selected == null) {
                            return@AppKey
                        }
                        val newIndex = selected!! - 6
                        selected = newIndex.coerceAtLeast(0)
                    }
                }),
            AppKey(
                Key.C, "Go one email up or scroll up",
                onKeyUp = {
                    previewScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isPreviewSelected) {
                        previewScrollVelocity.onScrollUpKeyPressed()
                    } else {
                        if (selected != null && selected!! >= 1) {
                            selected = selected!! - 1
                        }
                    }
                },
            )
        )
    }

    Column(
        modifier = Modifier
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .fillMaxSize()
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    if (it.type == KeyEventType.KeyDown && it.key == Key.Escape) {
                        appFocusRequester.requestFocus()
                        query = ""
                        queryState = TextFieldValue()
                        return@onPreviewKeyEvent true
                    }
                    if (it.key == Key.Enter) {
                        appFocusRequester.requestFocus()
                        return@onPreviewKeyEvent true
                    }
                    return@onPreviewKeyEvent false
                }
                return@onPreviewKeyEvent it.handleAppMap()
            }
            .padding(start = 12.dp, end = 12.dp)
    ) {
        EmailAppUiTopRow(
            folders = folderType,
            queryState = queryState,
            searchFocusRequester = searchFocusRequester,
            markedEmailsIds = markedEmailsIds,
            filteredEntries = filteredEntries,
            entries = entries,
            onFolderChange = { folderType = it },
            onQueryStateChange = { queryState = it },
            onQueryChange = { query = it },
            onSelectedChange = { selected = it }
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
        ) {
            EmailAppUiLeftList(
                filteredEntries = filteredEntries,
                selected = selected,
                isPreviewSelected = isPreviewSelected,
                markedEmailsIds = markedEmailsIds,
                query = query,
                leftScrollState = leftScrollState,
                onTrash = { trashEmail(it, showConfirmationDialog = false) },
                onMoveToInbox = { moveEmailToInbox(it) },
                onDownloadAttachments = { downloadAttachments(it) },
                onSelectedChange = { selected = it }
            )
            if (selected != null && filteredEntries.isNotEmpty()) {
                val email = filteredEntries.getOrNull(selected!!)
                if (email != null) {
                    EmailAppUiRightSide(email, isPreviewSelected, rightScrollState, query, selectedLink) {
                        anchors = it
                    }
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
        previewScrollVelocity.loopForeverAndTick()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(entries, selected) {
        if (selected == null) {
            lastSelectedEmail = null
            rightScrollState.scrollTo(0)
        } else {
            val email = filteredEntries.getOrNull(selected!!)
            if (email != lastSelectedEmail) {
                rightScrollState.scrollTo(0)
            }
            lastSelectedEmail = email
        }
    }
    LaunchedEffect(selected) {
        if (selected != null && filteredEntries.isNotEmpty()) {
            selectedLink = null
            val email = filteredEntries.getOrNull(selected!!) ?: return@LaunchedEffect
            if (!email.isSeen) {
                coroutineScope.launch(Dispatchers.Default) {
                    EmailDatabase.markAsRead(email)
                }
            }
            val scrollOffset = -(GlobalState.mainWindow.heightInDp / 2).toInt() - 200
            if (nextScrollToEmailSmooth) {
                leftScrollState.animateScrollToItem(selected!!, scrollOffset = scrollOffset)
            } else {
                leftScrollState.scrollToItem(selected!!, scrollOffset = scrollOffset)
                nextScrollToEmailSmooth = true // Reset for subsequent scrolls
            }
            GlobalState.email = GlobalState.email.copy(selectedUid = filteredEntries.getOrNull(selected!!)?.uid)
        }
    }
    LaunchedEffect(markedEmailsIds) {
        GlobalState.email = GlobalState.email.copy(markedEmails = markedEmailsIds)
    }
    LaunchedEffect(query, folderType) {
        getEmailsFromDatabaseAndUpdateSelected()
        selectedLink = null
        GlobalState.email = GlobalState.email.copy(query = query, folderType = folderType)
    }
    LaunchedEffectForAppKeys(paneState, localAppKeys)
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                if (isPreviewSelected) {
                    previewScrollVelocity.scrollToTop()
                } else {
                    selected = 0
                }
            }

            GlobalEvent.ScrollToBottom -> {
                if (isPreviewSelected) {
                    previewScrollVelocity.scrollToBottom()
                } else {
                    selected = filteredEntries.size - 1
                }
            }

            GlobalEvent.Reload -> {
                downloadAllEmailsInCurrentFolderType(
                    folderType = folderType,
                    coroutineScope = coroutineScope,
                    maxKnownEmailsBeforeStoppingDownload = 3,
                    showConfirmationDialog = false,
                    getEmailsFromDatabaseAndUpdateSelected = ::getEmailsFromDatabaseAndUpdateSelected
                )
            }

            GlobalEvent.ReloadData -> {
                val wasAtTop = selected == 0 && filteredEntries.isNotEmpty()
                val oldTopUidIfAny = if (wasAtTop) filteredEntries.firstOrNull()?.uid else null

                getEmailsFromDatabaseAndUpdateSelected() // This updates 'selected' and 'filteredEntries'
                Email.updateHasUnreadInboxEmails()

                if (wasAtTop && filteredEntries.isNotEmpty()) {
                    // If selection moved away from top OR the top item itself changed
                    if (selected != 0 || (oldTopUidIfAny != null && filteredEntries.first().uid != oldTopUidIfAny)) {
                        selected = 0 // Force selection to the new top
                        nextScrollToEmailSmooth = false // Ensure immediate scroll
                    }
                }
            }

            GlobalEvent.CopyAll -> {
                if (filteredEntries.isEmpty()) {
                    showErrorNotification("No emails to mark")
                    return@LaunchedEffectGlobalEventForApps
                }
                isPreviewSelected = false
                selected = 0
                markedEmailsIds = emptyList()
                filteredEntries.forEach {
                    markedEmailsIds = markedEmailsIds + (it.getFullUid())
                }
                if (markedEmailsIds.size == 1) {
                    showNotification("Marked 1 email")
                } else {
                    showNotification("Marked ${markedEmailsIds.size} emails")
                }
            }

            GlobalEvent.ToNewEmails -> {
                val switchToForNewEmailView = settings.email.folderTypes.switchToForNewEmailView()
                if (switchToForNewEmailView == null) {
                    showErrorNotification("No folder type defined for switchToForNewEmailView")
                    return@LaunchedEffectGlobalEventForApps
                }
                isPreviewSelected = false
                selectedLink = null
                if (GlobalState.app == AppType.Email) {
                    query = ""
                    queryState = TextFieldValue("")
                    GlobalState.email =
                        GlobalState.email.copy(isPreviewSelected = false, selectedUid = null, query = "")
                } else {
                    GlobalState.email = GlobalState.email.copy(isPreviewSelected = false, selectedUid = null)
                }
                folderType = switchToForNewEmailView
                selected = 0
            }

            else -> {}
        }
    }
}
