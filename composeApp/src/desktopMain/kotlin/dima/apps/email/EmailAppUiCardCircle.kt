package dima.apps.email

import GlobalStyling
import Globals
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

@Composable
internal fun EmailAppUiCardCircle(
    text: String,
    circleBackgroundColor: Color,
    isMarked: <PERSON>olean,
    isSmallSize: Boolean
) {
    Text(
        "",
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .size(if (isSmallSize) 24.dp else 40.dp)
            .drawBehind {
                if (isMarked && !GlobalState.isDarkMode) {
                    drawCircle(
                        color = GlobalStyling.getMarkedBackgroundColorForWhiteText(),
                        radius = this.size.maxDimension / 1.8f
                    )
                }
                drawCircle(
                    color = circleBackgroundColor,
                    radius = this.size.maxDimension / 2.0f
                )
                val textLayoutResult: TextLayoutResult =
                    TextMeasurer(
                        Globals.fontFamilyResolver,
                        Globals.density,
                        LayoutDirection.Ltr
                    ).measure(
                        text,
                        style = TextStyle(
                            fontSize = if (isSmallSize) {
                                if (isMarked) {
                                    15.sp
                                } else {
                                    18.sp
                                }
                            } else {
                                if (isMarked) {
                                    22.sp
                                } else {
                                    17.sp
                                }
                            },
                            fontWeight = if (isMarked) FontWeight.Medium else null,
                        ),
                    )
                val textSize = textLayoutResult.size
                val canvasWidth = this.size.width
                val canvasHeight = this.size.height
                drawText(
                    textLayoutResult,
                    color = if (isMarked) {
                        GlobalStyling.getMarkedBackgroundColorForWhiteText()
                    } else {
                        TailwindCssColors.white
                    },
                    topLeft = Offset(
                        (canvasWidth - textSize.width) / 2f,
                        (canvasHeight - textSize.height) / 2f
                    ),
                )
            }
    )
}
