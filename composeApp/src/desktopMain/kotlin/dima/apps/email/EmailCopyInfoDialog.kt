package dima.apps.email

import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.apps.email.models.EmailMessage
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import dima.utils.truncateWithEllipsis
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

private val prefixViewWidth = 72.dp

internal fun openCopyInfoFromEmailDialog(
    selected: Int?,
    filteredEntries: List<EmailMessage>,
) {
    if (selected == null) {
        return
    }
    val email = filteredEntries.getOrNull(selected)
    if (email == null) {
        return
    }
    val parts: List<CompletionDialogCandidate> = buildList {
        add(
            CompletionDialogCandidate(
                email.getFullUid(),
                prefixView = {
                    Text(
                        "ID: ",
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.width(prefixViewWidth)
                    )
                },
                additionalSearchString = "ID"
            )
        )
        if (email.subject.isNotBlank()) {
            add(
                CompletionDialogCandidate(
                    email.subject,
                    prefixView = {
                        Text(
                            "Subject: ",
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixViewWidth)
                        )
                    },
                    additionalSearchString = "Subject"
                )
            )
        }
        if (email.html != null) {
            add(
                CompletionDialogCandidate(
                    email.html.truncateWithEllipsis(300),
                    prefixView = {
                        Text(
                            "HTML: ",
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixViewWidth)
                        )
                    },
                    additionalSearchString = "HTML"
                )
            )
        }
        if (email.plainText != null) {
            add(
                CompletionDialogCandidate(
                    email.plainText.truncateWithEllipsis(300),
                    prefixView = {
                        Text(
                            "Plain text: ",
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixViewWidth)
                        )
                    },
                    additionalSearchString = "Plain Text"
                )
            )
        }
        if (email.from.isNotEmpty()) {
            email.from.forEach {
                val from = if (it.personal == null) {
                    it.email
                } else {
                    it.personal + " • " + it.email
                }
                add(
                    CompletionDialogCandidate(
                        from,
                        prefixView = {
                            Text(
                                "From: ",
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.width(prefixViewWidth)
                            )
                        },
                        additionalSearchString = "From: $from"
                    )
                )
            }
        }
        if (email.headers.isNotEmpty()) {
            add(
                CompletionDialogCandidate(
                    email.headers.map { it.key to it.value }.toMap().toString(),
                    prefixView = {
                        Text(
                            "Headers: ",
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixViewWidth)
                        )
                    },
                    additionalSearchString = "Headers"
                )
            )
        }
    }
    openCompletionDialog(
        "Copy from email",
        parts
    ) {
        val candidate = parts[it.index!!]
        if (candidate.additionalSearchString == "ID") {
            copyToClipboard(email.getFullUid())
        } else if (candidate.additionalSearchString == "Subject") {
            copyToClipboard(email.subject)
        } else if (candidate.additionalSearchString == "HTML") {
            copyToClipboard(email.html!!)
        } else if (candidate.additionalSearchString == "Plain Text") {
            copyToClipboard(email.plainText!!)
        } else if (candidate.additionalSearchString == "Headers") {
            val json = Json { prettyPrint = true }
            copyToClipboard(json.encodeToString(email.headers))
        } else if (candidate.additionalSearchString!!.startsWith("From: ")) {
            copyToClipboard(candidate.additionalSearchString.removePrefix("From: "))
        }
    }
}
