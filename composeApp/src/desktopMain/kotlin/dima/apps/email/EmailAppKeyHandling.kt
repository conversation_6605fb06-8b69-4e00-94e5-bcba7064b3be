package dima.apps.email

import dima.apps.email.models.EmailFolderType
import dima.dialogs.confirmation.openConfirmationDialog
import kotlinx.coroutines.CoroutineScope

internal fun downloadAllEmailsInCurrentFolder(
    folderType: EmailFolderType?,
    coroutineScope: CoroutineScope,
    getEmailsFromDatabaseAndUpdateSelected: () -> Unit
) {
    openConfirmationDialog("Download all emails in current folder?") {
        downloadAllEmailsInCurrentFolderType(
            folderType = folderType,
            coroutineScope = coroutineScope,
            // set high value to download all emails
            maxKnownEmailsBeforeStoppingDownload = 6000,
            showConfirmationDialog = false,
            getEmailsFromDatabaseAndUpdateSelected = getEmailsFromDatabaseAndUpdateSelected
        )
    }

}