package dima.apps.email.connection

import com.sun.mail.imap.IMAPFolder
import com.sun.mail.util.MailConnectException
import dima.apps.email.Email.emailFullIdWhitelist
import dima.apps.email.Email.loginAndGetStore
import dima.apps.email.Email.updateHasUnreadInboxEmails
import dima.apps.email.downloadFolder
import dima.apps.email.models.EmailNewDataInfo
import dima.apps.notifications.NotificationType
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.dateTime.DateTimeUtils
import dima.globalState.GlobalState
import dima.utils.ignoreAllExceptions
import javax.mail.FolderClosedException
import javax.mail.Store

/**
 * Manages IMAP folder connections, error handling, and notifications for email downloading.
 */
object EmailFolderConnectionManager {

    /**
     * Downloads emails from multiple server folders with connection management and error handling.
     *
     * @param serverFolderNames List of folder names to download from
     * @param maxKnownEmailsBeforeStoppingDownload Stop downloading emails after this many known emails
     * @param showNotifications Whether to show notifications during the process
     * @param onNew Callback called with the subject of new emails
     */
    fun downloadFolders(
        serverFolderNames: List<String>,
        maxKnownEmailsBeforeStoppingDownload: Int? = 3,
        showNotifications: Boolean,
        onNew: (String) -> Unit
    ) {
        if (emailFullIdWhitelist != null) {
            showNotification("Email full id whitelist active!", type = NotificationType.Error)
        }
        
        val notification = if (showNotifications) {
            showLoadingNotification("Downloading emails in ${serverFolderNames.joinToString(", ")}...")
        } else {
            null
        }
        
        var hasAnyException = false
        
        serverFolderNames.forEach { folder ->
            if (emailFullIdWhitelist != null && showNotifications) {
                notification!!.update(message = folder)
            }
            
            val result = processServerFolder(
                folder = folder,
                maxKnownEmailsBeforeStoppingDownload = maxKnownEmailsBeforeStoppingDownload,
                showNotifications = showNotifications,
                onNew = { emailInfo ->
                    notification?.update(message = "${emailInfo.subject}\n\n$folder: ${emailInfo.currentIndex}/${emailInfo.total}")
                    onNew(emailInfo.subject)
                }
            )
            
            if (!result.success) {
                hasAnyException = true
                if (showNotifications && result.errorMessage != null) {
                    notification?.toError(result.errorMessage, result.errorDetails)
                }
            }
        }
        
        if (!hasAnyException) {
            GlobalState.email = GlobalState.email.copy(lastUpdate = DateTimeUtils.nowAsIsoDateTime())
        }
        
        notification?.dismiss()
        updateHasUnreadInboxEmails()
    }

    /**
     * Result of processing a server folder.
     */
    data class FolderProcessResult(
        val success: Boolean,
        val errorMessage: String? = null,
        val errorDetails: String? = null
    )

    /**
     * Processes a single server folder with connection management and error handling.
     */
    private fun processServerFolder(
        folder: String,
        maxKnownEmailsBeforeStoppingDownload: Int?,
        showNotifications: Boolean,
        onNew: (EmailNewDataInfo) -> Unit
    ): FolderProcessResult {
        var imapFolder: IMAPFolder? = null
        var store: Store? = null
        
        return try {
            store = loginAndGetStore(showErrorNotifications = showNotifications)
            if (store == null) { // loginAndGetStore now returns null on auth failure
                return FolderProcessResult(false)
            }
            
            imapFolder = store.getFolder(folder) as IMAPFolder
            downloadFolder(folder, imapFolder, maxKnownEmailsBeforeStoppingDownload, onNew)
            
            FolderProcessResult(true)
            
        } catch (_: FolderClosedException) {
            FolderProcessResult(
                success = false,
                errorMessage = "Folder $folder closed prematurely"
            )
        } catch (e: MailConnectException) {
            FolderProcessResult(
                success = false,
                errorMessage = "Failed to download emails",
                errorDetails = e.message ?: "Unknown error"
            )
        } catch (e: Exception) {
            FolderProcessResult(
                success = false,
                errorMessage = "Error processing folder $folder",
                errorDetails = e.message ?: "Unknown error"
            )
        } finally {
            cleanupConnections(imapFolder, store)
        }
    }

    /**
     * Safely closes IMAP folder and store connections.
     */
    private fun cleanupConnections(imapFolder: IMAPFolder?, store: Store?) {
        ignoreAllExceptions {
            imapFolder?.close(true)
        }
        ignoreAllExceptions {
            store?.close()
        }
    }
}