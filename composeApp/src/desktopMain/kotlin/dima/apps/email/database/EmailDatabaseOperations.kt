package dima.apps.email.database

import dima.apps.email.EmailAttachment
import dima.apps.email.decodeViaMimeUtility
import dima.apps.email.models.EmailFrom
import dima.apps.email.processing.EmailContentProcessor
import dima.apps.email.processing.EmailContent
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.insertIgnore
import javax.mail.Address
import javax.mail.Flags.Flag
import javax.mail.Message
import javax.mail.internet.InternetAddress

/**
 * Handles all database operations related to email storage.
 */
object EmailDatabaseOperations {

    /**
     * Inserts a new email into the database with all its associated data.
     *
     * @param email The email message to insert
     * @param fullUid The full UID of the email
     * @param emailContent The extracted email content (plain text and HTML)
     * @param attachments List of email attachments
     * @param subject The email subject (can be null)
     */
    fun insertEmailToDatabase(
        email: Message,
        fullUid: String,
        emailContent: EmailContent,
        attachments: List<EmailAttachment>,
        subject: String?
    ) {
        val flags = email.flags
        val headers: Map<String, String> = email.allHeaders.asSequence().map {
            it.name to it.value
        }.toMap()

        EmailTable.insertIgnore { row ->
            row[this.uidAndFolderName] = fullUid
            row[this.plainText] = emailContent.plainText
            row[this.headers] = Json.encodeToString(headers)
            row[this.html] = emailContent.html
            row[this.attachments] = Json.encodeToString(attachments)
            row[this.attachmentCount] = attachments.size
            row[this.subject] = subject ?: "No Subject"
            row[this.date] = email.receivedDate.time.toString()
            row[this.from] = mapAddressesToJson(email.from)
            row[this.to] = mapAddressesToJson(email.getRecipients(Message.RecipientType.TO))
            row[this.cc] = mapAddressesToJson(email.getRecipients(Message.RecipientType.CC))
            row[this.bcc] = mapAddressesToJson(email.getRecipients(Message.RecipientType.BCC))
            row[this.isAnswered] = flags.contains(Flag.ANSWERED)
            row[this.isDeleted] = flags.contains(Flag.DELETED)
            row[this.isDraft] = flags.contains(Flag.DRAFT)
            row[this.isFlagged] = flags.contains(Flag.FLAGGED)
            row[this.isRecent] = flags.contains(Flag.RECENT)
            row[this.isSeen] = flags.contains(Flag.SEEN)
        }
    }

    /**
     * Converts an array of email addresses to a JSON encoded string of EmailFrom list.
     *
     * @param addresses Array of email addresses (can be null)
     * @return JSON encoded string of EmailFrom list
     */
    fun mapAddressesToJson(addresses: Array<Address>?): String {
        if (addresses == null) {
            return "[]"
        }
        return Json.encodeToString<List<EmailFrom>>(addresses.map {
            val internetAddress = it as InternetAddress
            EmailFrom(
                if (internetAddress.personal == null) null else decodeViaMimeUtility(internetAddress.personal),
                decodeViaMimeUtility(internetAddress.address)!!,
            )
        })
    }
}