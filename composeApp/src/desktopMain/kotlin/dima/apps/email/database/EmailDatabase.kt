package dima.apps.email.database

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.email.Email.cacheDirectory
import dima.apps.email.Email.getFullUid
import dima.apps.email.Email.updateHasUnreadInboxEmails
import dima.apps.email.NewEmailTray
import dima.apps.email.models.EmailFolderType
import dima.apps.email.models.EmailFrom
import dima.apps.email.models.EmailMessage
import dima.database.transactionToAvoidBusySqlite
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File
import java.time.Instant.ofEpochMilli
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.concurrent.Semaphore

internal object EmailTable : Table("Emails") {
    /**
     * The format is: uid@serverFolderName
     */
    val uidAndFolderName = text("uid_and_folder_name").uniqueIndex()
    val plainText = text("plain_text").nullable()
    val html = text("html").nullable()

    /**
     * A JSON string of an [dima.apps.email.EmailAttachment] array.
     */
    val attachments = text("attachments")
    val attachmentCount = integer("attachment_count").default(0)
    val subject = text("subject")

    /**
     * A JSON string of a String to String map.
     */
    val headers = text("headers")

    /**
     * Stores the epoch milliseconds / Date().time value.
     * It is the number of milliseconds since January 1, 1970, 00:00:00 GMT
     */
    val date = text("date")

    /**
     * A JSON string of an array of [dima.apps.email.models.EmailFrom].
     */
    val from = text("from")

    /**
     * A JSON string of an array of [dima.apps.email.models.EmailFrom].
     */
    val to = text("to")

    /**
     * A JSON string of an array of [dima.apps.email.models.EmailFrom].
     */
    val cc = text("cc")

    /**
     * A JSON string of an array of [dima.apps.email.models.EmailFrom].
     */
    val bcc = text("bcc")
    val isAnswered = bool("is_answered").default(false)
    val isDeleted = bool("is_deleted").default(false)
    val isDraft = bool("is_draft").default(false)
    val isFlagged = bool("is_flagged").default(false)
    val isRecent = bool("is_recent").default(false)
    val isSeen = bool("is_seen").default(false)
    val wasTrashed = bool("was_trashed").default(false)
}

object EmailDatabase {
    /**
     * Use for [NewEmailTray].
     */
    private val databaseSetupSemaphore = Semaphore(1)
    internal var database by mutableStateOf<Database?>(null)

    fun setup() {
        databaseSetupSemaphore.acquire()
        if (database != null) {
            databaseSetupSemaphore.release()
            return
        }
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        val dbDir = File(cacheDirectory, "db")
        if (!dbDir.exists()) {
            dbDir.mkdirs()
        }
        val dbFile = File(dbDir, "emails.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
        transaction(database) {
            SchemaUtils.create(EmailTable)
        }
        databaseSetupSemaphore.release()
    }

    internal fun getAllUntrashedForFolderType(folderType: EmailFolderType): List<EmailMessage> {
        val entries = transactionToAvoidBusySqlite(database) {
            val query = EmailTable.selectAll()
            query.andWhere { EmailTable.wasTrashed eq false }
            when (folderType) {
                is EmailFolderType.All -> {}
                is EmailFolderType.Inbox ->
                    query.andWhere { (EmailTable.uidAndFolderName like "%@${folderType.serverFolderName}") }

                is EmailFolderType.Trash ->
                    query.andWhere { (EmailTable.uidAndFolderName like "%@${folderType.serverFolderName}") }

                is EmailFolderType.SingleServerFolder ->
                    query.andWhere { (EmailTable.uidAndFolderName like "%@${folderType.serverFolderName}") }

                is EmailFolderType.MultiServerFolder -> {
                    val folderConditions = folderType.serverFolderNames.map { serverFolderName ->
                        Op.build { EmailTable.uidAndFolderName like "%@${serverFolderName}" }
                    }
                    query.andWhere { folderConditions.reduce { acc, op -> acc or op } }
                }
            }
            query.map {
                val uidParts = it[EmailTable.uidAndFolderName].split("@")
                val headers = Json.decodeFromString<Map<String, String>>(it[EmailTable.headers])
                EmailMessage(
                    uid = uidParts[0],
                    folderName = uidParts[1],
                    plainText = it[EmailTable.plainText],
                    html = it[EmailTable.html],
                    attachments = it[EmailTable.attachments],
                    attachmentCount = it[EmailTable.attachmentCount],
                    subject = it[EmailTable.subject],
                    headers = headers,
                    date = LocalDateTime.ofInstant(ofEpochMilli(it[EmailTable.date].toLong()), ZoneId.systemDefault()),
                    from = Json.decodeFromString<List<EmailFrom>>(it[EmailTable.from]),
                    to = Json.decodeFromString<List<EmailFrom>>(it[EmailTable.to]),
                    cc = Json.decodeFromString<List<EmailFrom>>(it[EmailTable.cc]),
                    bcc = Json.decodeFromString<List<EmailFrom>>(it[EmailTable.bcc]),
                    isAnswered = it[EmailTable.isAnswered],
                    isDeleted = it[EmailTable.isDeleted],
                    isDraft = it[EmailTable.isDraft],
                    isFlagged = it[EmailTable.isFlagged],
                    isRecent = it[EmailTable.isRecent],
                    isSeen = it[EmailTable.isSeen],
                    wasTrashed = it[EmailTable.wasTrashed],
                    messageId = headers["Message-ID"],
                    references = headers["References"]
                )
            }.sortedBy { it.date }.reversed()
        }
        val newSortedReferences = mutableListOf<EmailMessage>()
        val all = entries.toMutableList()
        while (all.isNotEmpty()) {
            val entry = all.removeFirst()
            newSortedReferences.add(entry)
            if (entry.references == null) {
                continue
            }
            val references = entry.references.trim().replace("\r", "").split("\n")
            for (reference: String in references) {
                val index = all.indexOfFirst { it.messageId?.trim() == reference.trim() }
                if (index == -1) {
                    all.removeIf {
                        if (it.references == null) {
                            return@removeIf false
                        }
                        return@removeIf if (it.references.contains(reference)) {
                            newSortedReferences.add(it.copy(isChildInThread = true))
                            true
                        } else {
                            false
                        }
                    }
                    continue
                } else {
                    val child = all.removeAt(index)
                    newSortedReferences.add(child.copy(isChildInThread = true))
                }
            }
        }
        return newSortedReferences
    }

    internal fun markAsRead(email: EmailMessage) {
        transactionToAvoidBusySqlite(database) {
            val fullUid = getFullUid(email.uid, email.folderName)
            EmailTable.update({ EmailTable.uidAndFolderName eq fullUid }) {
                it[this.isSeen] = true
            }
        }
        updateHasUnreadInboxEmails()
    }

    internal fun deleteEmail(email: EmailMessage) {
        transaction(database) {
            val fullUid = getFullUid(email.uid, email.folderName)
            EmailTable.deleteWhere { uidAndFolderName eq fullUid }
        }
    }
}