package dima.apps.email

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Email
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.rememberTrayState
import dima.utils.TrayIcon

@Composable
fun ApplicationScope.NewEmailTray() {
    val painter = rememberVectorPainter(Icons.Rounded.Email)
    val trayState = rememberTrayState()
    if (Email.hasUnreadInboxEmails) {
        Tray(state = trayState, icon = TrayIcon(painter))
    }
}