package dima.apps.email

import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.AnnotatedString

@Composable
internal fun EmailAppUiTextSelectedLink(
    annotatedString: AnnotatedString,
) {
    var onDraw: DrawScope.() -> Unit by remember { mutableStateOf({}) }
    Text(
        text = annotatedString,
        modifier = Modifier.drawBehind { onDraw() },
        onTextLayout = { layoutResult ->
            val annotation =
                annotatedString.getStringAnnotations(selectedLinkTagName, 0, annotatedString.length)
                    .firstOrNull()
            if (annotation == null) {
                onDraw = {}
                return@Text
            }
            val textBounds = layoutResult.getBoundingBoxes(
                annotation.start,
                annotation.end
            )
            onDraw = {
                val offset = 10f
                for (bound in textBounds) {
                    drawRoundRect(
                        color = selectedLinkBackground,
                        cornerRadius = CornerRadius(offset),
                        topLeft = bound.topLeft.minus(Offset(offset, offset)),
                        size = Size(bound.width + offset * 2, bound.height + offset * 2),
                    )
                }
            }
        })
}