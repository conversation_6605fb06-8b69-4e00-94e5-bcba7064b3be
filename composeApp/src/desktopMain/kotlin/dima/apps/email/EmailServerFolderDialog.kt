package dima.apps.email

import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.mail.Store

internal fun openServerFolderDialog(coroutineScope: CoroutineScope) {
    coroutineScope.launch(Dispatchers.IO) {
        val notification = showLoadingNotification("Fetching server folders...")
        var store: Store? = null
        try {
            store = Email.loginAndGetStore(showErrorNotifications = true)
            if (store == null) {
                notification.toError("Failed to connect to email server.")
                return@launch
            }
            val folders = store.defaultFolder.list("*") // Get all folders
            val folderNames = folders.map { it.fullName }.sorted()
            notification.dismiss()
            if (folderNames.isEmpty()) {
                showErrorNotification("No email folders found on the server.")
                return@launch
            }
            openCompletionDialog(
                title = "Copy server email folder path",
                candidates = folderNames
            ) { selection ->
                copyToClipboard(selection.text)
            }
        } catch (e: Exception) {
            notification.toError("Failed to fetch server folders", e.message ?: "Unknown error")
        } finally {
            store?.close()
        }
    }
}
