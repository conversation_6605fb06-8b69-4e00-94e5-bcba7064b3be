package dima.apps.dummy

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dialogs
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import handleLostFocus
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection

@Composable
fun DummyApp() {
    val appFocusRequester = remember { FocusRequester() }
    val selection = StringSelection("hiii")
    val systemClipboard = Toolkit.getDefaultToolkit().systemClipboard
    systemClipboard.getContents(null)
    systemClipboard.setContents(selection, selection)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(12.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
    ) {
        Text("DummyApp")

        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
        ) {
            TextFieldWithCandidateCountWithoutAnimation(
                value = TextFieldValue(""),
                onValueChange = {},
                topLabel = "BIG labellllllllllllllllllllllllllllllllllllllwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww",
                modifier = Modifier
            )
            TextFieldWithCandidateCountWithoutAnimation(
                value = TextFieldValue(""),
                onValueChange = {},
                topLabel = "Query",
                modifier = Modifier
            )
        }

        /*
                Terminal(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(400.dp),
        //            command = arrayOf("echo", "hi")
                    command = arrayOf("htop")
                )
        */
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
}