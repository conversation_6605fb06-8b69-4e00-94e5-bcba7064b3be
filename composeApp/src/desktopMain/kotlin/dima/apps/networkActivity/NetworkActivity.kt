package dima.apps.networkActivity

import GlobalStyling
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import dima.apps.networkActivity.NetworkActivity.entries
import dima.apps.networkActivity.NetworkActivity.getNewEntryId
import io.ktor.http.*
import io.ktor.util.*
import java.time.Duration.between
import java.time.LocalDateTime
import java.util.concurrent.Semaphore

enum class FakedNetworkActivityStatus {
    Info,
    InTransit,
    Success,
    Error,
}

object NetworkActivity {

    /**
     * Not stored in [dima.globalState.GlobalState] because the network activity is not saved to any database.
     */
    internal var selectedId by mutableStateOf<Int?>(null)
    internal var entries by mutableStateOf(listOf<Entry>())
    private var id = 0

    /**
     * Avoid concurrent modifications on [entries] and on [getNewEntryId].
     */
    private val semaphore = Semaphore(1)

    private fun getNewEntryId(): Int = id++

    data class Entry(
        val id: Int,
        val startDate: LocalDateTime,
        val endDate: LocalDateTime?,
        val method: RequestMethod?,
        /**
         * The URL without query parameters.
         */
        val url: String?,
        /**
         * The URL including query parameters.
         */
        val fullUrl: String?,
        val requestHeaders: StringValues,
        val responseHeaders: StringValues,
        /**
         * URL parameters.
         */
        val parameters: StringValues,
        val requestBody: String?,
        val contentLength: Long?,
        /**
         * If [fakedText] is non-null, this can be used for error messages.
         */
        val responseBody: String?,
        /**
         * A non faked entry is assumed to be in transit when [statusCode] is null.
         */
        val statusCode: Int?,
        /**
         * Always set [fakedStatus] if this non-null.
         *
         * The response body can be used for error messages.
         */
        val fakedText: String?,
        val fakedStatus: FakedNetworkActivityStatus?,
    )

    internal data class DurationWithDuration(val humanReadable: String, val color: Color)

    /**
     * @return "1s", "100ms", or "1.5s"
     */
    internal fun Entry.getDurationWithColor(): DurationWithDuration {
        require(endDate != null)
        val duration = between(startDate, endDate)
        val milliseconds = duration.toMillis()
        return when {
            milliseconds < 1000 -> DurationWithDuration("${milliseconds}ms", GlobalStyling.getGreenColor())
            milliseconds == 1000L -> DurationWithDuration("1s", GlobalStyling.getOrangeColor())
            else -> {
                val seconds = duration.seconds
                val millis = (milliseconds % 1000) / 100.0
                val text = if (millis == 0.0) {
                    String.format("%d", seconds)
                } else {
                    String.format("%.1fs", seconds + millis)
                }
                DurationWithDuration(text, GlobalStyling.getRedTextColor())
            }
        }
    }

    fun Entry.updateFaked(
        status: FakedNetworkActivityStatus? = null,
        text: String? = null,
        responseBody: String? = null,
        contentLength: Long? = null,
        setEndDate: Boolean = true,
    ) {
        semaphore.acquire()
        entries = entries.map {
            if (it.id == id) {
                val endDate = if (setEndDate) {
                    LocalDateTime.now()
                } else {
                    it.endDate
                }
                Entry(
                    id = it.id,
                    startDate = it.startDate,
                    endDate = endDate,
                    method = it.method,
                    requestHeaders = it.requestHeaders,
                    parameters = it.parameters,
                    responseHeaders = it.responseHeaders,
                    statusCode = it.statusCode,
                    requestBody = it.requestBody,
                    url = it.url,
                    fullUrl = it.fullUrl,
                    // modified
                    responseBody = responseBody ?: it.responseBody,
                    fakedText = text ?: it.fakedText,
                    fakedStatus = status ?: it.fakedStatus,
                    contentLength = contentLength ?: it.contentLength
                )
            } else {
                it
            }
        }.toMutableList()
        semaphore.release()
    }

    internal fun Entry.updateAfterSettingRequestInfo(
        fullUrl: String,
        requestHeaders: Headers,
        requestBody: String?,
        parameters: Parameters
    ) {
        semaphore.acquire()
        entries = entries.map {
            if (it.id == id) {
                Entry(
                    id = it.id,
                    startDate = it.startDate,
                    method = it.method,
                    requestHeaders = requestHeaders,
                    url = it.url,
                    fullUrl = fullUrl,
                    parameters = parameters,
                    fakedText = it.fakedText,
                    fakedStatus = it.fakedStatus,
                    requestBody = requestBody,
                    // modified
                    endDate = LocalDateTime.now(),
                    responseHeaders = responseHeaders,
                    responseBody = responseBody,
                    statusCode = statusCode,
                    contentLength = contentLength
                )
            } else {
                it
            }
        }
        semaphore.release()
    }

    internal fun Entry.updateWithResponse(
        responseBody: String?,
        responseHeaders: StringValues,
        statusCode: Int,
        setEndDate: Boolean = true,
        contentLength: Long? = null
    ) {
        semaphore.acquire()
        entries = entries.map {
            if (it.id == id) {
                val endDate = if (setEndDate) {
                    LocalDateTime.now()
                } else {
                    it.endDate
                }
                Entry(
                    id = it.id,
                    startDate = it.startDate,
                    method = it.method,
                    requestHeaders = it.requestHeaders,
                    url = it.url,
                    fullUrl = it.fullUrl,
                    parameters = it.parameters,
                    fakedText = it.fakedText,
                    fakedStatus = it.fakedStatus,
                    requestBody = it.requestBody,
                    // modified
                    endDate = endDate,
                    responseHeaders = responseHeaders,
                    responseBody = responseBody,
                    statusCode = statusCode,
                    contentLength = contentLength
                )
            } else {
                it
            }
        }
        semaphore.release()
    }

    internal fun addEntry(
        method: RequestMethod,
        url: String?,
        fullUrl: String?,
        requestHeaders: StringValues = StringValues.Empty,
        requestBody: String? = null,
        contentLength: Long? = null,
        parameters: StringValues = StringValues.Empty
    ): Entry {
        semaphore.acquire()
        val entry = Entry(
            endDate = null,
            statusCode = null,
            responseBody = null,
            fakedText = null,
            fakedStatus = null,
            // modified
            id = getNewEntryId(),
            startDate = LocalDateTime.now(),
            method = method,
            url = url,
            fullUrl = fullUrl,
            requestHeaders = requestHeaders,
            parameters = parameters,
            responseHeaders = StringValues.Empty,
            requestBody = requestBody,
            contentLength = contentLength
        )
        entries += entry
        semaphore.release()
        return entry
    }

    fun addFakedEntry(
        text: String,
        status: FakedNetworkActivityStatus,
        fullUrl: String? = null,
        statusCode: Int? = null,
        requestBody: String? = null,
        requestHeaders: StringValues = StringValues.Empty,
        responseHeaders: StringValues = StringValues.Empty,
        responseBody: String? = null,
        contentLength: Long? = null
    ): Entry {
        semaphore.acquire()
        val entry = Entry(
            endDate = null,
            method = null,
            parameters = StringValues.Empty,
            // modified
            id = getNewEntryId(),
            responseHeaders = responseHeaders,
            requestHeaders = requestHeaders,
            requestBody = requestBody,
            statusCode = statusCode,
            startDate = LocalDateTime.now(),
            fullUrl = fullUrl,
            url = fullUrl,
            responseBody = responseBody,
            fakedText = text,
            fakedStatus = status,
            contentLength = contentLength
        )
        entries += entry
        semaphore.release()
        return entry
    }

}