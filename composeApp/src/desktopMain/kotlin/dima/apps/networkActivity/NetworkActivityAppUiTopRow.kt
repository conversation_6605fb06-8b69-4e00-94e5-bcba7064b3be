package dima.apps.networkActivity

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dima.globalState.GlobalState
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import isTextFieldFocused

@Composable
internal fun NetworkActivityAppUiTopRow(
    queryState: TextFieldValue,
    onQueryStateChange: (TextFieldValue) -> Unit,
    onQueryChange: (String) -> Unit,
    searchFocusRequester: FocusRequester,
    query: String,
    filtered: List<NetworkActivity.Entry>
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .focusable()
            .fillMaxWidth()
            .padding(horizontal = GlobalStyling.paddingToOuterWindow)
    ) {
        Text(
            if (GlobalState.networkActivity.followMode) "Follow Mode" else "",
            color = GlobalStyling.getOrangeColor(),
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier
                .weight(0.3f)
        )
        Row(
            modifier = Modifier
                // not using GlobalStyling.paddingToOuterWindow for compact layout
                .padding(top = 4.dp)
                .weight(0.4f)
        ) {
            TextFieldWithCandidateCountWithoutAnimation(
                value = queryState,
                onValueChange = {
                    onQueryStateChange(it)
                    onQueryChange(it.text)
                },
                topLabel = "Search",
                singleLine = true,
                focusRequester = searchFocusRequester,
                modifier = Modifier
                    .onFocusChanged {
                        isTextFieldFocused = it.isFocused
                    }
                    .fillMaxWidth()
            )
        }
        val text = if (query.trim() == "" || (filtered.size == NetworkActivity.entries.size)) {
            "${NetworkActivity.entries.size} entries"
        } else {
            "${filtered.size} / ${NetworkActivity.entries.size} entries"
        }
        Text(
            text,
            color = GlobalStyling.getTextColor(),
            textAlign = TextAlign.End,
            modifier = Modifier
                .weight(0.3f)
        )
    }
}
