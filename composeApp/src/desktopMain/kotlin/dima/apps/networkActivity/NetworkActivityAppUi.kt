package dima.apps.networkActivity

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.utils.handleAppMap
import handleLostFocus
import isTextFieldFocused

@Composable
internal fun NetworkActivityAppUi(
    appFocusRequester: FocusRequester,
    queryState: TextFieldValue,
    onQueryStateChange: (TextFieldValue) -> Unit,
    query: String,
    onQueryChange: (String) -> Unit,
    searchFocusRequester: FocusRequester,
    filtered: List<NetworkActivity.Entry>,
    entriesScroll: LazyListState,
    previewSelected: Boolean,
    responseScroll: ScrollState,
) {
    val entry = NetworkActivity.entries.find { it.id == NetworkActivity.selectedId }
    Column(
        verticalArrangement = Arrangement.spacedBy(6.dp),
        modifier = Modifier
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    if ((it.key == Key.Escape || it.key == Key.Enter) && it.type == KeyEventType.KeyDown) {
                        appFocusRequester.requestFocus()
                        return@onPreviewKeyEvent true
                    }
                    return@onPreviewKeyEvent false
                }
                return@onPreviewKeyEvent it.handleAppMap()
            },
    ) {
        NetworkActivityAppUiTopRow(queryState, onQueryStateChange, onQueryChange, searchFocusRequester, query, filtered)
        NetworkActivityAppUiEntriesColumn(entriesScroll, filtered, previewSelected, query)
        NetworkActivityAppUiBottomTabsDetailPreview(
            previewSelected = previewSelected,
            entry = entry,
            responseScroll = responseScroll,
        )
    }
}