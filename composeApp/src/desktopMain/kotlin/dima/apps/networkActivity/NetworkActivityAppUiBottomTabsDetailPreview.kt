package dima.apps.networkActivity

import GlobalStyling
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.apps.dired.addSelectedBorder
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.utils.scrollbarStyleThemed

@Composable
internal fun ColumnScope.NetworkActivityAppUiBottomTabsDetailPreview(
    previewSelected: Boolean,
    entry: NetworkActivity.Entry?,
    responseScroll: ScrollState,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier
            .fillMaxWidth()
            .weight(1f)
            .padding(12.dp)
            .addSelectedBorder(previewSelected)
            .padding(top = 8.dp, start = 12.dp, end = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier.padding(vertical = 4.dp)
        ) {
            NetworkActivityUiTab(NetworkActivityAppTab.All, hasContent = true)
            NetworkActivityUiTab(
                NetworkActivityAppTab.RequestBody,
                hasContent = entry?.requestBody != null
            )
            NetworkActivityUiTab(
                NetworkActivityAppTab.ResponseBody,
                hasContent = entry?.responseBody != null
            )
            NetworkActivityUiTab(
                NetworkActivityAppTab.Url,
                hasContent = entry?.url != null
            )
            val hasRequestHeaders = if (entry == null) {
                false
            } else {
                !entry.requestHeaders.isEmpty()
            }
            NetworkActivityUiTab(
                NetworkActivityAppTab.RequestHeaders,
                hasContent = hasRequestHeaders
            )
            val hasResponseHeaders = if (entry == null) {
                false
            } else {
                !entry.responseHeaders.isEmpty()
            }
            NetworkActivityUiTab(
                NetworkActivityAppTab.ResponseHeaders,
                hasContent = hasResponseHeaders
            )
            if (entry != null) {
                Spacer(modifier = Modifier.width(12.dp))
                if (entry.statusCode == null) {
                    if (entry.fakedStatus == null) {
                        Text(
                            "Loading...",
                            color = TailwindCssColors.white,
                            modifier = Modifier
                                .background(
                                    if (previewSelected) {
                                        TailwindCssColors.orange600
                                    } else {
                                        TailwindCssColors.gray500
                                    },
                                    RoundedCornerShape(5.dp)
                                )
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                } else {
                    Text(
                        entry.statusCode.toString(),
                        color = TailwindCssColors.white,
                        modifier = Modifier
                            .background(
                                if (entry.statusCode in 200..299) {
                                    TailwindCssColors.green700
                                } else {
                                    TailwindCssColors.red600
                                },
                                RoundedCornerShape(5.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }
        }
        if (entry != null) {
            Box(modifier = Modifier.fillMaxSize()) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier
                        .verticalScroll(responseScroll)
                        .padding(end = 12.dp, bottom = 12.dp)
                ) {
                    when (GlobalState.networkActivity.tab) {
                        NetworkActivityAppTab.All -> allTab(entry)

                        NetworkActivityAppTab.RequestBody -> {
                            if (entry.requestBody != null) {
                                SelectionContainer {
                                    Text(
                                        entry.requestBody,
                                        color = GlobalStyling.getTextColor(),
                                    )
                                }
                            }
                        }

                        NetworkActivityAppTab.ResponseBody -> {
                            if (entry.responseBody != null) {
                                SelectionContainer {
                                    Text(
                                        entry.responseBody,
                                        color = GlobalStyling.getTextColor(),
                                    )
                                }
                            }
                        }

                        NetworkActivityAppTab.Url -> {
                            if (entry.url != null) {
                                Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                                    url(entry)
                                    renderTable(entry.parameters)
                                }
                            }
                        }

                        NetworkActivityAppTab.RequestHeaders -> {
                            renderTable(entry.requestHeaders)
                        }

                        NetworkActivityAppTab.ResponseHeaders -> {
                            renderTable(entry.responseHeaders)
                        }
                    }
                }
                VerticalScrollbar(
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = responseScroll),
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .offset(x = 8.dp)
                        .padding(
                            bottom = GlobalStyling.ScrollBar.outerPadding,
                            end = if (previewSelected) GlobalStyling.ScrollBar.outerPadding else 0.dp
                        )
                        .fillMaxHeight(),
                )
            }
        }
    }
}
