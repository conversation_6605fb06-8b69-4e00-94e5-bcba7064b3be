package dima.apps.networkActivity

import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import io.ktor.http.*

private fun generateCurlCommand(entry: NetworkActivity.Entry): String {
    val commandParts = mutableListOf<String>()
    commandParts.add("curl")

    // Method
    entry.method?.let {
        // cURL defaults to GET. For POST with -d, -X POST is often implied.
        // However, explicitly setting it for non-GET methods is clearer.
        if (it != RequestMethod.GET) {
            commandParts.add("-X ${it.name}")
        }
    }

    // URL
    // Prefer fullUrl if available, otherwise fallback to url.
    // Curl handles query parameters in the URL string directly.
    val urlToUse = entry.fullUrl ?: entry.url ?: ""
    commandParts.add("'${urlToUse.replace("'", "'\\''")}'") // Single quote and escape internal single quotes

    // Headers
    entry.requestHeaders.forEach { name, values ->
        values.forEach { value ->
            val escapedHeaderName = name.replace("'", "'\\''")
            val escapedValue = value.replace("'", "'\\''")
            commandParts.add("-H '${escapedHeaderName}: ${escapedValue}'")
        }
    }

    // Body
    // Add -d if there's a requestBody and the method is one that typically carries a body,
    // or if the method is not specified (curl might default to POST with -d).
    if (entry.requestBody != null) {
        if (entry.method == RequestMethod.POST ||
            entry.method == null // If method is null and body exists, -d implies POST
        ) {
            val escapedBody = entry.requestBody.replace("'", "'\\''")
            commandParts.add("-d '${escapedBody}'")
        }
    }

    // Add --compressed if 'Accept-Encoding' includes 'gzip' or 'deflate'
    // This tells curl to decompress the response automatically if it's compressed.
    val header: String? = entry.requestHeaders[HttpHeaders.AcceptEncoding]
    if (header != null && (header.contains("gzip", ignoreCase = true) || header.contains(
            "deflate",
            ignoreCase = true
        ))
    ) {
        commandParts.add("--compressed")
    }
    return commandParts.joinToString(separator = " \\\n  ") // Backslash for line continuation
}

private fun generateHttpieCommand(entry: NetworkActivity.Entry): String {
    val commandParts = mutableListOf<String>()
    val urlToUse = entry.fullUrl ?: entry.url ?: ""
    val escapedUrl = "'${urlToUse.replace("'", "'\\''")}'" // Single quote and escape internal single quotes

    if (entry.requestBody != null) {
        val escapedBody = entry.requestBody.replace("'", "'\\''")
        commandParts.add("echo '${escapedBody}' |")
        commandParts.add("http")
    } else {
        commandParts.add("http")
    }

    // Method
    // Httpie defaults to GET if no data items are present and no method is specified.
    // If data is piped (echo ... | http), it defaults to POST.
    // Explicitly set method for clarity, especially if it's not GET/POST or if a body implies POST.
    entry.method?.let {
        if (it != RequestMethod.GET || entry.requestBody != null) { // Add GET if body is present, otherwise it's implied POST
            commandParts.add(it.name)
        }
    }

    commandParts.add(escapedUrl)

    // Headers
    entry.requestHeaders.forEach { name, values ->
        values.forEach { value ->
            // Httpie header format: HeaderKey:HeaderValue (can be quoted if needed)
            val escapedHeaderName = name.replace("'", "'\\''")
            val escapedValue = value.replace("'", "'\\''")
            commandParts.add("'${escapedHeaderName}:${escapedValue}'") // Note: Httpie is flexible with/without space after colon
        }
    }
    return commandParts.joinToString(separator = " \\\n  ")
}


internal fun openCopyPartsDialog(filtered: List<NetworkActivity.Entry>) {
    val entry = filtered.find { it.id == NetworkActivity.selectedId }
    if (entry == null) {
        return
    }
    val candidates = buildList {
        add("cURL command")
        add("httpie command")
        if (entry.fullUrl != null) {
            add("URL")
        }
        if (entry.requestBody != null) {
            add("Request body")
        }
        if (entry.responseBody != null) {
            add("Response body")
        }
    }
    openCompletionDialog("Copy", candidates, hideCopyCmdActionInBottomBar = true) {
        when (it.text) {
            "cURL command" -> {
                val curlCommand = generateCurlCommand(entry)
                copyToClipboard(curlCommand)
            }
            "httpie command" -> {
                val httpieCommand = generateHttpieCommand(entry)
                copyToClipboard(httpieCommand)
            }
            "URL" -> copyToClipboard(entry.fullUrl!!)
            "Request body" -> copyToClipboard(entry.requestBody!!)
            "Response body" -> copyToClipboard(entry.responseBody!!)
        }
    }
}
