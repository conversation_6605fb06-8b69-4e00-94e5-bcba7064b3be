package dima.apps.networkActivity

import GlobalStyling
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.X
import dima.globalState.GlobalState
import dima.utils.clickableWithoutBackgroundRipple

@Composable
fun NetworkActivityUiTab(tab: NetworkActivityAppTab, hasContent: Boolean) {
    val isSelected = (GlobalState.networkActivity.tab == tab)
    val alpha = if (hasContent) 1f else GlobalStyling.DISABLED_ALPHA
    val color = GlobalStyling.getTextColor()
    val originalStrokeWidthForPadding = 3.dp

    Row(
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (!hasContent) {
            Icon(
                Lucide.X,
                contentDescription = null,
                tint = color.copy(alpha = alpha),
                modifier = Modifier.size(12.dp)
            )
        }
        Text(
            when (tab) {
                NetworkActivityAppTab.All -> "All"
                NetworkActivityAppTab.RequestBody -> "Request body"
                NetworkActivityAppTab.ResponseBody -> "Response body"
                NetworkActivityAppTab.Url -> "URL"
                NetworkActivityAppTab.RequestHeaders -> "Request headers"
                NetworkActivityAppTab.ResponseHeaders -> "Response headers"
            },
            color = color.copy(alpha = alpha),
            letterSpacing = (-0.2).sp,
            modifier = Modifier
                .clickableWithoutBackgroundRipple {
                    GlobalState.networkActivity = GlobalState.networkActivity.copy(tab = tab)
                }
                .then(
                    if (isSelected) {
                        Modifier
                            .drawBehind {
                                val strokeWidthPx = originalStrokeWidthForPadding.toPx()
                                val lineY = size.height - strokeWidthPx / 2f + 10
                                drawLine(
                                    color = GlobalStyling.getSelectedBorderColor(),
                                    strokeWidth = strokeWidthPx,
                                    start = Offset(if (hasContent) 0f else -28f, lineY),
                                    end = Offset(size.width, lineY)
                                )
                            }
                    } else {
                        Modifier
                    }
                )
        )
    }
}
