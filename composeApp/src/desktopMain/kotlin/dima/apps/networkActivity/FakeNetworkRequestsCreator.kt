package dima.apps.networkActivity

import dima.settings
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.request.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal fun createFakeRequests(coroutineScope: CoroutineScope) {
    coroutineScope.launch(Dispatchers.IO) {
        HttpClient(CIO).apply {
            getLogged("https://oeuthnhoetneuhotnuhoehnoehu.com/")
        }
    }
    coroutineScope.launch(Dispatchers.IO) {
        NetworkActivity.addFakedEntry("yoo", FakedNetworkActivityStatus.Info)
        repeat(2) {
            HttpClient(CIO).apply {
                getLogged("http://localhost:${settings.serverPort}/foooooooooooooooooooooooooofffffffffffoo/ooooooooofffffffffffffffffffffffffffoooooooooooooooooooooooooooommmmmmmmmmmmmmmwwwwwwwwwwwwwwwwwww")
            }
        }
        HttpClient(CIO).apply {
            deleteLogged("http://localhost:${settings.serverPort}/postman") {
                header(
                    "yoooooooooooooooooooo-brooooooooooooo",
                    "mmmmmmmmmmmmmmmmmmmmmmmmm mmmmmmmmmmmmmmmmmmmmmmmmmmm mmmmmmmmmmmmmmmmmmmmmm mmmmmmmmmmmmmmmmmm"
                )
                header("User-Agent", "bro")
            }
        }
        HttpClient(CIO).apply {
            postLogged("http://localhost:${settings.serverPort}/postman") {
                setBody("test")
                header(
                    "yoooooooooooooooooooo-brooooooooooooo",
                    "mmmmmmmmmmmmmmmmmmmmmmmmm mmmmmmmmmmmmmmmmmmmmmmmmmmm mmmmmmmmmmmmmmmmmmmmmm mmmmmmmmmmmmmmmmmm"
                )
                header("User-Agent", "bro")
            }
        }
    }
}