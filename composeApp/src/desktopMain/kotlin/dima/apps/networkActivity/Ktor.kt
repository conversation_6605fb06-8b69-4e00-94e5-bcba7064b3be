package dima.apps.networkActivity

import dima.apps.networkActivity.NetworkActivity.updateAfterSettingRequestInfo
import dima.apps.networkActivity.NetworkActivity.updateWithResponse
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.client.utils.*
import io.ktor.http.*
import io.ktor.http.content.*
import io.ktor.util.*
import io.ktor.utils.io.charsets.*
import kotlinx.io.InternalIoApi
import kotlinx.io.Source

enum class RequestMethod {
    GET,
    POST,
    DELETE
}

/**
 * Always check if [bodyBytes] is not null first.
 */
data class HttpResponse(
    val statusCode: Int,
    val body: String,
    val bodyBytes: ByteArray? = null,
    val contentLength: Long,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as HttpResponse
        if (statusCode != other.statusCode) return false
        if (contentLength != other.contentLength) return false
        if (body != other.body) return false
        if (!bodyBytes.contentEquals(other.bodyBytes)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = statusCode
        result = 31 * result + contentLength.hashCode()
        result = 31 * result + body.hashCode()
        result = 31 * result + bodyBytes.contentHashCode()
        return result
    }
}

/**
 * On any exception, status code 0 is returned.
 *
 * @param url should not contain query parameters
 */
suspend fun HttpClient.getLogged(
    url: String,
    binaryResponse: Boolean = false,
    request: HttpRequestBuilder.() -> Unit = {}
): HttpResponse {
    return executeLoggedRequest(
        url = url,
        method = RequestMethod.GET,
        binaryResponse = binaryResponse,
        formData = null,
        request = request
    )
}

/**
 * See [getLogged].
 */
suspend fun HttpClient.deleteLogged(url: String, request: HttpRequestBuilder.() -> Unit = {}): HttpResponse {
    return executeLoggedRequest(
        url = url,
        method = RequestMethod.DELETE,
        binaryResponse = null,
        formData = null,
        request = request
    )
}

/**
 * See [getLogged].
 */
suspend fun HttpClient.postLogged(url: String, request: HttpRequestBuilder.() -> Unit = {}): HttpResponse {
    return executeLoggedRequest(
        url = url,
        method = RequestMethod.POST,
        binaryResponse = null,
        formData = null,
        request = request
    )
}

suspend fun HttpClient.submitFormWithBinaryDataLogged(
    url: String,
    formData: List<PartData>,
    block: HttpRequestBuilder.() -> Unit = {}
): HttpResponse {
    return executeLoggedRequest(
        url = url,
        method = RequestMethod.POST,
        binaryResponse = null,
        formData = formData,
    ) {
        block()
        setBody(MultiPartFormDataContent(formData))
    }
}

/**
 * Executes a logged HTTP request and handles common exceptions.
 *
 * @param url URL of the request
 * @param method HTTP method for the request
 * @param request A lambda for additional request configuration
 * @return HttpResponse
 */
private suspend fun HttpClient.executeLoggedRequest(
    url: String,
    method: RequestMethod,
    binaryResponse: Boolean? = null,
    formData: List<PartData>? = null,
    request: HttpRequestBuilder.() -> Unit
): HttpResponse {

    fun createFakeRequestBodyFromException(exception: Exception): String {
        var result = exception::class.simpleName ?: "Exception"
        if (exception.message != null) {
            result += ": ${exception.message}"
        }
        return result
    }

    fun handleLoggingException(log: NetworkActivity.Entry, exception: Exception) {
        log.updateWithResponse(
            responseBody = createFakeRequestBodyFromException(exception),
            responseHeaders = StringValues.Empty,
            statusCode = 0,
        )
    }

    fun HttpRequestBuilder.updateLogEntryAfterSettingRequestInfo(
        log: NetworkActivity.Entry,
        method: RequestMethod,
    ) {
        log.updateAfterSettingRequestInfo(
            fullUrl = this.url.toString(),
            requestHeaders = headers.build(),
            requestBody = when {
                method == RequestMethod.POST || bodyType != null -> {
                    when (body) {
                        is EmptyContent -> null
                        is MultiPartFormDataContent -> {
                            val contentType = (body as MultiPartFormDataContent).contentType
                            val boundary = contentType.parameter("boundary")
                            val formInfo = formData?.joinToString("\n") { part ->
                                val name = part.name ?: "unnamed"
                                when (part) {
                                    is PartData.FileItem -> "$name: file '${part.originalFileName}'"
                                    is PartData.BinaryItem -> "$name: binary data"
                                    is PartData.FormItem -> "$name: ${part.value}"
                                    is PartData.BinaryChannelItem -> "$name: binary channel"
                                }
                            }
                            if (formInfo.isNullOrEmpty()) {
                                "[multipart/form-data with boundary=$boundary]"
                            } else {
                                "[multipart/form-data]\n$formInfo"
                            }
                        }

                        else -> body as String
                    }
                }

                else -> null
            },
            parameters = this.url.parameters.build()
        )
    }

    @OptIn(InternalIoApi::class)
    suspend fun mapKtorResponseToLoggedResponse(
        response: io.ktor.client.statement.HttpResponse,
        log: NetworkActivity.Entry?,
        binary: Boolean?
    ): HttpResponse {
        val binaryResponse = binary ?: run {
            // automatically determine binary by Content-Type
            val ct = response.headers[HttpHeaders.ContentType]?.lowercase()?.trim()
            ct != null && (ct.startsWith("application/octet-stream")
                || ct.startsWith("image/")
                || ct.startsWith("audio/")
                || ct.startsWith("video/")
                || ct.contains("octet-stream"))
        }

        return if (binaryResponse) {
            log!!.updateWithResponse(
                responseBody = "[BINARY]",
                statusCode = response.status.value,
                responseHeaders = response.headers,
            )
            HttpResponse(
                statusCode = response.status.value,
                body = "",
                bodyBytes = response.bodyAsBytes(),
                contentLength = response.contentLength() ?: 0
            )
        } else {
            // copy implementation from Ktor for response.bodyAsText()
            val originCharset = response.charset() ?: Charsets.UTF_8
            val decoder = originCharset.newDecoder()
            val input = response.body<Source>()
            val contentLength = input.buffer.size
            val stringResponse = decoder.decode(input)
            log!!.updateWithResponse(
                responseBody = stringResponse,
                statusCode = response.status.value,
                responseHeaders = response.headers,
                contentLength = contentLength
            )
            return HttpResponse(
                statusCode = response.status.value,
                body = stringResponse,
                contentLength = contentLength
            )
        }
    }

    // create a dummy entry because calling request() can already crash
    val log: NetworkActivity.Entry = NetworkActivity.addEntry(
        method = method,
        url = url,
        fullUrl = url
    )
    val response: io.ktor.client.statement.HttpResponse
    return try {
        response = when (method) {
            RequestMethod.GET -> get(url) {
                request()
                updateLogEntryAfterSettingRequestInfo(log, method)
            }

            RequestMethod.DELETE -> delete(url) {
                request()
                updateLogEntryAfterSettingRequestInfo(log, method)
            }

            RequestMethod.POST -> post(url) {
                request()
                updateLogEntryAfterSettingRequestInfo(log, method)
            }
        }
        mapKtorResponseToLoggedResponse(
            response = response,
            log = log,
            binary = if (method == RequestMethod.GET) binaryResponse else null
        )
    } catch (e: Exception) {
        handleLoggingException(log, e)
        HttpResponse(statusCode = 0, body = createFakeRequestBodyFromException(e), contentLength = 0)
    }
}
