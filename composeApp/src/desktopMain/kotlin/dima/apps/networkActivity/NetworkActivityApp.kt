package dima.apps.networkActivity

import GlobalEvent
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import dima.dialogs.confirmation.openConfirmationDialog
import dialogs
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.utils.AppKey
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import dima.utils.filterHuman
import globalEvent
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun NetworkActivityApp() {
    val appFocusRequester = remember { FocusRequester() }
    val entriesScroll = rememberLazyListState()
    val responseScroll = rememberScrollState()
    val responseScrollVelocity = remember { ScrollVelocity(responseScroll) }
    var previewSelected by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    val searchFocusRequester = remember { FocusRequester() }
    var filtered by remember { mutableStateOf<List<NetworkActivity.Entry>>(emptyList()) }
    var query by remember { mutableStateOf(GlobalState.networkActivity.query) }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = when {
                    query.isEmpty() -> TextRange.Zero
                    else -> TextRange(query.length, query.length)
                }
            )
        )
    }

    remember {
        appKeys = listOf(
            // use KeyUp to not insert s into the search
            AppKey(Key.S, "Focus search", onKeyUp = {
                previewSelected = false
                searchFocusRequester.requestFocus()
            }),
            AppKey(Key.D, "Delete current entry") {
                if (NetworkActivity.selectedId == null) {
                    return@AppKey
                }
                val entry = filtered.find { it.id == NetworkActivity.selectedId!! }
                if (entry == null) {
                    return@AppKey
                }
                openConfirmationDialog("Delete current entry?", entry.fullUrl) {
                    val oldIndex = filtered.indexOfFirst { it.id == NetworkActivity.selectedId }
                    NetworkActivity.entries = NetworkActivity.entries.filter { it.id != NetworkActivity.selectedId }
                    var new = filtered.getOrNull(oldIndex)?.id
                    if (new == null) {
                        new = filtered.lastOrNull()?.id
                    }
                    NetworkActivity.selectedId = new
                }
            },
            AppKey(Key.Z, "Delete all entries") {
                openConfirmationDialog("Delete all entries?") {
                    NetworkActivity.entries = emptyList()
                }
            },
            AppKey(Key.N, "Focus tab to the right") {
                GlobalState.networkActivity = GlobalState.networkActivity.copy(
                    tab = NetworkActivityAppTab.entries[(NetworkActivityAppTab.entries.indexOf(GlobalState.networkActivity.tab) + 1)
                        .coerceAtMost(NetworkActivityAppTab.entries.size - 1)]
                )
            },
            AppKey(Key.H, "Focus tab to the left") {
                GlobalState.networkActivity = GlobalState.networkActivity.copy(
                    tab = NetworkActivityAppTab.entries[(NetworkActivityAppTab.entries.indexOf(GlobalState.networkActivity.tab) - 1)
                        .coerceAtLeast(0)]
                )
            },
            AppKey(Key.Escape, "Leave search and focus entries") {
                appFocusRequester.requestFocus()
                previewSelected = false
            },
            AppKey(Key.Enter, "Select response") { previewSelected = true },
            AppKey(Key.J, "Copy parts") {
                openCopyPartsDialog(filtered)
            },
            AppKey(Key.F, "Toggle follow mode") {
                GlobalState.networkActivity =
                    GlobalState.networkActivity.copy(followMode = !GlobalState.networkActivity.followMode)
            },
            AppKey(Key.A, "Generate different fake") {
                createFakeRequests(coroutineScope)
            },
            AppKey(Key.T, "Select below", onKeyUp = { responseScrollVelocity.onKeyReleased() }, onKeyDown = {
                isFirstEntrySelected = false
                if (previewSelected) {
                    responseScrollVelocity.onScrollDownKeyPressed()
                } else {
                    if (NetworkActivity.selectedId == null) {
                        NetworkActivity.selectedId = filtered.firstOrNull()?.id
                    } else {
                        val currentIndex = filtered.indexOfFirst {
                            it.id == NetworkActivity.selectedId!!
                        }
                        if (currentIndex == -1) {
                            NetworkActivity.selectedId = filtered.firstOrNull()?.id
                        } else {
                            val next = filtered.getOrNull(currentIndex + 1)
                            if (next != null) {
                                NetworkActivity.selectedId = next.id
                            }
                        }
                    }
                    updateFirstEntrySelected()
                }
            }),
            AppKey(Key.C, "Select above", onKeyUp = { responseScrollVelocity.onKeyReleased() }, onKeyDown = {
                isFirstEntrySelected = false
                if (previewSelected) {
                    responseScrollVelocity.onScrollUpKeyPressed()
                } else {
                    if (NetworkActivity.selectedId == null) {
                        NetworkActivity.selectedId = filtered.firstOrNull()?.id
                    } else {
                        val currentIndex = filtered.indexOfFirst {
                            it.id == NetworkActivity.selectedId!!
                        }
                        if (currentIndex == -1) {
                            NetworkActivity.selectedId = filtered.firstOrNull()?.id
                        } else {
                            val next = filtered.getOrNull(currentIndex - 1)
                            if (next != null) {
                                NetworkActivity.selectedId = next.id
                            }
                        }
                    }
                }
                updateFirstEntrySelected()
            }),
            AppKey(Key.M, "Select 6 below", onKeyUp = { responseScrollVelocity.onKeyReleased() }, onKeyDown = {
                isFirstEntrySelected = false
                if (previewSelected) {
                    responseScrollVelocity.onScrollDownMoreKeyPressed()
                } else {
                    if (NetworkActivity.selectedId == null) {
                        NetworkActivity.selectedId = filtered.firstOrNull()?.id
                    } else {
                        val currentIndex = filtered.indexOfFirst {
                            it.id == NetworkActivity.selectedId!!
                        }
                        if (currentIndex == -1) {
                            NetworkActivity.selectedId = filtered.firstOrNull()?.id
                        } else {
                            val next = filtered.getOrNull(currentIndex + 6)
                            NetworkActivity.selectedId = next?.id ?: filtered.lastOrNull()?.id
                        }
                    }
                }
                updateFirstEntrySelected()
            }),
            AppKey(Key.V, "Select 6 above", onKeyUp = { responseScrollVelocity.onKeyReleased() }, onKeyDown = {
                isFirstEntrySelected = false
                if (previewSelected) {
                    responseScrollVelocity.onScrollUpMoreKeyPressed()
                } else {
                    if (NetworkActivity.selectedId == null) {
                        NetworkActivity.selectedId = filtered.firstOrNull()?.id
                    } else {
                        val currentIndex = filtered.indexOfFirst {
                            it.id == NetworkActivity.selectedId!!
                        }
                        if (currentIndex == -1) {
                            NetworkActivity.selectedId = filtered.firstOrNull()?.id
                        } else {
                            val next = filtered.getOrNull(currentIndex - 6)
                            NetworkActivity.selectedId = next?.id ?: filtered.firstOrNull()?.id
                        }
                    }
                }
                updateFirstEntrySelected()
            })
        )
    }

    NetworkActivityAppUi(
        appFocusRequester = appFocusRequester,
        queryState = queryState,
        onQueryStateChange = { queryState = it },
        query = query,
        onQueryChange = { query = it },
        searchFocusRequester = searchFocusRequester,
        filtered = filtered,
        entriesScroll = entriesScroll,
        previewSelected = previewSelected,
        responseScroll = responseScroll
    )

    var firstOpen by remember { mutableStateOf(true) }

    fun scrollTo(animateScroll: Boolean = true) {
        val index = filtered.indexOfFirst { it.id == NetworkActivity.selectedId!! }
        if (index != -1) {
            coroutineScope.launch {
                // delay a bit so scrolling on app open works correctly
                delay(10)
                if (animateScroll) {
                    entriesScroll.animateScrollToItem(index, -300)
                } else {
                    entriesScroll.scrollToItem(index, -300)
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
        responseScrollVelocity.loopForeverAndTick()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(query, NetworkActivity.entries) {
        val reversed = NetworkActivity.entries.reversed()
        if (reversed.isEmpty()) {
            NetworkActivity.selectedId = null
        } else if (NetworkActivity.selectedId == null) {
            NetworkActivity.selectedId = NetworkActivity.entries.first().id
        }
        filtered = if (query.isBlank()) {
            reversed
        } else {
            reversed
                .filter { it.fullUrl != null || it.fakedText != null }
                .filterHuman(query) {
                    buildList {
                        if (it.fullUrl != null) {
                            add(it.fullUrl)
                        }
                        if (it.fakedText != null) {
                            add(it.fakedText)
                        }
                    }.joinToString(" ")
                }
        }
        scrollTo(animateScroll = !firstOpen)
        firstOpen = false
        GlobalState.networkActivity = GlobalState.networkActivity.copy(query = query)
    }
    LaunchedEffect(NetworkActivity.entries, NetworkActivity.selectedId) {
        if (GlobalState.networkActivity.followMode) {
            val shouldSet = ((NetworkActivity.selectedId == null) || isFirstEntrySelected) && !previewSelected
            if (shouldSet) {
                val firstId = filtered.firstOrNull()?.id
                NetworkActivity.selectedId = firstId
            }
        }
        if (NetworkActivity.selectedId != null) {
            scrollTo()
        }
        updateFirstEntrySelected()
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                if (previewSelected) {
                    responseScrollVelocity.scrollToTop()
                } else {
                    NetworkActivity.selectedId = filtered.firstOrNull()?.id
                }
                isFirstEntrySelected = true
            }

            GlobalEvent.ScrollToBottom -> {
                if (previewSelected) {
                    responseScrollVelocity.scrollToBottom()
                } else {
                    NetworkActivity.selectedId = filtered.lastOrNull()?.id
                }
                isFirstEntrySelected = filtered.size <= 1
            }

            else -> {}
        }
    }
}