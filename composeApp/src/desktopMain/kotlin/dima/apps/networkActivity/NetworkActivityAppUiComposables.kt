package dima.apps.networkActivity

import GlobalStyling
import Globals
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import dima.apps.networkActivity.NetworkActivity.getDurationWithColor
import dima.dateTime.DateTimeFormat
import dima.utils.DarkLightDivider
import dima.utils.toCompactHumanByteString
import io.ktor.util.*

@Composable
internal fun renderTable(stringValues: StringValues) {
    if (stringValues.isEmpty()) {
        return
    }
    val measurer = TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
    var widestKey = 0
    val keyWidthPx = 450
    stringValues.entries().forEach { (key, _) ->
        val width = measurer.measure(
            key,
            style = TextStyle(fontWeight = FontWeight.Bold),
            constraints = Constraints(minWidth = keyWidthPx, maxWidth = keyWidthPx)
        ).size.width
        if (width > widestKey) {
            widestKey = width
        }
    }
    val widestKeyDp = with(Globals.density) {
        widestKey.toDp()
    }
    val color = GlobalStyling.getTextColor()
    SelectionContainer {
        Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            stringValues.entries().forEach { (key, values) ->
                values.forEach { value ->
                    Row {
                        Text(
                            text = key,
                            fontWeight = FontWeight.Bold,
                            color = color,
                            modifier = Modifier
                                .widthIn(min = widestKeyDp, max = widestKeyDp)
                        )
                        Text(
                            text = value,
                            color = color,
                            modifier = Modifier
                        )
                    }
                }
            }
        }
    }
}

@Composable
internal fun startTime(entry: NetworkActivity.Entry) {
    Text(
        entry.startDate.format(DateTimeFormat.hourMinuteSecondSeperatedByColon),
        color = GlobalStyling.getTextColor(),
        textAlign = TextAlign.End,
    )
}

@Composable
internal fun duration(entry: NetworkActivity.Entry) {
    val maxLength = 5
    var durationWithDuration: NetworkActivity.DurationWithDuration? = null
    val s = if (entry.endDate == null) {
        " ".repeat(maxLength)
    } else {
        durationWithDuration = entry.getDurationWithColor()
        durationWithDuration.humanReadable.padStart(5)
    }
    val color = when (durationWithDuration) {
        null -> GlobalStyling.getTextColor()
        else -> durationWithDuration.color
    }
    Text(
        s,
        color = color,
        textAlign = TextAlign.End,
        // max length string is "999ms"
        modifier = Modifier.width(50.dp)
    )
}

@Composable
internal fun contentLength(entry: NetworkActivity.Entry) {
    val maxLength = 5
    // ignore -1
    val s = if (entry.contentLength != null && entry.contentLength >= 1) {
        entry.contentLength.toCompactHumanByteString().padStart(maxLength)
    } else {
        " ".repeat(maxLength)
    }
    val color = when (entry.contentLength) {
        null -> {
            GlobalStyling.getTextColor()
        }

        in 1..(1024 * 3) -> {
            // check for 3 MB
            GlobalStyling.getGreenColor()
        }

        in 1..((1024 * 3) / 2) -> {
            // check for 1.5 MB
            GlobalStyling.getOrangeColor()
        }

        else -> {
            GlobalStyling.getRedTextColor()
        }
    }
    Text(
        s,
        color = color,
        textAlign = TextAlign.End,
        // max length string is "1008G"
        modifier = Modifier.width(50.dp),
    )
}

@Composable
internal fun statusCode(entry: NetworkActivity.Entry) {
    if (entry.statusCode != null) {
        Text(
            entry.statusCode.toString(),
            color = when (entry.statusCode) {
                in 200..299 -> GlobalStyling.getGreenColor()
                else -> GlobalStyling.getRedTextColor()
            },
        )
    }
}

@Composable
internal fun url(entry: NetworkActivity.Entry) {
    if (entry.fullUrl != null) {
        SelectionContainer {
            Text(
                buildAnnotatedString {
                    if (entry.method != null) {
                        append(
                            AnnotatedString(
                                entry.method.toString(),
                                spanStyle = SpanStyle(fontWeight = FontWeight.SemiBold)
                            )
                        )
                        append("  ")
                    }
                    append(entry.fullUrl)
                },
                color = GlobalStyling.getTextColor(),
            )
        }
    }
}

@Composable
internal fun allTab(entry: NetworkActivity.Entry) {
    val color = GlobalStyling.getTextColor()
    if (entry.fakedText == null) {
        var needsDivider = false
        if (entry.url != null) {
            url(entry)
            renderTable(entry.parameters)
            needsDivider = true
        }
        if (entry.requestBody != null) {
            if (needsDivider) {
                DarkLightDivider()
            }
            Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                Text(
                    "Request body",
                    color = color,
                    fontWeight = FontWeight.SemiBold
                )
                SelectionContainer {
                    Text(
                        entry.requestBody,
                        color = color,
                    )
                }
            }
            needsDivider = true
        }
        if (entry.responseBody != null) {
            if (needsDivider) {
                DarkLightDivider()
            }
            Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                Text(
                    "Response body",
                    color = color,
                    fontWeight = FontWeight.SemiBold
                )
                SelectionContainer {
                    Text(
                        entry.responseBody,
                        color = color,
                    )
                }
            }
            needsDivider = true
        }
        if (!entry.requestHeaders.isEmpty()) {
            if (needsDivider) {
                DarkLightDivider()
            }
            Text(
                "Request headers",
                color = color,
                fontWeight = FontWeight.SemiBold
            )
            renderTable(entry.requestHeaders)
            needsDivider = true
        }
        if (!entry.responseHeaders.isEmpty()) {
            if (needsDivider) {
                DarkLightDivider()
            }
            Text(
                "Response headers",
                color = color,
                fontWeight = FontWeight.SemiBold
            )
            renderTable(entry.responseHeaders)
        }
    } else {
        SelectionContainer {
            Text(
                entry.fakedText,
                color = color,
            )
        }
        if (entry.fullUrl != null) {
            DarkLightDivider()
            url(entry)
        }
        if (entry.responseBody != null) {
            DarkLightDivider()
            SelectionContainer {
                Text(
                    entry.responseBody,
                    color = color,
                )
            }
        }
    }
}
