package dima.apps.networkActivity

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.text.TextMarked
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleThemed

@Composable
internal fun ColumnScope.NetworkActivityAppUiEntriesColumn(
    entriesScroll: LazyListState,
    filtered: List<NetworkActivity.Entry>,
    previewSelected: <PERSON>olean,
    query: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .weight(1f)
            .padding(top = 12.dp, start = 12.dp, end = 12.dp)
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            SelectionContainer {
                LazyColumn(
                    state = entriesScroll,
                    verticalArrangement = Arrangement.spacedBy(1.dp),
                    modifier = Modifier
                        .padding(end = if (entriesScroll.canScrollBackward || entriesScroll.canScrollForward) 12.dp else 0.dp)
                ) {
                    items(filtered, key = { it.id }) { entry ->
                        val isSelected = (NetworkActivity.selectedId == entry.id)
                        Column(
                            modifier = Modifier
                                .clickableWithoutBackgroundRipple {
                                    NetworkActivity.selectedId = entry.id
                                }
                                .background(
                                    if (isSelected && previewSelected) {
                                        GlobalStyling.getSelectedInactiveBackgroundColor()
                                    } else {
                                        if (entry.fakedText != null) {
                                            if (isSelected) {
                                                GlobalStyling.getSelectedBackgroundColor()
                                            } else {
                                                TailwindCssColors.transparent
                                            }
                                        } else if (isSelected) {
                                            GlobalStyling.getSelectedBackgroundColor()
                                        } else {
                                            GlobalStyling.getWindowBackgroundColor()
                                        }
                                    },
                                    GlobalStyling.smallRoundedCorners
                                )
                                .alpha(if (previewSelected && !isSelected) 0.4f else 1f)
                                .padding(8.dp)
                                .fillMaxWidth()
                        ) {
                            if (entry.fakedText == null) {
                                Column {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                                    ) {
                                        Text(
                                            if (entry.statusCode == null) {
                                                "○"
                                            } else {
                                                "●"
                                            },
                                            color = when (entry.statusCode) {
                                                null -> TailwindCssColors.orange600
                                                in 200..299 -> TailwindCssColors.green600
                                                else -> TailwindCssColors.red600
                                            },
                                            fontSize = 12.sp
                                        )
                                        if (entry.method != null) {
                                            Text(
                                                entry.method.toString(),
                                                textAlign = TextAlign.End,
                                                color = when (entry.method) {
                                                    RequestMethod.GET -> GlobalStyling.getBlueColor()
                                                    RequestMethod.POST -> GlobalStyling.getOrangeColor()
                                                    RequestMethod.DELETE -> GlobalStyling.getRedTextColor()
                                                },
                                                // make "DELETE" fit for end alignment
                                                modifier = Modifier.width(55.dp)
                                            )
                                        }
                                        if (entry.url != null) {
                                            TextMarked(
                                                shortenUrlForList(entry.url),
                                                query,
                                                color = GlobalStyling.getTextColor(),
                                                overflow = TextOverflow.Ellipsis,
                                                maxLines = 1,
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                        statusCode(entry)
                                        contentLength(entry)
                                        duration(entry)
                                        startTime(entry)
                                    }
                                }
                            } else {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                                ) {
                                    Text(
                                        if (entry.fakedStatus == FakedNetworkActivityStatus.InTransit) {
                                            "○"
                                        } else {
                                            "●"
                                        },
                                        color = when (entry.fakedStatus) {
                                            FakedNetworkActivityStatus.Info, null -> TailwindCssColors.blue600
                                            FakedNetworkActivityStatus.InTransit -> TailwindCssColors.gray600
                                            FakedNetworkActivityStatus.Success -> TailwindCssColors.green600
                                            FakedNetworkActivityStatus.Error -> TailwindCssColors.red600
                                        },
                                        fontSize = 12.sp
                                    )
                                    // use 55.dp width from "DELETE" to align
                                    Text("", modifier = Modifier.width(55.dp))
                                    val color = GlobalStyling.getTextColor()
                                    Column(modifier = Modifier.weight(1f)) {
                                        TextMarked(
                                            entry.fakedText,
                                            query,
                                            color = color,
                                        )
                                        if (entry.url != null) {
                                            TextMarked(
                                                shortenUrlForList(entry.url),
                                                query,
                                                overflow = TextOverflow.Ellipsis,
                                                maxLines = 1,
                                                color = color,
                                            )
                                        }
                                    }
                                    statusCode(entry)
                                    contentLength(entry)
                                    duration(entry)
                                    startTime(entry)
                                }
                            }
                        }
                    }
                }
            }
            VerticalScrollbar(
                style = scrollbarStyleThemed(),
                adapter = rememberScrollbarAdapter(entriesScroll),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
            )
        }
    }
}
