package dima.apps.dired

import dima.apps.notifications.showErrorNotification
import dima.globalState.GlobalState
import dima.utils.ScrollVelocity
import java.io.File

// Key.H
fun goDirectoryBack(
    getEntry: () -> Dired.Entry?,
    changeAvoidAnimatedScrollOnDirectoryChange: (Boolean) -> Unit,
) {
    if (GlobalState.dired.isPreviewActive) {
        val entry = getEntry() ?: return
        if (entry.fileTypeWithColor.type == FileType.Image) {
            GlobalState.dired.setImageState(
                GlobalState.dired.imageState.copy(
                    offsetX = GlobalState.dired.imageState.offsetX + imageTranslationStep
                )
            )
        }
        return
    }
    val currentDirectory = File(GlobalState.dired.directory)
    val parent = currentDirectory.parent
    if (parent == null) {
        showErrorNotification("No parent directory to go back", durationMillis = 500)
        return
    }
    changeAvoidAnimatedScrollOnDirectoryChange(true)
    DiredDatabase.rememberSelected()
    GlobalState.dired = GlobalState.dired.copy(
        directory = parent,
        selectedFile = currentDirectory.absolutePath
    )
}

// Key.V
fun selectMultipleAbove(
    getEntry: () -> Dired.Entry?,
    filtered: List<Dired.Entry>,
    previewScrollVelocity: ScrollVelocity
) {
    if (GlobalState.dired.isPreviewActive) {
        val entry = getEntry() ?: return
        when (entry.fileTypeWithColor.type) {
            FileType.Image -> {
                if (GlobalState.dired.imageState.zoom >= 0.2f) {
                    GlobalState.dired.setImageState(
                        GlobalState.dired.imageState.copy(
                            zoom = GlobalState.dired.imageState.zoom - imageZoomStep
                        )
                    )
                }
                return
            }

            FileType.Pdf -> {
                previewScrollVelocity.onScrollUpMoreKeyPressed()
                return
            }

            FileType.Text, FileType.Video, FileType.Markdown -> {
                previewScrollVelocity.onScrollUpMoreKeyPressed()
                return
            }

            else -> {}
        }
        if (entry.fileTypeWithColor.type.isTreeSitterLanguage) {
            previewScrollVelocity.onScrollUpMoreKeyPressed()
        }
        return
    }
    if (GlobalState.dired.selectedFile == null) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    val currentIndex = filtered.indexOfFirst {
        it.file.absolutePath == GlobalState.dired.selectedFile
    }
    if (currentIndex == -1) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    var next = filtered.getOrNull(currentIndex - 6)
    if (next == null) {
        next = filtered.first()
    }
    Dired.keepSameFilesOnDiredGlobalStateChange = true
    GlobalState.dired.setSelectedFile(next.file.absolutePath)
}

// Key.M
internal fun selectMultipleBelow(
    getEntry: () -> Dired.Entry?,
    filtered: List<Dired.Entry>,
    previewScrollVelocity: ScrollVelocity
) {
    if (GlobalState.dired.isPreviewActive) {
        val entry = getEntry() ?: return
        when (entry.fileTypeWithColor.type) {
            FileType.Image -> {
                GlobalState.dired.setImageState(
                    GlobalState.dired.imageState.copy(
                        zoom = GlobalState.dired.imageState.zoom + imageZoomStep
                    )
                )
                return
            }

            FileType.Pdf -> {
                previewScrollVelocity.onScrollDownMoreKeyPressed()
                return
            }

            FileType.Text, FileType.Video, FileType.Markdown -> {
                previewScrollVelocity.onScrollDownMoreKeyPressed()
                return
            }

            else -> {}
        }
        if (entry.fileTypeWithColor.type.isTreeSitterLanguage) {
            previewScrollVelocity.onScrollDownMoreKeyPressed()
        }
        return
    }
    if (GlobalState.dired.selectedFile == null) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    val currentIndex = filtered.indexOfFirst {
        it.file.absolutePath == GlobalState.dired.selectedFile
    }
    if (currentIndex == -1) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    var next = filtered.getOrNull(currentIndex + 6)
    if (next == null) {
        next = filtered.last()
    }
    Dired.keepSameFilesOnDiredGlobalStateChange = true
    GlobalState.dired.setSelectedFile(next.file.absolutePath)
}

// Key.N
fun enterDirectory(
    getEntry: () -> Dired.Entry?,
    changeAvoidAnimatedScrollOnDirectoryChange: (Boolean) -> Unit,
) {
    if (GlobalState.dired.isPreviewActive) {
        val entry = getEntry() ?: return
        if (entry.fileTypeWithColor.type == FileType.Image) {
            GlobalState.dired.setImageState(
                GlobalState.dired.imageState.copy(
                    offsetX = (GlobalState.dired.imageState.offsetX - imageTranslationStep)
                )
            )
        }
        return
    }
    // ignore errors silently
    if (GlobalState.dired.selectedFile == null) {
        return
    }
    val selected = File(GlobalState.dired.selectedFile!!)
    if (!selected.isDirectory) {
        return
    }
    DiredDatabase.rememberSelected()
    val lastSelected = DiredDatabase.getLastSelectedFileInDirectory(selected)
    changeAvoidAnimatedScrollOnDirectoryChange(true)
    GlobalState.dired = GlobalState.dired.copy(
        directory = selected.absolutePath,
        selectedFile = lastSelected
    )
}

// Key.T
fun selectBelow(
    getEntry: () -> Dired.Entry?,
    filtered: List<Dired.Entry>,
    previewScrollVelocity: ScrollVelocity
) {
    if (GlobalState.dired.isPreviewActive) {
        val entry = getEntry() ?: return
        when (entry.fileTypeWithColor.type) {
            FileType.Image -> {
                GlobalState.dired.setImageState(
                    GlobalState.dired.imageState.copy(
                        offsetY = GlobalState.dired.imageState.offsetY - imageTranslationStep
                    )
                )
                return
            }

            FileType.Pdf -> {
                previewScrollVelocity.onScrollDownKeyPressed()
                return
            }

            FileType.Text, FileType.Video, FileType.Markdown -> {
                previewScrollVelocity.onScrollDownKeyPressed()
                return
            }

            else -> {}
        }
        if (entry.fileTypeWithColor.type.isTreeSitterLanguage) {
            previewScrollVelocity.onScrollDownKeyPressed()
        }
        return
    }
    if (GlobalState.dired.selectedFile == null) {
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    val currentIndex = filtered.indexOfFirst {
        it.file.absolutePath == GlobalState.dired.selectedFile
    }
    if (currentIndex == -1) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    val next = filtered.getOrNull(currentIndex + 1) ?: return
    Dired.keepSameFilesOnDiredGlobalStateChange = true
    GlobalState.dired.setSelectedFile(next.file.absolutePath)
}

// Key.C
fun selectAbove(
    getEntry: () -> Dired.Entry?,
    filtered: List<Dired.Entry>,
    previewScrollVelocity: ScrollVelocity
) {
    if (GlobalState.dired.isPreviewActive) {
        val entry = getEntry() ?: return
        when (entry.fileTypeWithColor.type) {
            FileType.Image -> {
                GlobalState.dired.setImageState(
                    GlobalState.dired.imageState.copy(
                        offsetY = GlobalState.dired.imageState.offsetY + imageTranslationStep
                    )
                )
                return
            }

            FileType.Pdf -> {
                previewScrollVelocity.onScrollUpKeyPressed()
                return
            }

            FileType.Text, FileType.Video, FileType.Markdown -> {
                previewScrollVelocity.onScrollUpKeyPressed()
                return
            }

            else -> {}
        }
        if (entry.fileTypeWithColor.type.isTreeSitterLanguage) {
            previewScrollVelocity.onScrollUpKeyPressed()
        }
        return
    }
    if (GlobalState.dired.selectedFile == null) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    val currentIndex = filtered.indexOfFirst {
        it.file.absolutePath == GlobalState.dired.selectedFile
    }
    if (currentIndex == -1) {
        Dired.keepSameFilesOnDiredGlobalStateChange = true
        GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
        return
    }
    val next = filtered.getOrNull(currentIndex - 1) ?: return
    Dired.keepSameFilesOnDiredGlobalStateChange = true
    GlobalState.dired.setSelectedFile(next.file.absolutePath)
}