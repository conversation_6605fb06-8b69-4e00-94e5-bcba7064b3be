package dima.apps.dired

import androidx.compose.foundation.layout.*
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PictureAsPdf
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.X
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.dired.preview.DiredPdfPreview
import dima.color.TailwindCssColors
import dima.globalState.DiredFileRowThumbnailMode
import dima.globalState.GlobalState
import dima.images.CustomAsyncImage
import dima.images.ImageDisplaySize
import dima.settings
import dima.text.TextMarked
import dima.utils.sha256Hash
import dima.utils.toNiceHumanByteString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

@Composable
internal fun DiredAppUiPdfEntryRow(
    entry: Dired.Entry,
    isMarked: Boolean,
    color: Color,      // Pre-calculated text color
    showSize: Boolean,
    showLastModified: Boolean,
    query: String,
    modifier: Modifier = Modifier // Modifier passed from DiredEntryRow
) {
    var pdfThumbnailPath by remember { mutableStateOf<String?>(null) }
    var pdfThumbnailFailed by remember(
        entry.file.absolutePath,
        GlobalState.dired.fileRowThumbnailMode
    ) { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(entry.file.absolutePath, GlobalState.dired.fileRowThumbnailMode) {
        pdfThumbnailPath = null // Reset path
        pdfThumbnailFailed = false // Reset failure state

        if (entry.fileTypeWithColor.type == FileType.Pdf &&
            GlobalState.dired.fileRowThumbnailMode == DiredFileRowThumbnailMode.ShowThumbnail &&
            settings.cli.libpdfiumDylibDirectory != null
        ) {
            val pdfFileHash = entry.file.sha256Hash()
            val thumbnailPrefix = "$pdfFileHash-thumb"
            val expectedThumbnailFile = File(DiredPdfPreview.cacheDirectory, "$thumbnailPrefix.png")

            if (expectedThumbnailFile.exists()) {
                pdfThumbnailPath = expectedThumbnailFile.absolutePath
            } else {
                DiredPdfPreview.cacheDirectory.mkdirs() // Ensure cache directory exists
                coroutineScope.launch(Dispatchers.IO) {
                    try {
                        val p = process(
                            "pdf2png", entry.file.absolutePath,
                            "--library-directory", settings.cli.libpdfiumDylibDirectory.absolutePath,
                            "--resolution-pixels", "210", // Adjusted resolution
                            "--first-page-only",
                            "--prefix", thumbnailPrefix,
                            "--output-directory", DiredPdfPreview.cacheDirectory.absolutePath,
                            stdout = Redirect.CAPTURE,
                            stderr = Redirect.CAPTURE,
                        )
                        if (p.resultCode == 0 && expectedThumbnailFile.exists()) {
                            pdfThumbnailPath = expectedThumbnailFile.absolutePath
                        } else {
                            pdfThumbnailFailed = true
                        }
                    } catch (_: Exception) {
                        pdfThumbnailFailed = true
                    }
                }
            }
        }
    }

    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier // Apply the pre-configured modifier
    ) {
        Box( // Fixed size box for thumbnail or icon
            contentAlignment = Alignment.Center,
            modifier = Modifier.size(90.dp)
        ) {
            if (pdfThumbnailPath != null) { // Thumbnail available
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    CustomAsyncImage(
                        fileSystemUrl = pdfThumbnailPath!!,
                        displaySize = ImageDisplaySize(120, 120),
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize()
                    )
                    Icon(
                        imageVector = Icons.Default.PictureAsPdf,
                        contentDescription = "PDF Icon Overlay",
                        tint = if (GlobalState.isDarkMode) TailwindCssColors.red400 else TailwindCssColors.red600,
                        modifier = Modifier.size(24.dp)
                    )
                }
            } else { // PDF thumbnail not yet available or failed
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    Icon( // PDF Icon
                        imageVector = Icons.Default.PictureAsPdf,
                        contentDescription = "PDF Icon",
                        tint = if (GlobalState.isDarkMode) TailwindCssColors.red400 else TailwindCssColors.red600,
                        modifier = Modifier.size(36.dp)
                    )
                    if (pdfThumbnailFailed) {
                        Icon( // Red X
                            imageVector = Lucide.X,
                            contentDescription = "Thumbnail Failed",
                            tint = TailwindCssColors.red600,
                            modifier = Modifier.size(24.dp).padding(top = 4.dp)
                        )
                    }
                }
            }
            // Overlay MarkedCircle if marked
            if (isMarked) {
                MarkedCircle(
                    fontSize = 14.sp,
                    size = 22.dp,
                    modifier = Modifier.align(Alignment.TopStart)
                )
            }
        }

        Row(verticalAlignment = Alignment.CenterVertically) {
            if (showSize) {
                Text(
                    entry.fileSizeBytes.toNiceHumanByteString(),
                    color = color,
                    modifier = Modifier
                        .width(80.dp)
                        .padding(end = 12.dp)
                )
            } else if (showLastModified) {
                Text(
                    formatLastTimeModifiedForHuman(entry.lastModified),
                    color = color,
                    modifier = Modifier
                        .width(120.dp)
                        .padding(end = 12.dp)
                )
            }
            TextMarked(
                text = entry.file.name,
                searchQuery = query,
                color = color,
            )
        }
    }
}
