package dima.apps.dired

import GlobalStyling
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.settings
import dima.utils.FileReadResult
import dima.utils.Result
import dima.utils.abbreviatePath
import dima.utils.readTextUpTo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

internal fun openCopyFileInfoDialog(
    getEntry: () -> Dired.Entry?
) {
    if (GlobalState.dired.selectedFile == null) {
        return
    }
    val current = File(GlobalState.dired.selectedFile!!)
    val entry = getEntry()
    val prefixTextWidth = 80.dp
    val textColor = GlobalStyling.getTextColor()
    val parts: List<CompletionDialogCandidate> = buildList {
        if (entry?.fileTypeWithColor?.type == FileType.Pdf && settings.cli.libpdfiumDylibDirectory != null) {
            add(
                CompletionDialogCandidate(
                    "Will be extracted...",
                    prefixView = {
                        Text(
                            "PDF Text: ",
                            color = textColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixTextWidth)
                        )
                    },
                    additionalSearchString = "PDF Text"
                )
            )
        }
        add(
            CompletionDialogCandidate(
                current.name,
                prefixView = {
                    Text(
                        "Base: ",
                        color = textColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.width(prefixTextWidth)
                    )
                },
                additionalSearchString = "Base"
            )
        )
        val git = current.abbreviatePathToGit()
        if (git != null && git != current.name) {
            add(
                CompletionDialogCandidate(
                    git,
                    prefixView = {
                        Text(
                            "Git: ",
                            color = textColor,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.width(prefixTextWidth)
                        )
                    },
                    additionalSearchString = "Home"
                )
            )
        }
        add(
            CompletionDialogCandidate(
                current.absolutePath.abbreviatePath(),
                prefixView = {
                    Text(
                        "Home: ",
                        color = textColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.width(prefixTextWidth)
                    )
                },
                additionalSearchString = "Home"
            )
        )
        add(
            CompletionDialogCandidate(
                current.absolutePath,
                prefixView = {
                    Text(
                        "Full: ",
                        color = textColor,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.width(prefixTextWidth)
                    )
                },
                additionalSearchString = "Full"
            )
        )
        if (!current.isDirectory) {
            val content: Result<FileReadResult> = current.readTextUpTo(limit = 500)
            if (content is Result.Success) {
                add(
                    CompletionDialogCandidate(
                        content.value.text,
                        prefixView = {
                            Text(
                                "Contents: ",
                                color = textColor,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.width(prefixTextWidth)
                            )
                        },
                        additionalSearchString = "Contents"
                    )
                )
            }
        }
    }
    openCompletionDialog("Copy", parts, hideCopyCmdActionInBottomBar = true) {
        val candidate = parts[it.index!!]
        when (candidate.additionalSearchString) {
            "Base" -> {
                copyToClipboard(current.name)
            }

            "Will be extracted..." -> {
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        val p = process(
                            "pdf2png", current.absolutePath,
                            "--library-directory", settings.cli.libpdfiumDylibDirectory!!.absolutePath,
                            "--text-only",
                            stdout = Redirect.CAPTURE,
                            stderr = Redirect.CAPTURE,
                        )
                        val output = p.output.joinToString("\n").trim()
                        if (p.resultCode == 0) {
                            if (output.isBlank()) {
                                showErrorNotification(
                                    "There is no text to extract in PDF",
                                    entry!!.file.name
                                )
                            } else {
                                copyToClipboard(output, showNotification = true)
                            }
                        } else {
                            showErrorNotification("pdf2png failed", output)
                        }
                    } catch (e: Exception) {
                        showErrorNotification("Failed to extract PDF text", e.message)
                    }
                }
            }

            "Full" -> {
                copyToClipboard(current.absolutePath)
            }

            "Home" -> {
                copyToClipboard(current.absolutePath.abbreviatePath())
            }

            "Git" -> {
                copyToClipboard(current.abbreviatePathToGit()!!)
            }

            "Contents" -> {
                copyToClipboard(current.readText())
            }
        }
    }
}
