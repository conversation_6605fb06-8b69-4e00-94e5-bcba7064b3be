package dima.apps.dired

import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.images.CustomAsyncImage
import dima.images.ImageDisplaySize
import dima.text.TextMarked
import dima.utils.toNiceHumanByteString

@Composable
internal fun DiredAppUiImageEntryRow(
    entry: Dired.Entry,
    isMarked: Boolean,
    color: Color,      // Pre-calculated text color
    showSize: Boolean,
    showLastModified: Boolean,
    query: String,
    modifier: Modifier = Modifier // Modifier passed from DiredEntryRow, includes click, alpha, background, padding, dragSource
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier // Apply the pre-configured modifier
    ) {
        Box( // Fixed size box for thumbnail
            contentAlignment = Alignment.Center,
            modifier = Modifier.size(90.dp)
        ) {
            CustomAsyncImage(
                fileSystemUrl = entry.file.absolutePath,
                displaySize = ImageDisplaySize(120, 120),
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
            // Overlay MarkedCircle if marked
            if (isMarked) {
                MarkedCircle(
                    fontSize = 14.sp,
                    size = 22.dp,
                    modifier = Modifier.align(Alignment.TopStart)
                )
            }
        }

        Row(verticalAlignment = Alignment.CenterVertically) {
            if (showSize) {
                Text(
                    entry.fileSizeBytes.toNiceHumanByteString(),
                    color = color,
                    modifier = Modifier
                        .width(80.dp)
                        .padding(end = 12.dp)
                )
            } else if (showLastModified) {
                Text(
                    formatLastTimeModifiedForHuman(entry.lastModified),
                    color = color,
                    modifier = Modifier
                        .width(120.dp)
                        .padding(end = 12.dp)
                )
            }
            TextMarked(
                text = entry.file.name,
                searchQuery = query,
                color = color,
            )
        }
    }
}
