package dima.apps.dired

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.globalState.GlobalState
import dima.utils.abbreviatePath
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

internal fun trash(
    filtered: List<Dired.Entry>,
    onFilteredChange: (List<Dired.Entry>) -> Unit,
    fileListSearchQuery: String,
    getMarkedFilesInCurrentDirectory: () -> List<File>
) {
    if (GlobalState.dired.selectedFile == null) {
        showErrorNotification("No file selected to trash")
        return
    }
    val marked = getMarkedFilesInCurrentDirectory()
    if (marked.isEmpty()) {
        val full = GlobalState.dired.selectedFile!!
        val file = full.abbreviatePath()
        openConfirmationDialog("Trash?", subTitle = file) {
            CoroutineScope(Dispatchers.IO).launch {
                val result = process(
                    "trash", full,
                    stdout = Redirect.CAPTURE,
                    stderr = Redirect.CAPTURE,
                )
                if (result.resultCode == 0) {
                    val oldIndex = filtered.indexOfFirst {
                        it.file.absolutePath == GlobalState.dired.selectedFile
                    }
                    val newFiltered = Dired.getEntries(
                        GlobalState.dired.directory,
                        GlobalState.dired.sort,
                        fileListSearchQuery
                    )
                    onFilteredChange(newFiltered)
                    if (newFiltered.isEmpty()) {
                        GlobalState.dired = GlobalState.dired.copy(
                            selectedFile = null,
                            isPreviewActive = false
                        )
                    } else {
                        val newIndex = oldIndex.coerceAtMost(newFiltered.size - 1)
                        GlobalState.dired = GlobalState.dired.copy(
                            selectedFile = newFiltered[newIndex].file.absolutePath,
                            isPreviewActive = false
                        )
                    }
                } else {
                    showErrorNotification("Failed to trash", result.output.joinToString("\n"))
                    onFilteredChange(
                        Dired.getEntries(
                            GlobalState.dired.directory,
                            GlobalState.dired.sort,
                            fileListSearchQuery
                        )
                    )
                }
            }
        }
        Dired.removeDeletedMarkedPaths()
        return
    }
    val title = if (marked.size == 1) {
        "Trash 1 marked path?"
    } else {
        "Trash ${marked.size} marked paths?"
    }
    val subTitle = if (marked.size == 1) {
        marked.first().abbreviatePath()
    } else {
        marked.joinToString("\n") {
            "• ${it.abbreviatePath()}"
        }
    }
    openConfirmationDialog(title, subTitle) {
        CoroutineScope(Dispatchers.IO).launch {
            val files = marked.map {
                it.absolutePath
            }.toTypedArray()
            val result = process(
                "trash", *files,
                stdout = Redirect.CAPTURE,
                stderr = Redirect.CAPTURE,
            )
            if (result.resultCode == 0) {
                if (marked.size == 1) {
                    showNotification("Trashed 1 path", subTitle)
                } else {
                    showNotification("Trashed ${marked.size} paths", subTitle)
                }
                // Get the current selected file and its position before trashing
                // Fixed: When trashing multiple files, the point now moves up to a better position
                val currentSelectedPath = GlobalState.dired.selectedFile
                val oldIndex = filtered.indexOfFirst {
                    it.file.absolutePath == currentSelectedPath
                }

                // Calculate a better position for cursor after trashing multiple files
                // For multiple files, we want to position the cursor higher up in the list
                val targetIndex = if (oldIndex > 0) (oldIndex - marked.size).coerceAtLeast(0) else 0

                val newFiltered = Dired.getEntries(
                    GlobalState.dired.directory,
                    GlobalState.dired.sort,
                    fileListSearchQuery
                )
                // Refresh the file list after trashing
                onFilteredChange(newFiltered)

                if (newFiltered.isEmpty()) {
                    GlobalState.dired = GlobalState.dired.copy(
                        selectedFile = null
                    )
                } else {
                    // Use the calculated target index instead of the old index
                    val newIndex = targetIndex.coerceAtMost(newFiltered.size - 1)
                    GlobalState.dired = GlobalState.dired.copy(
                        selectedFile = newFiltered[newIndex].file.absolutePath
                    )
                }
            } else {
                showErrorNotification("Failed to trash", result.output.joinToString("\n"))
                onFilteredChange(
                    Dired.getEntries(
                        GlobalState.dired.directory,
                        GlobalState.dired.sort,
                        fileListSearchQuery
                    )
                )
            }
            Dired.removeDeletedMarkedPaths()
        }
    }

}