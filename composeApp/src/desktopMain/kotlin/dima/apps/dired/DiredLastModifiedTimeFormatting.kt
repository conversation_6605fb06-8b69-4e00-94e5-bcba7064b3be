package dima.apps.dired

import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*

/**
 * Return like macOS Finder.
 */
internal fun formatLastTimeModifiedForHuman(lastModified: Long): String {
    val lastModifiedInstant = Instant.ofEpochMilli(lastModified)
    val now = Instant.now()
    val days = ChronoUnit.DAYS.between(lastModifiedInstant, now)
    return when {
        days == 0L -> "Heute"
        days == 1L -> "Gestern"
        days <= 7 -> "Letzte 7 Tage"
        days <= 30 -> "Letzte 30 Tage"
        lastModifiedInstant.atZone(ZoneId.systemDefault()).year != now.atZone(ZoneId.systemDefault()).year ->
            lastModifiedInstant.atZone(ZoneId.systemDefault()).year.toString()

        else -> lastModifiedInstant.atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("MMMM", Locale.GERMAN))
    }
}
