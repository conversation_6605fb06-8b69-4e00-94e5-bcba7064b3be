package dima.apps.dired

import dima.apps.AppType
import dima.apps.notifications.showErrorNotification
import dima.globalState.GlobalState
import dima.os.homeWithoutSlash
import setCurrentApp
import java.io.File

internal fun reload(
    filtered: List<Dired.Entry>,
    changeFiltered: (List<Dired.Entry>) -> Unit,
    fileListSearchQuery: String
) {
    val newFiltered = Dired.getEntries(
        GlobalState.dired.directory,
        GlobalState.dired.sort,
        fileListSearchQuery
    )
    changeFiltered(newFiltered)
    
    if (GlobalState.dired.selectedFile == null) {
        return
    }
    
    val index = filtered.indexOfFirst {
        it.file.absolutePath == GlobalState.dired.selectedFile!!
    }
    
    if (index != -1) {
        val newIndex = newFiltered.indexOfFirst {
            it.file.absolutePath == GlobalState.dired.selectedFile!!
        }
        if (newIndex == -1) {
            val adjustedIndex = if (index < newFiltered.size) index else newFiltered.size - 1
            if (newFiltered.isNotEmpty()) {
                GlobalState.dired.setSelectedFile(
                    newFiltered[adjustedIndex].file.absolutePath
                )
            } else {
                GlobalState.dired.setSelectedFile(null)
            }
        }
    }
}


/**
 * Updates the selected file if it was the source of a move operation.
 * Marking/unmarking is handled by DiredApp after the dialog closes.
 */
internal fun updateSelectedFileAfterMove(
    sourceFile: File,
    targetFile: File
) {
    if (GlobalState.dired.selectedFile == sourceFile.absolutePath) {
        GlobalState.dired.setSelectedFile(targetFile.absolutePath)
    }
}

internal fun resolveTargetFile(sourceFile: File, newName: String): File? {
    val trimmedName = newName.trim()
    if (trimmedName.isBlank()) {
        showErrorNotification("New name cannot be empty")
        return null
    }

    return when {
        trimmedName.startsWith("~/") -> File(homeWithoutSlash + trimmedName.removePrefix("~"))
        trimmedName.startsWith("/") -> File(trimmedName)
        else -> File(sourceFile.parentFile, trimmedName)
    }
}

internal fun getFileForOperation(operation: String, markedFiles: List<File>): File? {
    return when {
        markedFiles.size == 1 -> markedFiles.first()
        markedFiles.size > 1 -> {
            showErrorNotification("Aborting $operation: Multiple files marked in current directory")
            null
        }

        GlobalState.dired.selectedFile != null -> {
            File(GlobalState.dired.selectedFile!!)
        }

        else -> {
            showErrorNotification("No file selected or marked to $operation")
            null
        }
    }
}

internal fun getMarkedFilesInCurrentDirectory(): List<File> {
    val currentDirectory = File(GlobalState.dired.directory)
    val markedFiles = GlobalState.dired.markedFiles
    val filesToKeepMarked = markedFiles.filter {
        it.parentFile == currentDirectory
    }
    return filesToKeepMarked
}

fun File.openDirectoryInDired() {
    val path = this.absolutePath
    GlobalState.dired = GlobalState.dired.copy(
        directory = path,
        selectedFile = DiredDatabase.getLastSelectedFileInDirectory(File(path))
    )
    if (GlobalState.app != AppType.Dired) {
        setCurrentApp(AppType.Dired)
    }
}

fun File.openFileInDired() {
    // If file is a dot file, automatically enable showing hidden files
    if (this.name.startsWith(".") && !GlobalState.dired.showHiddenFiles) {
        GlobalState.dired = GlobalState.dired.copy(
            showHiddenFiles = true,
            directory = this.parent,
            selectedFile = this.absolutePath
        )
    } else {
        GlobalState.dired = GlobalState.dired.copy(
            directory = this.parent,
            selectedFile = this.absolutePath
        )
    }
    if (GlobalState.app != AppType.Dired) {
        setCurrentApp(AppType.Dired)
    }
}

fun File.abbreviatePathToGit(): String? {
    fun findGitDirectoryStartingFrom(file: File): File? {
        var currentDir = file.parentFile
        while (currentDir != null) {
            val gitDir = File(currentDir, ".git")
            if (gitDir.isDirectory) {
                return gitDir
            }
            currentDir = currentDir.parentFile
        }
        return null
    }

    val gitDir = findGitDirectoryStartingFrom(this) ?: return null
    return absolutePath
        .removePrefix(gitDir.parentFile.absolutePath)
        .removePrefix("/")
}

