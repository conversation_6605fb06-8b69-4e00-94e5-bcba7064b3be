package dima.apps.dired

import GlobalStyling
import Globals
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Folder
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import dev.tclement.fonticons.FontIcon
import dev.tclement.fonticons.StaticIconFont
import dima.color.TailwindCssColors
import java.awt.Image
import java.awt.image.BufferedImage

@Composable
internal fun MarkedCircle(fontSize: TextUnit, size: Dp, modifier: Modifier = Modifier) {
    Text(
        "",
        modifier = modifier
            .size(size)
            .drawBehind {
                drawCircle(
                    color = GlobalStyling.getMarkedBackgroundColorForWhiteText(),
                    radius = this.size.maxDimension / 1.7f
                )
                drawCircle(
                    color = TailwindCssColors.white,
                    radius = this.size.maxDimension / 2.0f
                )
                val textLayoutResult: TextLayoutResult =
                    TextMeasurer(
                        Globals.fontFamilyResolver,
                        Globals.density,
                        LayoutDirection.Ltr
                    ).measure(
                        "✓",
                        style = TextStyle(
                            fontSize = fontSize,
                            fontWeight = FontWeight.Medium,
                        ),
                    )
                val textSize = textLayoutResult.size
                val canvasWidth = this.size.width
                val canvasHeight = this.size.height
                drawText(
                    textLayoutResult,
                    color = GlobalStyling.getMarkedBackgroundColorForWhiteText(),
                    topLeft = Offset(
                        (canvasWidth - textSize.width) / 2f,
                        (canvasHeight - textSize.height) / 2f
                    ),
                )
            }
    )
}

@Composable
internal fun Modifier.addSelectedBorder(
    shouldDrawBorder: Boolean,
    borderSize: Dp = GlobalStyling.selectedBorderWidth
): Modifier {
    return composed {
        if (shouldDrawBorder) {
            this.then(
                Modifier.border(
                    borderSize,
                    GlobalStyling.getSelectedBorderColor(),
                    GlobalStyling.smallRoundedCorners
                )
            )
        } else {
            Modifier
        }
    }
}

fun toBufferedImage(img: Image): BufferedImage {
    if (img is BufferedImage) {
        return img
    }
    val bufferedImage = BufferedImage(img.getWidth(null), img.getHeight(null), BufferedImage.TYPE_INT_ARGB)
    val bGr = bufferedImage.createGraphics()
    bGr.drawImage(img, 0, 0, null)
    bGr.dispose()
    return bufferedImage
}

@Composable
internal fun FileData(key: String, value: String, width: Dp, bold: Boolean = false) {
    Row {
        SelectionContainer {
            Text(
                key,
                color = GlobalStyling.getGrayColor(),
                modifier = Modifier
                    .width(width)
            )
        }
        SelectionContainer {
            Text(
                value,
                color = GlobalStyling.getTextColor(),
                fontWeight = if (bold) FontWeight.Bold else null,
            )
        }
    }
}

@Composable
internal fun DiredDirectoryIcon(iconSize: Dp, tint: Color) {
    Icon(
        Icons.Default.Folder,
        contentDescription = null,
        tint = tint,
        modifier = Modifier.size(iconSize + 2.dp)
    )
}

@Composable
internal fun DiredEntryFileIcon(
    iconSize: Dp,
    entry: Dired.Entry,
    tint: Color,
    faBrandsFont: StaticIconFont
) {
    val iconModifier = Modifier
        .padding(start = 2.dp, end = 2.dp)
        .size(iconSize)
    when (val fileTypeWithColor = entry.fileTypeWithColor) { // Shadow the outer scope entry temporarily
        is FileTypeWithColor.CharIcon -> {
            FontIcon(
                icon = fileTypeWithColor.iconChar,
                contentDescription = null,
                tint = tint,
                iconFont = faBrandsFont,
                modifier = iconModifier
            )
        }

        is FileTypeWithColor.VectorIcon -> {
            Icon(
                fileTypeWithColor.icon,
                contentDescription = null,
                tint = tint,
                modifier = iconModifier
            )
        }
    }
}

