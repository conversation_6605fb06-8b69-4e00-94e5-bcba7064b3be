package dima.apps.dired

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.globalState.GlobalState
import dima.utils.abbreviatePath
import dima.utils.toNiceHumanByteString
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal fun calculateFileOrDirectorySize(getEntry: () -> Dired.Entry?) {
    if (GlobalState.dired.selectedFile == null) {
        showErrorNotification("No file selected to get size")
        return
    }
    val entry = getEntry()!!
    val abbreviated = entry.file.abbreviatePath()
    if (entry.file.isDirectory) {
        CoroutineScope(Dispatchers.IO).launch {
            val notification = showLoadingNotification("Loading file size", abbreviated)
            val p = process(
                // -s for single output line
                "du", "-s", entry.file.absolutePath,
                stdout = Redirect.CAPTURE,
                stderr = Redirect.CAPTURE,
            )
            if (p.resultCode == 0) {
                val output = p.output.joinToString("\n")
                val byteString = output.trim().substringBefore("\t")
                // note that this is not exactly the same size as macOS Finder reports, but it is reasonably close
                val bytes = (byteString.toLong() * 512).toNiceHumanByteString()
                notification.toInfo(title = abbreviated, message = bytes)
            } else {
                notification.toError(
                    title = "du -s ... failed with ${p.resultCode}",
                    message = p.output.joinToString("\n"),
                )
            }
        }
    } else {
        val byteString = entry.fileSizeBytes.toNiceHumanByteString()
        showNotification(abbreviated, byteString)
    }
}
