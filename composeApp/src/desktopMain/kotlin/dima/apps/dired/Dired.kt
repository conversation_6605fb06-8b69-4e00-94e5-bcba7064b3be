package dima.apps.dired

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.files.NaturalComparator
import dima.globalState.GlobalState
import dima.utils.filterHuman
import java.io.File

fun List<Dired.Entry>.hasAnyFileWithThumbnails(): Boolean {
    return any {
        it.fileTypeWithColor.type == FileType.Image || it.fileTypeWithColor.type == FileType.Pdf
    }
}

object Dired {

    const val PREVIEW_FILE_SIZE_LIMIT_CHARS = 30_000

    var hideNextReloadNotification: Boolean = false

    /**
     * Toggle this to reload the preview state.
     */
    internal var reload by mutableStateOf(false)

    /**
     * For DiredAppEffects.kt to not reload the entries on any file change from the usual navigation keys.
     * Set to true to keep the same files on the next LaunchedEffect trigger of the dired global state.
     */
    internal var keepSameFilesOnDiredGlobalStateChange by mutableStateOf(false)

    /**
     * @param fileTypeWithColor is ignored when the file is a directory.
     */
    data class Entry(
        val file: File,
        val lastModified: Long,
        val fileSizeBytes: Long,
        val fileTypeWithColor: FileTypeWithColor
    )

    fun removeDeletedMarkedPaths() {
        val existing = GlobalState.dired.markedFiles.filter {
            it.exists()
        }
        GlobalState.dired = GlobalState.dired.copy(markedFiles = existing)
    }

    /**
     * Consider [dima.globalState.GlobalDiredState.showHiddenFiles].
     *
     * @param fileListSearchQuery if an empty string, do not filter by it
     * @return sorted entries
     */
    internal fun getEntries(path: String, sort: DiredSort, fileListSearchQuery: String): List<Entry> {
        if (!File(path).exists()) {
            throw IllegalArgumentException("Path does not exist: $path")
        }
        val list = mutableListOf<Entry>()
        val dir = File(path)
        if (dir.exists() && dir.isDirectory) {
            dir.listFiles()?.forEach { file ->

                fun addEntry() {
                    list.add(
                        Entry(
                            file = file,
                            fileSizeBytes = file.length(),
                            lastModified = file.lastModified(),
                            fileTypeWithColor = file.getFileType()
                        )
                    )
                }

                if (GlobalState.dired.showHiddenFiles) {
                    addEntry()
                } else {
                    if (!file.isHidden) {
                        addEntry()
                    }
                }
            }
        }

        val (directories, files) = list.partition { it.file.isDirectory }
        val sortedList = when (sort) {
            DiredSort.Name -> {
                /* sort non-natural, just by chars
                 directories.sortedBy {
                    it.file.absolutePath.lowercase()
                } + files.sortedBy {
                    it.file.absolutePath.lowercase()
                }
                 */
                directories.sortedWith(compareBy(NaturalComparator()) { it.file.name }) + files.sortedWith(
                    compareBy(
                        NaturalComparator()
                    ) { it.file.name })
            }

            DiredSort.Size -> {
                // index 0 has the largest file
                directories.sortedBy {
                    it.file.name.lowercase()
                } + files.sortedBy {
                    it.fileSizeBytes
                }.reversed()
            }

            DiredSort.LastModified -> {
                // index 0 has the most recently modified
                list.sortedBy {
                    it.lastModified
                }.reversed()
            }
        }
        return if (fileListSearchQuery.isBlank()) {
            sortedList
        } else {
            sortedList.filterHuman(fileListSearchQuery) {
                it.file.name
            }
        }
    }

}
