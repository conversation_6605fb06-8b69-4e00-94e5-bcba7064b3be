package dima.apps.dired

import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.input.TextFieldValue
import dima.apps.dired.findFileDialog.openDiredFindFileDialog
import dima.apps.dired.preview.OpenPdfPageSelectionAtCurrentPageEvent
import dima.apps.dired.transientDialog.openDiredTransientDialog
import dima.apps.notifications.showErrorNotification
import dima.events.emitEasy
import dima.globalState.DiredImageState
import dima.globalState.GlobalState
import dima.utils.AppKey
import dima.utils.ScrollVelocity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File

object DiredAppKeyBindings {
    fun createKeyBindings(
        getEntry: () -> Dired.Entry?,
        onFilesChangedByDialog: (List<File>) -> Unit,
        coroutineScope: CoroutineScope,
        getShowSearch: () -> Boolean,
        searchFocusRequester: FocusRequester,
        onShowSearchChange: (Boolean) -> Unit,
        getFiltered: () -> List<Dired.Entry>,
        onFilteredChange: (List<Dired.Entry>) -> Unit,
        getFileListSearchQuery: () -> TextFieldValue,
        changeAvoidAnimatedScrollOnDirectoryChange: (Boolean) -> Unit,
        previewScrollVelocity: ScrollVelocity,
        appFocusRequester: FocusRequester,
        getLastMarkedFile: () -> File?,
        onLastMarkedFileChange: (File?) -> Unit
    ): List<AppKey> = listOf(
        AppKey(Key.R, "Move (marked) paths") {
            renameFiles(getEntry, onFilesChangedByDialog)
        },
        AppKey(Key.S, "Copy (marked) paths") {
            copyFiles(getEntry, onFilesChangedByDialog)
        },
        AppKey(Key.I, "Get file size recursively") { calculateFileOrDirectorySize(getEntry) },
        AppKey(Key.Period, "Toggle dotfile display") { toggleDotFiles() },
        AppKey(Key.NumPadEquals, "Open in Emacs") {
            openInEmacs(getEntry(), coroutineScope)
        },
        // use KeyUp to not insert s into the search
        AppKey(Key.B, "Focus search", onKeyUp = {
            if (getShowSearch()) {
                searchFocusRequester.requestFocus()
            } else {
                onShowSearchChange(true)
                // Focus will be requested by LaunchedEffect
            }
        }),
        AppKey(Key.D, "Trash file") {
            trash(
                filtered = getFiltered(),
                onFilteredChange = { onFilteredChange(it) },
                fileListSearchQuery = getFileListSearchQuery().text,
                getMarkedFilesInCurrentDirectory = ::getMarkedFilesInCurrentDirectory
            )
        },
        AppKey(Key.H, "Go directory back") {
            goDirectoryBack(
                getEntry = getEntry,
                changeAvoidAnimatedScrollOnDirectoryChange = {
                    changeAvoidAnimatedScrollOnDirectoryChange(it)
                }
            )
        },
        AppKey(Key.H, "Move PDF preview left", isCtrl = true),
        AppKey(Key.DirectionLeft, "Go directory back") {
            goDirectoryBack(
                getEntry = getEntry,
                changeAvoidAnimatedScrollOnDirectoryChange = {
                    changeAvoidAnimatedScrollOnDirectoryChange(it)
                }
            )
        },
        AppKey(Key.N, "Enter directory") {
            enterDirectory(
                getEntry = getEntry,
                changeAvoidAnimatedScrollOnDirectoryChange = {
                    changeAvoidAnimatedScrollOnDirectoryChange(it)
                }
            )
        },
        AppKey(Key.N, "Move PDF preview right", isCtrl = true),
        AppKey(Key.DirectionRight, "Enter directory or open file (like Enter)") {
            onEnter(
                entry = getEntry(),
                scrollPreviewToTop = {
                    coroutineScope.launch {
                        previewScrollVelocity.scrollToTop()
                    }
                },
                enterDirectory = {
                    enterDirectory(
                        getEntry = getEntry,
                        changeAvoidAnimatedScrollOnDirectoryChange = {
                            changeAvoidAnimatedScrollOnDirectoryChange(it)
                        }
                    )
                }
            )
        },
        AppKey(Key.Escape, "Leave search to focus entries on left side") {
            if (GlobalState.dired.isPreviewActive) {
                Dired.keepSameFilesOnDiredGlobalStateChange = true
                GlobalState.dired = GlobalState.dired.copy(isPreviewActive = false)
            }
            appFocusRequester.requestFocus()
        },
        AppKey(Key.Enter, "Select file, play video file in mpv or play audio file") {
            onEnter(
                entry = getEntry(),
                scrollPreviewToTop = {
                    coroutineScope.launch {
                        previewScrollVelocity.scrollToTop()
                    }
                },
                enterDirectory = {
                    enterDirectory(
                        getEntry = getEntry,
                        changeAvoidAnimatedScrollOnDirectoryChange = {
                            changeAvoidAnimatedScrollOnDirectoryChange(it)
                        }
                    )
                })
        },
        AppKey(Key.U, "Open dialog to jump to file") { openDiredFindFileDialog(GlobalState.dired.directory) },
        AppKey(
            Key.Z, "Open transient",
            onKeyDown = {
                openDiredTransientDialog(
                    entry = getEntry(),
                    onDirectoryCreate = { createdFile ->
                        onFilteredChange(
                            Dired.getEntries(
                                GlobalState.dired.directory,
                                GlobalState.dired.sort,
                                getFileListSearchQuery().text
                            )
                        )
                        GlobalState.dired = GlobalState.dired.copy(selectedFile = createdFile.absolutePath)
                    },
                    openedByKey = Key.Z
                )
            }
        ),
        AppKey(
            Key.Y, "Mark/unmark file",
            onKeyDown = {
                val entry = getEntry()
                if (entry == null) {
                    showErrorNotification("No entry to mark")
                    return@AppKey
                }
                if (getLastMarkedFile() == entry.file) {
                    return@AppKey
                }
                val currentMarkedFiles = GlobalState.dired.markedFiles
                val updatedMarkedFiles = if (currentMarkedFiles.contains(entry.file)) {
                    currentMarkedFiles - entry.file
                } else {
                    currentMarkedFiles + entry.file
                }
                GlobalState.dired = GlobalState.dired.copy(markedFiles = updatedMarkedFiles)
                onLastMarkedFileChange(entry.file)
                selectBelow(getEntry, getFiltered(), previewScrollVelocity)
            },
            onKeyUp = {
                onLastMarkedFileChange(null)
            }
        ),
        AppKey(Key.X, "Mark/unmark all in current directory") { markOrUnmarkAllInCurrentDirectory(getFiltered()) },
        AppKey(Key.X, "Unmark all across all directories", isShift = true) { unmarkAllFilesAcrossAllDirectories() },
        AppKey(Key.J, "Copy file info") { openCopyFileInfoDialog(getEntry) },
        AppKey(Key.F, "Open with default macOS app") { openWithDefaultMacOsApp(getEntry) },
        AppKey(Key.R, "Reset PDF preview image state", isCtrl = true) {
            GlobalState.dired = GlobalState.dired.copy(pdfImageState = DiredImageState())
        },
        AppKey(Key.C, "Select above") {
            selectAbove(
                getEntry = getEntry,
                filtered = getFiltered(),
                previewScrollVelocity = previewScrollVelocity
            )
        },
        AppKey(Key.C, "Move PDF preview up", isCtrl = true) {
            val entry = getEntry() ?: return@AppKey
            if (entry.fileTypeWithColor.type == FileType.Pdf) {
                previewScrollVelocity.onScrollUpKeyPressed()
            }
        },
        AppKey(Key.T, "Select below") { selectBelow(getEntry, getFiltered(), previewScrollVelocity) },
        AppKey(Key.DirectionUp, "Select above (arrow up)") {
            selectAbove(
                getEntry = getEntry,
                filtered = getFiltered(),
                previewScrollVelocity = previewScrollVelocity
            )
        },
        AppKey(Key.DirectionDown, "Select below (arrow down)") {
            selectBelow(getEntry, getFiltered(), previewScrollVelocity)
        },
        AppKey(Key.T, "Move PDF preview down", isCtrl = true) {
            val entry = getEntry() ?: return@AppKey
            if (entry.fileTypeWithColor.type == FileType.Pdf) {
                previewScrollVelocity.onScrollDownKeyPressed()
            }
        },
        AppKey(Key.V, "Select 6 above") {
            selectMultipleAbove(
                getEntry = getEntry,
                filtered = getFiltered(),
                previewScrollVelocity = previewScrollVelocity
            )
        },
        AppKey(Key.V, "Zoom PDF preview out", isCtrl = true) {
            val entry = getEntry() ?: return@AppKey
            if (entry.fileTypeWithColor.type == FileType.Pdf) {
                previewScrollVelocity.onScrollUpMoreKeyPressed()
            } else if (entry.fileTypeWithColor.type == FileType.Image) {
                // Keep existing zoom functionality for images
                val imageState = GlobalState.dired.imageState
                GlobalState.dired.setImageState(imageState.copy(zoom = (imageState.zoom * 0.8f).coerceAtLeast(0.1f)))
            }
        },
        AppKey(Key.M, "Select 6 below") {
            selectMultipleBelow(
                getEntry = getEntry,
                filtered = getFiltered(),
                previewScrollVelocity = previewScrollVelocity
            )
        },
        AppKey(Key.M, "Zoom PDF preview in", isCtrl = true) {
            val entry = getEntry() ?: return@AppKey
            if (entry.fileTypeWithColor.type == FileType.Pdf) {
                previewScrollVelocity.onScrollDownMoreKeyPressed()
            } else if (entry.fileTypeWithColor.type == FileType.Image) {
                // Keep existing zoom functionality for images
                val imageState = GlobalState.dired.imageState
                GlobalState.dired.setImageState(imageState.copy(zoom = imageState.zoom * 1.2f))
            }
        },
        AppKey(Key.K, "Ask question about image") {
            val entry = getEntry() ?: return@AppKey
            openImageAiDialog(entry)
        },
        AppKey(Key.Tab, "Toggle full width preview") { toggleFullWidthPreview() },
        AppKey(Key.E, "Mark files by extension") { markFilesByExtension(getFiltered()) },
        AppKey(Key.G, "Show PDF page selection dialog at current page") {
            val entry = getEntry()
            if (entry != null && entry.file.extension.lowercase() == "pdf") {
                // Emit event to open the PDF page selection dialog at the current visible page
                // This will be handled by DiredPdfPreview which has access to the LazyListState
                coroutineScope.launch {
                    OpenPdfPageSelectionAtCurrentPageEvent(entry).emitEasy()
                }
            } else {
                showErrorNotification("Selected file is not a PDF")
            }
        },
        AppKey(key = Key.V, text = "Open pasted path", isCmd = true) { pasteFromCmdV() },
    )
}