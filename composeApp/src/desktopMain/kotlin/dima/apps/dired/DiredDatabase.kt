package dima.apps.dired

import dima.database.transactionToAvoidBusySqlite
import dima.globalState.GlobalState
import dima.utils.RecentHistory
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.selectAll
import java.io.File

object DiredDatabase {

    internal var database: Database? = null
    private const val RELATIVE_CACHE_DIRECTORY = "cache/dired"
    private val cacheDirectory = File(RELATIVE_CACHE_DIRECTORY)

    fun setup() {
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        if (database != null) {
            return
        }
        val dbDir = File(cacheDirectory, "db")
        if (!dbDir.exists()) {
            dbDir.mkdirs()
        }
        val dbFile = File(dbDir, "dired.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
    }

    private object Table : org.jetbrains.exposed.sql.Table("SelectedFiles") {
        val directory = text("directory").uniqueIndex()
        val selectedFile = text("selected_file").nullable()
    }

    internal fun getLastSelectedFileInDirectory(directory: File): String? {
        if (!directory.isDirectory) {
            throw IllegalArgumentException("Not a directory: $directory")
        }
        return transactionToAvoidBusySqlite(database) {
            Table.selectAll()
                .where { Table.directory eq directory.absolutePath }
                .firstOrNull()
                ?.get(Table.selectedFile)
        }
    }

    internal fun rememberSelected() {
        val new = mutableListOf<Pair<String, String>>()
        if (GlobalState.dired.selectedFile != null) {
            new.add(GlobalState.dired.directory to GlobalState.dired.selectedFile!!)
        }
        if (lastRememberSelectedToDatabase != new && new.isNotEmpty()) {
            new.forEach { (dir, selectedFile) ->
                RecentHistory.rememberFile(selectedFile)
                transactionToAvoidBusySqlite(database) {
                    Table.deleteWhere { directory eq dir }
                    Table.insert { row ->
                        row[this.directory] = dir
                        row[this.selectedFile] = selectedFile
                    }
                }
            }
        }
        lastRememberSelectedToDatabase = new
    }

    private var lastRememberSelectedToDatabase = emptyList<Pair<String, String>>()

    internal fun deleteMissingDirectoriesFromDatabase() {
        transactionToAvoidBusySqlite(database) {
            val delete = mutableListOf<String>()
            Table.selectAll().forEach { row ->
                if (!File(row[Table.directory]).exists()) {
                    delete.add(row[Table.directory])
                }
            }
            delete.forEach { dir ->
                Table.deleteWhere { directory eq dir }
            }
        }
    }

}

