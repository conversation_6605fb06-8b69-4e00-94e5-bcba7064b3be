package dima.apps.dired

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.apps.dired.preview.DiredPreview
import dima.globalState.GlobalState
import dima.utils.scrollbarStyleForDarkBackground
import dima.utils.scrollbarStyleThemed

@Composable
internal fun RowScope.DiredAppUiRightSidePreview(
    previewEntry: Dired.Entry?,
    rightPreviewScrollState: ScrollState
) {
    Column(
        modifier = Modifier
            .padding(top = 12.dp, end = 12.dp, bottom = 12.dp)
            .then(
                if (previewEntry == null) {
                    Modifier
                } else {
                    Modifier.background(
                        GlobalStyling.getWindowBackgroundColor(),
                        shape = GlobalStyling.smallRoundedCorners
                    )
                }
            )
            .weight(if (GlobalState.dired.isPreviewFullWidth) 1.5f else 0.5f)
            .fillMaxSize()
    ) {
        if (previewEntry != null) {
            val isPreviewActive = GlobalState.dired.isPreviewActive
            val isTreesitter = previewEntry.fileTypeWithColor.type.isTreeSitterLanguage
            Box(
                modifier = Modifier
                    // do not draw for tree sitter because it has to draw its own border because of the dark background
                    // and because it has padding to the right, so the scrollbar is visible
                    .addSelectedBorder(shouldDrawBorder = isPreviewActive && !isTreesitter)
            ) {
                DiredPreview(
                    entry = previewEntry,
                    drawSelectedBorder = isPreviewActive,
                    rightPreviewScrollState = rightPreviewScrollState,
                    imageState = if (previewEntry.fileTypeWithColor.type == FileType.Pdf) {
                        GlobalState.dired.pdfImageState
                    } else {
                        GlobalState.dired.imageState
                    }
                )
                if (previewEntry.fileTypeWithColor.type != FileType.Image) {
                    VerticalScrollbar(
                        style = if (isTreesitter) {
                            scrollbarStyleForDarkBackground()
                        } else {
                            scrollbarStyleThemed()
                        },
                        adapter = rememberScrollbarAdapter(scrollState = rightPreviewScrollState),
                        modifier = Modifier
                            .padding(
                                top = GlobalStyling.ScrollBar.outerPadding,
                                bottom = GlobalStyling.ScrollBar.outerPadding,
                                end = if (isPreviewActive && isTreesitter) {
                                    GlobalStyling.ScrollBar.outerPadding
                                } else {
                                    if (isPreviewActive) {
                                        GlobalStyling.ScrollBar.outerPadding
                                    } else {
                                        0.dp
                                    }
                                }
                            )
                            .align(Alignment.CenterEnd),
                    )
                }
            }
        }
    }
}
