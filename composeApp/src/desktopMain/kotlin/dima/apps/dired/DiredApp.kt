package dima.apps.dired

import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.input.TextFieldValue
import dima.globalState.GlobalState
import dima.globalState.PaneState
import dima.utils.LaunchedEffectForAppKeys
import dima.utils.ScrollVelocity
import java.io.File

@Composable
fun DiredApp(paneState: PaneState) {
    remember { DiredDatabase.setup() }
    val heldKeys: MutableSet<Key> = remember { mutableSetOf() }
    val heldCtrlKeys: MutableSet<Key> = remember { mutableSetOf() }
    val appFocusRequester = remember { FocusRequester() }
    val searchFocusRequester = remember { FocusRequester() }
    val coroutineScope = rememberCoroutineScope()
    val leftScrollState = rememberLazyListState()
    val rightPreviewScrollState = rememberScrollState()
    val previewScrollVelocity = remember { ScrollVelocity(rightPreviewScrollState) }
    var avoidAnimatedScrollOnDirectoryChange by remember { mutableStateOf(false) }
    var showSearch by remember { mutableStateOf(false) }
    var isFirstScrollToAvoidAnimation by remember { mutableStateOf(true) }
    val scrollOffset = 600
    // Remember the last marked file to prevent flickering when pressing 'y' at the end of the file list
    var lastMarkedFile by remember { mutableStateOf<File?>(null) }

    /** Those are sorted. */
    var filtered by remember { mutableStateOf<List<Dired.Entry>>(emptyList()) }
    var isSearchFocused by remember { mutableStateOf(false) }
    var fileListSearchQuery by remember { mutableStateOf(TextFieldValue(GlobalState.dired.search)) }

    fun getEntry(): Dired.Entry? {
        return if (GlobalState.dired.selectedFile == null) {
            null
        } else {
            filtered.find {
                it.file.absolutePath == GlobalState.dired.selectedFile
            }
        }
    }

    fun onFilesChangedByDialog(successfullyProcessedSourceFiles: List<File>) {
        if (successfullyProcessedSourceFiles.isNotEmpty()) {
            val currentMarked = GlobalState.dired.markedFiles.toMutableList()
            var unmarkedCount = 0
            successfullyProcessedSourceFiles.forEach { processedFile ->
                if (currentMarked.remove(processedFile)) {
                    unmarkedCount++
                }
            }
            if (unmarkedCount > 0) {
                GlobalState.dired = GlobalState.dired.copy(markedFiles = currentMarked)
            }
        }
        reload(
            filtered = filtered,
            changeFiltered = { filtered = it },
            fileListSearchQuery = fileListSearchQuery.text
        )
    }

    val localAppKeys = remember {
        DiredAppKeyBindings.createKeyBindings(
            getEntry = ::getEntry,
            onFilesChangedByDialog = ::onFilesChangedByDialog,
            coroutineScope = coroutineScope,
            getShowSearch = { showSearch },
            searchFocusRequester = searchFocusRequester,
            onShowSearchChange = { showSearch = it },
            getFiltered = { filtered },
            onFilteredChange = { filtered = it },
            getFileListSearchQuery = { fileListSearchQuery },
            changeAvoidAnimatedScrollOnDirectoryChange = { avoidAnimatedScrollOnDirectoryChange = it },
            previewScrollVelocity = previewScrollVelocity,
            appFocusRequester = appFocusRequester,
            getLastMarkedFile = { lastMarkedFile },
            onLastMarkedFileChange = { lastMarkedFile = it }
        )
    }
    DiredAppUi(
        showSearch = showSearch,
        fileListSearchQuery = fileListSearchQuery,
        searchFocusRequester = searchFocusRequester,
        leftScrollState = leftScrollState,
        filtered = filtered,
        rightPreviewScrollState = rightPreviewScrollState,
        getEntry = ::getEntry,
        onSearchQueryChange = { fileListSearchQuery = it },
        onSearchFocusedChange = { isSearchFocused = it },
        appFocusRequester = appFocusRequester,
        onFileListSearchQueryChange = { fileListSearchQuery = it },
        previewScrollVelocity = previewScrollVelocity,
        onShowSearchChange = { showSearch = it },
        heldKeys = heldKeys,
        heldCtrlKeys = heldCtrlKeys,
        goDirectoryBack = {
            goDirectoryBack(
                getEntry = ::getEntry,
                changeAvoidAnimatedScrollOnDirectoryChange = { avoidAnimatedScrollOnDirectoryChange = it }
            )
        }
    )
    LaunchedEffectForAppKeys(paneState, localAppKeys)
    DiredAppEffects(
        filtered = filtered,
        fileListSearchQuery = fileListSearchQuery,
        searchFocusRequester = searchFocusRequester,
        appFocusRequester = appFocusRequester,
        coroutineScope = coroutineScope,
        leftScrollState = leftScrollState,
        previewScrollVelocity = previewScrollVelocity,
        showSearch = showSearch,
        isSearchFocused = isSearchFocused,
        scrollOffset = scrollOffset,
        heldKeys = heldKeys,
        heldCtrlKeys = heldCtrlKeys,
        isFirstScrollToAvoidAnimation = isFirstScrollToAvoidAnimation,
        onIsFirstScrollToAvoidAnimationChange = { isFirstScrollToAvoidAnimation = it },
        avoidAnimatedScrollOnDirectoryChange = avoidAnimatedScrollOnDirectoryChange,
        onAvoidAnimatedScrollOnDirectoryChange = { avoidAnimatedScrollOnDirectoryChange = it },
        onFilteredChange = { filtered = it },
        getEntry = ::getEntry,
        selectAbove = {
            selectAbove(
                getEntry = ::getEntry,
                filtered = filtered,
                previewScrollVelocity = previewScrollVelocity
            )
        },
        callSelectBelow = {
            selectBelow(::getEntry, filtered, previewScrollVelocity)
        },
        selectMultipleAbove = {
            selectMultipleAbove(
                getEntry = ::getEntry,
                filtered = filtered,
                previewScrollVelocity = previewScrollVelocity
            )
        },
        selectMultipleBelow = {
            selectMultipleBelow(
                getEntry = ::getEntry,
                filtered = filtered,
                previewScrollVelocity = previewScrollVelocity
            )
        },
        enterDirectory = {
            enterDirectory(
                getEntry = ::getEntry,
                changeAvoidAnimatedScrollOnDirectoryChange = { avoidAnimatedScrollOnDirectoryChange = it }
            )
        },
        goDirectoryBack = {
            goDirectoryBack(
                getEntry = ::getEntry,
                changeAvoidAnimatedScrollOnDirectoryChange = { avoidAnimatedScrollOnDirectoryChange = it }
            )
        },
        reload = {
            reload(
                filtered = filtered,
                changeFiltered = { filtered = it },
                fileListSearchQuery = fileListSearchQuery.text
            )
        }
    )
}