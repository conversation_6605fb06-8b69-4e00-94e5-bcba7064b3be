package dima.apps.dired.findFileDialog

import Globals.faBrandsFont
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import dima.apps.dired.openDirectoryInDired
import dima.apps.dired.openFileInDired
import dima.clipboard.readClipboard
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dima.os.copyToClipboard
import dima.utils.RecentHistory
import dima.utils.ScrollVelocity
import dima.utils.abbreviatePath
import java.io.File

@Composable
fun DiredFindFileDialog(id: Long, data: Any?) {
    val params = data as Params
    val startDirectory = File(params.startPath)
    val initialCandidates = remember {
        val files = startDirectory.listFiles() ?: emptyArray()
        sortFilesByHistoryAndType(files)
    }
    var candidates by remember { mutableStateOf(initialCandidates.toList()) }
    // Use abbreviated path in the query field with trailing slash for directories
    var query by remember {
        val abbreviatedPath = params.startPath.abbreviatePath()
        val pathWithSlash = ensureDirectorySlash(abbreviatedPath)
        mutableStateOf(pathWithSlash)
    }
    var queryState by remember {
        mutableStateOf(
            TextFieldValue(
                text = query,
                selection = TextRange(query.length, query.length)
            )
        )
    }
    var dialogWidth by remember { mutableStateOf(Dp.Unspecified) }
    var dialogHeight by remember { mutableStateOf(Dp.Unspecified) }
    var currentCandidate: FindFileDialogCandidate? by remember { mutableStateOf(candidates.getOrNull(0)) }
    val searchFocusRequester = remember { FocusRequester() }
    val listState: LazyListState = rememberLazyListState()
    val filePreviewScrollState = rememberScrollState()
    val directoryPreviewLazyListState = rememberLazyListState()
    val filePreviewScrollVelocity = remember { ScrollVelocity(filePreviewScrollState) }
    var currentPath by remember { mutableStateOf(startDirectory.absolutePath) }

    DialogOverlay(
        dialogId = id,
        dialogPadding = null,
        widthFraction = null,
        alpha = if (dialogWidth == Dp.Unspecified) 0f else 1f,
        onInitialSize = { width, height ->
            dialogWidth = width
            dialogHeight = height
        },
        onPreviewKeyEvent = onPreviewKeyEvent@{
            if (it.type == KeyEventType.KeyUp) {
                if (it.key == Key.Enter) {
                    // If there's a selected candidate, open it in Dired
                    if (currentCandidate != null) {
                        val selectedFile = currentCandidate!!.file
                        if (selectedFile.exists()) {
                            if (selectedFile.isDirectory) {
                                RecentHistory.rememberFile(selectedFile)
                                selectedFile.openDirectoryInDired()
                            } else {
                                RecentHistory.rememberFile(selectedFile)
                                selectedFile.openFileInDired()
                            }
                            closeDialogWithId(id)
                        }
                    }
                    return@onPreviewKeyEvent true
                }

                // Handle Escape key to close dialog
                if (it.key == Key.Escape) {
                    closeDialogWithId(id)
                    return@onPreviewKeyEvent true
                }

                // Handle key releases for scroll velocity
                if (it.key == Key.C || it.key == Key.T || it.key == Key.M || it.key == Key.V) {
                    filePreviewScrollVelocity.onKeyReleased()
                    return@onPreviewKeyEvent true
                }

                return@onPreviewKeyEvent false
            }

            val isKeyDown = it.type == KeyEventType.KeyDown
            if (!isKeyDown) {
                return@onPreviewKeyEvent false
            }

            // Handle scroll keys for preview
            if (it.isCtrlPressed) {
                when (it.key) {
                    Key.C -> {
                        filePreviewScrollVelocity.onScrollUpKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.T -> {
                        filePreviewScrollVelocity.onScrollDownKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.V -> {
                        filePreviewScrollVelocity.onScrollUpMoreKeyPressed()
                        return@onPreviewKeyEvent true
                    }

                    Key.M -> {
                        filePreviewScrollVelocity.onScrollDownMoreKeyPressed()
                        return@onPreviewKeyEvent true
                    }
                }
            }

            if (it.key == Key.Tab || it.key == Key.DirectionRight) {
                if (currentCandidate == null) {
                    return@onPreviewKeyEvent true
                }
                if (it.key == Key.DirectionRight) {
                    val cursorPosition = queryState.selection.start
                    if (cursorPosition != query.length) {
                        return@onPreviewKeyEvent false
                    }
                }

                val isDirectory = currentCandidate!!.file.isDirectory
                if (isDirectory) {
                    // Update currentPath state when navigating into a directory
                    currentPath = currentCandidate!!.file.absolutePath
                    // Update query to reflect new path and clear filter part
                    val newQuery = ensureDirectorySlash(currentPath.abbreviatePath())
                    query = newQuery
                    queryState = TextFieldValue(text = newQuery, selection = TextRange(newQuery.length))
                } else {
                    // If it's a file, just update query to full path, no change in currentPath
                    query = currentCandidate!!.file.absolutePath.abbreviatePath()
                    queryState = TextFieldValue(text = query, selection = TextRange(query.length))
                }
                return@onPreviewKeyEvent true
            }

            // Handle arrow keys for navigation
            if (it.key == Key.DirectionDown) {
                if (candidates.isEmpty()) {
                    return@onPreviewKeyEvent true
                }
                val index =
                    candidates.indexOfFirst { candidate -> candidate.file.absolutePath == currentCandidate?.file?.absolutePath }
                currentCandidate = if (index == -1 || index == candidates.size - 1) {
                    candidates.firstOrNull()
                } else {
                    candidates.getOrNull(index + 1)
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionUp) {
                if (candidates.isEmpty()) {
                    return@onPreviewKeyEvent true
                }
                val index =
                    candidates.indexOfFirst { candidate -> candidate.file.absolutePath == currentCandidate?.file?.absolutePath }
                currentCandidate = if (index == -1 || index == 0) {
                    candidates.lastOrNull()
                } else {
                    candidates.getOrNull(index - 1)
                }
                return@onPreviewKeyEvent true
            }
            if (it.key == Key.DirectionLeft) {
                // Special handling for left arrow key when cursor is right after a slash
                val cursorPosition = queryState.selection.start
                if (cursorPosition > 0 && query[cursorPosition - 1] == '/') {
                    // Navigate up in path if at a slash
                    val currentExpandedPath =
                        expandPath(query.substring(0, query.length - 1)) // Remove trailing slash for File op
                    val parentFile = File(currentExpandedPath).parentFile
                    if (parentFile != null && parentFile.exists()) {
                        currentPath = parentFile.absolutePath // Update currentPath state
                        val newQuery = ensureDirectorySlash(currentPath.abbreviatePath())
                        query = newQuery
                        queryState = TextFieldValue(text = newQuery, selection = TextRange(newQuery.length))
                    }
                    return@onPreviewKeyEvent true
                }
                return@onPreviewKeyEvent false // Allow default TextField behavior
            }


            if (it.key == Key.V && it.isMetaPressed) {
                val clipboard = readClipboard()
                if (clipboard == null) {
                    return@onPreviewKeyEvent true
                }
                if (clipboard.startsWith("/") || clipboard.startsWith("~/")) {
                    val expandedPath = expandPath(clipboard)
                    val file = File(expandedPath)
                    if (file.exists()) {
                        val pathToUse = if (file.isDirectory) {
                            ensureDirectorySlash(clipboard.abbreviatePath())
                        } else {
                            clipboard.abbreviatePath()
                        }
                        query = pathToUse
                        queryState = TextFieldValue(
                            text = pathToUse,
                            selection = TextRange(pathToUse.length, pathToUse.length)
                        )
                        if (file.isDirectory) {
                            currentPath = file.absolutePath // Update currentPath if clipboard is a directory
                        } else { // It's a file, open it
                            RecentHistory.rememberFile(file)
                            file.openFileInDired()
                            closeDialogWithId(id)
                        }
                    } else { // Pasted path doesn't exist, treat as filter text appended to current query
                        val newQuery = query + clipboard
                        query = newQuery
                        queryState = TextFieldValue(
                            text = newQuery,
                            selection = TextRange(newQuery.length, newQuery.length)
                        )
                    }
                    return@onPreviewKeyEvent true
                }
                // If not an absolute/home path, let TextField handle it as normal paste
                return@onPreviewKeyEvent false
            }
            if (it.isMetaPressed && it.key == Key.E) {
                if (currentCandidate != null) {
                    copyToClipboard(currentCandidate!!.file.absolutePath)
                }
                return@onPreviewKeyEvent true
            }
            false
        }) {
        DiredFindFileDialogUi(
            id = id,
            faBrandsFont = faBrandsFont,
            candidates = candidates,
            currentCandidate = currentCandidate,
            queryState = queryState,
            searchFocusRequester = searchFocusRequester,
            lazyListState = directoryPreviewLazyListState,
            listState = listState,
            filePreviewScrollState = filePreviewScrollState,
            dialogWidth = dialogWidth,
            dialogHeight = dialogHeight,
            onQueryChange = { query = it },
            onQueryStateChange = { queryState = it },
            initialCandidates = initialCandidates
        )
    }

    DiredFindFileDialogEffects(
        currentPath = currentPath,
        query = query,
        candidates = candidates,
        currentCandidate = currentCandidate,
        listState = listState,
        id = id,
        searchFocusRequester = searchFocusRequester,
        filePreviewScrollVelocity = filePreviewScrollVelocity,
        onCandidatesChange = { candidates = it },
        onCurrentCandidateChange = { currentCandidate = it },
        onCurrentPathChange = { currentPath = it }
    )
}

