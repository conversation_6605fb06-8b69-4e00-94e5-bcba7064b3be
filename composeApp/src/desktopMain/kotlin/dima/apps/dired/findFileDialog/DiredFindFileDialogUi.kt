package dima.apps.dired.findFileDialog

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dev.tclement.fonticons.StaticIconFont
import dima.apps.AppType
import dima.apps.dired.Dired
import dima.apps.dired.getFileType
import dima.apps.dired.preview.DiredPreview
import dima.dialogs.closeDialogWithId
import dima.dialogs.completion.CompletionDialogSearchTextField
import dima.globalState.DiredImageState
import dima.globalState.GlobalState
import dima.utils.RecentHistory
import dima.utils.scrollbarStyleThemed
import setCurrentApp

@Composable
internal fun DiredFindFileDialogUi(
    id: Long,
    dialogWidth: Dp,
    dialogHeight: Dp,
    initialCandidates: List<FindFileDialogCandidate>,
    candidates: List<FindFileDialogCandidate>,
    currentCandidate: FindFileDialogCandidate?,
    listState: LazyListState,
    filePreviewScrollState: ScrollState,
    lazyListState: LazyListState,
    searchFocusRequester: FocusRequester,
    faBrandsFont: StaticIconFont,
    queryState: TextFieldValue,
    onQueryStateChange: (TextFieldValue) -> Unit,
    onQueryChange: (String) -> Unit
) {
    Row(modifier = Modifier.width(dialogWidth)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.weight(0.5f)
        ) {
            CompletionDialogSearchTextField(
                queryState = queryState,
                onQueryChange = {
                    onQueryStateChange(it)
                    onQueryChange(it.text)
                },
                title = "Find File",
                totalCandidates = initialCandidates.size,
                filteredCandidates = candidates.size,
                focusRequester = searchFocusRequester,
                dialogWidth = dialogWidth
            )
            if (candidates.isEmpty()) {
                Text(
                    "No matches found",
                    color = GlobalStyling.getRedTextColor(),
                    modifier = Modifier
                        .padding(12.dp)
                )
            }
            Box(modifier = Modifier.weight(1f)) {
                LazyColumn(state = listState) {
                    items(candidates) { candidate ->
                        DiredFindFileDialogUiItem(
                            candidate = candidate,
                            isSelected = currentCandidate == candidate,
                            faBrandsFont = faBrandsFont,
                            listState = listState,
                            query = queryState.text.substringAfterLast('/'),
                            onClick = {
                                // Open the file in dired
                                RecentHistory.rememberFile(candidate.file)
                                GlobalState.dired.setSelectedFile(candidate.file.absolutePath)
                                setCurrentApp(AppType.Dired)
                                closeDialogWithId(id)
                            }
                        )
                    }
                }
                if (dialogHeight != Dp.Unspecified) {
                    VerticalScrollbar(
                        style = scrollbarStyleThemed(),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(horizontal = 3.dp),
                        adapter = rememberScrollbarAdapter(scrollState = listState)
                    )
                }
            }
        }

        // Right side file preview
        Box(
            modifier = Modifier
                .weight(0.5f)
                .fillMaxHeight()
        ) {
            if (currentCandidate != null) {
                DiredPreview(
                    entry = Dired.Entry(
                        file = currentCandidate.file,
                        fileTypeWithColor = currentCandidate.file.getFileType(),
                        fileSizeBytes = currentCandidate.file.length(),
                        lastModified = currentCandidate.file.lastModified()
                    ),
                    drawSelectedBorder = false,
                    rightPreviewScrollState = filePreviewScrollState,
                    imageState = DiredImageState(),
                    roundedCorners = false
                )
            }
            VerticalScrollbar(
                style = scrollbarStyleThemed(),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxHeight(),
                adapter = rememberScrollbarAdapter(scrollState = filePreviewScrollState)
            )
        }
    }
}