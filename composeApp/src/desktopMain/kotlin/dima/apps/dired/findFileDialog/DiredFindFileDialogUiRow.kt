package dima.apps.dired.findFileDialog

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dev.tclement.fonticons.StaticIconFont
import dima.apps.dired.Dired
import dima.apps.dired.DiredDirectoryIcon
import dima.apps.dired.DiredEntryFileIcon
import dima.apps.dired.getFileType
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.text.TextMarked
import dima.utils.clickableWithoutBackgroundRipple

@Composable
internal fun DiredFindFileDialogUiItem(
    candidate: FindFileDialogCandidate,
    query: String,
    isSelected: <PERSON>ole<PERSON>,
    onClick: () -> Unit,
    faBrandsFont: StaticIconFont,
    listState: LazyListState
) {
    val background = if (isSelected) {
        GlobalStyling.getSelectedBackgroundColor()
    } else {
        GlobalStyling.getWindowBackgroundColor()
    }

    val iconSize = 16.dp

    // Add padding at the end when scrollbar is visible
    val hasScrollbar = listState.canScrollForward || listState.canScrollBackward

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickableWithoutBackgroundRipple(onClick = onClick)
            .then(
                if (hasScrollbar) {
                    Modifier.padding(end = Styling.rowEndPaddingForScrollbar)
                } else {
                    Modifier
                }
            )
            .background(background)
            .padding(horizontal = 12.dp, vertical = 4.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            val fileTypeWithColor = remember(candidate.file) { candidate.file.getFileType() }
            val iconTint = if (GlobalState.isDarkMode) {
                fileTypeWithColor.darkColor
            } else {
                fileTypeWithColor.lightColor
            }

            if (candidate.file.isDirectory) {
                val dirIconTint = if (GlobalState.isDarkMode) TailwindCssColors.gray400 else TailwindCssColors.gray600
                DiredDirectoryIcon(iconSize, dirIconTint)
            } else {
                DiredEntryFileIcon(
                    iconSize,
                    Dired.Entry( // Create a temporary Dired.Entry just for the icon
                        file = candidate.file,
                        lastModified = candidate.file.lastModified(),
                        fileSizeBytes = candidate.file.length(),
                        fileTypeWithColor = fileTypeWithColor
                    ),
                    tint = iconTint,
                    faBrandsFont = faBrandsFont
                )
            }

            TextMarked(
                text = candidate.file.name,
                searchQuery = query,
                color = GlobalStyling.getTextColor(),
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}