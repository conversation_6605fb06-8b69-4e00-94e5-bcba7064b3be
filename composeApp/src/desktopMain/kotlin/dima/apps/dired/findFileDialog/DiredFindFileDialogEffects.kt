package dima.apps.dired.findFileDialog

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.focus.FocusRequester
import dialogs
import dima.utils.ScrollVelocity
import dima.utils.filterHuman
import java.io.File

@Composable
internal fun DiredFindFileDialogEffects(
    currentPath: String,
    query: String,
    candidates: List<FindFileDialogCandidate>, // This is the state `candidates` from parent
    currentCandidate: FindFileDialogCandidate?, // This is the state `currentCandidate` from parent
    listState: LazyListState,
    id: Long,
    searchFocusRequester: FocusRequester,
    filePreviewScrollVelocity: ScrollVelocity,
    onCandidatesChange: (List<FindFileDialogCandidate>) -> Unit,
    onCurrentCandidateChange: (FindFileDialogCandidate?) -> Unit,
    onCurrentPathChange: (String) -> Unit
) {
    // Request focus when component is first mounted
    LaunchedEffect(Unit) {
        searchFocusRequester.requestFocus()
        filePreviewScrollVelocity.loopForeverAndTick()
    }

    // Scroll to the current candidate when it changes
    LaunchedEffect(currentCandidate, candidates) { // Ensure re-scroll if candidates list changes but selection might be stale
        if (currentCandidate != null) {
            val mappedIndex = candidates.indexOfFirst { it.file.absolutePath == currentCandidate.file.absolutePath }
            if (mappedIndex != -1) {
                listState.animateScrollToItem(mappedIndex, scrollOffset = -200)
            }
        }
        filePreviewScrollVelocity.scrollToTop(instantly = true)
    }

    // Request focus when this dialog becomes the top dialog
    LaunchedEffect(dialogs) {
        if (dialogs.lastOrNull()?.id == id) {
            searchFocusRequester.requestFocus()
        }
    }

    // Update candidates when query or currentPath changes
    LaunchedEffect(query, currentPath) {
        val localFinalCandidateList: List<FindFileDialogCandidate>

        if (query.isEmpty()) {
            // If query is empty, list files from the current effective path
            val files = File(currentPath).listFiles() ?: emptyArray()
            localFinalCandidateList = sortFilesByHistoryAndType(files)
            // No path change needed here as currentPath is the context
        } else {
            val expandedQuery = expandPath(query)
            val queryFile = File(expandedQuery)

            if (queryFile.exists() && queryFile.isDirectory && (expandedQuery.endsWith("/") || query == "~")) {
                // Query is a full directory path, list its contents
                val files = queryFile.listFiles() ?: emptyArray()
                localFinalCandidateList = sortFilesByHistoryAndType(files)
                if (queryFile.absolutePath != currentPath) {
                    onCurrentPathChange(queryFile.absolutePath) // Update state if path changed due to full dir query
                }
            } else {
                // Query is a partial path or includes a filter part
                // Determine the base directory for listing files
                val basePathForListing = if (queryFile.parent != null) {
                    val parentDir = File(queryFile.parent)
                    if (parentDir.exists() && parentDir.isDirectory) parentDir.absolutePath else currentPath
                } else {
                    currentPath // Fallback if query has no discernible parent (e.g., just "filterText")
                }

                if (basePathForListing != currentPath) {
                    onCurrentPathChange(basePathForListing) // Update state if base path for listing changed
                }

                val filterText = query.substringAfterLast('/')
                val filesOnDisk = File(basePathForListing).listFiles() ?: emptyArray()

                // Apply the filterHuman logic to the files in the base directory
                val filteredDiskFiles = filesOnDisk.toList().filterHuman(filterText) { file -> file.name }
                localFinalCandidateList = sortFilesByHistoryAndType(filteredDiskFiles.toTypedArray())
            }
        }

        onCandidatesChange(localFinalCandidateList)

        // Update currentCandidate (selection in the list) based on the new list
        if (localFinalCandidateList.isEmpty()) {
            onCurrentCandidateChange(null)
        } else {
            val stillPresent = localFinalCandidateList.any { it.file.absolutePath == currentCandidate?.file?.absolutePath }
            if (!stillPresent) {
                onCurrentCandidateChange(localFinalCandidateList[0])
            }
            // If still present, currentCandidate remains the same, its list position might change, handled by LaunchedEffect(currentCandidate, candidates)
        }
    }
}

