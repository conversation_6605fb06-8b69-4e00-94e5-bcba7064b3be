package dima.apps.dired.findFileDialog

import dima.dialogs.DialogIdentifier
import dima.dialogs.openDialog
import dima.os.homeWithoutSlash
import dima.utils.sortedByHistory
import java.io.File

internal data class Params(
    val startPath: String,
)

fun openDiredFindFileDialog(startPath: String) {
    val params = Params(startPath)
    openDialog(DialogIdentifier.DiredFindFile, params) { id, data ->
        DiredFindFileDialog(id, data)
    }
}

internal data class FindFileDialogCandidate(
    val file: File,
    val index: Int
)

/**
 * Expands a path that might contain a tilde (~) to represent the home directory.
 */
internal fun expandPath(path: String): String {
    return if (path.startsWith("~/")) {
        path.replaceFirst("~", homeWithoutSlash)
    } else if (path == "~") {
        homeWithoutSlash
    } else {
        path
    }
}

/**
 * Ensures that directory paths end with a slash.
 */
internal fun ensureDirectorySlash(path: String): String {
    return if (path.isEmpty() || path.endsWith("/")) {
        path
    } else {
        val file = File(expandPath(path))
        if (file.exists() && file.isDirectory) {
            "$path/"
        } else {
            path
        }
    }
}

internal fun sortFilesByHistoryAndType(files: Array<File>): List<FindFileDialogCandidate> {
    return files.sortedByHistory().mapIndexed { index, file ->
        FindFileDialogCandidate(File(file), index)
    }
}
