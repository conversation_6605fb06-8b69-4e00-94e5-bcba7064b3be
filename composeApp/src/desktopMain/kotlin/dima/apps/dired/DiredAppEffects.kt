package dima.apps.dired

import GlobalEvent
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.text.input.TextFieldValue
import dima.apps.dired.preview.DiredPdfProvider
import dima.apps.dired.preview.resetImagePreviewCache
import dima.apps.notifications.showNotification
import dialogs
import dima.globalState.GlobalState
import dima.treeSitter.TreeSitterCliHighlights
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.milliseconds

@Composable
internal fun DiredAppEffects(
    filtered: List<Dired.Entry>,
    fileListSearchQuery: TextFieldValue,
    searchFocusRequester: FocusRequester,
    appFocusRequester: FocusRequester,
    coroutineScope: CoroutineScope,
    leftScrollState: LazyListState,
    previewScrollVelocity: ScrollVelocity,
    avoidAnimatedScrollOnDirectoryChange: Boolean,
    isFirstScrollToAvoidAnimation: Boolean,
    onIsFirstScrollToAvoidAnimationChange: (Boolean) -> Unit,
    showSearch: Boolean,
    isSearchFocused: Boolean,
    scrollOffset: Int,
    heldKeys: MutableSet<Key>,
    heldCtrlKeys: MutableSet<Key>,
    getEntry: () -> Dired.Entry?,
    onFilteredChange: (List<Dired.Entry>) -> Unit,
    onAvoidAnimatedScrollOnDirectoryChange: (Boolean) -> Unit,
    selectAbove: () -> Unit,
    callSelectBelow: () -> Unit,
    selectMultipleAbove: () -> Unit,
    selectMultipleBelow: () -> Unit,
    enterDirectory: () -> Unit,
    goDirectoryBack: () -> Unit,
    reload: () -> Unit,
) {
    var previousSelectedPathForScrollReset by remember { mutableStateOf<String?>(null) }
    var previousFileTypeForScrollReset by remember { mutableStateOf<FileType?>(null) }

    fun isTextPreviewType(fileType: FileType?): Boolean {
        return fileType == FileType.Markdown ||
                fileType == FileType.Text ||
                fileType?.isTreeSitterLanguage == true
    }

    LaunchedEffect(GlobalState.dired.selectedFile) {
        val currentSelectedPath = GlobalState.dired.selectedFile
        val currentEntry = getEntry() // This uses the `getEntry` passed to DiredAppEffects

        if (currentSelectedPath != null && previousSelectedPathForScrollReset != null && currentSelectedPath != previousSelectedPathForScrollReset) {
            val currentFileType = currentEntry?.fileTypeWithColor?.type
            val previousFileType = previousFileTypeForScrollReset

            if (isTextPreviewType(currentFileType) && isTextPreviewType(previousFileType)) {
                previewScrollVelocity.scrollToTop(instantly = true)
            }
        }

        // Update for the next change
        previousSelectedPathForScrollReset = currentSelectedPath
        previousFileTypeForScrollReset = currentEntry?.fileTypeWithColor?.type
    }


    LaunchedEffect(Unit) {
        launch {
            val sleepDuration = 20.milliseconds.inWholeMilliseconds
            while (true) {
                val entry = getEntry()
                if (entry != null) {
                    if (GlobalState.dired.isPreviewActive && entry.fileTypeWithColor.type == FileType.Image) {
                        heldKeys.forEach { key ->
                            when (key) {
                                Key.C -> selectAbove()
                                Key.N -> enterDirectory()
                                Key.H -> goDirectoryBack()
                                Key.T -> callSelectBelow()
                                Key.M -> selectMultipleBelow()
                                Key.V -> selectMultipleAbove()
                            }
                        }
                    }
                }
                delay(sleepDuration)
            }
        }
    }
    LaunchedEffect(Unit) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
        previewScrollVelocity.loopForeverAndTick()
        // LazyScrollVelocity removed - no longer needed
    }
    LaunchedEffect(Unit, GlobalState.dired) {

        fun scrollTo(index: Int) {
            val animate = if (avoidAnimatedScrollOnDirectoryChange || isFirstScrollToAvoidAnimation) {
                onAvoidAnimatedScrollOnDirectoryChange(false)
                false
            } else {
                true
            }
            onIsFirstScrollToAvoidAnimationChange(false)
            coroutineScope.launch {
                // Add delay for going directory backwards where it does not scroll to the selected directory.
                // This only does not work when the current directory is empty, and you go backwards
                //  into a directory which has many items like in Library. Test with:
                //  ~/Library/IdentityServices
                delay(10)
                if (animate) {
                    leftScrollState.animateScrollToItem(index, -scrollOffset)
                } else {
                    leftScrollState.scrollToItem(index, -scrollOffset)
                }
            }
        }

        if (Dired.keepSameFilesOnDiredGlobalStateChange) {
            Dired.keepSameFilesOnDiredGlobalStateChange = false
            val index = filtered.indexOfFirst { it.file.absolutePath == GlobalState.dired.selectedFile }
            if (index == -1) {
                GlobalState.dired.setSelectedFile(filtered.firstOrNull()?.file?.absolutePath)
            } else {
                scrollTo(index)
            }
            return@LaunchedEffect
        }
        
        // Clear search when navigating to a new directory
        val currentSearchQuery = fileListSearchQuery.text
        if (currentSearchQuery.isNotBlank()) {
            // Note: We need to clear the search in the parent component
            // This will be handled by updating the global state
            GlobalState.dired = GlobalState.dired.copy(search = "")
            return@LaunchedEffect
        }
        val newFiltered = Dired.getEntries(
            path = GlobalState.dired.directory,
            sort = GlobalState.dired.sort,
            fileListSearchQuery = fileListSearchQuery.text
        )
        onFilteredChange(newFiltered)
        if (GlobalState.dired.selectedFile == null && newFiltered.isNotEmpty()) {
            GlobalState.dired.setSelectedFile(newFiltered.firstOrNull()?.file?.absolutePath)
        }
        if (GlobalState.dired.selectedFile == null) {
            return@LaunchedEffect
        }
        val index = newFiltered.indexOfFirst { it.file.absolutePath == GlobalState.dired.selectedFile }
        if (index == -1) {
            GlobalState.dired.setSelectedFile(newFiltered.firstOrNull()?.file?.absolutePath)
        } else {
            scrollTo(index)
        }
    }
    LaunchedEffect(showSearch) {
        if (showSearch) {
            searchFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            if (isSearchFocused) {
                searchFocusRequester.requestFocus()
            } else {
                appFocusRequester.requestFocus()
            }
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.Reload -> {
                DiredPdfProvider.resetCache()
                Dired.keepSameFilesOnDiredGlobalStateChange = false
                TreeSitterCliHighlights.clearCache()
                reload()
                resetImagePreviewCache()
                if (Dired.hideNextReloadNotification) {
                    Dired.hideNextReloadNotification = false
                } else {
                    showNotification("Reloaded Dired", durationMillis = 500)
                }
            }

            GlobalEvent.ScrollToBottom -> {
                if (GlobalState.dired.isPreviewActive) {
                    previewScrollVelocity.scrollToBottom()
                    return@LaunchedEffectGlobalEventForApps
                }
                val newSelected = filtered.lastOrNull()?.file?.absolutePath
                if (newSelected == GlobalState.dired.selectedFile) {
                    return@LaunchedEffectGlobalEventForApps
                }
                Dired.keepSameFilesOnDiredGlobalStateChange = true
                GlobalState.dired.setSelectedFile(newSelected)
                if (newSelected != null) {
                    DiredDatabase.rememberSelected()
                }
            }

            GlobalEvent.ScrollToTop -> {
                if (GlobalState.dired.isPreviewActive) {
                    previewScrollVelocity.scrollToTop()
                    return@LaunchedEffectGlobalEventForApps
                }
                val newSelected = filtered.firstOrNull()?.file?.absolutePath
                if (newSelected == GlobalState.dired.selectedFile) {
                    return@LaunchedEffectGlobalEventForApps
                }
                Dired.keepSameFilesOnDiredGlobalStateChange = true
                GlobalState.dired.setSelectedFile(newSelected)
                if (newSelected != null) {
                    DiredDatabase.rememberSelected()
                }
            }

            else -> {}
        }
    }
    LaunchedEffect(fileListSearchQuery.text) { // React to text changes
        val newFiltered = Dired.getEntries(
            GlobalState.dired.directory,
            GlobalState.dired.sort,
            fileListSearchQuery.text
        )
        onFilteredChange(newFiltered)
        val currentSelected = GlobalState.dired.selectedFile
        if (currentSelected == null || newFiltered.none { it.file.absolutePath == currentSelected }) {
            GlobalState.dired.setSelectedFile(newFiltered.firstOrNull()?.file?.absolutePath)
        }
        GlobalState.dired = GlobalState.dired.copy(search = fileListSearchQuery.text) // Update global state
    }
    DisposableEffect(Unit) {
        onDispose {
            DiredDatabase.rememberSelected()
        }
    }
}
