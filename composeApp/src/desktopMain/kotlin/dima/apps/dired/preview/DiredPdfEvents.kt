package dima.apps.dired.preview

import dima.apps.dired.Dired
import dima.events.DimaEvent

/**
 * Event to scroll to a specific page in a PDF document
 */
data class ScrollToPdfPageEvent(val filePath: String, val pageIndex: Int) : DimaEvent

/**
 * Event to open the PDF page selection dialog at the current visible page
 */
data class OpenPdfPageSelectionAtCurrentPageEvent(val entry: Dired.Entry) : DimaEvent