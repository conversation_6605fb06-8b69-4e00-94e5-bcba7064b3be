package dima.apps.dired.preview

import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.toComposeImageBitmap
import androidx.compose.ui.unit.dp
import dima.apps.dired.Dired
import dima.apps.dired.FileData
import dima.apps.dired.opus.OpusFileReader
import dima.apps.notifications.showErrorNotification
import dima.audio.Audio
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jaudiotagger.audio.AudioFileIO
import org.jaudiotagger.tag.FieldKey
import org.jaudiotagger.tag.Tag
import java.io.ByteArrayInputStream
import java.util.logging.Level
import java.util.logging.Logger
import javax.imageio.ImageIO

private fun getTag(tag: Tag?, field: FieldKey): String? {
    if (tag == null) {
        return null
    }
    return try {
        val t = tag.getFirst(field)
        if (t == "") null else t
    } catch (_: Exception) {
        null
    }
}

@Composable
internal fun DiredAudioPreview(entry: Dired.Entry, scrollState: ScrollState) {
    var painter by remember { mutableStateOf<BitmapPainter?>(null) }
    var duration by remember { mutableStateOf<String?>(null) }
    var artist by remember { mutableStateOf<String?>(null) }
    var album by remember { mutableStateOf<String?>(null) }
    var sampleRate by remember { mutableStateOf<String?>(null) }
    var bitRate by remember { mutableStateOf<String?>(null) }
    var currentEntry by remember { mutableStateOf(entry) }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .padding(12.dp)
            .verticalScroll(scrollState)
    ) {
        if (painter != null) {
            Image(
                painter = painter!!,
                contentDescription = null,
                modifier = Modifier
                    .clip(RoundedCornerShape(5.dp))
                    .widthIn(max = 300.dp)
            )
        }
        val width = 100.dp
        if (artist != null) {
            FileData("Artist", artist!!, width)
        }
        if (album != null) {
            FileData("Album", album!!, width)
        }
        if (duration != null) {
            FileData("Duration", duration!!, width)
        }
        if (sampleRate != null) {
            FileData("Sample rate", sampleRate!!, width)
        }
        if (bitRate != null) {
            FileData("Bitrate", bitRate!!, width)
        }
    }

    LaunchedEffect(Unit, entry) {
        painter = null
        duration = null
        artist = null
        album = null
        bitRate = null
        sampleRate = null
        // apparently, storing this as a job and then canceling the job, does not fix the flickering
        CoroutineScope(Dispatchers.IO).launch {
            silenceWarnings()

            var newDuration: String? = null
            var newSampleRate: String? = null
            var newArtist: String? = null
            var newAlbum: String? = null
            var newBitRate: String? = null
            var newPainter: BitmapPainter? = null
            try {
                val extension = entry.file.extension.lowercase()
                if (extension == "opus" || extension == "ogg") {
                    val reader = OpusFileReader()
                    val metaData = reader.read(entry.file)
                    newDuration = Audio.formatMsDuration((metaData.audioHeader.trackLength * 1000).toLong())
                } else {
                    val audioMetadata = AudioFileIO.read(entry.file)
                    val sampleRateNumber = audioMetadata.audioHeader.sampleRateAsNumber
                    newSampleRate = if (sampleRateNumber >= 1000) {
                        "${sampleRateNumber / 1000.0} kHz"
                    } else {
                        "$sampleRateNumber Hz"
                    }
                    if (extension == "wav") {
                        newBitRate = audioMetadata.audioHeader.bitsPerSample.toString()
                    } else if (extension == "mp3") {
                        val image = audioMetadata.tag?.firstArtwork?.binaryData
                        newPainter = try {
                            BitmapPainter(
                                ImageIO.read(ByteArrayInputStream(image)).toComposeImageBitmap()
                            )
                        } catch (_: Exception) {
                            // ignore files where an artwork is set, but it is broken. This happens in Apple Music as well.
                            // for instance: ~/Documents/Spanisch lernen/Spanisch/Spanish 1-1.mp3
                            null
                        }
                        newArtist = getTag(audioMetadata.tag, FieldKey.ARTIST)
                        newAlbum = getTag(audioMetadata.tag, FieldKey.ALBUM)
                    }
                    newDuration = Audio.formatMsDuration((audioMetadata.audioHeader.trackLength * 1000).toLong())
                }
            } catch (e: Exception) {
                showErrorNotification("Error reading audio metadata", e.message)
            }
            // avoid the flicker on changing files quickly, although it is not perfect
            if (currentEntry == entry) {
                duration = newDuration
                sampleRate = newSampleRate
                bitRate = newBitRate
                painter = newPainter
                artist = newArtist
                album = newAlbum
                return@launch
            }
        }
        currentEntry = entry
    }
}

/**
 * Silence because they are annoying and just polluting the stdout.
 */
private fun silenceWarnings() {
    Logger.getLogger("org.jaudiotagger.tag.id3").level = Level.OFF
    Logger.getLogger("org.jaudiotagger.audio.wav").level = Level.OFF
    Logger.getLogger("org.jaudiotagger").level = Level.OFF
    Logger.getLogger("org.jaudiotagger.audio").level = Level.OFF
    Logger.getLogger("org.jaudiotagger.audio.mp3").level = Level.OFF
    Logger.getLogger("org.jaudiotagger.audio.mp3.MP3File").level = Level.OFF
}