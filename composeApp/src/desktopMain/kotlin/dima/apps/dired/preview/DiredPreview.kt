package dima.apps.dired.preview

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import dima.apps.dired.Dired
import dima.apps.dired.FileType
import dima.apps.dired.preview.video.DiredVideoPreview
import dima.globalState.DiredImageState
import dima.treeSitter.TreeSitterLanguage

/**
 * Preview component that handles different file types with appropriate scroll states.
 */
@Composable
fun DiredPreview(
    entry: Dired.Entry,
    drawSelectedBorder: Boolean,
    rightPreviewScrollState: ScrollState,
    imageState: DiredImageState = DiredImageState(),
    roundedCorners: Boolean = true
) {

    @Composable
    fun TreeSitterText(language: TreeSitterLanguage) {
        DiredTreeSitterText(
            entry = entry,
            drawSelectedBorder = drawSelectedBorder,
            language = language,
            roundedCorners = roundedCorners,
            rightPreviewScrollState = rightPreviewScrollState
        )
    }

    if (entry.file.isDirectory) {
        DiredDirectoryPreview(
            entry = entry,
            listState = rememberLazyListState(),
        )
    } else {
        when (entry.fileTypeWithColor.type) {
            FileType.Audio -> DiredAudioPreview(entry, rightPreviewScrollState)
            FileType.Image -> DiredImagePreview(entry, rightPreviewScrollState, imageState)
            FileType.Video -> DiredVideoPreview(entry, rightPreviewScrollState)
            FileType.Pdf -> DiredPdfPreview(entry, rightPreviewScrollState, imageState)
            FileType.Markdown -> DiredMarkdownPreview(entry, rightPreviewScrollState)
            FileType.Archive -> DiredArchivePreview(entry, rightPreviewScrollState)

            FileType.Json -> TreeSitterText(TreeSitterLanguage.JSON)
            FileType.Toml -> TreeSitterText(TreeSitterLanguage.Toml)
            FileType.Rust -> TreeSitterText(TreeSitterLanguage.Rust)
            FileType.Groovy -> TreeSitterText(TreeSitterLanguage.Groovy)
            FileType.PHP -> TreeSitterText(TreeSitterLanguage.PHP)
            FileType.Python -> TreeSitterText(TreeSitterLanguage.Python)
            FileType.Java -> TreeSitterText(TreeSitterLanguage.Java)
            FileType.Bash -> TreeSitterText(TreeSitterLanguage.Bash)
            FileType.Dockerfile -> TreeSitterText(TreeSitterLanguage.Dockerfile)
            FileType.CSS -> TreeSitterText(TreeSitterLanguage.CSS)
            FileType.HTML -> TreeSitterText(TreeSitterLanguage.HTML)
            FileType.JavaScript -> TreeSitterText(TreeSitterLanguage.JavaScript)
            FileType.Text -> DiredAppUiTextPreview(entry, rightPreviewScrollState)
        }
    }
}