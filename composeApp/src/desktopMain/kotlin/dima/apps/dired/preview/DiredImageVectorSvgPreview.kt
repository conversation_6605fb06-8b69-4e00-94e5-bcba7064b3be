package dima.apps.dired.preview

import Globals
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.toComposeImageBitmap
import androidx.compose.ui.unit.dp
import dima.apps.dired.Dired
import dima.apps.dired.FileData
import dima.globalState.DiredImageState
import dima.images.clearCustomAsyncImageCache
import dima.utils.Result
import dima.utils.Ring
import dima.utils.readTextUpTo
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.jetbrains.compose.resources.ExperimentalResourceApi
import org.jetbrains.compose.resources.decodeToSvgPainter
import java.awt.image.BufferedImage
import java.io.File
import java.io.IOException
import javax.imageio.ImageIO
import kotlin.coroutines.cancellation.CancellationException

/**
 * @param resolution null for SVGs
 * @param content set for SVGs and on errors
 */
private data class CachedImagePreview(
    val path: String,
    val painter: Painter? = null,
    val resolution: String? = null,
    val height: Int? = null,
    val error: String? = null,
    val content: String? = null
)

private data class ImageProviderState(
    val requestedPath: String? = null, // Path of the image currently being requested by the UI
    val painter: Painter? = null,
    val widthHeightResolutionString: String? = null,
    val error: String? = null,
    val content: String? = null,
    val isLoading: Boolean = false
)

/**
 * Gemini 2.5 Pro 0506 wrote this. I have no idea how exactly it works, but it works flawlessly!
 */
private object DiredImageProvider {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var imagePreviewCache: Ring<CachedImagePreview> = Ring(100)
    private val activeJobs = mutableMapOf<String, Job>() // Changed from currentJob

    private val _imageStateFlow = MutableStateFlow(ImageProviderState()) // Initialize with default
    val imageStateFlow: StateFlow<ImageProviderState> = _imageStateFlow.asStateFlow()

    fun loadImage(entry: Dired.Entry) {
        val path = entry.file.absolutePath

        val cached = imagePreviewCache.find { it.path == path }
        if (cached != null) {
            _imageStateFlow.value = ImageProviderState(
                requestedPath = path,
                painter = cached.painter,
                widthHeightResolutionString = cached.resolution,
                error = cached.error,
                content = cached.content,
                isLoading = false
            )
            return
        }

        if (activeJobs[path]?.isActive == true) {
            // Job already running for this path. If the UI is not already showing loading for this path, update it.
            if (_imageStateFlow.value.requestedPath != path || !_imageStateFlow.value.isLoading) {
                _imageStateFlow.value = ImageProviderState(requestedPath = path, isLoading = true)
            }
            return
        }

        // Indicate loading for the new request
        _imageStateFlow.value = ImageProviderState(requestedPath = path, isLoading = true)
        val currentEntryFileCopy = File(entry.file.absolutePath) // Use a copy for the coroutine

        val job = scope.launch {
            // Variables to hold the results of this specific loading job
            var jobPainter: Painter? = null
            var jobResolution: String? = null
            var jobError: String? = null
            var jobContent: String? = null
            var jobHeight: Int?  // for non-SVG, if needed for cache
            val currentPath = currentEntryFileCopy.absolutePath // Use a path consistently

            try {
                ensureActive()
                if (currentEntryFileCopy.extension.lowercase() == "svg") {
                    val bytes = currentEntryFileCopy.readBytes()
                    ensureActive()
                    val contentResult = currentEntryFileCopy.readTextUpTo()
                    if (contentResult is Result.Error) {
                        jobError = contentResult.error
                        imagePreviewCache.add(CachedImagePreview(currentPath, error = jobError))
                        return@launch
                    }
                    contentResult as Result.Success
                    jobContent = contentResult.value.text
                    try {
                        val p = bytes.decodeToSvgPainter(Globals.density)
                        ensureActive()
                        jobPainter = p
                        imagePreviewCache.add(CachedImagePreview(currentPath, painter = p, content = jobContent))
                    } catch (e: Exception) {
                        ensureActive()
                        jobError = "Error decoding to SVG Painter: Is the SVG valid?\n${e.message}"
                        imagePreviewCache.add(CachedImagePreview(currentPath, content = jobContent, error = jobError))
                    }
                } else { // Non-SVG
                    val image: BufferedImage? = try {
                        ensureActive()
                        ImageIO.read(currentEntryFileCopy)
                    } catch (_: IOException) {
                        ensureActive()
                        jobError = "Incorrect file path passed or failed to read image"
                        imagePreviewCache.add(CachedImagePreview(currentPath, error = jobError))
                        null // image is null
                    }

                    ensureActive() // Check after ImageIO.read potentially long operation

                    if (image == null) {
                        // This block handles both explicit null from ImageIO.read
                        // and the case where jobError was set by IOException.
                        if (jobError == null) { // If the error isn't already set by IOException
                            jobError = "Failed to decode as image"
                        }
                        // Try to read content if not already an IO error for a path
                        if (jobError != "Incorrect file path passed or failed to read image") {
                            val contentResult = currentEntryFileCopy.readTextUpTo()
                            ensureActive()
                            when (contentResult) {
                                is Result.Error -> jobError += " " + contentResult.error
                                is Result.Success -> jobContent = contentResult.value.text
                            }
                        }
                        imagePreviewCache.add(CachedImagePreview(currentPath, error = jobError, content = jobContent))
                    } else { // Image successfully read and decoded
                        ensureActive()
                        val converted = image.toComposeImageBitmap()
                        jobResolution = "${converted.width} x ${converted.height}"
                        jobPainter = BitmapPainter(converted)
                        jobHeight = image.height
                        imagePreviewCache.add(
                            CachedImagePreview(
                                currentPath,
                                painter = jobPainter,
                                resolution = jobResolution,
                                height = jobHeight
                            )
                        )
                    }
                }
            } catch (e: CancellationException) {
                // Job explicitly cancelled (e.g., by resetCache or scope closing).
                // Don't update the flow, just rethrow to ensure cleanup.
                throw e
            } catch (e: Exception) {
                // Catch any other unexpected errors during loading
                ensureActive()
                jobError = "Unexpected error loading image: ${e.message}"
                // Try to get content if not already available
                if (jobContent == null && currentEntryFileCopy.exists() && currentEntryFileCopy.isFile) {
                    try {
                        val contentResult = currentEntryFileCopy.readTextUpTo()
                        ensureActive()
                        when (contentResult) {
                            is Result.Error -> jobError += " " + contentResult.error
                            is Result.Success -> jobContent = contentResult.value.text
                        }
                    } catch (_: Exception) {
                    }
                }
                imagePreviewCache.add(CachedImagePreview(currentPath, error = jobError, content = jobContent))
            } finally {
                activeJobs.remove(currentPath)
            }

            // After job completion (or cancellation that didn't throw out of launch scope)
            // Update the main flow ONLY if this job corresponds to the currently UI-requested image
            // AND the UI is still in a loading state for this path.
            if (isActive) { // Check if coroutine itself is still active (not cancelled out)
                if (_imageStateFlow.value.requestedPath == currentPath && _imageStateFlow.value.isLoading) {
                    _imageStateFlow.value = ImageProviderState(
                        requestedPath = currentPath,
                        painter = jobPainter,
                        widthHeightResolutionString = jobResolution,
                        error = jobError,
                        content = jobContent,
                        isLoading = false // Mark as not loading
                    )
                }
            }
        }
        activeJobs[path] = job // Add new job to active jobs map
    }

    fun resetCache() {
        // Cancel all active jobs before clearing the cache
        activeJobs.values.forEach { it.cancel("Cache reset") }
        activeJobs.clear()

        imagePreviewCache.clear()
        _imageStateFlow.value = ImageProviderState() // Reset to the initial state
        Dired.reload = !Dired.reload
    }
}

internal fun resetImagePreviewCache() {
    DiredImageProvider.resetCache()
    clearCustomAsyncImageCache()
}

/**
 * Displaying SVGs is not perfect. I saw a missing text on the ones generated for the TreeSitter dot graphs.
 */
@OptIn(ExperimentalResourceApi::class)
@Composable
internal fun DiredImagePreview(entry: Dired.Entry, scrollState: ScrollState, imageState: DiredImageState) {
    val providerState by DiredImageProvider.imageStateFlow.collectAsState()

    // It's important to launch the loading effect when the entry or Dired.reload changes.
    // Dired.reload is a global trigger that might necessitate reloading even the same image (e.g., cache cleared).
    LaunchedEffect(entry, Dired.reload) {
        DiredImageProvider.loadImage(entry)
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .padding(12.dp)
            .verticalScroll(scrollState)
    ) {
        if (providerState.widthHeightResolutionString != null) {
            FileData("Resolution", providerState.widthHeightResolutionString!!, 90.dp)
        }
        if (providerState.error != null) {
            FileData("Error", providerState.error!!, 90.dp)
        }
        if (providerState.painter != null) {
            Image(
                painter = providerState.painter!!,
                contentDescription = null,
                modifier = Modifier
                    .clip(RoundedCornerShape(5.dp))
                    .fillMaxWidth()
                    .height(700.dp * imageState.zoom)
                    .graphicsLayer {
                        // I tried using some ChatGPTed math for some pinch-to-zoom code, but it never worked properly.
                        // This isn't perfect, but at least the zoom is very predictable since the transform origin is in the middle of the image
                        scaleX = imageState.zoom
                        scaleY = imageState.zoom
                        // this is not exactly perfect with the .height(), but good enough
                        translationX = imageState.offsetX * imageState.zoom
                        translationY = imageState.offsetY * imageState.zoom
                    }
            )
        }
        if (providerState.content != null) {
            FileData("File content", providerState.content!!, 90.dp)
        }
    }
}
