package dima.apps.dired.preview

import GlobalStyling
import androidx.compose.foundation.Image
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PictureAsPdf
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.X
import dima.apps.dired.Dired
import dima.apps.dired.FileData
import dima.apps.notifications.showErrorNotification
import dima.color.TailwindCssColors
import dima.events.collectEvent
import dima.events.emitEasy
import dima.apps.dired.pdfPageDialog.openPdfPageSelectionDialog
import dima.globalState.DiredImageState
import dima.globalState.GlobalState
import dima.utils.Result
import dima.utils.abbreviatePath
import dima.utils.readTextUpTo
import dima.utils.toNiceHumanByteString
import kotlinx.coroutines.launch
import java.io.File

object DiredPdfPreview {
    val cacheDirectory = File("cache/pdf")
}

@Composable
internal fun DiredPdfPreview(entry: Dired.Entry, scrollState: ScrollState, imageState: DiredImageState) {
    val providerState by DiredPdfProvider.pdfStateFlow.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    
    // We're now using the scrollState parameter which is a ScrollState
    // This allows for smooth scrolling with ScrollVelocity

    LaunchedEffect(entry, Dired.reload) {
        if (!DiredPdfPreview.cacheDirectory.exists()) {
            val success = DiredPdfPreview.cacheDirectory.mkdirs()
            if (!success) {
                showErrorNotification(
                    "Failed to create PDF cache directory",
                    DiredPdfPreview.cacheDirectory.abbreviatePath()
                )
            }
        }
        DiredPdfProvider.loadPdf(entry)
    }


    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(12.dp)
    ) {
        // Display based on providerState, ensuring it's for the current entry
        if (providerState.requestedPath == entry.file.absolutePath) {
            when {
                providerState.isLoading -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth().padding(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PictureAsPdf,
                            contentDescription = "Loading PDF Preview",
                            tint = if (GlobalState.isDarkMode) TailwindCssColors.gray400 else TailwindCssColors.gray600,
                            modifier = Modifier.size(64.dp)
                        )
                        Text(
                            "Loading PDF preview...",
                            color = GlobalStyling.getGrayColor(),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }

                providerState.error != null -> {
                    Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                        val width = 80.dp
                        FileData("Error", providerState.error!!, width)
                        FileData("Size", entry.fileSizeBytes.toNiceHumanByteString(), width)
                        val contentResult = entry.file.readTextUpTo()
                        when (contentResult) {
                            is Result.Error -> FileData("Error reading file", contentResult.error, width)
                            is Result.Success -> {
                                if (contentResult.value.text.isEmpty()) {
                                    FileData("Contents", "empty", width, bold = true)
                                } else {
                                    FileData("Contents", contentResult.value.text, width)
                                }
                            }
                        }
                    }
                }

                providerState.images.isNotEmpty() && providerState.pageCount != null -> {
                    Column {
                        val textColor = GlobalStyling.getTextColor()
                        Text(
                            "Total pages: ${providerState.pageCount}",
                            textAlign = TextAlign.Center,
                            color = textColor,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        )

                        Box {
                            Column(
                                verticalArrangement = Arrangement.spacedBy(16.dp),
                                modifier = Modifier
                                    .fillMaxSize()
                                    .verticalScroll(scrollState)
                            ) {
                                // Use regular Column with ScrollState for smooth scrolling
                                providerState.images.forEachIndexed { index, imageBitmap ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(end = 12.dp) // Ensure some padding for the scrollbar if needed
                                    ) {
                                        Text(
                                            "Page ${index + 1}",
                                            color = textColor,
                                            modifier = Modifier
                                                .padding(horizontal = 8.dp)
                                        )
                                        Image(
                                            painter = BitmapPainter(imageBitmap),
                                            contentDescription = "PDF Page ${index + 1}",
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .clipToBounds()
                                                .graphicsLayer {
                                                    scaleX = imageState.zoom
                                                    scaleY = imageState.zoom
                                                    translationX = imageState.offsetX * imageState.zoom
                                                    translationY = imageState.offsetY * imageState.zoom
                                                }
                                        )
                                    }
                                }
                            }
                            VerticalScrollbar(
                                modifier = Modifier
                                    .align(Alignment.CenterEnd)
                                    .fillMaxHeight(),
                                adapter = rememberScrollbarAdapter(scrollState = scrollState)
                            )
                        }

                    }
                }

                else -> { // Not loading, no error, but no images - e.g. pdf2png failed but didn't report specific error
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth().padding(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PictureAsPdf,
                            contentDescription = "PDF Icon",
                            tint = if (GlobalState.isDarkMode) TailwindCssColors.red400 else TailwindCssColors.red600,
                            modifier = Modifier.size(64.dp)
                        )
                        Icon(
                            imageVector = Lucide.X,
                            contentDescription = "Preview Failed",
                            tint = TailwindCssColors.red600,
                            modifier = Modifier.size(32.dp).padding(top = 4.dp)
                        )
                        Text(
                            "Failed to generate PDF preview",
                            color = GlobalStyling.getRedTextColor(),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        } else if (providerState.isLoading) { // Loading a *different* PDF
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth().padding(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.PictureAsPdf,
                    contentDescription = "Loading PDF Preview",
                    tint = if (GlobalState.isDarkMode) TailwindCssColors.gray400 else TailwindCssColors.gray600,
                    modifier = Modifier.size(64.dp)
                )
                Text(
                    "Loading PDF preview...",
                    color = GlobalStyling.getGrayColor(),
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
        // If providerState.requestedPath is not current entry's path and not loading, it means an old state is shown.
        // The LaunchedEffect should kick in soon to request the new one.
    }

    LaunchedEffect(Unit) {
        collectEvent<ScrollToPdfPageEvent> { event ->
            if (event.filePath == entry.file.absolutePath && providerState.images.isNotEmpty()) {
                val validPageIndex = event.pageIndex.coerceIn(0, providerState.images.size - 1)
                // For ScrollState, we need to calculate the scroll position based on page index
                // This is a simplified approach - in a real implementation you might want to 
                // calculate the exact position based on image heights
                val scrollPosition = validPageIndex * 500 // Approximate scroll per page
                coroutineScope.launch {
                    scrollState.animateScrollTo(scrollPosition)
                }
            }
        }
        
        collectEvent<OpenPdfPageSelectionAtCurrentPageEvent> { event ->
            if (event.entry.file.absolutePath == entry.file.absolutePath && providerState.images.isNotEmpty()) {
                // For ScrollState, estimate the current page based on scroll position
                // This is a simplified approach
                val currentScrollPosition = scrollState.value
                val estimatedPageIndex = (currentScrollPosition / 500).coerceAtLeast(0)
                
                // Open the PDF page selection dialog with the current visible page as the initial page
                openPdfPageSelectionDialog(
                    entry = entry,
                    initialPageIndex = estimatedPageIndex,
                    onConfirm = { pageIndex ->
                        // Emit event to scroll to the selected page
                        coroutineScope.launch {
                            ScrollToPdfPageEvent(entry.file.absolutePath, pageIndex).emitEasy()
                        }
                    }
                )
            }
        }
    }
}
