package dima.apps.dired.preview

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.unit.dp
import dima.apps.dired.Dired
import dima.apps.dired.addSelectedBorder
import dima.treeSitter.IntellijDarculaTheme
import dima.treeSitter.TreeSitterLanguage
import dima.treeSitter.TreeSitterText
import dima.utils.DiredTruncatedFileNote
import dima.utils.FileReadResult
import dima.utils.Result
import dima.utils.readTextUpTo

@Composable
internal fun DiredTreeSitterText(
    entry: Dired.Entry,
    drawSelectedBorder: Boolean,
    language: TreeSitterLanguage,
    roundedCorners: Boolean,
    rightPreviewScrollState: ScrollState
) {
    var contents by remember { mutableStateOf<Result<FileReadResult>>(Result.Success(FileReadResult("", false))) }

    LaunchedEffect(entry.file.absolutePath, language) { // React to file or language changes
        contents = entry.file.readTextUpTo()
    }

    Column(
        modifier = Modifier
            .fillMaxHeight()
            .background(
                IntellijDarculaTheme.background,
                if (roundedCorners) GlobalStyling.smallRoundedCorners else RectangleShape
            )
            // set larger border size because with the dark background, it is hard to see otherwise
            .addSelectedBorder(drawSelectedBorder, borderSize = 6.dp)
            .verticalScroll(rightPreviewScrollState)
    ) {
        SelectionContainer {
            when (val currentContents = contents) { // Use local variable for smart casting
                is Result.Error -> {
                    Column(
                        modifier = Modifier
                            .padding(12.dp)
                    ) {
                        Text(
                            "Failed to read file: ${currentContents.error}",
                            color = GlobalStyling.getRedTextColor(),
                            modifier = Modifier
                                .padding(bottom = 20.dp)
                        )
                    }
                }

                is Result.Success -> {
                    Column {
                        TreeSitterText(
                            currentContents.value.text,
                            language,
                            modifier = Modifier
                                .padding(12.dp)
                                .fillMaxWidth()
                        )
                        if (currentContents.value.isTruncated) {
                            DiredTruncatedFileNote(useDarkTextColor = true)
                        }
                    }
                }
            }
        }
    }
}
