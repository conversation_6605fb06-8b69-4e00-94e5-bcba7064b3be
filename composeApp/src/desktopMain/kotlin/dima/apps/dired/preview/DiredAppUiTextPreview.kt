package dima.apps.dired.preview

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.apps.dired.Dired
import dima.utils.DiredTruncatedFileNote
import dima.utils.FileReadResult
import dima.utils.Result
import dima.utils.readTextUpTo

@Composable
internal fun DiredAppUiTextPreview(
    entry: Dired.Entry,
    rightPreviewScrollState: ScrollState
) {
    val content by remember(entry) {
        mutableStateOf(entry.file.readTextUpTo(showErrorNotification = false))
    }
    Column(
        modifier = Modifier
            .verticalScroll(rightPreviewScrollState)
            .padding(12.dp)
    ) {
        when (content) {
            is Result.Error -> {
                Text(
                    (content as Result.Error).error,
                    color = GlobalStyling.getRedTextColor(),
                    modifier = Modifier.fillMaxWidth()
                )
            }

            is Result.Success -> {
                Text(
                    (content as Result.Success<FileReadResult>).value.text,
                    color = GlobalStyling.getTextColor(),
                    modifier = Modifier.fillMaxWidth()
                )
                if ((content as Result.Success<FileReadResult>).value.isTruncated) {
                    DiredTruncatedFileNote(useDarkTextColor = false)
                }
            }
        }
    }
}
