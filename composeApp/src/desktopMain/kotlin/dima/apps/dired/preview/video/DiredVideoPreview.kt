package dima.apps.dired.preview.video

import Globals
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.toComposeImageBitmap
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.dired.Dired
import dima.apps.dired.FileData
import dima.apps.dired.toBufferedImage
import dima.utils.Ring
import kotlinx.coroutines.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.bytedeco.ffmpeg.global.avutil
import org.bytedeco.javacv.FFmpegFrameGrabber
import org.bytedeco.javacv.Java2DFrameConverter
import java.awt.Image
import java.awt.image.BufferedImage

@Serializable
private data class FfmpegJsonStream(
    /**
     * Format: 16:9
     *
     * I noticed that it is null for videos downloaded from Instagram.
     */
    @SerialName("display_aspect_ratio")
    val displayAspectRatio: String? = null
)

@Serializable
private data class FfmpegJsonFormat(
    /**
     * Format: 0:09:56.96000
     */
    val duration: String
)

@Serializable
private data class FfmpegJson(
    val streams: List<FfmpegJsonStream>,
    val format: FfmpegJsonFormat
)

private val json = Json {
    ignoreUnknownKeys = true
}

/**
 * @param s in "16:9" format
 * @return pair of width and height
 */
private fun parseAspectRatio(s: String): Double {
    val parts = s.split(":")
    return parts[0].toInt().div(parts[1].toDouble())
}

private data class Cache(
    val absolutePath: String,
    val aspectRatio: Double?,
    val dimensions: String?,
    val title: String?,
    val metaData: Map<String, String>,
    val duration: String?,
    val images: List<ImageBitmap>
)

private var videoCache: Ring<Cache> = Ring(120)

@Composable
internal fun DiredVideoPreview(entry: Dired.Entry, scrollState: ScrollState) {
    var state by remember { mutableStateOf<Cache?>(null) }
    var job by remember { mutableStateOf<Job?>(null) }

    if (state != null) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .verticalScroll(scrollState)
                // use larger end padding for the scroll bar when this right side is active
                .padding(start = 12.dp, end = 20.dp, top = 12.dp, bottom = 12.dp)
        ) {
            val width = 90.dp
            if (state!!.duration != null) {
                FileData("Duration", state!!.duration.toString(), width)
            }
            if (state!!.dimensions != null) {
                FileData("Dimensions", state!!.dimensions!!, width)
            }
            if (state!!.title != null) {
                FileData("Title", state!!.title!!, width)
            }
            VideoPreviewImages(state!!.images)
            if (state!!.metaData.isNotEmpty()) {
                var metaDataWidth: Int = width.value.toInt()
                state!!.metaData.keys.forEach { key ->
                    val textLayoutResult: TextLayoutResult =
                        TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr).measure(key)
                    if (textLayoutResult.size.width > metaDataWidth) {
                        metaDataWidth = textLayoutResult.size.width
                    }
                }
                val convertedWidth = with(Globals.density) {
                    metaDataWidth.toDp()
                }
                state!!.metaData.forEach { (key, value) ->
                    FileData(key, value, convertedWidth.plus(15.dp))
                }
            }
        }
    }

    LaunchedEffect(Unit, entry) {
        if (job != null) {
            job!!.cancel()
            job = null
        }
        val cached = videoCache.find { it.absolutePath == entry.file.absolutePath }
        if (cached != null) {
            state = cached
            return@LaunchedEffect
        }
        state = Cache(
            absolutePath = entry.file.absolutePath,
            aspectRatio = null,
            dimensions = null,
            title = null,
            metaData = emptyMap(),
            duration = null,
            images = emptyList()
        )
        val currentFile = entry.file
        job = CoroutineScope(Dispatchers.IO).launch {
            // Using FFmpegFrameGrabber is the easiest. However, I noticed on Feuerwehrmann Sam MKV files
            // that the image is squashed because of PAR (pixel aspect ratio), so I use ffprobe to get the
            // display aspect ratio and also the correct duration, and not an approximate one from FFmpegFrameGrabber
            try {
                val s = process(
                    "ffprobe",
                    "-v",
                    "error",
                    "-sexagesimal",
                    "-select_streams",
                    "v:0",
                    "-show_entries",
                    "format=duration:stream=display_aspect_ratio",
                    "-of",
                    "json=c=1",
                    currentFile.absolutePath,
                    stdout = Redirect.CAPTURE,
                    stderr = Redirect.SILENT,
                )
                ensureActive()
                val videoData = json.decodeFromString<FfmpegJson>(s.output.joinToString(""))
                val hasAspectRatio = videoData.streams[0].displayAspectRatio != null
                val newAspectRatio = if (hasAspectRatio) {
                    parseAspectRatio(videoData.streams[0].displayAspectRatio!!)
                } else {
                    null
                }
                val newDuration = videoData.format.duration.substringBeforeLast(".").removePrefix("0:")
                ensureActive()
                state = state!!.copy(
                    aspectRatio = newAspectRatio,
                    duration = newDuration
                )
                val nextImages = mutableListOf<ImageBitmap>()
                var tmpMetaData = mapOf<String, String>()
                // silence excessive logging from ffmpeg
                avutil.av_log_set_level(avutil.AV_LOG_QUIET)
                val frameGrabber = FFmpegFrameGrabber(currentFile)
                frameGrabber.start()
                val totalFrames = frameGrabber.lengthInVideoFrames
                var readMetadata = false
                listOf(0.05, 0.2, 0.35, 0.5, 0.65, 0.8, 0.9, 0.95).forEach {
                    ensureActive()
                    frameGrabber.setVideoFrameNumber((totalFrames * it).toInt())
                    val frame = frameGrabber.grabImage()
                    val width = frame.imageWidth
                    val height = frame.imageHeight
                    val converter = Java2DFrameConverter()
                    val convertedFrame: BufferedImage = converter.convert(frame)
                    ensureActive()
                    if (newAspectRatio == null || width.toDouble() / height == newAspectRatio) {
                        nextImages.add(convertedFrame.toComposeImageBitmap())
                        state = state!!.copy(
                            dimensions = "${width}x$height"
                        )
                    } else {
                        val correctedWidth = (height * newAspectRatio).toInt()
                        val adjustedImage = convertedFrame.getScaledInstance(correctedWidth, height, Image.SCALE_SMOOTH)
                        state = state!!.copy(
                            dimensions = "${correctedWidth}x$height"
                        )
                        ensureActive()
                        nextImages.add(toBufferedImage(adjustedImage).toComposeImageBitmap())
                    }
                    ensureActive()
                    if (!readMetadata) {
                        tmpMetaData = frameGrabber.metadata
                        readMetadata = true
                    }
                }
                ensureActive()
                frameGrabber.stop()
                state = state!!.copy(
                    metaData = tmpMetaData,
                    title = tmpMetaData["title"],
                    images = nextImages
                )
                videoCache.add(state!!)
            } catch (_: CancellationException) {
            }
        }
    }
}
