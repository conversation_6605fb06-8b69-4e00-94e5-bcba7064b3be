package dima.apps.dired.preview

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.apps.dired.*

@Composable
internal fun DiredDirectoryPreview(
    entry: Dired.Entry,
    listState: LazyListState
) {
    val entries = Dired.getEntries(
        path = entry.file.absolutePath,
        sort = DiredSort.Name,
        fileListSearchQuery = ""
    )
    val lastSelectedFile = DiredDatabase.getLastSelectedFileInDirectory(entry.file)

    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxWidth()
            .padding(end = if (listState.canScrollForward || listState.canScrollBackward) 12.dp else 0.dp)
    ) {
        items(entries) { fileEntry ->
            DiredEntryRow(
                entry = fileEntry,
                isSelected = fileEntry.file.absolutePath == lastSelectedFile,
                isMarked = false,
                isActive = true,
                useGraySelectedColor = true,
                showSize = false,
                showLastModified = false,
                query = "",
                hasAtLeastOneFileWithThumbnailPreview = entries.hasAnyFileWithThumbnails(),
                onClick = {}
            )
        }
    }
}
