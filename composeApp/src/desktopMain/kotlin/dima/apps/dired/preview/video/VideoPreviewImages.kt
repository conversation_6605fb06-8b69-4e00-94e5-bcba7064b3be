package dima.apps.dired.preview.video

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors

@Composable
internal fun VideoPreviewImages(images: List<ImageBitmap>) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        var i = 0
        val rounding = 5.dp
        images.chunked(2).forEach { imagePair ->
            Row {
                imagePair.forEach { image ->
                    Image(
                        painter = BitmapPainter(image),
                        contentDescription = null,
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier
                            .weight(0.5f)
                            .then(
                                when (i) {
                                    0 -> {
                                        Modifier.clip(RoundedCornerShape(topStart = rounding))
                                    }

                                    1 -> {
                                        Modifier.clip(RoundedCornerShape(topEnd = rounding))
                                    }

                                    images.size - 2 -> {
                                        Modifier.clip(RoundedCornerShape(bottomStart = rounding))
                                    }

                                    images.size - 1 -> {
                                        Modifier.clip(RoundedCornerShape(bottomEnd = rounding))
                                    }

                                    else -> {
                                        Modifier
                                    }
                                }
                            )
                            .background(TailwindCssColors.red600),
                    )
                    i++
                }
            }
        }
    }
}
