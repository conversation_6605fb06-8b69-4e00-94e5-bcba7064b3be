package dima.apps.dired.preview

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.apps.dired.Dired
import dima.apps.dired.FileData
import dima.utils.Result
import dima.utils.readTextUpTo
import dima.utils.toNiceHumanByteString

@Composable
internal fun DiredArchivePreview(entry: Dired.Entry, scrollState: ScrollState) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(scrollState)
    ) {
        Text(
            "Hit Enter to decompress",
            color = GlobalStyling.getTextColor(),
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 30.dp)
        )
        val width = 90.dp
        FileData("File size", entry.fileSizeBytes.toNiceHumanByteString(), width = width)
        Spacer(modifier = Modifier.height(12.dp))
        val content = entry.file.readTextUpTo()
        when (content) {
            is Result.Error -> FileData("Error", content.error, width = width)
            is Result.Success -> FileData("Content", content.value.text, width = width)
        }
    }
}
