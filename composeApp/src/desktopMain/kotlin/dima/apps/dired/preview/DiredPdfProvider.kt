package dima.apps.dired.preview

import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.toComposeImageBitmap
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.dired.Dired
import dima.apps.notifications.showErrorNotification
import dima.settings
import dima.utils.Ring
import dima.utils.abbreviatePath
import dima.utils.sha256Hash
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import javax.imageio.ImageIO
import kotlin.coroutines.cancellation.CancellationException

internal data class PdfPreviewData(
    val requestedPath: String? = null,
    val images: List<ImageBitmap> = emptyList(),
    val pageCount: Int? = null,
    val error: String? = null,
    val isLoading: Boolean = false
)

private data class CachedPdfPreview(
    val path: String, // Full path of the PDF
    val images: List<ImageBitmap>,
    val pageCount: Int?,
    val error: String? = null // Cache errors too
)

internal object DiredPdfProvider {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeJobs = mutableMapOf<String, Job>()
    private val pdfPreviewCache = Ring<CachedPdfPreview>(10) // Cache for loaded PDF previews

    private val _pdfStateFlow = MutableStateFlow(PdfPreviewData())
    val pdfStateFlow: StateFlow<PdfPreviewData> = _pdfStateFlow.asStateFlow()

    fun loadPdf(entry: Dired.Entry) {
        val path = entry.file.absolutePath

        // If UI is already showing loading for this exact path, do nothing.
        if (_pdfStateFlow.value.requestedPath == path && _pdfStateFlow.value.isLoading) {
            return
        }

        // Check cache
        val cached = pdfPreviewCache.find { it.path == path }
        if (cached != null) {
            activeJobs[path]?.cancel("Found in PDF Provider cache") // Cancel any active job for this path
            activeJobs.remove(path)
            _pdfStateFlow.value = PdfPreviewData(
                requestedPath = path,
                images = cached.images,
                pageCount = cached.pageCount,
                error = cached.error,
                isLoading = false
            )
            return
        }

        // Cancel all other active jobs if a new path is requested
        activeJobs.filter { it.key != path }.forEach { (_, oldJob) ->
            oldJob.cancel("New PDF request for a different path")
        }
        activeJobs.entries.removeIf { it.key != path && !it.value.isActive } // Clean up


        // If a job for *this* path is already running (e.g., if loadPdf is called again before previous job for same path finished),
        // we don't need to start a new one. Just ensure the UI reflects loading.
        if (activeJobs[path]?.isActive == true) {
            if (!_pdfStateFlow.value.isLoading || _pdfStateFlow.value.requestedPath != path) {
                _pdfStateFlow.value = PdfPreviewData(requestedPath = path, isLoading = true)
            }
            return
        }


        _pdfStateFlow.value = PdfPreviewData(requestedPath = path, isLoading = true)
        val currentEntryFileCopy = File(entry.file.absolutePath) // Use a copy for the coroutine

        val job = scope.launch {
            var jobImages: List<ImageBitmap> = emptyList()
            var jobPageCount: Int? = null
            var jobError: String? = null

            try {
                ensureActive()

                if (!currentEntryFileCopy.exists()) {
                    jobError = "File does not exist!"
                } else if (settings.cli.libpdfiumDylibDirectory == null) {
                    jobError = "libpdfium is not configured in settings"
                } else {
                    val hash = currentEntryFileCopy.sha256Hash()
                    val pageCountFile = File(DiredPdfPreview.cacheDirectory, "$hash.txt")

                    if (pageCountFile.exists()) {
                        val count = pageCountFile.readText().toIntOrNull()
                        if (count != null && count > 0) {
                            jobPageCount = count
                            jobImages = readPdfImagesFromFilesystem(hash, count)
                        } else {
                            // Invalid cache, try to regenerate
                            pageCountFile.delete() // Delete invalid cache entry
                            // Proceed to generate
                        }
                    }

                    // If not found in cache or cache was invalid, generate
                    if (jobPageCount == null) {
                        val p = process(
                            "pdf2png", currentEntryFileCopy.absolutePath,
                            "--library-directory", settings.cli.libpdfiumDylibDirectory.absolutePath,
                            "--prefix", hash,
                            "--output-directory", DiredPdfPreview.cacheDirectory.absolutePath,
                            stdout = Redirect.CAPTURE,
                            stderr = Redirect.CAPTURE,
                        )
                        ensureActive()
                        val output = p.output.joinToString("\n").trim()
                        if (p.resultCode != 0) {
                            jobError = "pdf2png failed with: $output"
                        } else {
                            val count = output.toIntOrNull()
                            if (count != null && count > 0) {
                                jobPageCount = count
                                pageCountFile.writeText(count.toString())
                                jobImages = readPdfImagesFromFilesystem(hash, count)
                            } else {
                                jobError = "pdf2png returned invalid page count or zero pages."
                            }
                        }
                    }
                }

                if (jobError == null && jobImages.isEmpty()) {
                    jobError =
                        "Failed to load/read preview images from filesystem cache for '${currentEntryFileCopy.name}'."
                }

            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                ensureActive()
                jobError = "Error loading PDF preview for '${currentEntryFileCopy.name}': ${e.message}"
            } finally {
                activeJobs.remove(path)
            }

            if (isActive) {
                // Only update if this job corresponds to the currently requested path AND the UI is still waiting for it
                if (_pdfStateFlow.value.requestedPath == path && _pdfStateFlow.value.isLoading) {
                    val newState = PdfPreviewData(
                        requestedPath = path,
                        images = jobImages,
                        pageCount = jobPageCount,
                        error = jobError,
                        isLoading = false
                    )
                    _pdfStateFlow.value = newState

                    // Add to cache if successful
                    if (jobError == null && jobImages.isNotEmpty()) {
                        pdfPreviewCache.add(CachedPdfPreview(path, jobImages, jobPageCount))
                    } else if (jobError != null) { // Cache errors too, to prevent re-trying immediately
                        pdfPreviewCache.add(CachedPdfPreview(path, emptyList(), null, jobError))
                    }
                }
            }
        }
        activeJobs[path] = job
    }

    private fun readPdfImagesFromFilesystem(hash: String, count: Int): List<ImageBitmap> {
        val newList = mutableListOf<ImageBitmap>()
        for (i in 0 until count) {
            val file = File(DiredPdfPreview.cacheDirectory, "$hash-$i.png")
            try {
                // Ensure file exists before attempting to read
                if (file.exists()) {
                    ImageIO.read(file)?.let { bufferedImage ->
                        newList.add(bufferedImage.toComposeImageBitmap())
                    } ?: run {
                        // Log or handle if ImageIO.read returns null
                        // This can happen for corrupted or unsupported image files
                        showErrorNotification("Failed to read image (null) for PDF preview", file.abbreviatePath())
                    }
                } else {
                    // Log or handle if a specific page image file is missing
                    showErrorNotification("Missing image file for PDF preview page", file.abbreviatePath())
                }
            } catch (e: Exception) {
                showErrorNotification("Failed reading image for PDF preview", "${file.abbreviatePath()}: ${e.message}")
            }
        }
        return newList.toList()
    }

    fun resetCache() {
        activeJobs.values.forEach { it.cancel("PDF Provider Cache Reset") }
        activeJobs.clear()
        pdfPreviewCache.clear()
        _pdfStateFlow.value = PdfPreviewData() // Reset to initial state
        Dired.reload = !Dired.reload
    }
}
