package dima.apps.dired.preview

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dima.apps.dired.Dired
import dima.color.TailwindCssColors
import dima.markdownRendererLibraryPatches.MyMarkdown
import dima.markdownRendererLibraryPatches.myMarkdownComponents
import dima.treeSitter.IntellijDarculaTheme
import dima.treeSitter.TreeSitterLanguage
import dima.treeSitter.TreeSitterText
import dima.utils.DiredTruncatedFileNote
import dima.utils.Result
import dima.utils.readTextUpTo
import org.intellij.markdown.MarkdownTokenTypes
import org.intellij.markdown.ast.ASTNode
import org.intellij.markdown.ast.findChildOfType
import org.intellij.markdown.ast.getTextInNode

/**
 * Note that the parameters to this are not reactive.
 */
@Composable
private fun TreeSitterMarkdownText(content: String, node: ASTNode) {
    var text by remember { mutableStateOf<String?>(null) }
    var language by remember { mutableStateOf<TreeSitterLanguage?>(null) }

    if (text != null) {
        TreeSitterText(
            text!!, language,
            modifier = Modifier
                .padding(vertical = 8.dp)
                .background(IntellijDarculaTheme.background, RoundedCornerShape(6.dp))
                .padding(8.dp)
                .fillMaxWidth()
        )
    }

    LaunchedEffect(node, content) { // React to node or content changes
        // copied from MarkdownCodeFence()
        val languageInMarkdown = node.findChildOfType(MarkdownTokenTypes.FENCE_LANG)?.getTextInNode(content)?.toString()
        if (node.children.size >= 3) {
            val start = node.children[2].startOffset
            val end = node.children[(node.children.size - 2).coerceAtLeast(2)].endOffset
            text = content.subSequence(start, end).toString().replaceIndent()
        } else {
            // invalid code block, skipping
            text = null
            return@LaunchedEffect
        }
        if (languageInMarkdown == null) {
            language = null
            return@LaunchedEffect
        }
        language = TreeSitterLanguage.getForMarkdown(languageInMarkdown)
    }
}

@Composable
internal fun DiredMarkdownPreview(entry: Dired.Entry, scrollState: ScrollState) {
    val content = entry.file.readTextUpTo(showErrorNotification = false)

    Column(
        modifier = Modifier
            .padding(12.dp)
            .verticalScroll(scrollState)
    ) {
        when (content) {
            is Result.Error -> {
                Text(
                    "Failed to read file: ${content.error}",
                    color = GlobalStyling.getRedTextColor(),
                    modifier = Modifier
                        .padding(bottom = 20.dp)
                )
            }

            is Result.Success -> {
                if (content.value.text == "") {
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        Text(
                            "no content",
                            textAlign = TextAlign.Center,
                            color = GlobalStyling.getTextColor(),
                            modifier = Modifier
                                .padding(top = 30.dp)
                                .background(TailwindCssColors.orange700, RoundedCornerShape(5.dp))
                                .padding(10.dp)
                        )
                    }
                } else {
                    SelectionContainer {
                        MyMarkdown(
                            content.value.text,
                            components = myMarkdownComponents(
                                codeFence = {
                                    TreeSitterMarkdownText(it.content, it.node)
                                },
                            ),
                            modifier = Modifier
                                .padding(end = 16.dp, bottom = 4.dp)
                                .fillMaxWidth()
                        )
                    }
                }
                if (content.value.isTruncated) {
                    DiredTruncatedFileNote(useDarkTextColor = false)
                }
            }
        }
    }
}
