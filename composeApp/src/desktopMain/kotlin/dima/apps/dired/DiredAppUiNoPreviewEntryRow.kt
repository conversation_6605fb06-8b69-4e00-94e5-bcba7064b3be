package dima.apps.dired

import Globals
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.globalState.DiredFileRowThumbnailMode
import dima.globalState.GlobalState
import dima.text.TextMarked
import dima.utils.toNiceHumanByteString

@Composable
internal fun DiredAppUiNoPreviewEntryRow(
    entry: Dired.Entry,
    isMarked: Boolean,
    color: Color,      // Pre-calculated text color
    showSize: Boolean,
    showLastModified: Boolean,
    hasAtLeastOneFileWithThumbnailPreview: Boolean,
    query: String,
    modifier: Modifier = Modifier // Modifier passed from DiredEntryRow
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier // Apply the pre-configured modifier
    ) {
        val iconSize = 16.dp
        val iconTint = if (GlobalState.isDarkMode) {
            entry.fileTypeWithColor.darkColor
        } else {
            entry.fileTypeWithColor.lightColor
        }

        if (isMarked) {
            if (entry.file.isDirectory) {
                MarkedCircle(fontSize = 14.sp, size = iconSize + 2.dp)
            } else {
                MarkedCircle(fontSize = 12.sp, size = iconSize, Modifier.padding(start = 2.dp, end = 2.dp))
            }
        } else if (entry.file.isDirectory) {
            val dirIconTint = if (GlobalState.isDarkMode) TailwindCssColors.gray400 else TailwindCssColors.gray600
            DiredDirectoryIcon(iconSize, dirIconTint)
        } else {
            DiredEntryFileIcon(iconSize, entry, iconTint, Globals.faBrandsFont)
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.then(
                if (hasAtLeastOneFileWithThumbnailPreview &&
                    GlobalState.dired.fileRowThumbnailMode == DiredFileRowThumbnailMode.ShowThumbnail &&
                    entry.fileTypeWithColor.type != FileType.Image && entry.fileTypeWithColor.type != FileType.Pdf
                ) {
                    Modifier.padding(start = 70.dp) // Align with items that DO have thumbnails (90dp - icon width - spacing)
                } else {
                    Modifier
                }
            )
        ) {
            if (showSize) {
                if (!entry.file.isDirectory) {
                    Text(
                        entry.fileSizeBytes.toNiceHumanByteString(),
                        color = color,
                        modifier = Modifier
                            .width(80.dp)
                            .padding(end = 12.dp)
                    )
                }
            } else if (showLastModified) {
                Text(
                    formatLastTimeModifiedForHuman(entry.lastModified),
                    color = color,
                    modifier = Modifier
                        .width(120.dp)
                        .padding(end = 12.dp)
                )
            }
            TextMarked(
                text = entry.file.name,
                searchQuery = query,
                color = color,
            )
        }
    }
}
