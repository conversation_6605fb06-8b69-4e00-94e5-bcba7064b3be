package dima.apps.dired

import GlobalStyling
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.globalState.DiredFileRowThumbnailMode
import dima.globalState.GlobalState
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.fileDragSource
import java.nio.file.Path
import java.nio.file.Paths

@OptIn(ExperimentalFoundationApi::class, ExperimentalComposeUiApi::class)
@Composable
fun DiredEntryRow(
    entry: Dired.Entry,
    hasAtLeastOneFileWithThumbnailPreview: Boolean,
    isSelected: <PERSON><PERSON><PERSON>,
    isMarked: Boolean,
    isActive: Boolean,
    query: String,
    useGraySelectedColor: Boolean = false,
    showSize: Boolean = false,
    showLastModified: Boolean = false,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val background = if (isSelected) {
        if (useGraySelectedColor) {
            if (GlobalState.isDarkMode) {
                TailwindCssColors.gray700
            } else {
                TailwindCssColors.blue100
            }
        } else if (isActive) {
            GlobalStyling.getSelectedBackgroundColor()
        } else {
            GlobalStyling.getSelectedInactiveBackgroundColor()
        }
    } else {
        if (isMarked) {
            GlobalStyling.getMarkedBackgroundColor()
        } else {
            GlobalStyling.getWindowBackgroundColor()
        }
    }

    val alpha = if (isSelected) {
        1.0f
    } else {
        if (isActive) {
            1.0f
        } else {
            GlobalStyling.DISABLED_ALPHA
        }
    }

    val color = GlobalStyling.getTextColor()

    val pathsToDrag: List<Path> =
        if (isMarked && GlobalState.dired.markedFiles.size > 1 && GlobalState.dired.markedFiles.contains(entry.file)) {
            GlobalState.dired.markedFiles.map { Paths.get(it.absolutePath) }
        } else {
            listOf(Paths.get(entry.file.absolutePath))
        }

    val useFixedHeightThumbnailBox =
        GlobalState.dired.fileRowThumbnailMode == DiredFileRowThumbnailMode.ShowThumbnail &&
                (entry.fileTypeWithColor.type == FileType.Image || entry.fileTypeWithColor.type == FileType.Pdf)

    val rowModifier = modifier
        .fillMaxWidth()
        .clickableWithoutBackgroundRipple(onClick = onClick)
        .alpha(alpha)
        .background(background, GlobalStyling.smallRoundedCorners)
        .padding(horizontal = 8.dp, vertical = 6.dp)
        .fileDragSource(pathsToDrag)

    when {
        useFixedHeightThumbnailBox && entry.fileTypeWithColor.type == FileType.Image -> {
            DiredAppUiImageEntryRow(
                entry = entry,
                isMarked = isMarked,
                color = color,
                showSize = showSize,
                showLastModified = showLastModified,
                query = query,
                modifier = rowModifier
            )
        }

        useFixedHeightThumbnailBox && entry.fileTypeWithColor.type == FileType.Pdf -> {
            DiredAppUiPdfEntryRow(
                entry = entry,
                isMarked = isMarked,
                color = color,
                showSize = showSize,
                showLastModified = showLastModified,
                query = query,
                modifier = rowModifier
            )
        }

        else -> {
            DiredAppUiNoPreviewEntryRow(
                entry = entry,
                isMarked = isMarked,
                color = color,
                showSize = showSize,
                showLastModified = showLastModified,
                hasAtLeastOneFileWithThumbnailPreview = hasAtLeastOneFileWithThumbnailPreview,
                query = query,
                modifier = rowModifier
            )
        }
    }
}
