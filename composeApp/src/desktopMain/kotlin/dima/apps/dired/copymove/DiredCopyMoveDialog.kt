package dima.apps.dired.copymove

import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import dima.apps.dired.Dired
import dima.apps.dired.DiredSort
import dima.clipboard.readClipboard
import dima.dialogs.closeDialogWithId
import dima.utils.ensureSuffix
import dima.utils.abbreviatePath
import java.io.File

@Composable
fun DiredCopyMoveDialog(id: Long, data: Any?) {
    val params = data as DiredCopyMoveDialogParams
    var targetPathTfv by remember { mutableStateOf(getInitialTextFieldValue(params)) }
    var currentDisplayPath by remember { mutableStateOf(File(params.initialTargetPath).absolutePath.ensureSuffix("/")) }
    var destinationFiles by remember { mutableStateOf<List<Dired.Entry>>(emptyList()) }
    var selectedDestinationFileIndex by remember { mutableStateOf<Int?>(null) }
    val listState = rememberLazyListState()
    val previewScrollState = rememberScrollState()
    val lazyListState = rememberLazyListState()
    val inputFocusRequester = remember { FocusRequester() }
    val listFocusRequester = remember { FocusRequester() }
    var isListFocused by remember { mutableStateOf(false) }

    fun updateDestinationFiles(path: String) {
        val dir = File(path)
        if (dir.exists() && dir.isDirectory) {
            val newDisplayPath = dir.absolutePath.ensureSuffix("/")
            if (newDisplayPath != currentDisplayPath || destinationFiles.isEmpty()) {
                currentDisplayPath = newDisplayPath
                val all = Dired.getEntries(dir.absolutePath, DiredSort.Name, "")
                destinationFiles = if (params.sourceFiles.size > 1) all.filter { it.file.isDirectory } else all
                selectedDestinationFileIndex = if (destinationFiles.isNotEmpty()) 0 else null
            }
        } else {
            // If path is not an existing directory, clear the list
            // This might need refinement based on how partial paths should behave
            if (currentDisplayPath.isNotEmpty()) { // Avoid clearing if it was already empty
                destinationFiles = emptyList()
                selectedDestinationFileIndex = null
                currentDisplayPath = "" // Reset display path if it's no longer valid
            }
        }
    }

    fun navigateIntoSelectedDirectory() {
        selectedDestinationFileIndex?.let { index ->
            destinationFiles.getOrNull(index)?.let { entry ->
                if (entry.file.isDirectory) {
                    val newPath = entry.file.absolutePath.ensureSuffix("/")
                    targetPathTfv = TextFieldValue(newPath, TextRange(newPath.length))
                    updateDestinationFiles(newPath)
                }
            }
        }
    }

    fun navigateUpDirectory() {
        val currentDir = File(currentDisplayPath.removeSuffix("/"))
        currentDir.parentFile?.let { parentDir ->
            val newPath = parentDir.absolutePath.ensureSuffix("/")
            targetPathTfv = TextFieldValue(newPath, TextRange(newPath.length))
            updateDestinationFiles(newPath)
        }
    }

    val onPreviewKeyEvent: (KeyEvent) -> Boolean = { event ->
        when (event.type) {
            KeyEventType.KeyUp -> {
                when (event.key) {
                    // use KeyUp to not falsely hit Enter in the confirmation dialog on file conflicts
                    Key.Enter -> {
                        if (isListFocused) { // Enter in List
                            selectedDestinationFileIndex?.let {
                                if (destinationFiles.getOrNull(it)?.file?.isDirectory == true) {
                                    navigateIntoSelectedDirectory()
                                } else {
                                    performOperation(targetPathTfv, params, id)
                                }
                            } ?: performOperation(targetPathTfv, params, id)
                        } else { // Enter in TextField
                            performOperation(targetPathTfv, params, id)
                        }
                        true
                    }

                    else -> false
                }
            }

            KeyEventType.KeyDown -> {
                when (event.key) {
                    Key.Escape -> {
                        closeDialogWithId(id)
                        true
                    }

                    Key.DirectionDown -> {
                        if (!isListFocused) {
                            if (destinationFiles.isNotEmpty()) {
                                isListFocused = true
                                selectedDestinationFileIndex = 0
                                listFocusRequester.requestFocus()
                            }
                        } else {
                            selectedDestinationFileIndex =
                                selectedDestinationFileIndex?.let { (it + 1).coerceAtMost(destinationFiles.size - 1) }
                                    ?: 0
                        }
                        true
                    }

                    Key.DirectionUp -> {
                        if (isListFocused) {
                            if (selectedDestinationFileIndex == 0 || selectedDestinationFileIndex == null) {
                                // If we are at the top or no selection, jump to parent directory (like Backspace) and return focus to input
                                navigateUpDirectory()
                                isListFocused = false
                                inputFocusRequester.requestFocus()
                                selectedDestinationFileIndex = null
                            } else {
                                selectedDestinationFileIndex = (selectedDestinationFileIndex!! - 1).coerceAtLeast(0)
                            }
                        }
                        true
                    }

                    Key.DirectionLeft -> {
                        if (isListFocused) {
                            // Navigate up directory when list is focused, similar to Backspace behavior
                            navigateUpDirectory()
                            isListFocused = false
                            inputFocusRequester.requestFocus()
                            true
                        } else {
                            false
                        }
                    }

                    Key.Backspace -> {
                        if (!isListFocused && targetPathTfv.selection.start == 0 && targetPathTfv.selection.end == 0) {
                            // If cursor is at the beginning of the TextField and Backspace is pressed
                            navigateUpDirectory()
                            isListFocused = false // Ensure focus returns to TextField if it was on list
                            inputFocusRequester.requestFocus()
                            true
                        } else if (isListFocused) { // If list is focused, Backspace always navigates up
                            navigateUpDirectory()
                            isListFocused = false
                            inputFocusRequester.requestFocus()
                            true
                        } else {
                            false // Let TextField handle Backspace otherwise
                        }
                    }



                    Key.Tab -> {
                        if (isListFocused) {
                            selectedDestinationFileIndex?.let { index ->
                                destinationFiles.getOrNull(index)?.let { entry ->
                                    val newPath =
                                        entry.file.absolutePath.ensureSuffix(if (entry.file.isDirectory) "/" else "")
                                    val displayPath = newPath.abbreviatePath()
                                    targetPathTfv = TextFieldValue(displayPath, selection = TextRange(displayPath.length))
                                    isListFocused = false
                                    inputFocusRequester.requestFocus()
                                    if (entry.file.isDirectory) {
                                        updateDestinationFiles(newPath)
                                    }
                                }
                            }
                        } else {
                            var targetFile: File? = null
                            if (destinationFiles.isNotEmpty()) {
                                if (selectedDestinationFileIndex != null && selectedDestinationFileIndex!! < destinationFiles.size - 1) {
                                    targetFile = destinationFiles[selectedDestinationFileIndex!! + 1].file
                                } else if (selectedDestinationFileIndex == null) {
                                    targetFile = destinationFiles.first().file
                                }
                            }
                            targetFile?.let {
                                val newPath = it.absolutePath.ensureSuffix(if (it.isDirectory) "/" else "")
                                val displayPath = newPath.abbreviatePath()
                                targetPathTfv = TextFieldValue(displayPath, selection = TextRange(displayPath.length))
                                if (it.isDirectory) {
                                    updateDestinationFiles(newPath)
                                }
                            }
                        }
                        true
                    }

                    Key.DirectionRight -> {
                        if (isListFocused) {
                            selectedDestinationFileIndex?.let { index ->
                                destinationFiles.getOrNull(index)?.let { entry ->
                                    if (entry.file.isDirectory) {
                                        val newPath = entry.file.absolutePath.ensureSuffix("/")
                                        val displayPath = newPath.abbreviatePath()
                                        targetPathTfv = TextFieldValue(displayPath, selection = TextRange(displayPath.length))
                                        isListFocused = false
                                        inputFocusRequester.requestFocus()
                                        updateDestinationFiles(newPath)
                                        true
                                    } else {
                                        false
                                    }
                                }
                            } ?: false
                        } else {
                            // When TextField is focused, behave like Tab: choose next candidate if needed, and enter if directory
                            var targetFile: File? = null
                            if (destinationFiles.isNotEmpty()) {
                                targetFile = if (selectedDestinationFileIndex != null && selectedDestinationFileIndex!! < destinationFiles.size - 1) {
                                    destinationFiles[selectedDestinationFileIndex!! + 1].file
                                } else if (selectedDestinationFileIndex == null) {
                                    destinationFiles.first().file
                                } else {
                                    destinationFiles[selectedDestinationFileIndex!!].file
                                }
                            }
                            targetFile?.let { f ->
                                if (f.isDirectory) {
                                    val newPath = f.absolutePath.ensureSuffix("/")
                                    val displayPath = newPath.abbreviatePath()
                                    targetPathTfv = TextFieldValue(displayPath, selection = TextRange(displayPath.length))
                                    updateDestinationFiles(newPath)
                                } else {
                                    val newPath = f.absolutePath
                                    val displayPath = newPath.abbreviatePath()
                                    targetPathTfv = TextFieldValue(displayPath, selection = TextRange(displayPath.length))
                                }
                                true
                            } ?: false
                        }
                    }

                    Key.V -> if (event.isMetaPressed && !isListFocused) {
                        val clipboardText = readClipboard()
                        if (clipboardText != null) {
                            val currentText = targetPathTfv.text
                            val selection = targetPathTfv.selection
                            val newText = currentText.replaceRange(selection.min, selection.max, clipboardText)
                            targetPathTfv = TextFieldValue(newText, TextRange(selection.min + clipboardText.length))

                            val expandedForList = dima.apps.dired.findFileDialog.expandPath(newText)
                            val fileInput = File(expandedForList)
                            val dirToList: File? = when {
                                fileInput.isDirectory && fileInput.exists() -> fileInput
                                fileInput.parentFile?.isDirectory == true && fileInput.parentFile?.exists() == true -> fileInput.parentFile
                                else -> null
                            }
                            dirToList?.let { updateDestinationFiles(it.absolutePath) }
                                ?: run { destinationFiles = emptyList(); selectedDestinationFileIndex = null }
                        }
                        true
                    } else false

                    else -> false
                }
            }

            else -> false
        }
    }

    DiredCopyMoveDialogUi(
        id = id,
        params = params,
        targetPathTfv = targetPathTfv,
        onTargetPathChange = { textFieldValue ->
            targetPathTfv = textFieldValue
            val pathInput = textFieldValue.text
            val expandedPath = dima.apps.dired.findFileDialog.expandPath(pathInput)
            val fileInput = File(expandedPath)

            val dirToList: File? = when {
                fileInput.isDirectory && fileInput.exists() -> fileInput
                fileInput.parentFile?.isDirectory == true && fileInput.parentFile?.exists() == true -> fileInput.parentFile
                else -> {
                    var parent = fileInput
                    while (!parent.exists()) {
                        parent = parent.parentFile
                    }
                    if (parent.isDirectory && parent.exists()) {
                        parent
                    } else {
                        null
                    }
                }
            }
            // Always list the nearest existing directory (parent if input is a file or non-existent)
            if (dirToList != null) {
                if (dirToList.absolutePath.ensureSuffix("/") != currentDisplayPath) {
                    updateDestinationFiles(dirToList.absolutePath)
                }
            }
        },
        destinationFiles = destinationFiles,
        selectedDestinationFileIndex = selectedDestinationFileIndex,
        onDestinationFileClick = { index ->
            selectedDestinationFileIndex = index
            isListFocused = true
            listFocusRequester.requestFocus()
            destinationFiles.getOrNull(index)?.let { entry ->
                if (entry.file.isDirectory) {
                    navigateIntoSelectedDirectory()
                }
            }
        },
        listState = listState,
        previewScrollState = previewScrollState,
        lazyListState = lazyListState,
        inputFocusRequester = inputFocusRequester,
        listFocusRequester = listFocusRequester,
        isListFocused = isListFocused,
        onPreviewKeyEvent = onPreviewKeyEvent,
        onJumpToParentDirectoryRequested = {
            val text = targetPathTfv.text
            val expanded = dima.apps.dired.findFileDialog.expandPath(text.removeSuffix("/"))
            val parent = File(expanded).parentFile
            if (parent != null && parent.exists()) {
                val newPathAbs = parent.absolutePath.ensureSuffix("/")
                val display = newPathAbs.abbreviatePath()
                targetPathTfv = TextFieldValue(display, TextRange(display.length))
                updateDestinationFiles(newPathAbs)
                isListFocused = false
                inputFocusRequester.requestFocus()
            }
        }
    )

    DiredCopyMoveDialogEffects(
        params = params,
        updateDestinationFiles = ::updateDestinationFiles,
        inputFocusRequester = inputFocusRequester,
        listFocusRequester = listFocusRequester,
        isListFocused = isListFocused,
        selectedDestinationFileIndex = selectedDestinationFileIndex,
        destinationFiles = destinationFiles,
        listState = listState,
        id = id
    )
}