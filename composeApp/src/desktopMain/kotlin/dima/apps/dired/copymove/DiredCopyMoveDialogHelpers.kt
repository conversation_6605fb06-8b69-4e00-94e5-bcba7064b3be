package dima.apps.dired.copymove

import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import dima.utils.ensureSuffix
import dima.utils.abbreviatePath

internal fun getInitialTextFieldValue(params: DiredCopyMoveDialogParams): TextFieldValue {
    // Always start with directory path only, no prefilled filename
    val initialText = params.initialTargetPath.ensureSuffix("/").abbreviatePath()
    return TextFieldValue(initialText, TextRange(initialText.length))
}
