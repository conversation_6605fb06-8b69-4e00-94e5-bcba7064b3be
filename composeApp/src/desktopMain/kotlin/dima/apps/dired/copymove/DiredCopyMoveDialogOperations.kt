package dima.apps.dired.copymove

import androidx.compose.ui.text.input.TextFieldValue
import dima.apps.dired.updateSelectedFileAfterMove
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dialogs.closeDialogWithId
import dima.dialogs.confirmation.openConfirmationDialog
import dima.utils.abbreviatePath
import java.io.File

internal fun performOperation(
    targetPathTfv: TextFieldValue,
    params: DiredCopyMoveDialogParams,
    id: Long
) {
    val targetName = dima.apps.dired.findFileDialog.expandPath(targetPathTfv.text.trim())
    if (targetName.isBlank()) {
        showErrorNotification("Target path cannot be empty.")
        return
    }

    val potentialTargets = mutableListOf<Pair<File, File>>() // Pair of <Source, ResolvedTarget>
    val conflicts = mutableListOf<File>()

    if (params.sourceFiles.size == 1) {
        val sourceFile = params.sourceFiles.first()
        val targetAsFile = File(targetName)
        val actualTargetFile: File

        if (!targetName.endsWith("/") && !targetAsFile.isDirectory) {
            actualTargetFile = targetAsFile
        } else {
            val actualTargetDir = if (targetAsFile.isDirectory && targetAsFile.exists()) targetAsFile else File(
                targetName.removeSuffix("/")
            )
            actualTargetFile = File(actualTargetDir, sourceFile.name)
        }
        potentialTargets.add(sourceFile to actualTargetFile)
        if (actualTargetFile.exists()) {
            conflicts.add(actualTargetFile)
        }
    } else { // Multiple source files
        val targetDir = File(targetName.removeSuffix("/"))
        params.sourceFiles.forEach { sourceFile ->
            val targetFileInDir = File(targetDir, sourceFile.name)
            potentialTargets.add(sourceFile to targetFileInDir)
            if (targetFileInDir.exists()) {
                conflicts.add(targetFileInDir)
            }
        }
    }

    fun executeFileSystemOperations() {
        val successfullyProcessedSourceFiles = mutableListOf<File>()
        var successCount = 0

        potentialTargets.forEach { (sourceFile, actualTargetFile) ->
            val actualTargetDir = actualTargetFile.parentFile ?: sourceFile.parentFile
            if (!actualTargetDir.exists()) {
                if (!actualTargetDir.mkdirs()) {
                    showErrorNotification("Failed to create target directory", actualTargetDir.absolutePath)
                    return@forEach // Skip this file
                }
            } else if (!actualTargetDir.isDirectory) {
                showErrorNotification("Target path is not a directory", actualTargetDir.absolutePath)
                return@forEach // Skip this file
            }

            try {
                when (params.operation) {
                    DiredCopyMoveOperation.COPY -> {
                        sourceFile.copyRecursively(actualTargetFile, overwrite = true)
                        successCount++
                        successfullyProcessedSourceFiles.add(sourceFile)
                    }

                    DiredCopyMoveOperation.MOVE -> {
                        if (sourceFile.absolutePath == actualTargetFile.absolutePath) { // Renaming in same dir
                            if (sourceFile.renameTo(actualTargetFile)) {
                                updateSelectedFileAfterMove(sourceFile, actualTargetFile)
                                successCount++
                                successfullyProcessedSourceFiles.add(sourceFile)
                            } else {
                                showErrorNotification("Failed to rename ${sourceFile.name} to ${actualTargetFile.name}")
                            }
                        } else { // Moving, possibly to different dir or renaming across dirs
                            sourceFile.copyRecursively(actualTargetFile, overwrite = true)
                            if (sourceFile.deleteRecursively()) {
                                updateSelectedFileAfterMove(sourceFile, actualTargetFile)
                                successCount++
                                successfullyProcessedSourceFiles.add(sourceFile)
                            } else {
                                showErrorNotification(
                                    "Failed to delete source after copy (move)",
                                    sourceFile.absolutePath
                                )
                                // Note: file was copied, but original not deleted.
                                // Should ideally handle this partial success (e.g., inform user, don't add to successfullyProcessedSourceFiles if strict)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                showErrorNotification(
                    "Error ${params.operation.name.lowercase()}ing ${sourceFile.name} to ${actualTargetFile.name}",
                    e.message
                )
            }
        }

        if (successCount > 0) {
            val opString = if (params.operation == DiredCopyMoveOperation.COPY) "Copied" else "Moved"
            val fileString =
                if (successCount == 1 && params.sourceFiles.size == 1) params.sourceFiles.first().name else "$successCount items"
            val targetDirDisplay = File(targetName.removeSuffix("/")).abbreviatePath()
            showNotification("$opString $fileString", "to $targetDirDisplay")
            params.onFilesChanged(successfullyProcessedSourceFiles)
        }
        closeDialogWithId(id)
    }

    if (conflicts.isEmpty()) {
        executeFileSystemOperations()
    } else {
        val operationName = when (params.operation) {
            DiredCopyMoveOperation.COPY -> "copy"
            DiredCopyMoveOperation.MOVE -> "move"
        }
        val conflictMessage =
            "The following ${if (conflicts.size == 1) "file" else "files"} already exist and will be overwritten:\n\n" +
                    conflicts.joinToString("\n") { "• ${it.name}" }
        openConfirmationDialog(
            title = "Confirm overriding $operationName ",
            subTitle = conflictMessage,
            confirmButtonText = "Overwrite"
        ) {
            executeFileSystemOperations()
        }
    }
}
