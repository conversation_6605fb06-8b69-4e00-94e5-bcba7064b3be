package dima.apps.dired.copymove

import dima.dialogs.DialogIdentifier
import dima.dialogs.openDialog
import java.io.File

internal data class DiredCopyMoveDialogParams(
    val sourceFiles: List<File>,
    val operation: DiredCopyMoveOperation,
    val initialTargetPath: String,
    val onFilesChanged: (List<File>) -> Unit
)

fun openDiredCopyMoveDialog(
    sourceFiles: List<File>,
    operation: DiredCopyMoveOperation,
    initialTargetPath: String,
    onFilesChanged: (List<File>) -> Unit
) {
    val params = DiredCopyMoveDialogParams(sourceFiles, operation, initialTargetPath, onFilesChanged)
    openDialog(DialogIdentifier.DiredCopyMove, params) { id, data ->
        DiredCopyMoveDialog(id, data)
    }
}
