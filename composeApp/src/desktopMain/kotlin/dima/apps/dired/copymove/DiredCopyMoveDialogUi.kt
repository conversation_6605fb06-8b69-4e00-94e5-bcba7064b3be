package dima.apps.dired.copymove

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.apps.dired.*
import dima.apps.dired.preview.DiredPreview
import dima.dialogs.DialogOverlay
import dima.globalState.DiredImageState
import dima.globalState.GlobalState
import dima.utils.SmartTextFieldCandidateCount
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import dima.utils.scrollbarStyleThemed

@Composable
internal fun DiredCopyMoveDialogUi(
    id: Long,
    params: DiredCopyMoveDialogParams,
    targetPathTfv: TextFieldValue,
    onTargetPathChange: (TextFieldValue) -> Unit,
    destinationFiles: List<Dired.Entry>,
    selectedDestinationFileIndex: Int?,
    onDestinationFileClick: (Int) -> Unit,
    listState: LazyListState,
    previewScrollState: ScrollState,
    lazyListState: LazyListState,
    inputFocusRequester: FocusRequester,
    listFocusRequester: FocusRequester,
    isListFocused: Boolean,
    onPreviewKeyEvent: (KeyEvent) -> Boolean,
    onJumpToParentDirectoryRequested: () -> Unit
) {
    DialogOverlay(
        dialogId = id,
        widthFraction = 0.8f,
        onPreviewKeyEvent = onPreviewKeyEvent
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .fillMaxSize()
        ) {
            TextFieldWithCandidateCountWithoutAnimation(
                value = targetPathTfv,
                onValueChange = onTargetPathChange,
                topLabel = if (params.sourceFiles.size == 1) {
                    when (params.operation) {
                        DiredCopyMoveOperation.COPY -> "Copy ${params.sourceFiles.first().name} to: "
                        DiredCopyMoveOperation.MOVE -> "Rename ${params.sourceFiles.first().name} to: "
                    }
                } else {
                    when (params.operation) {
                        DiredCopyMoveOperation.COPY -> "Copy ${params.sourceFiles.size} items to: "
                        DiredCopyMoveOperation.MOVE -> "Move ${params.sourceFiles.size} items to: "
                    }
                },
                singleLine = true,
                count = if (destinationFiles.isEmpty()) {
                    null
                } else {
                    SmartTextFieldCandidateCount(
                        currentCount = null,
                        maxCount = destinationFiles.size
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .focusRequester(inputFocusRequester)
                    .onFocusChanged { }
                    .onPreviewKeyEvent { event ->
                        when {
                            event.type == KeyEventType.KeyDown && event.key == Key.DirectionLeft -> {
                                val cursor = targetPathTfv.selection.start
                                val text = targetPathTfv.text
                                if (cursor > 0 && cursor <= text.length && text[cursor - 1] == '/') {
                                    onJumpToParentDirectoryRequested()
                                    true
                                } else false
                            }

                            else -> false
                        }
                    }
                    .padding(bottom = 8.dp)
            )

            Row(modifier = Modifier.weight(1f).fillMaxWidth()) {
                Box(modifier = Modifier.weight(0.5f).fillMaxHeight()) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .focusRequester(listFocusRequester)
                            .focusable(enabled = isListFocused)
                            .padding(end = 8.dp)
                    ) {
                        itemsIndexed(destinationFiles, key = { _, item -> item.file.absolutePath }) { index, entry ->
                            DiredEntryRow(
                                entry = entry,
                                isSelected = index == selectedDestinationFileIndex && isListFocused,
                                isMarked = false,
                                isActive = true,
                                showSize = GlobalState.dired.sort == DiredSort.Size,
                                showLastModified = GlobalState.dired.sort == DiredSort.LastModified,
                                hasAtLeastOneFileWithThumbnailPreview = destinationFiles.hasAnyFileWithThumbnails(),
                                query = targetPathTfv.text,
                                onClick = { onDestinationFileClick(index) }
                            )
                        }
                    }
                    VerticalScrollbar(
                        style = scrollbarStyleThemed(),
                        adapter = rememberScrollbarAdapter(scrollState = listState),
                        modifier = Modifier
                            .offset(x = 12.dp)
                            .align(Alignment.CenterEnd).fillMaxHeight().padding(end = 4.dp),
                    )
                }

                Spacer(Modifier.width(12.dp))

                Box(modifier = Modifier.weight(0.5f).fillMaxHeight()) {
                    val selectedEntry = selectedDestinationFileIndex?.let { destinationFiles.getOrNull(it) }
                    val effectiveSelectedEntry = selectedEntry?.let { entry ->
                        if (params.sourceFiles.size > 1 && !entry.file.isDirectory) null else entry
                    }
                    if (effectiveSelectedEntry != null) {
                        DiredPreview(
                            entry = effectiveSelectedEntry,
                            drawSelectedBorder = false,
                            rightPreviewScrollState = previewScrollState,
                            imageState = DiredImageState()
                        )
                        val scrollbarAdapter =
                            if (selectedEntry.file.isDirectory || selectedEntry.fileTypeWithColor.type == FileType.Pdf) {
                                rememberScrollbarAdapter(scrollState = lazyListState)
                            } else {
                                rememberScrollbarAdapter(scrollState = previewScrollState)
                            }
                        VerticalScrollbar(
                            style = scrollbarStyleThemed(),
                            adapter = scrollbarAdapter,
                            modifier = Modifier
                                .offset(x = 8.dp)
                                .align(Alignment.CenterEnd)
                                .fillMaxHeight()
                                .padding(end = 4.dp)
                        )
                    }
                }
            }
        }
    }
}