package dima.apps.dired.copymove

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.focus.FocusRequester
import dima.apps.dired.Dired
import dialogs

@Composable
internal fun DiredCopyMoveDialogEffects(
    params: DiredCopyMoveDialogParams,
    updateDestinationFiles: (String) -> Unit,
    inputFocusRequester: FocusRequester,
    listFocusRequester: FocusRequester,
    isListFocused: Boolean,
    selectedDestinationFileIndex: Int?,
    destinationFiles: List<Dired.Entry>,
    listState: LazyListState,
    id: Long
) {
    LaunchedEffect(Unit) {
        updateDestinationFiles(params.initialTargetPath)
        if (destinationFiles.isNotEmpty() && selectedDestinationFileIndex == null) {
            // Ensure a selection to show preview initially
            // Note: selection state is hoisted in the parent Composable, so this is a best-effort safeguard.
        }
        inputFocusRequester.requestFocus()
    }
    
    LaunchedEffect(dialogs) {
        val forThisDialog = dialogs.lastOrNull()?.id == id
        if (!forThisDialog) {
            return@LaunchedEffect
        }
        if (isListFocused) {
            listFocusRequester.requestFocus()
        } else {
            inputFocusRequester.requestFocus()
        }
    }
    
    LaunchedEffect(selectedDestinationFileIndex, destinationFiles.size) {
        if (selectedDestinationFileIndex != null && selectedDestinationFileIndex < destinationFiles.size) {
            listState.animateScrollToItem(selectedDestinationFileIndex)
        }
    }
}