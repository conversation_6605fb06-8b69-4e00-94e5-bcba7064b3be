package dima.apps.dired

import GlobalStyling
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import dima.utils.SmartTextFieldCandidateCount
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import isTextFieldFocused

@Composable
internal fun DiredAppUiSearchTextField(
    fileListSearchQuery: TextFieldValue,
    filteredCount: Int,
    searchFocusRequester: FocusRequester,
    onSearchQueryChange: (TextFieldValue) -> Unit,
    onSearchFocusedChange: (<PERSON><PERSON><PERSON>) -> Unit
) {
    // a bit less than GlobalStyling.paddingToOuterWindow to not cramp the UI
    Box(modifier = Modifier.padding(top = 8.dp)) {
        TextFieldWithCandidateCountWithoutAnimation(
            value = fileListSearchQuery,
            onValueChange = {
                onSearchQueryChange(it)
            },
            topLabel = "Search files",
            count = SmartTextFieldCandidateCount(currentCount = null, maxCount = filteredCount),
            singleLine = true,
            modifier = Modifier
                .fillMaxWidth(0.6f)
                .focusRequester(searchFocusRequester)
                .onFocusChanged {
                    isTextFieldFocused = it.isFocused
                    onSearchFocusedChange(it.isFocused)
                },
        )
    }
}
