package dima.apps.dired

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.os.homeWithoutSlash
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleThemed
import java.io.File

@Composable
internal fun RowScope.DiredAppUiLeftSideDirectory(
    leftScrollState: LazyListState,
    filtered: List<Dired.Entry>,
    goDirectoryBack: () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .padding(12.dp)
            .weight(0.5f)
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth(),
        ) {
            val suffixParts = buildList {
                if (GlobalState.dired.sort == DiredSort.LastModified) {
                    add("Sorted by last modified")
                } else if (GlobalState.dired.sort == DiredSort.Size) {
                    add("Sorted by size")
                }
                if (GlobalState.dired.showHiddenFiles) {
                    add("+hidden")
                }
            }
            val file = File(GlobalState.dired.directory)
            val textToDisplay = if (file.absolutePath == homeWithoutSlash) {
                homeWithoutSlash
            } else {
                file.absolutePath.replace(homeWithoutSlash, "~")
            }
            val pathClickableModifier = Modifier // No specific padding here, alignment is handled by Row and Box
                .clickableWithoutBackgroundRipple {
                    goDirectoryBack()
                }
            val textColor = GlobalStyling.getTextColor()

            Box(
                modifier = Modifier
                    .weight(
                        1f,
                        fill = false
                    ).padding(end = 8.dp)
            ) {
                if (suffixParts.isEmpty()) {
                    Text(
                        textToDisplay,
                        color = textColor,
                        modifier = pathClickableModifier
                    )
                } else {
                    Text(
                        buildAnnotatedString {
                            append("$textToDisplay    ") // Spaces for visual separation
                            append(
                                AnnotatedString(
                                    suffixParts.joinToString("    "),
                                    spanStyle = SpanStyle(
                                        fontWeight = FontWeight.SemiBold,
                                        color = GlobalStyling.getBlueColor()
                                    )
                                )
                            )
                        },
                        color = textColor,
                        modifier = pathClickableModifier
                    )
                }
            }
            val markedFilesCount = getMarkedFilesInCurrentDirectory().size
            if (markedFilesCount > 0) {
                Text(
                    text = "$markedFilesCount marked",
                    color = TailwindCssColors.white,
                    fontSize = 13.sp,
                    modifier = Modifier
                        .background(
                            GlobalStyling.getMarkedBackgroundColorForWhiteText(),
                            GlobalStyling.smallRoundedCorners
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
            } else {
                // blank string to avoid shifting the height
                Text(
                    text = "",
                    color = TailwindCssColors.transparent,
                    fontSize = 13.sp,
                    modifier = Modifier
                        .width(0.dp)
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
        }

        Box {
            LazyColumn(
                state = leftScrollState,
                modifier = Modifier.padding(start = 8.dp)
            ) {
                itemsIndexed(filtered, { _, entry -> entry.file.absolutePath }) { index, entry ->
                    val isMarked = GlobalState.dired.markedFiles.contains(entry.file)
                    val isSelected = GlobalState.dired.selectedFile == entry.file.absolutePath
                    DiredEntryRow(
                        entry = entry,
                        isSelected = isSelected,
                        isMarked = isMarked,
                        isActive = !GlobalState.dired.isPreviewActive,
                        showSize = GlobalState.dired.sort == DiredSort.Size,
                        showLastModified = GlobalState.dired.sort == DiredSort.LastModified,
                        hasAtLeastOneFileWithThumbnailPreview = filtered.hasAnyFileWithThumbnails(),
                        query = GlobalState.dired.search,
                        onClick = {
                            GlobalState.dired.setSelectedFile(entry.file.absolutePath)
                        }
                    )
                }
            }
            VerticalScrollbar(
                style = scrollbarStyleThemed(),
                adapter = rememberScrollbarAdapter(scrollState = leftScrollState),
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .offset(x = (-6).dp)
                    .fillMaxHeight(),
            )
        }
    }
}
