package dima.apps.dired

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.input.TextFieldValue
import dima.globalState.GlobalState
import dima.utils.DummyFocusable
import dima.utils.ScrollVelocity
import dima.utils.handleAppMap
import handleLostFocus
import isTextFieldFocused

/**
 * Note that the left scrollbar always has the same padding, regardless if the scrollbar is visible.
 * The row entries will otherwise flicker a tiny bit horizontally on directory change.
 */
@Composable
internal fun DiredAppUi(
    appFocusRequester: FocusRequester,
    showSearch: <PERSON><PERSON><PERSON>,
    fileListSearchQuery: TextFieldValue,
    onFileListSearchQueryChange: (TextFieldValue) -> Unit,
    previewScrollVelocity: ScrollVelocity,
    onShowSearchChange: (Boolean) -> Unit,
    heldKeys: MutableSet<Key>,
    heldCtrlKeys: MutableSet<Key>,
    searchFocusRequester: FocusRequester,
    leftScrollState: LazyListState,
    filtered: List<Dired.Entry>,
    rightPreviewScrollState: ScrollState,
    goDirectoryBack: () -> Unit,
    getEntry: () -> Dired.Entry?,
    onSearchQueryChange: (TextFieldValue) -> Unit,
    onSearchFocusedChange: (Boolean) -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    when {
                        it.key == Key.Escape && it.type == KeyEventType.KeyDown -> {
                            // Clear search and hide field
                            onFileListSearchQueryChange(TextFieldValue(""))
                            onShowSearchChange(false)
                            appFocusRequester.requestFocus()
                            return@onPreviewKeyEvent true
                        }

                        it.key == Key.Enter && it.type == KeyEventType.KeyDown -> {
                            appFocusRequester.requestFocus()
                            return@onPreviewKeyEvent true
                        }
                    }
                    return@onPreviewKeyEvent false
                }
                if (it.type == KeyEventType.KeyDown) {
                    heldKeys.add(it.key)
                    if (it.isCtrlPressed) {
                        heldCtrlKeys.add(it.key)
                    }
                } else if (it.type == KeyEventType.KeyUp) {
                    heldKeys.remove(it.key)
                    if (it.isCtrlPressed) { // Check if Ctrl was pressed during this KeyUp
                        heldCtrlKeys.remove(it.key)
                    }
                    if (it.key == Key.C || it.key == Key.T || it.key == Key.M || it.key == Key.V) {
                        previewScrollVelocity.onKeyReleased()
                    }
                }
                val blacklist = if (GlobalState.dired.isPreviewActive) {
                    val entry = getEntry()
                    if (entry != null && (entry.fileTypeWithColor.type == FileType.Image || entry.fileTypeWithColor.type == FileType.Pdf)) {
                        listOf(Key.C, Key.N, Key.H, Key.T, Key.M, Key.V)
                    } else {
                        emptyList()
                    }
                } else {
                    emptyList()
                }
                return@onPreviewKeyEvent it.handleAppMap(blacklist = blacklist)
            }
    ) {
        DummyFocusable()
        if (showSearch || fileListSearchQuery.text.isNotBlank()) {
            DiredAppUiSearchTextField(
                fileListSearchQuery = fileListSearchQuery,
                filteredCount = filtered.size,
                searchFocusRequester = searchFocusRequester,
                onSearchQueryChange = { onSearchQueryChange(it) },
                onSearchFocusedChange = { onSearchFocusedChange(it) }
            )
        }
        Row(modifier = Modifier.fillMaxSize()) {
            DiredAppUiLeftSideDirectory(leftScrollState, filtered, goDirectoryBack)
            DiredAppUiRightSidePreview(getEntry(), rightPreviewScrollState)
        }
    }
}