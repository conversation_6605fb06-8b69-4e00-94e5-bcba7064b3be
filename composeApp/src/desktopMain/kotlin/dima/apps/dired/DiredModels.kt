package dima.apps.dired

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector

enum class DiredSort {
    Name,
    Size,
    LastModified,
}

enum class FileType(val isTreeSitterLanguage: Boolean = false) {
    Audio,
    Image,
    Video,
    Pdf,
    Text,
    Markdown,
    Archive,

    Json(isTreeSitterLanguage = true),
    Rust(isTreeSitterLanguage = true),
    <PERSON><PERSON>(isTreeSitterLanguage = true),
    <PERSON>roovy(isTreeSitterLanguage = true),
    PHP(isTreeSitterLanguage = true),
    Python(isTreeSitterLanguage = true),
    Java(isTreeSitterLanguage = true),
    Bash(isTreeSitterLanguage = true),
    Dockerfile(isTreeSitterLanguage = true),
    HTML(isTreeSitterLanguage = true),
    CSS(isTreeSitterLanguage = true),
    JavaScript(isTreeSitterLanguage = true),
}

sealed class FileTypeWithColor(val type: FileType) {
    abstract val lightColor: Color
    abstract val darkColor: Color

    data class VectorIcon(
        val fileType: FileType,
        val icon: ImageVector,
        override val lightColor: Color,
        override val darkColor: Color
    ) : FileTypeWithColor(fileType)

    data class CharIcon(
        val fileType: FileType,
        /**
         * This can be a char like 'T' or something like: FontAwesome.Brands.Css3.
         */
        val iconChar: Char,
        override val lightColor: Color,
        override val darkColor: Color
    ) : FileTypeWithColor(fileType)
}
