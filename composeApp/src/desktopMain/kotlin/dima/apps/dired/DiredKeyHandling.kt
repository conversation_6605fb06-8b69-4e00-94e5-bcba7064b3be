package dima.apps.dired

import dima.apps.dired.copymove.DiredCopyMoveOperation
import dima.apps.dired.copymove.openDiredCopyMoveDialog
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.clipboard.readClipboard
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.os.Emacs
import dima.os.openUrl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.io.File

internal fun copyFiles(getEntry: () -> Dired.Entry?, onFilesChanged: (List<File>) -> Unit) {
    val markedFiles = getMarkedFilesInCurrentDirectory()
    val sourceFiles = markedFiles.ifEmpty {
        getEntry()?.file?.let { listOf(it) } ?: run {
            showErrorNotification("No file selected or marked to copy")
            return
        }
    }

    val initialDialogDir = if (sourceFiles.size == 1) {
        sourceFiles.first().parentFile?.absolutePath ?: GlobalState.dired.directory
    } else {
        GlobalState.dired.directory
    }

    openDiredCopyMoveDialog(
        sourceFiles = sourceFiles,
        operation = DiredCopyMoveOperation.COPY,
        initialTargetPath = initialDialogDir,
        onFilesChanged = onFilesChanged
    )
}

internal fun renameFiles(getEntry: () -> Dired.Entry?, onFilesChanged: (List<File>) -> Unit) {
    val markedFiles = getMarkedFilesInCurrentDirectory()
    val sourceFiles = markedFiles.ifEmpty {
        getEntry()?.file?.let { listOf(it) } ?: run {
            showErrorNotification("No file selected or marked to move/rename")
            return
        }
    }

    val initialDialogDir = if (sourceFiles.size == 1 && !sourceFiles.first().isDirectory) {
        // For single file rename, the dialog should list the parent directory's contents
        sourceFiles.first().parentFile?.absolutePath ?: GlobalState.dired.directory
    } else {
        // For moving directories or multiple files, suggest current Dired directory
        GlobalState.dired.directory
    }

    openDiredCopyMoveDialog(
        sourceFiles = sourceFiles,
        operation = DiredCopyMoveOperation.MOVE,
        initialTargetPath = initialDialogDir, // This is now the directory for the list
        onFilesChanged = onFilesChanged
    )
}


internal fun markOrUnmarkAllInCurrentDirectory(filtered: List<Dired.Entry>) {
    val currentDirectory = File(GlobalState.dired.directory)
    val markedFiles = GlobalState.dired.markedFiles
    val markedInCurrentDirectory = markedFiles.filter {
        it.parentFile == currentDirectory
    }
    if (markedInCurrentDirectory.isEmpty()) {
        // Mark all files in current directory
        val allFilesInDir = filtered.map { it.file }
        val newMarkedFiles = markedFiles + allFilesInDir
        GlobalState.dired = GlobalState.dired.copy(markedFiles = newMarkedFiles)
        val title = if (allFilesInDir.size == 1) {
            "Marked 1 path"
        } else {
            "Marked ${allFilesInDir.size} paths"
        }
        showNotification(title, durationMillis = 500L)
    } else {
        val filesToKeepMarked = markedFiles.filter {
            it.parentFile != currentDirectory
        }
        val count = markedFiles.size - filesToKeepMarked.size
        GlobalState.dired = GlobalState.dired.copy(markedFiles = filesToKeepMarked)
        val title = if (count == 1) {
            "Unmarked 1 path"
        } else {
            "Unmarked $count paths"
        }
        showNotification(title, durationMillis = 500L)
    }
}

internal fun unmarkAllFilesAcrossAllDirectories() {
    val all = GlobalState.dired.markedFiles
    if (all.isEmpty()) {
        showErrorNotification("There are no files to unmark", durationMillis = 500L)
        return
    }
    GlobalState.dired = GlobalState.dired.copy(markedFiles = emptyList())
    val title = if (all.size == 1) {
        "Unmarked 1 path across all directories"
    } else {
        "Unmarked ${all.size} paths across all directories"
    }
    showNotification(title, durationMillis = 500L)
}

internal fun toggleFullWidthPreview() {
    GlobalState.dired = GlobalState.dired.copy(
        isPreviewFullWidth = !GlobalState.dired.isPreviewFullWidth
    )
}

internal fun openWithDefaultMacOsApp(getEntry: () -> Dired.Entry?) {
    val entry = getEntry()
    if (entry == null) {
        showErrorNotification("No file selected open in macOS")
        return
    }
    openUrl(entry.file.absolutePath)
}

internal fun pasteFromCmdV() {
    val clipboard = readClipboard()
    if (clipboard == null) {
        showErrorNotification("Clipboard is empty")
        return
    }
    val file = File(clipboard)
    if (file.isAbsolute) {
        if (!file.exists()) {
            showErrorNotification("File does not exist", clipboard)
            return
        }
        if (file.isDirectory) {
            GlobalState.dired = GlobalState.dired.copy(
                directory = file.absolutePath,
                selectedFile = null
            )
        } else {
            file.openFileInDired()
        }
        showNotification("Opened file", file.name, durationMillis = 2000)
        return
    }
    val currentDir = File(GlobalState.dired.directory)
    val relativeFile = currentDir.resolve(clipboard)
    if (!relativeFile.exists()) {
        showErrorNotification("File does not exist", clipboard)
        return
    }
    if (relativeFile.isDirectory) {
        GlobalState.dired = GlobalState.dired.copy(
            directory = relativeFile.absolutePath,
            selectedFile = null
        )
    } else {
        relativeFile.openFileInDired()
    }
    showNotification("Opened file", file.name, durationMillis = 2000)
}

internal fun toggleDotFiles() {
    GlobalState.dired = GlobalState.dired.copy(showHiddenFiles = !GlobalState.dired.showHiddenFiles)
    if (GlobalState.dired.showHiddenFiles) {
        showNotification("Showing hidden files", durationMillis = 1000)
    } else {
        showNotification("Hiding hidden files", durationMillis = 1000)
    }
}

internal fun openInEmacs(entry: Dired.Entry?, coroutineScope: CoroutineScope) {
    if (entry == null) {
        showErrorNotification("No selected file to open")
        return
    }
    coroutineScope.launch {
        if (entry.file.isDirectory) {
            Emacs.openFileInDiredAndFocus(entry.file)
        } else {
            if (entry.fileTypeWithColor.type.isTreeSitterLanguage ||
                entry.fileTypeWithColor.type == FileType.Text ||
                entry.fileTypeWithColor.type == FileType.Markdown
            ) {
                Emacs.openFileContentAndFocus(entry.file)
            } else {
                Emacs.openFileInDiredAndFocus(entry.file)
            }
        }
    }
}

internal fun markFilesByExtension(filtered: List<Dired.Entry>) {
    openTextInputDialog("Mark files by extension") { extension ->
        val normalizedExtension = extension.removePrefix(".").lowercase()
        val filesToMark = filtered.filter { entry ->
            entry.file.extension.lowercase() == normalizedExtension
        }.map { it.file }
        if (filesToMark.isEmpty()) {
            showErrorNotification("No files found with extension: $extension")
        } else {
            val currentMarkedFiles = GlobalState.dired.markedFiles
            val updatedMarkedFiles = (currentMarkedFiles + filesToMark).distinct()
            GlobalState.dired = GlobalState.dired.copy(markedFiles = updatedMarkedFiles)
            showNotification(
                if (filesToMark.size == 1) {
                    "Marked 1 file with extension: $extension"
                } else {
                    "Marked ${filesToMark.size} files with extension: $extension"
                }
            )
        }
        TextInputDialogConfirmAction.Close
    }
}

