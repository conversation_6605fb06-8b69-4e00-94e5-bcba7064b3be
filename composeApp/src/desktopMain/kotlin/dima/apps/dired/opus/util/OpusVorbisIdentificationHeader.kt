package dima.apps.dired.opus.util

import dima.apps.dired.opus.OpusHeader
import org.jaudiotagger.audio.generic.Utils
import java.io.ByteArrayInputStream
import java.nio.ByteBuffer
import java.nio.charset.Charset
import java.util.logging.Logger

/**
 * - Magic signature: "OpusHead" (64 bits)
 * - Version number (8 bits unsigned): 0x01 for this spec
 * - Channel count 'c' (8 bits unsigned): MUST be > 0
 * - Pre-skip (16 bits unsigned, little endian)
 * - Input sample rate (32 bits unsigned, little endian): informational only
 * - Output gain (16 bits, little endian, signed Q7.8 in dB) to apply when
 * decoding
 * - Channel mapping family (8 bits unsigned)
 * --  0 = one stream: mono or L,R stereo
 * --  1 = channels in vorbis spec order: mono or L,R stereo or ... or FL,C,FR,RL,RR,LFE, ...
 * --  2..254 = reserved (treat as 255)
 * --  255 = no defined channel meaning
 * If channel mapping family > 0
 * - Stream count 'N' (8 bits unsigned): MUST be > 0
 * - Two-channel stream count 'M' (8 bits unsigned): MUST satisfy M <= N, M+N <= 255
 * - Channel mapping (8*c bits)
 * -- one stream index (8 bits unsigned) per channel (255 means silent throughout the file)
 */
class OpusVorbisIdentificationHeader(vorbisData: ByteArray) {
    var isValid: Boolean = false

    var vorbisVersion: Byte = 0
    var audioChannels: Byte = 0
    var preSkip: Short = 0
    var audioSampleRate: Int = 0
    var outputGain: Short = 0
    var channelMapFamily: Byte = 0

    var streamCount: Byte = 0
    var streamCountTwoChannel: Byte = 0
    var channelMap: ByteArray? = null

    var bitrateMinimal: Int = 0
    var bitrateNominal: Int = 0
    var bitrateMaximal: Int = 0


    init {
        decodeHeader(vorbisData)
    }

    /**
     * Read a string of a specified number of ASCII bytes.
     */
    private fun readHeader(bytes: ByteBuffer): String {
        val buf = ByteArray(8)
        bytes[buf]
        return String(buf, Charset.forName("US-ASCII"))
    }

    private fun decodeHeader(b: ByteArray) {
        val buf = ByteBuffer.wrap(b)
        val oggHead = readHeader(buf)

        if (oggHead == OpusHeader.HEAD_CAPTURE_PATTERN) {
            this.vorbisVersion = buf.get()
            this.audioChannels = buf.get()
            this.preSkip = buf.getShort()
            this.audioSampleRate = buf.getInt()
            this.outputGain = buf.getShort()
            this.channelMapFamily = buf.get()

            if (channelMapFamily > 0) {
                this.streamCount = buf.get()
                this.streamCountTwoChannel = buf.get()

                this.channelMap = ByteArray(audioChannels.toInt())
                for (i in 0..<audioChannels) {
                    channelMap!![i] = buf.get()
                }
            }

            isValid = true
        }
    }

    override fun toString(): String {
        val sb = StringBuffer("OpusVorbisIdentificationHeader{")
        sb.append("isValid=").append(isValid)
        sb.append(", vorbisVersion=").append(vorbisVersion.toInt())
        sb.append(", audioChannels=").append(audioChannels.toInt())
        sb.append(", preSkip=").append(preSkip.toInt())
        sb.append(", audioSampleRate=").append(audioSampleRate)
        sb.append(", outputGain=").append(outputGain.toInt())
        sb.append(", channelMapFamily=").append(channelMapFamily.toInt())
        sb.append(", streamCount=").append(streamCount.toInt())
        sb.append(", streamCountTwoChannel=").append(streamCountTwoChannel.toInt())
        sb.append(", channelMap=")
        if (channelMap == null) sb.append("null")
        else {
            sb.append('[')
            for (i in channelMap!!.indices) sb.append(if (i == 0) "" else ", ").append(channelMap!![i].toInt())
            sb.append(']')
        }
        sb.append(", bitrateMinimal=").append(bitrateMinimal)
        sb.append(", bitrateNominal=").append(bitrateNominal)
        sb.append(", bitrateMaximal=").append(bitrateMaximal)
        sb.append('}')
        return sb.toString()
    }

    companion object {
        var logger: Logger = Logger.getLogger("org.jaudiotagger.audio.ogg.opus")
    }
}

