package dima.apps.dired.opus

import org.jaudiotagger.audio.exceptions.CannotReadException
import org.jaudiotagger.audio.generic.GenericAudioHeader
import org.jaudiotagger.audio.ogg.util.OggPageHeader
import dima.apps.dired.opus.util.OpusVorbisIdentificationHeader
import org.jaudiotagger.logging.ErrorMessage
import org.jaudiotagger.tag.id3.AbstractID3v2Tag
import java.io.IOException
import java.io.RandomAccessFile
import java.util.logging.Logger

/**
 * Read encoding info, only implemented for vorbis streams
 */
class OpusInfoReader {
    @Throws(CannotReadException::class, IOException::class)
    fun read(raf: RandomAccessFile): GenericAudioHeader {
        var start = raf.filePointer
        val info = GenericAudioHeader()
        logger.fine("Started")

        //Check start of file does it have Ogg pattern
        val b = ByteArray(OggPageHeader.CAPTURE_PATTERN.size)
        raf.read(b)
        if (!(b.contentEquals(OggPageHeader.CAPTURE_PATTERN))) {
            raf.seek(0)
            if (AbstractID3v2Tag.isId3Tag(raf)) {
                raf.read(b)
                if ((b.contentEquals(OggPageHeader.CAPTURE_PATTERN))) {
                    start = raf.filePointer
                }
            } else {
                throw CannotReadException(ErrorMessage.OGG_HEADER_CANNOT_BE_FOUND.getMsg(String(b)))
            }
        }
        raf.seek(start)

        //1st page = Identification Header
        val pageHeader = OggPageHeader.read(raf)
        val vorbisData = ByteArray(pageHeader.pageLength)

        raf.read(vorbisData)
        val opusIdHeader = OpusVorbisIdentificationHeader(vorbisData)

        //Map to generic encodingInfo
        info.channelNumber = opusIdHeader.audioChannels.toInt()
        info.setSamplingRate(opusIdHeader.audioSampleRate)
        info.encodingType = "Opus Vorbis 1.0"

        // find last Opus Header
        val last = lastValidHeader(raf)
            ?: throw CannotReadException("Opus file contains ID and Comment headers but no audio content")

        info.noOfSamples = (last.absoluteGranulePosition - opusIdHeader.preSkip).toLong()
        info.setPreciseLength(info.noOfSamples / 48000.0)

        return info
    }

    @Throws(IOException::class)
    private fun lastValidHeader(raf: RandomAccessFile): OggPageHeader? {
        var best: OggPageHeader? = null
        while (true) {
            try {
                val candidate = OggPageHeader.read(raf)
                raf.seek(raf.filePointer + candidate.pageLength)
                if (candidate.isValid && !candidate.isLastPacketIncomplete) {
                    best = candidate
                }
            } catch (ignored: CannotReadException) {
                break
            }
        }

        return best
    }

    companion object {
        var logger: Logger = Logger.getLogger("org.jaudiotagger.audio.opus.atom")
    }
}

