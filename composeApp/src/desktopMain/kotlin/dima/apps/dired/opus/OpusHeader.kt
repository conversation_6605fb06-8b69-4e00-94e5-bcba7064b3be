package dima.apps.dired.opus

/**
 * Defines variables common to all vorbis headers
 */
interface OpusHeader {
    companion object {
        const val HEAD_CAPTURE_PATTERN: String = "OpusHead"
        val HEAD_CAPTURE_PATTERN_AS_BYTES: ByteArray =
            HEAD_CAPTURE_PATTERN.toByteArray(Charsets.ISO_8859_1)

        const val HEAD_CAPTURE_PATTERN_POS: Int = 0
        val HEAD_CAPTURE_PATTERN_LENGTH: Int = HEAD_CAPTURE_PATTERN_AS_BYTES.size

        //Capture pattern at start of header
        const val TAGS_CAPTURE_PATTERN: String = "OpusTags"
        val TAGS_CAPTURE_PATTERN_AS_BYTES: ByteArray =
            TAGS_CAPTURE_PATTERN.toByteArray(Charsets.ISO_8859_1)

        const val TAGS_CAPTURE_PATTERN_POS: Int = 0
        val TAGS_CAPTURE_PATTERN_LENGTH: Int = TAGS_CAPTURE_PATTERN_AS_BYTES.size
    }
}
