package dima.apps.dired

import dima.ai.standaloneChatDialog.openAiStandaloneDialog
import dima.apps.notifications.showErrorNotification
import dima.dialogs.completion.openCompletionDialog
import dima.utils.RecentHistory
import dima.utils.abbreviatePath

internal fun openImageAiDialog(entry: Dired.Entry) {
    if (entry.fileTypeWithColor.type != FileType.Image) {
        showErrorNotification("Can only ask questions about images", durationMillis = 1000)
        return
    }
    val recentTexts = RecentHistory.getRecentTexts()
    openCompletionDialog(
        title = "Ask AI about " + entry.file.abbreviatePath(),
        candidates = recentTexts,
        matchRequired = false
    ) {
        RecentHistory.rememberText(it.text)
        openAiStandaloneDialog(
            initialPrompt = it.text,
            imageFile = if (entry.fileTypeWithColor.type == FileType.Image) entry.file else null,
            copyFirstResponseToClipboard = true
        )
    }
}
