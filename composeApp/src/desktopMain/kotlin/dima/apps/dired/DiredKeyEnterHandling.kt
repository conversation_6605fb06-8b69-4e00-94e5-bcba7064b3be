package dima.apps.dired

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.dired.preview.DiredPdfProvider
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.audio.Audio
import dima.globalState.DiredImageState
import dima.globalState.GlobalState
import dima.utils.RecentHistory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal fun onEnter(
    entry: Dired.Entry?,
    scrollPreviewToTop: () -> Unit,
    enterDirectory: () -> Unit
) {

    fun rememberSelectedFile() {
        if (GlobalState.dired.selectedFile != null) {
            RecentHistory.rememberFile(GlobalState.dired.selectedFile!!)
        }
    }

    fun makePreviewActive() {
        if (!GlobalState.dired.isPreviewActive) {
            rememberSelectedFile()
            Dired.keepSameFilesOnDiredGlobalStateChange = true
            GlobalState.dired = GlobalState.dired.copy(isPreviewActive = true)
        }
    }

    if (entry == null) {
        showErrorNotification("No selected file to open")
        return
    }
    if (entry.file.isDirectory) {
        enterDirectory()
        return
    }
    when (entry.fileTypeWithColor.type) {
        FileType.Archive -> decompressArchive(entry)

        FileType.Audio -> {
            rememberSelectedFile()
            val current = GlobalState.audioPlayer.currentTrack
            if (current == entry.file.absolutePath) {
                if (!GlobalState.audioPlayer.isPlaying) {
                    Audio.togglePlayPause() // resume
                    showNotification("Resumed", durationMillis = 800)
                } else {
                    showNotification("Already playing", durationMillis = 800)
                }
            } else {
                Audio.playTrack(entry.file.absolutePath, startPlaying = true)
            }
        }

        FileType.Image -> {
            if (GlobalState.dired.isPreviewActive) {
                Dired.keepSameFilesOnDiredGlobalStateChange = true
                GlobalState.dired = GlobalState.dired.copy(imageState = DiredImageState())
                showNotification("Reset image zoom and translation", durationMillis = 500)
            } else {
                makePreviewActive()
            }
        }

        FileType.Video -> {
            if (GlobalState.dired.isPreviewActive) {
                CoroutineScope(Dispatchers.IO).launch {
                    val result = process(
                        "mpv",
                        entry.file.absolutePath,
                        stdout = Redirect.CAPTURE,
                        stderr = Redirect.CAPTURE,
                    )
                    if (result.resultCode != 0) {
                        showErrorNotification(
                            "Failed to open video in mpv",
                            result.output.joinToString("\n")
                        )
                    }
                }
            } else {
                makePreviewActive()
            }
        }

        FileType.Pdf -> {
            if (GlobalState.dired.isPreviewActive) {
                // Resetting zoom/pan state for PDF preview
                GlobalState.dired = GlobalState.dired.copy(pdfImageState = DiredImageState())
                // Optionally, if you want to force a re-render/reload of PDF from provider:
                DiredPdfProvider.loadPdf(entry) // Re-request to ensure fresh state if needed
                showNotification("Reset PDF zoom and translation", durationMillis = 500)
            } else {
                makePreviewActive()
            }
        }

        FileType.Markdown, FileType.Text,
        FileType.Json, FileType.Toml, FileType.Rust, FileType.Groovy, FileType.PHP,
        FileType.Python, FileType.Java, FileType.Bash, FileType.Dockerfile, FileType.HTML,
        FileType.CSS, FileType.JavaScript -> {
            if (GlobalState.dired.isPreviewActive) {
                scrollPreviewToTop()
            } else {
                makePreviewActive()
            }
        }
    }
}

