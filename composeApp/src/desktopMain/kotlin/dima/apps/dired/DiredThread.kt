package dima.apps.dired

import dima.apps.AppType
import dima.globalState.GlobalState
import dima.utils.RecentHistory
import kotlin.time.Duration.Companion.seconds

class DiredSaveCurrentPathToHistory : Thread() {
    override fun run() {
        val sleepDuration = 3.seconds.inWholeMilliseconds
        while (true) {
            if (GlobalState.app == AppType.Dired) {
                val file = GlobalState.dired.selectedFile
                if (file != null) {
                    RecentHistory.rememberFile(file)
                }
            }
            sleep(sleepDuration)
        }
    }
}

class DiredWipeMarkedThread : Thread() {
    override fun run() {
        val sleepDuration = 60.seconds.inWholeMilliseconds
        while (true) {
            sleep(sleepDuration)
            Dired.removeDeletedMarkedPaths()
            DiredDatabase.deleteMissingDirectoriesFromDatabase()
        }
    }
}

class DiredSaveToDatabaseThread : Thread() {
    override fun run() {
        val sleepDuration = 3.seconds.inWholeMilliseconds
        while (true) {
            sleep(sleepDuration)
            if (GlobalState.app != AppType.Dired) {
                continue
            }
            DiredDatabase.rememberSelected()
        }
    }
}