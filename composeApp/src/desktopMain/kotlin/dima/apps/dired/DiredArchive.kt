package dima.apps.dired

import GlobalEvent
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.process.LoggedProcess
import dima.utils.FileSystem
import dima.utils.abbreviatePath
import dima.utils.slugify
import globalEvent
import java.io.File

internal fun decompressArchive(entry: Dired.Entry) {
    val archiveFile = entry.file
    val archivePath = archiveFile.absolutePath
    val parentDir = archiveFile.parentFile ?: run {
        showErrorNotification("Archive file has no parent directory.", archivePath)
        return
    }
    val outDirName = archiveFile.nameWithoutExtension
    val outDir = File(parentDir, outDirName)
    val command: String
    val args: List<String>
    val workingDirectory: File
    val notificationTitle: String
    val baseSuccessMessage: String
    when {
        archiveFile.extension.equals("zip", ignoreCase = true) -> {
            val newOutDirName = FileSystem.getUniqueFileByFileName(archiveFile.parentFile, outDirName.slugify()).name
            command = "ditto"
            args = listOf(
                "-V",
                "-x",
                "-k",
                "--sequesterRsrc",
                "--rsrc",
                archivePath,
                // slugify to avoid having dots in the file name which would not create a new directory
                newOutDirName
            )
            workingDirectory = parentDir
            notificationTitle = "Uncompressing ZIP with ditto"
            baseSuccessMessage = "Uncompressed to $newOutDirName"
        }

        archivePath.endsWith(".tar.xz", ignoreCase = true) -> {
            command = "tar"
            args = listOf("-xf", archivePath)
            workingDirectory = parentDir
            notificationTitle = "Uncompressing .tar.xz with tar"
            baseSuccessMessage = "Uncompressed ${archiveFile.name} in current directory"
        }

        else -> {
            command = "atool"
            args = listOf("--extract", "--explain", archivePath)
            workingDirectory = parentDir
            notificationTitle = "Uncompressing with atool"
            baseSuccessMessage = "Uncompressed ${archiveFile.name}"
        }
    }
    val notification = showLoadingNotification(notificationTitle, archiveFile.name)
    LoggedProcess(
        command = command,
        args = args,
        workingDirectory = workingDirectory,
        showErrorNotifications = false,
        onFinish = { processResult ->
            if (processResult.exitCode.value != 0) {
                notification.toError("Failed to uncompress", processResult.getOutput())
                return@LoggedProcess
            }
            var newTitle = baseSuccessMessage
            var newMessage: String? = null
            when (command) {
                "ditto" -> {
                    newTitle = "Uncompressed ${archiveFile.name}"
                    newMessage = "To new directory:\n" + File(archiveFile.parentFile, processResult.args.last()).abbreviatePath()
                }
                "tar" -> { /* No specific directory to select, Dired reloads current */
                }

                "atool" -> newTitle += if (outDir.exists() && outDir.isDirectory) {
                    " to ${outDir.name}"
                } else {
                    " in current directory"
                }
            }
            notification.toInfo(newTitle, newMessage)
            Dired.hideNextReloadNotification = true
            globalEvent = GlobalEvent.Reload
        }
    ).startAsync()
}
