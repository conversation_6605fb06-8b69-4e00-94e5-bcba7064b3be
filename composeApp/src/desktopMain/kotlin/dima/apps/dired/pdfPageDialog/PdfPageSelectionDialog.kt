package dima.apps.dired.pdfPageDialog

import GlobalStyling
import androidx.compose.foundation.Image
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dialogs
import dima.apps.dired.Dired
import dima.apps.dired.addSelectedBorder
import dima.apps.dired.preview.DiredPdfProvider
import dima.dialogs.DialogIdentifier
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.dialogs.openDialog
import dima.utils.DummyFocusable
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.isQuestionMark

data class PdfPageSelectionParams(
    val entry: Dired.Entry,
    val initialPageIndex: Int = 0,
    val onConfirm: (pageIndex: Int) -> Unit = {}
)

fun openPdfPageSelectionDialog(entry: Dired.Entry, initialPageIndex: Int = 0, onConfirm: (pageIndex: Int) -> Unit = {}) {
    val params = PdfPageSelectionParams(entry, initialPageIndex, onConfirm)
    openDialog(DialogIdentifier.PdfPageSelection, params) { id, data ->
        PdfPageSelectionDialog(id, data as PdfPageSelectionParams)
    }
}

@Composable
fun PdfPageSelectionDialog(dialogId: Long, params: PdfPageSelectionParams) {
    val providerState by DiredPdfProvider.pdfStateFlow.collectAsState()
    val scrollState = rememberLazyListState()
    val dialogFocusRequester = remember { FocusRequester() }
    var selectedPageIndex by remember { mutableStateOf(params.initialPageIndex) }

    fun onPreviewKeyEvent(keyEvent: KeyEvent): Boolean {
        if (keyEvent.type != KeyEventType.KeyDown) {
            return false
        }
        return when (keyEvent.key) {
            Key.Escape -> {
                closeDialogWithId(dialogId)
                true
            }

            Key.H -> {
                if (selectedPageIndex > 0) {
                    selectedPageIndex--
                }
                true
            }

            Key.N -> {
                val maxIndex = (providerState.images.size - 1).coerceAtLeast(0)
                if (selectedPageIndex < maxIndex) {
                    selectedPageIndex++
                }
                true
            }

            Key.V -> {
                selectedPageIndex = (selectedPageIndex - 6).coerceAtLeast(0)
                true
            }

            Key.M -> {
                val maxIndex = (providerState.images.size - 1).coerceAtLeast(0)
                selectedPageIndex = (selectedPageIndex + 6).coerceAtMost(maxIndex)
                true
            }

            Key.Enter -> {
                // Switch to the selected page
                if (providerState.images.isNotEmpty() && selectedPageIndex < providerState.images.size) {
                    params.onConfirm(selectedPageIndex)
                    closeDialogWithId(dialogId)
                }
                true
            }

            else -> if (keyEvent.isQuestionMark()) {
                openMiniHelpDialog(
                    title = "PDF Page Selection Help",
                    keys = listOf(
                        MiniHelpDialogKey(listOf(Key.H), "Select previous page"),
                        MiniHelpDialogKey(listOf(Key.N), "Select next page"),
                        MiniHelpDialogKey(listOf(Key.V), "Jump 6 pages backward"),
                        MiniHelpDialogKey(listOf(Key.M), "Jump 6 pages forward"),
                        MiniHelpDialogKey(listOf(Key.Enter), "Confirm selection and jump to selected page"),
                        MiniHelpDialogKey(listOf(Key.Escape), "Close dialog")
                    )
                )
                true
            } else {
                false
            }
        }
    }

    DialogOverlay(
        dialogId = dialogId,
        showHelpIcon = true,
        onPreviewKeyEvent = ::onPreviewKeyEvent,
        widthFraction = 1f
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .focusable()
                .focusRequester(dialogFocusRequester)
        ) {
            DummyFocusable(dialogFocusRequester)
            Text(
                text = "Jump to page in ${params.entry.file.name}",
                fontSize = 14.sp,
                color = GlobalStyling.getGrayColor(),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )

            when {
                providerState.isLoading -> {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Text(
                            text = "Loading PDF pages...",
                            color = GlobalStyling.getGrayColor()
                        )
                    }
                }

                providerState.error != null -> {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Text(
                            text = "Error loading PDF: ${providerState.error}",
                            color = GlobalStyling.getRedTextColor()
                        )
                    }
                }

                providerState.images.isNotEmpty() -> {
                    LazyRow(
                        state = scrollState,
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        modifier = Modifier.fillMaxSize().padding(horizontal = 16.dp)
                    ) {
                        itemsIndexed(providerState.images) { index, imageBitmap ->
                            val isSelected = index == selectedPageIndex
                            Box(
                                contentAlignment = Alignment.TopCenter,
                                modifier = Modifier
                                    .width(600.dp)  // Fixed width for each item
                                    .fillMaxHeight()
                                    .clickableWithoutBackgroundRipple {
                                        selectedPageIndex = index
                                    }
                                    .addSelectedBorder(isSelected)
                                    .padding(8.dp)
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    modifier = Modifier.fillMaxSize()
                                ) {
                                    Text(
                                        text = "Page ${index + 1}",
                                        color = GlobalStyling.getTextColor(),
                                        modifier = Modifier.padding(bottom = 8.dp)
                                    )
                                    // Image fills available height while preserving aspect ratio
                                    Image(
                                        painter = BitmapPainter(imageBitmap),
                                        contentDescription = "PDF Page ${index + 1}",
                                        modifier = Modifier
                                            .fillMaxHeight()
                                            .clipToBounds()
                                    )
                                }
                            }
                        }
                    }
                }

                else -> {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.fillMaxSize()
                    ) {
                        Text(
                            text = "No PDF pages available",
                            color = GlobalStyling.getGrayColor()
                        )
                    }
                }
            }
        }
    }

    // Track if this is the first scroll (initial positioning)
    var isFirstScroll by remember { mutableStateOf(true) }
    
    LaunchedEffect(selectedPageIndex) {
        if (providerState.images.isNotEmpty()) {
            if (isFirstScroll) {
                // For the initial scroll, use scrollToItem for immediate positioning without animation
                scrollState.scrollToItem(selectedPageIndex, scrollOffset = -500)
                isFirstScroll = false
            } else {
                // For subsequent scrolls, use animateScrollToItem for smooth transitions
                scrollState.animateScrollToItem(selectedPageIndex, scrollOffset = -500)
            }
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.last().id == dialogId) {
            dialogFocusRequester.requestFocus()
        }
    }
}