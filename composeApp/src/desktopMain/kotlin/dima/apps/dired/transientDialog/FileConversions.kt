package dima.apps.dired.transientDialog

import GlobalEvent
import dima.apps.AppType
import dima.apps.dired.Dired
import dima.apps.notifications.showLoadingNotification
import dima.globalState.GlobalState
import dima.process.LoggedProcess
import dima.utils.FileSystem
import globalEvent
import java.io.File

private fun reloadDired() {
    if (GlobalState.app != AppType.Dired) {
        return
    }
    Dired.hideNextReloadNotification = true
    globalEvent = GlobalEvent.Reload
}

/**
 * Convert image to PNG in same directory.
 */
internal fun File.convertImageToPng() {
    val baseOutputFile = File(
        parent,
        "$nameWithoutExtension.png"
    )
    val outputFile = FileSystem.getUniqueFileByFileName(
        baseOutputFile.parentFile,
        baseOutputFile.name
    )
    val notification = showLoadingNotification(
        "Converting image to PNG",
        "$name → ${outputFile.name}"
    )
    LoggedProcess(
        command = "magick",
        args = listOf(
            "-verbose",
            absolutePath,
            outputFile.absolutePath
        ),
        workingDirectory = parentFile,
        onFinish = {
            if (it.exitCode.value == 0) {
                notification.toInfo("Converted to PNG", "$name → ${outputFile.name}")
                reloadDired()
            } else {
                notification.toError("Failed to convert to PNG", it.getOutput())
                outputFile.delete()
            }
        },
        onOutputLine = {
            notification.update(message = "$name\n→\n${outputFile.name}\n$it")
        }).startAsync()
}

/**
 * Convert video to MP3 in same directory.
 */
internal fun File.convertVideoToMp3() {
    val baseOutputFile = File(
        parent,
        "$nameWithoutExtension.mp3"
    )
    val outputFile = FileSystem.getUniqueFileByFileName(
        baseOutputFile.parentFile,
        baseOutputFile.name
    )
    val notification = showLoadingNotification(
        "Converting video to MP3",
        "$name → ${outputFile.name}"
    )
    LoggedProcess(
        command = "ffmpeg",
        args = listOf(
            "-n",  // don't overwrite
            "-i", absolutePath,
            "-vn",  // no video
            "-acodec", "libmp3lame",  // use MP3 codec
            "-q:a", "0",  // highest quality
            outputFile.absolutePath
        ),
        workingDirectory = parentFile,
        onFinish = {
            if (it.exitCode.value == 0) {
                notification.toInfo("Converted to MP3", "$name\n→\n${outputFile.name}")
                reloadDired()
            } else {
                notification.toError("Failed to convert to MP3", it.getOutput())
                outputFile.delete()
            }
        },
        onOutputLine = {
            notification.update(message = "$name → ${outputFile.name}\n$it")
        }).startAsync()
}
