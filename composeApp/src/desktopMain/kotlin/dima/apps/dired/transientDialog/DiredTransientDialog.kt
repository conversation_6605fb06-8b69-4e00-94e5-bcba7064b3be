package dima.apps.dired.transientDialog

import GlobalEvent
import Globals
import androidx.compose.ui.input.key.Key
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.ai.transcribe.transcribeAudioToClipboard
import dima.apps.dired.Dired
import dima.apps.dired.DiredSort
import dima.apps.dired.FileType
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.dialogs.DialogIdentifier
import dima.dialogs.archiveScanned.canOpenArchiveScannedDialog
import dima.dialogs.archiveScanned.openArchiveScannedDialog
import dima.dialogs.confirmation.openConfirmationDialog
import dialogs
import dima.dialogs.generic.GenericDialogNumberValidator
import dima.dialogs.generic.GenericDialogRow
import dima.dialogs.generic.getTextInputSingleLineContent
import dima.dialogs.generic.openGenericDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.dialogs.transient.TransientDialogGroup
import dima.dialogs.transient.TransientDialogKey
import dima.dialogs.transient.openTransientDialog
import dima.globalState.DiredFileRowThumbnailMode
import dima.globalState.GlobalState
import dima.images.TemporaryFileUploader
import dima.os.copyImageToClipboard
import dima.os.copyToClipboard
import dima.os.revealInFinderViaOsascript
import dima.settings
import dima.telegram.openTelegramFileSenderDialog
import dima.utils.Result
import dima.utils.abbreviatePath
import dima.utils.asLowerCase
import globalEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import java.nio.file.Files.createDirectory
import java.nio.file.Files.exists
import java.nio.file.Path
import java.nio.file.Paths.get

/**
 * Opens a transient dialog for Dired operations
 *
 * @param entry can be null when in an empty directory
 * @param onDirectoryCreate callback when a new directory is created
 */
fun openDiredTransientDialog(
    entry: Dired.Entry?,
    onDirectoryCreate: (File) -> Unit,
    openedByKey: Key? = null
) {
    if (dialogs.any { it.identifier == DialogIdentifier.Transient }) {
        return
    }
    val actionKeys = mutableListOf<TransientDialogKey>()
    val viewKeys = mutableListOf<TransientDialogKey>()
    val fileTypeKeys = mutableListOf<TransientDialogKey>()

    viewKeys.add(TransientDialogKey(Key.S, "Cycle sort: Name, Size, Last Modified", keepOpen = true) {
        val newSort = when (GlobalState.dired.sort) {
            DiredSort.Name -> DiredSort.Size
            DiredSort.Size -> DiredSort.LastModified
            DiredSort.LastModified -> DiredSort.Name
        }
        GlobalState.dired = GlobalState.dired.copy(sort = newSort)
    })

    actionKeys.add(TransientDialogKey(Key.N, "Create new directory") {
        openTextInputDialog("Create new directory") { newDirectoryName ->
            if (newDirectoryName.contains("/")) {
                showErrorNotification("New directory is not allowed to contain /")
                return@openTextInputDialog TextInputDialogConfirmAction.KeepOpen
            }
            val path: Path = get(GlobalState.dired.directory, newDirectoryName)
            if (exists(path)) {
                showErrorNotification("Directory already exists")
                return@openTextInputDialog TextInputDialogConfirmAction.KeepOpen
            }
            try {
                createDirectory(path)
                showNotification("Directory created successfully")
                onDirectoryCreate(path.toFile())
            } catch (e: Exception) {
                showErrorNotification("Failed to create directory", e.message)
                return@openTextInputDialog TextInputDialogConfirmAction.KeepOpen
            }
            return@openTextInputDialog TextInputDialogConfirmAction.Close
        }
    })

    actionKeys.add(TransientDialogKey(Key.Tab, "Toggle row image preview") {
        GlobalState.dired = GlobalState.dired.copy(
            fileRowThumbnailMode = when (GlobalState.dired.fileRowThumbnailMode) {
                DiredFileRowThumbnailMode.NoThumbnail -> DiredFileRowThumbnailMode.ShowThumbnail
                DiredFileRowThumbnailMode.ShowThumbnail -> DiredFileRowThumbnailMode.NoThumbnail
            }
        )
    })

    actionKeys.add(TransientDialogKey(Key.B, "Open last modified file") {
        val lastModifiedFiles = GlobalState.dired.lastModifiedFiles
        if (lastModifiedFiles.isNotEmpty()) {
            val lastModifiedFile = File(lastModifiedFiles.first())
            if (lastModifiedFile.exists()) {
                GlobalState.dired = GlobalState.dired.copy(
                    directory = lastModifiedFile.parent ?: GlobalState.dired.directory,
                    selectedFile = lastModifiedFile.absolutePath
                )
            } else {
                showErrorNotification("Last modified file no longer exists")
            }
        } else {
            showErrorNotification("No recently modified files found", durationMillis = 1000)
        }
    })

    // Actions that require a selected file
    if (entry != null) {
        actionKeys.add(TransientDialogKey(Key.F, "Reveal in Finder.app") {
            entry.file.revealInFinderViaOsascript()
        })
        if (entry.fileTypeWithColor.type == FileType.Pdf) {
            actionKeys.add(TransientDialogKey(Key.P, "Open first PDF page") {
                openFirstPdfPage(entry)
            })
        }
        if (entry.fileTypeWithColor.type == FileType.Audio) {
            actionKeys.add(TransientDialogKey(Key.U, "Transcribe audio") {
                entry.file.transcribeAudioToClipboard()
            })
        }
        if (entry.fileTypeWithColor.type == FileType.Audio || entry.fileTypeWithColor.type == FileType.Image) {
            if (entry.fileTypeWithColor.type == FileType.Audio) {
                actionKeys.add(TransientDialogKey(Key.Z, "Convert to MP3") {
                    entry.file.convertVideoToMp3()
                })
            } else {
                fileTypeKeys.add(TransientDialogKey(Key.Z, "Convert to PNG") {
                    entry.file.convertImageToPng()
                })
            }
        }
        if (entry.fileTypeWithColor.type == FileType.Image) {
            fileTypeKeys.add(TransientDialogKey(Key.Minus, "Prepare img2img CLI command") {
                if (entry.file.absolutePath.contains("'")) {
                    showErrorNotification("File path contains ', not supported")
                    return@TransientDialogKey
                }
                openGenericDialog(
                    "Generate img2img CLI command for download into ~/Downloads",
                    layout = listOf(
                        GenericDialogRow.TextInputSingleLine("Prompt", isRequired = true),
                        GenericDialogRow.TextInputSingleLine(
                            "Number of images",
                            content = "50",
                            isRequired = true,
                            validator = ::GenericDialogNumberValidator,
                        ),
                    )
                ) { result ->
                    val prompt = result.getTextInputSingleLineContent("Prompt").replace("\"", "\\\"")
                    val n = result.getTextInputSingleLineContent("Number of images").toIntOrNull() ?: 1
                    val command =
                        "image-to-image --prompt \"$prompt\" -n $n --input-file '${entry.file.absolutePath}' --output-dir ~/Downloads"
                    copyToClipboard(command)
                }
            })
            fileTypeKeys.add(TransientDialogKey(Key.NumPadAdd, "Upload to tmpfiles.org") {
                val notification = showLoadingNotification("Uploading to tmpfiles.org...", entry.file.name)
                Globals.coroutineScope.launch(Dispatchers.IO) {
                    val result = TemporaryFileUploader.uploadFile(entry.file)
                    when (result) {
                        is Result.Error -> notification.toError("Failed to upload", result.error)
                        is Result.Success -> {
                            notification.toInfo("Uploaded and copied to clipboard", result.value)
                            copyToClipboard(result.value, showNotification = false)
                        }
                    }
                }
            })
            fileTypeKeys.add(TransientDialogKey(Key.G, "Open in GIMP") {
                entry.file.openInGimp()
            })
            fileTypeKeys.add(TransientDialogKey(Key.DirectionLeft, "Rotate left", keepOpen = true) {
                rotateImage(entry.file, left = true)
            })
            fileTypeKeys.add(TransientDialogKey(Key.DirectionRight, "Rotate right", keepOpen = true) {
                rotateImage(entry.file, left = false)
            })
            fileTypeKeys.add(TransientDialogKey(Key.C, "Copy to clipboard") {
                Globals.coroutineScope.launch(Dispatchers.IO) {
                    entry.file.copyImageToClipboard()
                    openConfirmationDialog("Trash image?") {
                        runBlocking {
                            val result = process(
                                "trash", entry.file.absolutePath,
                                stdout = Redirect.CAPTURE,
                                stderr = Redirect.CAPTURE,
                            )
                            if (result.resultCode == 0) {
                                showNotification("Trashed", entry.file.name)
                                globalEvent = GlobalEvent.Reload
                            } else {
                                showErrorNotification("Failed to trash image", result.output.joinToString("\n"))
                            }
                        }
                    }
                }
            })
        }
        // Add Archive action if applicable
        if (entry.file.isFile &&
            settings.scannedDirectory != null &&
            settings.scannedDirectory.exists() &&
            canOpenArchiveScannedDialog(entry.file)
        ) {
            actionKeys.add(TransientDialogKey(Key.A, "Archive into scanned directory") {
                openArchiveScannedDialog(entry.file)
            })
        }

        // Add Telegram file sending option for files
        // Check if there are marked files in the current directory
        val markedFiles: List<File> = GlobalState.dired.markedFiles.filter {
            it.parentFile?.absolutePath == File(
                GlobalState.dired.directory
            ).absolutePath
        }

        if (markedFiles.isNotEmpty()) {
            // If there are marked files, offer to send them all
            val fileCount = markedFiles.size
            val fileText = if (fileCount == 1) "file" else "files"
            actionKeys.add(TransientDialogKey(Key.T, "Send $fileCount marked $fileText via Telegram") {
                openTelegramFileSenderDialog(markedFiles)
            })
        } else if (entry.file.isFile) {
            // If no marked files but a file is selected, offer to send just that file
            actionKeys.add(TransientDialogKey(Key.T, "Send via Telegram") {
                openTelegramFileSenderDialog(entry.file)
            })
        }
    }
    openTransientDialog(
        groups = buildList {
            add(TransientDialogGroup("Actions", actionKeys.sortedBy { it.key.asLowerCase() }))
            if (fileTypeKeys.isNotEmpty()) {
                val name = if (entry?.fileTypeWithColor?.type == FileType.Image) "Image" else "Video"
                add(TransientDialogGroup(name, fileTypeKeys.sortedBy { it.key.asLowerCase() }))
            }
            add(TransientDialogGroup("View", viewKeys.sortedBy { it.key.asLowerCase() }))
        },
        subTitle = entry?.file?.abbreviatePath(),
        openedByKey = openedByKey
    )
}