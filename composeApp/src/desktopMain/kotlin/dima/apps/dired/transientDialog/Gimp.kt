package dima.apps.dired.transientDialog

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.utils.abbreviatePath
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

internal fun File.openInGimp() {
    CoroutineScope(Dispatchers.IO).launch {
        try {
            val p = process(
                "open",
                "-a",
                "/Applications/GIMP.app",
                absolutePath,
                stdout = Redirect.CAPTURE,
                stderr = Redirect.CAPTURE
            )
            if (p.resultCode == 0) {
                showNotification("Opened in GIMP", abbreviatePath())
            } else {
                showErrorNotification("Failed to open in GIMP", p.output.joinToString("\n"))
            }
        } catch (e: Exception) {
            showErrorNotification("Failed to open in GIMP", e.message ?: "Unknown error")
        }
    }
}
