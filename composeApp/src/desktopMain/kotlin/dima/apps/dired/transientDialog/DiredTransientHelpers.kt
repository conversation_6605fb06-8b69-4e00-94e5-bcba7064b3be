package dima.apps.dired.transientDialog

import GlobalEvent
import Globals
import dima.apps.dired.Dired
import dima.apps.dired.preview.DiredPdfPreview
import dima.apps.dired.rotateLeft
import dima.apps.dired.rotateRight
import dima.apps.notifications.showErrorNotification
import dima.globalState.GlobalState
import dima.utils.SimpleResult
import dima.utils.sha256Hash
import globalEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * Opens the first page of a PDF file
 */
internal fun openFirstPdfPage(entry: Dired.Entry) {
    val hash = entry.file.sha256Hash()
    val firstPageFile = File(DiredPdfPreview.cacheDirectory, "$hash-0.png")
    if (!firstPageFile.exists()) {
        showErrorNotification(
            "First page file not found",
            "Preview the PDF first to generate the pages"
        )
        return
    }
    GlobalState.dired = GlobalState.dired.copy(
        directory = firstPageFile.parent,
        selectedFile = firstPageFile.absolutePath,
        isPreviewFullWidth = true
    )
}

internal fun rotateImage(file: File, left: Boolean) {
    Globals.coroutineScope.launch(Dispatchers.IO) {
        val result = if (left) rotateLeft(file) else rotateRight(file)
        when (result) {
            is SimpleResult.Success -> {
                Dired.hideNextReloadNotification = true
                globalEvent = GlobalEvent.Reload
            }

            is SimpleResult.Error -> showErrorNotification("Failed to rotate image", result.error)
        }
    }
}
