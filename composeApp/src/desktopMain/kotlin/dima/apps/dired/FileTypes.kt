package dima.apps.dired

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PictureAsPdf
import com.composables.icons.lucide.*
import dev.tclement.fonticons.fa.*
import dima.color.TailwindCssColors
import dima.treeSitter.TreeSitterLanguage
import dima.utils.FileSystem
import java.io.File

/**
 * Only uses for files, not directories!
 */
fun File.getFileType(): FileTypeWithColor {
    val extension = extension.lowercase()
    if (FileSystem.isAudioExtension(extension)) {
        return FileTypeWithColor.VectorIcon(
            fileType = FileType.Audio,
            icon = Lucide.FileAudio,
            lightColor = TailwindCssColors.red600,
            darkColor = TailwindCssColors.red400
        )
    }
    if (listOf("zip", "rar").contains(extension)) {
        return FileTypeWithColor.VectorIcon(
            fileType = FileType.Archive,
            icon = Lucide.Archive,
            lightColor = TailwindCssColors.blue600,
            darkColor = TailwindCssColors.blue400
        )
    }
    if (listOf("jpg", "jpeg", "png", "gif", "bmp", "svg", "webp").contains(extension)) {
        return FileTypeWithColor.VectorIcon(
            fileType = FileType.Image,
            icon = Lucide.FileImage,
            lightColor = TailwindCssColors.blue600,
            darkColor = TailwindCssColors.blue400
        )
    }
    if ("pdf" == extension) {
        return FileTypeWithColor.VectorIcon(
            fileType = FileType.Pdf,
            icon = Icons.Default.PictureAsPdf,
            lightColor = TailwindCssColors.red600,
            darkColor = TailwindCssColors.red400
        )
    }
    if (TreeSitterLanguage.JSON.matchesExtension(extension)) {
        return FileTypeWithColor.VectorIcon(
            fileType = FileType.Json,
            icon = Lucide.FileJson,
            lightColor = TailwindCssColors.yellow600,
            darkColor = TailwindCssColors.yellow400
        )
    }
    if (TreeSitterLanguage.Rust.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            fileType = FileType.Rust,
            iconChar = FontAwesome.Brands.Rust,
            lightColor = TailwindCssColors.yellow600,
            darkColor = TailwindCssColors.yellow400
        )
    }
    if (TreeSitterLanguage.Groovy.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            fileType = FileType.Groovy,
            iconChar = 'G',
            lightColor = TailwindCssColors.blue600,
            darkColor = TailwindCssColors.blue400
        )
    }
    if (extension == "css") {
        return FileTypeWithColor.CharIcon(
            fileType = FileType.CSS,
            iconChar = FontAwesome.Brands.Css3,
            lightColor = TailwindCssColors.blue400,
            darkColor = TailwindCssColors.sky400 // Using sky for dark to differentiate from other blues
        )
    }
    if (TreeSitterLanguage.Toml.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            fileType = FileType.Toml,
            iconChar = 'T',
            lightColor = TailwindCssColors.yellow600,
            darkColor = TailwindCssColors.yellow400
        )
    }
    if (TreeSitterLanguage.PHP.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            FileType.PHP,
            iconChar = FontAwesome.Brands.Php,
            lightColor = TailwindCssColors.purple600,
            darkColor = TailwindCssColors.purple400
        )
    }
    if (TreeSitterLanguage.Python.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            FileType.Python,
            iconChar = FontAwesome.Brands.Python,
            lightColor = TailwindCssColors.blue600,
            darkColor = TailwindCssColors.blue400
        )
    }
    if (TreeSitterLanguage.Java.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            FileType.Java,
            iconChar = FontAwesome.Brands.Java,
            lightColor = TailwindCssColors.orange600,
            darkColor = TailwindCssColors.orange400
        )
    }
    if (TreeSitterLanguage.Bash.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            FileType.Bash,
            iconChar = '$',
            lightColor = TailwindCssColors.green600,
            darkColor = TailwindCssColors.green400
        )
    }
    if (name.lowercase() == "dockerfile") {
        return FileTypeWithColor.CharIcon(
            FileType.Dockerfile,
            iconChar = FontAwesome.Brands.Docker,
            lightColor = TailwindCssColors.blue600,
            darkColor = TailwindCssColors.blue400
        )
    }
    if ("md" == extension) {
        return FileTypeWithColor.CharIcon(
            FileType.Markdown,
            iconChar = FontAwesome.Brands.Markdown,
            lightColor = TailwindCssColors.cyan600,
            darkColor = TailwindCssColors.cyan400
        )
    }
    if (listOf("mp4", "avi", "mkv", "mov", "webm").contains(extension)) {
        return FileTypeWithColor.VectorIcon(
            FileType.Video,
            Lucide.FileVideo,
            lightColor = TailwindCssColors.teal600,
            darkColor = TailwindCssColors.teal400
        )
    }
    if (TreeSitterLanguage.HTML.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            FileType.HTML,
            iconChar = FontAwesome.Brands.Html5,
            lightColor = TailwindCssColors.orange600,
            darkColor = TailwindCssColors.orange400
        )
    }
    if (TreeSitterLanguage.JavaScript.matchesExtension(extension)) {
        return FileTypeWithColor.CharIcon(
            FileType.JavaScript,
            iconChar = FontAwesome.Brands.Js,
            lightColor = TailwindCssColors.yellow600,
            darkColor = TailwindCssColors.yellow400
        )
    }
    return FileTypeWithColor.VectorIcon(
        FileType.Text,
        Lucide.FileText,
        lightColor = TailwindCssColors.emerald600,
        darkColor = TailwindCssColors.emerald400
    )
}

