package dima.apps.dired

import com.github.pgreze.process.process
import dima.utils.SimpleResult
import java.io.File

/**
 * Rotates an image file 90 degrees clockwise (to the right).
 */
suspend fun rotateRight(file: File): SimpleResult {
    return rotateImage(file, 90)
}

/**
 * Rotates an image file 90 degrees counter-clockwise (to the left).
 */
suspend fun rotateLeft(file: File): SimpleResult {
    return rotateImage(file, -90)
}

/**
 * Rotates an image file by the specified degrees.
 *
 * @param file The image file to rotate
 * @param degrees The rotation angle in degrees (+90 for right, -90 for left)
 * @return True if rotation was successful, false otherwise
 */
private suspend fun rotateImage(file: File, degrees: Int): SimpleResult {
    return try {
        val result = process(
            "magick",
            file.absolutePath,
            "-auto-orient",
            "-rotate",
            degrees.toString(),
            file.absolutePath
        )
        if (result.resultCode == 0) {
            SimpleResult.Success
        } else {
            SimpleResult.Error(result.output.joinToString("\n"))
        }
    } catch (e: Exception) {
        SimpleResult.Error(e.message ?: "Unknown error")
    }
}

