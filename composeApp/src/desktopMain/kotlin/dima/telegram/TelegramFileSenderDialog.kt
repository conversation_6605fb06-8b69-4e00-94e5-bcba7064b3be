package dima.telegram

import dima.apps.notifications.showLoadingNotification
import dima.utils.SimpleResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File

/**
 * Cache for recently used chat IDs for sending files.
 * Stored in TelegramFileSenderDialog.kt as per user request.
 */
internal object TelegramSendRecencyCache {
    private val cacheDir = File("cache/telegram")
    private val recentSendChatIdsFile = File(cacheDir, "recent_telegram_send_chats.json")
    private val lock = Any() // For thread safety

    init {
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }

    fun getRecentSendChatIds(): List<String> {
        synchronized(lock) {
            return if (recentSendChatIdsFile.exists()) {
                try {
                    Json.decodeFromString<List<String>>(recentSendChatIdsFile.readText())
                } catch (e: Exception) {
                    // Log error or handle corrupt file
                    println("Error reading recent send chat IDs cache: ${e.message}")
                    emptyList()
                }
            } else {
                emptyList()
            }
        }
    }

    fun rememberSendChatId(chatId: String) {
        synchronized(lock) {
            val currentRecents = getRecentSendChatIds().toMutableList()
            currentRecents.remove(chatId) // Remove if exists to move to front
            currentRecents.add(0, chatId) // Add to the beginning (most recent)

            // Limit cache size, e.g., to 50 recents
            val toSave = if (currentRecents.size > 50) currentRecents.take(50) else currentRecents
            try {
                recentSendChatIdsFile.writeText(Json.encodeToString(toSave))
            } catch (e: Exception) {
                // Log error or handle
                println("Error writing recent send chat IDs cache: ${e.message}")
            }
        }
    }
}


/**
 * Opens a dialog to select a Telegram contact and sends the file to the selected contact
 */
fun openTelegramFileSenderDialog(file: File) {
    openTelegramFileSenderDialog(listOf(file))
}

/**
 * Opens a dialog to select a Telegram contact and sends multiple files to the selected contact
 */
fun openTelegramFileSenderDialog(files: List<File>) {
    val title = if (files.size == 1) {
        "Send ${files.first().name} via Telegram"
    } else {
        "Send ${files.size} files via Telegram"
    }
    openTelegramChatSelectionDialog(title) { contact ->
        CoroutineScope(Dispatchers.IO).launch {
            val fileCount = files.size
            val fileText = if (fileCount == 1) {
                files.first().name
            } else {
                "$fileCount files"
            }
            val sendingNotification = showLoadingNotification(
                "Sending $fileText...",
                if (contact.isGroup) "Group: ${contact.name}" else contact.name
            )
            val result = TelegramFileSender.sendFiles(contact.id, files)
            when (result) {
                is SimpleResult.Error -> sendingNotification.toError("Failed to send files", result.error)
                SimpleResult.Success -> sendingNotification.toInfo(
                    "Sent successfully to Telegram",
                    durationMillis = 1000
                )
            }
        }
    }
}
