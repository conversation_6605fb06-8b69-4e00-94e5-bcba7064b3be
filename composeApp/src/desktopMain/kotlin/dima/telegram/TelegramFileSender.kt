package dima.telegram

import dima.process.LoggedProcess
import dima.settings
import dima.utils.SimpleResult
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.io.File
import kotlin.coroutines.resume

/**
 * Data class representing a Telegram contact
 */
@Serializable
data class TelegramContact(
    val id: String,
    val name: String,
    @SerialName("contact_type")
    val contactType: String,
    @SerialName("profile_pic")
    val profilePic: String? = null
) {
    /**
     * Determines if this contact is a group based on the contactType
     */
    val isGroup: Boolean
        get() = contactType.contains("group", ignoreCase = true) ||
                contactType.contains("channel", ignoreCase = true)
}

/**
 * Main class for handling Telegram file sending operations
 */
object TelegramFileSender {

    /**
     * Sends multiple files to a specified Telegram contact.
     */
    suspend fun sendFiles(userId: String, files: List<File>): SimpleResult {
        if (settings.python3BinaryFile == null) {
            return SimpleResult.Error("Python3 binary is not set in settings")
        }
        if (settings.telegram?.pythonFileSenderScriptFile == null) {
            return SimpleResult.Error("Python Telegram file sender script is not set in settings")
        }
        
        val missingFiles = files.filter { !it.exists() }
        if (missingFiles.isNotEmpty()) {
            val missingPaths = missingFiles.joinToString(", ") { it.absolutePath }
            return SimpleResult.Error("Files do not exist: $missingPaths")
        }
        
        val args = mutableListOf(
            settings.telegram.pythonFileSenderScriptFile.absolutePath,
            "--config", settings.telegram.pythonFileSenderScriptFile.parentFile.absolutePath + "/config.json",
            "--user-ids", userId,
            "--files"
        )
        args.addAll(files.map { it.absolutePath })
        
        return suspendCancellableCoroutine { continuation ->
            val loggedProcess = LoggedProcess(
                command = settings.python3BinaryFile.absolutePath,
                args = args,
                workingDirectory = settings.telegram.pythonFileSenderScriptFile.parentFile,
                showErrorNotifications = false,
                onFinish = { process ->
                    val result = if (process.exitCode.value == 0) {
                        SimpleResult.Success
                    } else {
                        SimpleResult.Error(process.getOutput())
                    }
                    continuation.resume(result)
                }
            )
            
            continuation.invokeOnCancellation {
                loggedProcess.kill()
            }
            
            loggedProcess.startAsync()
        }
    }
}