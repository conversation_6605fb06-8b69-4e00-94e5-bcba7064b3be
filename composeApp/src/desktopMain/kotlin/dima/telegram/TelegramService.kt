package dima.telegram

import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.settings
import dima.telegram.app.TelegramChatMessage
import dima.utils.Result
import dima.utils.addAll
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * Service for interacting with the Telegram Python script
 */
internal object TelegramService {
    private val json = Json { ignoreUnknownKeys = true }

    suspend fun getDatabaseContactsOrDownloadWithNotification(): Result<List<TelegramContact>> {
        val dbContacts: List<TelegramContact> = TelegramDatabase.getContactsFromDatabase()
        return if (dbContacts.isEmpty()) {
            val loadingNotification = showLoadingNotification("Downloading Telegram Contacts...")
            val contacts = downloadContactsFromApi(downloadProfilePics = false)
            loadingNotification.dismiss()
            contacts
        } else {
            Result.Success(dbContacts)
        }
    }

    /**
     * @param downloadProfilePics if true, downloads the profile pictures of the contacts. It is very slow!
     */
    suspend fun downloadContactsFromApi(downloadProfilePics: Boolean): Result<List<TelegramContact>> {
        if (settings.python3BinaryFile == null) {
            return Result.Error("Python3 binary is not set in settings")
        }
        if (settings.telegram?.pythonFileSenderScriptFile == null) {
            return Result.Error("Python Telegram file sender script is not set in settings")
        }
        return withContext(Dispatchers.IO) {
            try {
                val args = buildList {
                    addAll(
                        settings.telegram.pythonFileSenderScriptFile.absolutePath,
                        "--list-contacts",
                        "--config", settings.telegram.pythonFileSenderScriptFile.parentFile.absolutePath + "/config.json"
                    )
                    if (downloadProfilePics) {
                        add("--download-profile-pictures")
                    }
                }
                val process = ProcessBuilder(
                    settings.python3BinaryFile.absolutePath,
                    *args.toTypedArray()
                )
                    .directory(settings.telegram.pythonFileSenderScriptFile.parentFile)
                    .start()

                val reader = BufferedReader(InputStreamReader(process.inputStream))
                val output = StringBuilder()
                var line: String?

                while (reader.readLine().also { line = it } != null) {
                    output.append(line).append("\n")
                }

                val exitCode = process.waitFor()
                if (exitCode != 0) {
                    val errorOutput = BufferedReader(InputStreamReader(process.errorStream)).readText()
                    throw Exception("Failed to get contacts: $errorOutput")
                }
                val contacts = json.decodeFromString<List<TelegramContact>>(output.toString()).filter {
                    it.name.isNotBlank()
                }
                TelegramDatabase.saveContacts(contacts)
                Result.Success(contacts)
            } catch (e: Exception) {
                Result.Error(e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Fetches chat history from the database first, then updates from the API
     */
    suspend fun getChatHistory(contactId: String, limit: Int = 10): List<TelegramChatMessage> {
        // Initialize database
        TelegramDatabase.setup()

        // Get messages from database first
        val dbMessages = TelegramDatabase.getMessages(contactId, limit)

        // If we have messages in the database, return them immediately
        if (dbMessages.isNotEmpty()) {
            // Fetch fresh messages in the background
            refreshChatHistoryFromApi(contactId, limit)
            return dbMessages
        }

        // If no messages in database, fetch from API
        return fetchChatHistoryFromApi(contactId, limit)
    }

    /**
     * Fetches chat history from the API and updates the database
     */
    private suspend fun refreshChatHistoryFromApi(contactId: String, limit: Int = 10) {
        withContext(Dispatchers.IO) {
            try {
                val messages = fetchChatHistoryFromApi(contactId, limit)
                TelegramDatabase.saveMessages(contactId, messages)
            } catch (_: Exception) {
                // Silently fail as this is a background refresh
            }
        }
    }

    /**
     * Fetches chat history directly from the API
     */
    private suspend fun fetchChatHistoryFromApi(contactId: String, limit: Int = 10): List<TelegramChatMessage> {
        if (settings.python3BinaryFile == null) {
            showErrorNotification("Python3 binary is not set in settings")
            return emptyList()
        }
        if (settings.telegram?.pythonFileSenderScriptFile == null) {
            showErrorNotification("Python Telegram file sender script is not set in settings")
            return emptyList()
        }

        return withContext(Dispatchers.IO) {
            try {
                val process = ProcessBuilder(
                    settings.python3BinaryFile.absolutePath,
                    settings.telegram.pythonFileSenderScriptFile.absolutePath,
                    "--get-chat-history",
                    "--user-ids", contactId,
                    "--history-limit", limit.toString(),
                    "--config", settings.telegram.pythonFileSenderScriptFile.parentFile.absolutePath + "/config.json"
                )
                    .directory(settings.telegram.pythonFileSenderScriptFile.parentFile)
                    .start()

                val reader = BufferedReader(InputStreamReader(process.inputStream))
                val output = StringBuilder()
                var line: String?

                while (reader.readLine().also { line = it } != null) {
                    output.append(line).append("\n")
                }

                val exitCode = process.waitFor()
                if (exitCode != 0) {
                    val errorOutput = BufferedReader(InputStreamReader(process.errorStream)).readText()
                    throw Exception("Failed to get chat history: $errorOutput")
                }

                val messages = json.decodeFromString<List<TelegramChatMessage>>(output.toString())

                // Save to database
                TelegramDatabase.saveMessages(contactId, messages)

                messages
            } catch (e: Exception) {
                throw Exception("Failed to get chat history: ${e.message}")
            }
        }
    }

    /**
     * Sends a message to a specific contact and updates the database
     */
    suspend fun sendMessage(contactId: String, message: String): Boolean {
        if (settings.python3BinaryFile == null) {
            showErrorNotification("Python3 binary is not set in settings")
            return false
        }
        if (settings.telegram?.pythonFileSenderScriptFile == null) {
            showErrorNotification("Python Telegram file sender script is not set in settings")
            return false
        }

        return withContext(Dispatchers.IO) {
            try {
                val process = ProcessBuilder(
                    settings.python3BinaryFile.absolutePath,
                    settings.telegram.pythonFileSenderScriptFile.absolutePath,
                    "--user-ids", contactId,
                    "--message", message,
                    "--config", settings.telegram.pythonFileSenderScriptFile.parentFile.absolutePath + "/config.json"
                )
                    .directory(settings.telegram.pythonFileSenderScriptFile.parentFile)
                    .start()

                val exitCode = process.waitFor()
                if (exitCode != 0) {
                    val errorOutput = BufferedReader(InputStreamReader(process.errorStream)).readText()
                    throw Exception("Failed to send message: $errorOutput")
                }

                // After sending a message, refresh the chat history to include the new message
                refreshChatHistoryFromApi(contactId)

                true
            } catch (e: Exception) {
                throw Exception("Failed to send message: ${e.message}")
            }
        }
    }
}
