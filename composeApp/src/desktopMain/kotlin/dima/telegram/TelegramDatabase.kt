package dima.telegram

import dima.database.transactionToAvoidBusySqlite
import dima.telegram.app.TelegramChatMessage
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Database for storing Telegram contacts and messages
 */
internal object TelegramDatabase {
    private var database: Database? = null
    private const val RELATIVE_CACHE_DIRECTORY = "cache/telegram"
    private val cacheDirectory = File(RELATIVE_CACHE_DIRECTORY)

    object ContactsTable : Table("TelegramContacts") {
        val id = text("id").uniqueIndex()
        val name = text("name")
        val contactType = text("contact_type")
        val profilePic = text("profile_pic").nullable()
        val lastUpdated = text("last_updated")

        override val primaryKey = PrimaryKey(id)
    }

    object MessagesTable : Table("TelegramMessages") {
        val id = long("id")
        val contactId = text("contact_id").references(ContactsTable.id)
        val text = text("text").nullable()
        val date = text("date")
        val out = bool("out")
        val fromId = text("from_id").nullable()
        val toId = text("to_id").nullable()
        val media = bool("media")
        val replyToMsgId = long("reply_to_msg_id").nullable()

        override val primaryKey = PrimaryKey(id, contactId)
    }

    /**
     * Set up the database connection and create tables if they don't exist
     */
    fun setup() {
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        if (database != null) {
            return
        }
        val dbDir = File(cacheDirectory, "db")
        if (!dbDir.exists()) {
            dbDir.mkdirs()
        }
        val dbFile = File(dbDir, "telegram.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")

        transaction(database) {
            SchemaUtils.create(ContactsTable, MessagesTable)
        }
    }

    /**
     * Save contacts to the database
     */
    fun saveContacts(contacts: List<TelegramContact>) {
        if (database == null) setup()

        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME)

        transactionToAvoidBusySqlite(database) {
            contacts.forEach { contact ->
                ContactsTable.upsert(ContactsTable.id) {
                    it[id] = contact.id
                    it[name] = contact.name
                    it[contactType] = contact.contactType
                    it[profilePic] = contact.profilePic
                    it[lastUpdated] = timestamp
                }
            }
        }
    }

    fun getContactsFromDatabase(): List<TelegramContact> {
        if (database == null) {
            setup()
        }
        return transactionToAvoidBusySqlite(database) {
            ContactsTable.selectAll().map { row ->
                TelegramContact(
                    id = row[ContactsTable.id],
                    name = row[ContactsTable.name],
                    contactType = row[ContactsTable.contactType],
                    profilePic = row[ContactsTable.profilePic]
                )
            }
        }
    }

    fun getContact(id: String): TelegramContact? {
        if (database == null) {
            setup()
        }
        return transactionToAvoidBusySqlite(database) {
            val result = ContactsTable.selectAll().where { ContactsTable.id eq id }.firstOrNull()
            if (result == null) {
                return@transactionToAvoidBusySqlite null
            }
            return@transactionToAvoidBusySqlite TelegramContact(
                id = result[ContactsTable.id],
                name = result[ContactsTable.name],
                contactType = result[ContactsTable.contactType],
                profilePic = result[ContactsTable.profilePic]
            )
        }
    }

    /**
     * Save messages for a specific contact to the database
     */
    fun saveMessages(contactId: String, messages: List<TelegramChatMessage>) {
        if (database == null) setup()

        transactionToAvoidBusySqlite(database) {
            messages.forEach { message ->
                MessagesTable.upsert(MessagesTable.id, MessagesTable.contactId) {
                    it[id] = message.id
                    it[this.contactId] = contactId
                    it[text] = message.text
                    it[date] = message.date
                    it[out] = message.out
                    it[fromId] = message.fromId
                    it[toId] = message.toId
                    it[media] = message.media
                    it[replyToMsgId] = message.replyToMsgId
                }
            }
        }
    }

    /**
     * Get messages for a specific contact from the database
     */
    fun getMessages(contactId: String, limit: Int = 100): List<TelegramChatMessage> {
        if (database == null) setup()

        return transactionToAvoidBusySqlite(database) {
            MessagesTable.selectAll().where { MessagesTable.contactId eq contactId }
                .orderBy(MessagesTable.date, SortOrder.DESC)
                .limit(limit)
                .map { row ->
                    TelegramChatMessage(
                        id = row[MessagesTable.id],
                        text = row[MessagesTable.text],
                        date = row[MessagesTable.date],
                        out = row[MessagesTable.out],
                        fromId = row[MessagesTable.fromId],
                        toId = row[MessagesTable.toId],
                        media = row[MessagesTable.media],
                        replyToMsgId = row[MessagesTable.replyToMsgId]
                    )
                }
        }
    }
}
