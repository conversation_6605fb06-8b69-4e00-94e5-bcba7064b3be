package dima.telegram.app

import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import GlobalStyling
import dima.color.TailwindCssColors
import dima.dateTime.formatDateTimeRelative
import dima.images.CustomAsyncImage
import dima.images.ImageDisplaySize
import dima.settings
import dima.telegram.TelegramContact
import dima.telegram.TelegramDatabase
import dima.utils.scrollbarStyleThemed

@Composable
internal fun ReadOnlyChatArea(
    chatContact: TelegramContact?,
    isLoading: Boolean,
    messages: List<TelegramChatMessage>,
    messagesListState: LazyListState,
    selectedMessageId: Long?
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(GlobalStyling.paddingToOuterWindow)
    ) {
        // Messages area
        Box(
            modifier = Modifier
                .fillMaxSize() // Changed from weight(1f)
        ) {
            if (chatContact == null) {
                Text(
                    "Select a chat to view messages",
                    modifier = Modifier.align(Alignment.Center),
                    color = GlobalStyling.getTextColor()
                )
            } else if (isLoading && messages.isEmpty()) {
                Text(
                    "Loading messages...",
                    modifier = Modifier.align(Alignment.Center),
                    color = GlobalStyling.getTextColor()
                )
            } else {
                Box(modifier = Modifier.fillMaxSize()) {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(30.dp),
                        state = messagesListState,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .padding(end = 12.dp)
                            .fillMaxSize()
                    ) {
                        items(messages.reversed()) { message ->
                            Box(
                                modifier = Modifier.widthIn(max = 900.dp)
                            ) {
                                MessageItem(
                                    chatContact = chatContact,
                                    message = message,
                                    isSelected = message.id == selectedMessageId
                                )
                            }
                        }
                    }
                    VerticalScrollbar(
                        adapter = rememberScrollbarAdapter(scrollState = messagesListState),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .fillMaxHeight(),
                        style = scrollbarStyleThemed()
                    )
                }
            }
        }
        // Input area removed
    }
}

@Composable
internal fun MessageItem(
    chatContact: TelegramContact?,
    message: TelegramChatMessage,
    isSelected: Boolean = false
) {
    val contactsMap: Map<String, TelegramContact> =
        remember { TelegramDatabase.getContactsFromDatabase().associateBy { it.id } }
    println(message)
    val profilePic = if (message.out) {
        contactsMap[settings.telegram?.ownId.toString()]?.profilePic
    } else {
        chatContact?.profilePic
    }
    val senderName = if (message.out) {
        contactsMap[settings.telegram?.ownId.toString()]?.name ?: "You"
    } else {
        chatContact?.name ?: "Unknown"
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (isSelected) {
                    Modifier.background(
                        GlobalStyling.getSelectedBackgroundColor(),
                        GlobalStyling.smallRoundedCorners
                    )
                } else {
                    Modifier
                }
            )
            .padding(start = 16.dp, top = 8.dp, bottom = 8.dp, end = 16.dp)
    ) {
        if (profilePic == null) {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(TailwindCssColors.gray400, shape = CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = senderName.take(1), // Show the first initial if no profile pic
                    color = TailwindCssColors.white
                )
            }
        } else {
            CustomAsyncImage(
                fileSystemUrl = profilePic,
                displaySize = ImageDisplaySize(80, 80),
                modifier = Modifier.size(40.dp).clip(CircleShape)
            )
        }

        Column(
            modifier = Modifier
                .padding(start = 8.dp)
                .fillMaxWidth()
        ) {
            Row(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = senderName,
                    fontSize = 13.sp,
                    color = if (message.out) TailwindCssColors.orange600 else TailwindCssColors.gray800,
                    modifier = Modifier
                        .weight(1f)
                        .padding(bottom = 4.dp, end = 8.dp)
                )
                Text(
                    text = message.dateTime.formatDateTimeRelative(),
                    fontSize = 12.sp,
                    textAlign = TextAlign.End,
                    color = GlobalStyling.getTextColor(),
                    modifier = Modifier
                        .width(80.dp)
                        .padding(end = 8.dp)
                )
            }

            // Show reply indicator if this is a reply to another message
            if (message.replyToMsgId != null) {
                Box(
                    modifier = Modifier
                        .background(TailwindCssColors.gray200, RoundedCornerShape(4.dp))
                        .padding(8.dp)
                ) {
                    Text(
                        text = "Replying to message #${message.replyToMsgId}",
                        fontSize = 12.sp,
                        color = TailwindCssColors.gray600,
                    )
                }
                Spacer(modifier = Modifier.height(4.dp))
            }

            if (message.text != null && message.text.isNotEmpty()) {
                Text(
                    text = message.text,
                    fontSize = 14.sp,
                    color = GlobalStyling.getTextColor()
                )
            }

            // Media indicator
            if (message.media) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "[Media attachment]",
                    fontSize = 12.sp,
                    color = TailwindCssColors.blue600,
                )
            }
        }
    }
}
