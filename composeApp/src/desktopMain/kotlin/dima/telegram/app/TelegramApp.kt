package dima.telegram.app

import GlobalEvent
import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.*
import dialogs
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.clipboard.setClipboard
import dima.dialogs.help.appKeys
import dima.events.DimaEvent
import dima.events.collectEvent
import dima.globalState.GlobalState
import dima.globalState.PaneState
import dima.globalState.TelegramAppState
import dima.globalState.updatePaneState
import dima.telegram.TelegramContact
import dima.telegram.TelegramDatabase
import dima.telegram.TelegramService
import dima.telegram.openTelegramChatSelectionDialog
import dima.utils.*
import globalEvent
import handleLostFocus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

data class ChangeTelegramAppChatEvent(val id: String) : DimaEvent

@Composable
fun TelegramApp(state: TelegramAppState, paneState: PaneState) {
    remember { TelegramDatabase.setup() }
    val appFocusRequester = remember { FocusRequester() }
    var contactId by remember { mutableStateOf(state.selectedContactId) }
    var messages by remember { mutableStateOf<List<TelegramChatMessage>>(emptyList()) }
    var isLoading by remember { mutableStateOf(false) }
    var selectedMessageId by remember { mutableStateOf(state.selectedMessageId) }
    val messagesListState = rememberLazyListState()
    val scope = rememberCoroutineScope()

    // Scroll to keep selected message in view
    // Visual offset scroll function like NotificationApp
    suspend fun scrollToSelectedMessage(smooth: Boolean = true) {
        if (selectedMessageId == null || messages.isEmpty()) return

        val selectedIndex = messages.indexOfFirst { it.id == selectedMessageId }
        if (selectedIndex < 0) return

        val reversedIndex = messages.size - 1 - selectedIndex
        val offset = (-GlobalState.mainWindow.heightInDp / 2).toInt()

        if (smooth) {
            messagesListState.animateScrollToItem(reversedIndex, scrollOffset = offset)
        } else {
            messagesListState.scrollToItem(reversedIndex, scrollOffset = offset)
        }
    }

    /**
     * Load chat history from the Python script
     */
    fun loadChatHistory(
        contactId: String,
        scope: CoroutineScope,
        onResult: (List<TelegramChatMessage>) -> Unit
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                isLoading = true
                val notification = showLoadingNotification("Loading chat history", "Please wait...")
                val messagesResult = TelegramService.getChatHistory(contactId) // This already uses DB first
                notification.dismiss()
                isLoading = false
                onResult(messagesResult)
            } catch (e: Exception) {
                isLoading = false
                showErrorNotification("Failed to load chat history", e.message)
                onResult(emptyList())
            }
        }
    }

    remember {
        appKeys = listOf(
            AppKey(Key.C, "Go one message up", onKeyDown = {
                if (messages.isNotEmpty()) {
                    val currentIndex = messages.indexOfFirst { it.id == selectedMessageId }
                    if (currentIndex >= 0 && currentIndex < messages.size - 1) {
                        selectedMessageId = messages[currentIndex + 1].id
                    } else if (selectedMessageId == null && messages.isNotEmpty()) {
                        selectedMessageId = messages.first().id
                    }
                }
            }),
            AppKey(Key.T, "Go one message down", onKeyDown = {
                if (messages.isNotEmpty()) {
                    val currentIndex = messages.indexOfFirst { it.id == selectedMessageId }
                    if (currentIndex > 0) {
                        selectedMessageId = messages[currentIndex - 1].id
                    } else if (selectedMessageId == null && messages.isNotEmpty()) {
                        selectedMessageId = messages.first().id
                    }
                }
            }),
            AppKey(Key.M, "Go 6 messages down", onKeyDown = {
                if (messages.isNotEmpty()) {
                    val currentIndex = messages.indexOfFirst { it.id == selectedMessageId }
                    if (currentIndex >= 0) {
                        val newIndex = (currentIndex - 6).coerceAtLeast(0)
                        selectedMessageId = messages[newIndex].id
                    } else if (selectedMessageId == null && messages.isNotEmpty()) {
                        selectedMessageId = messages.first().id
                    }
                }
            }),
            AppKey(Key.V, "Go 6 messages up", onKeyDown = {
                if (messages.isNotEmpty()) {
                    val currentIndex = messages.indexOfFirst { it.id == selectedMessageId }
                    if (currentIndex >= 0) {
                        val newIndex = (currentIndex + 6).coerceAtMost(messages.size - 1)
                        selectedMessageId = messages[newIndex].id
                    } else if (selectedMessageId == null && messages.isNotEmpty()) {
                        selectedMessageId = messages.first().id
                    }
                }
            }),
            AppKey(Key.J, "Copy selected message", onKeyUp = {
                val selectedMessage = messages.find { it.id == selectedMessageId }
                if (selectedMessage?.text?.isNotEmpty() == true) {
                    setClipboard(selectedMessage.text)
                    showNotification(
                        "Message copied to clipboard",
                        selectedMessage.text.truncateWithEllipsis(90)
                    )
                } else {
                    showNotification("No message selected or no text to copy")
                }
            }),
            AppKey(Key.U, "Open chat picker to switch chat", onKeyUp = {
                openTelegramChatSelectionDialog("Select Chat") { selectedContact ->
                    contactId = selectedContact.id
                    GlobalState.telegram = GlobalState.telegram.copy(selectedContactId = selectedContact.id)
                    val updatedState = state.copy(selectedContactId = selectedContact.id)
                    updatePaneState(paneState.id, updatedState)
                }
            })
        )
    }

    LaunchedEffect(contactId) {
        val updatedState = state.copy(selectedContactId = contactId)
        updatePaneState(paneState.id, updatedState)
    }

    // Effect for loading chat history when contactId changes
    LaunchedEffect(contactId) {
        val currentContactId = contactId
        if (currentContactId != null) {
            // Preserve current selection when switching chats
            val previousSelection = selectedMessageId
            
            // Immediately clear old messages and load cached ones
            messages = emptyList()
            selectedMessageId = null

            // Load cached messages instantly
            val cachedMessages = TelegramDatabase.getMessages(currentContactId)
            if (cachedMessages.isNotEmpty()) {
                messages = cachedMessages
                // Only auto-select if we don't have a previous selection or if this is the first load
                if (previousSelection == null) {
                    selectedMessageId = cachedMessages.first().id
                    // Scroll to bottom for cached messages only if no previous selection
                    scope.launch {
                        messagesListState.animateScrollToItem(0) // 0 because messages are reversed
                    }
                } else {
                    // If we have a previous selection, ensure we scroll to it once messages are loaded
                    scope.launch {
                        kotlinx.coroutines.delay(50) // Small delay to ensure messages are rendered
                        scrollToSelectedMessage(smooth = false)
                    }
                }
            }

            // Load fresh chat history from API
            loadChatHistory(currentContactId, scope) { loadedMessages ->
                val wasEmpty = messages.isEmpty()
                val currentSelection = selectedMessageId
                messages = loadedMessages

                // Only auto-select if we don't have a current selection or if this was the first load
                if (loadedMessages.isNotEmpty()) {
                    if (currentSelection == null && previousSelection == null) {
                        // First time loading and no previous selection - go to newest message
                        selectedMessageId = loadedMessages.first().id
                        // Auto-scroll to bottom if this was the first time loading messages
                        if (wasEmpty) {
                            scope.launch {
                                messagesListState.animateScrollToItem(0) // 0 because messages are reversed
                            }
                        }
                    } else if (currentSelection != null) {
                        // Preserve existing selection if the message still exists
                        if (loadedMessages.any { it.id == currentSelection }) {
                            selectedMessageId = currentSelection
                            // Ensure we scroll to the preserved selection
                            scope.launch {
                                kotlinx.coroutines.delay(50)
                                scrollToSelectedMessage(smooth = false)
                            }
                        } else {
                            // Selected message no longer exists, go to newest
                            selectedMessageId = loadedMessages.first().id
                        }
                    }
                    // If previousSelection exists but currentSelection is null, don't auto-select anything
                }
            }
        } else {
            // No contact selected, clear messages
            messages = emptyList()
            selectedMessageId = null
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                if (it.type == KeyEventType.KeyDown && it.key == Key.Escape) {
                    appFocusRequester.requestFocus()
                    // isTextFieldFocused = false // Removed
                    return@onPreviewKeyEvent true
                }
                // Removed Enter key logic for sending message and focusing input
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        DummyFocusable()
        val currentContact: TelegramContact? = contactId?.let { TelegramDatabase.getContact(it) }
        Row(
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .background(GlobalStyling.getWindowBackgroundColor())
        ) {
            if (currentContact == null) {
                ChatHeaderNothingSelected()
            } else {
                ChatHeader(contact = currentContact)
            }
        }

        ReadOnlyChatArea(
            chatContact = currentContact,
            isLoading = isLoading,
            messages = messages,
            messagesListState = messagesListState,
            selectedMessageId = selectedMessageId
        )
    }

    LaunchedEffect(Unit) {
        // Initialize contactId from GlobalState
        contactId = GlobalState.telegram.selectedContactId
        appFocusRequester.requestFocus()
    }

    // Also watch for changes in GlobalState.telegram.selectedContactId
    LaunchedEffect(GlobalState.telegram.selectedContactId) {
        contactId = GlobalState.telegram.selectedContactId
    }

    LaunchedEffect(selectedMessageId) {
        if (selectedMessageId != null && messages.isNotEmpty()) {
            scrollToSelectedMessage(smooth = true)
        }
        val updatedState = state.copy(selectedMessageId = selectedMessageId)
        updatePaneState(paneState.id, updatedState)
    }
    // Auto-scroll to bottom when opening TelegramApp
    LaunchedEffect(Unit) {
        // Always go to most recent message when opening app
        if (messages.isNotEmpty()) {
            selectedMessageId = messages.first().id
            scrollToSelectedMessage(smooth = false)
        }
    }

    LaunchedEffect(Unit) {
        collectEvent<ChangeTelegramAppChatEvent> { event ->
            val contact = TelegramDatabase.getContact(event.id)
            if (contact == null) {
                showErrorNotification("Failed to change chat", "Contact not found")
            } else {
                contactId = event.id
                GlobalState.telegram = GlobalState.telegram.copy(selectedContactId = event.id)
                val updatedState = state.copy(selectedContactId = event.id)
                updatePaneState(paneState.id, updatedState)
            }
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.Reload -> {
                val currentContactId = contactId
                if (currentContactId == null) {
                    showErrorNotification("No chat selected to reload.")
                    return@LaunchedEffectGlobalEventForApps
                }
                // Preserve current selection during reload
                val currentSelection = selectedMessageId
                loadChatHistory(currentContactId, scope) { loadedMessages ->
                    messages = loadedMessages
                    // Restore previous selection if it still exists, otherwise keep current
                    if (currentSelection != null && loadedMessages.any { it.id == currentSelection }) {
                        selectedMessageId = currentSelection
                    }
                    // Don't auto-select anything if we had a valid selection
                }
            }

            // ScrollToTop goes to newest messages (first in DESC order)
            GlobalEvent.ScrollToTop -> {
                if (messages.isNotEmpty()) {
                    selectedMessageId = messages.first().id
                }
            }

            // ScrollToBottom goes to oldest messages (last in DESC order)
            GlobalEvent.ScrollToBottom -> {
                if (messages.isNotEmpty()) {
                    selectedMessageId = messages.last().id
                }
            }

            else -> {}
        }
    }
}
