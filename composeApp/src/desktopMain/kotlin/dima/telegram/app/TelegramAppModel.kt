package dima.telegram.app

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Serializable
data class TelegramAppState(
    val selectedContactId: String? = null,
    val inputTextPerChat: Map<String, String> = emptyMap() // Changed from inputText: String
)

/**
 * Data class representing a chat message
 */
@Serializable
internal data class TelegramChatMessage(
    val id: Long,
    val text: String?,
    val date: String,
    val out: Boolean,
    @SerialName("from_id")
    val fromId: String? = null,
    @SerialName("to_id")
    val toId: String? = null,
    val media: Boolean = false,
    @SerialName("reply_to_msg_id")
    val replyToMsgId: Long? = null
) {
    val dateTime: LocalDateTime
        get() = LocalDateTime.parse(date, DateTimeFormatter.ISO_DATE_TIME)
}
