package dima.telegram.app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import GlobalStyling
import dima.color.TailwindCssColors
import dima.images.CustomAsyncImage
import dima.telegram.TelegramContact

@Composable
internal fun ChatHeader(contact: TelegramContact) {
    Row(
        modifier = Modifier
            .height(56.dp)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        if (contact.profilePic == null) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(40.dp)
                    .background(TailwindCssColors.blue200, CircleShape)
            ) {
                Text(
                    text = contact.name.take(1).uppercase(),
                    color = TailwindCssColors.blue800,
                    fontWeight = FontWeight.Bold
                )
            }
        } else {
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
            ) {
                CustomAsyncImage(
                    fileSystemUrl = contact.profilePic,
                    modifier = Modifier
                        .fillMaxSize()
                )
            }
        }
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = contact.name,
            fontWeight = FontWeight.Bold,
            fontSize = 18.sp,
            color = GlobalStyling.getTextColor()
        )
    }
}

@Composable
internal fun ChatHeaderNothingSelected() {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .height(56.dp)
    ) {
        Text(
            "No chat selected",
            color = GlobalStyling.getTextColor()
        )
    }
}