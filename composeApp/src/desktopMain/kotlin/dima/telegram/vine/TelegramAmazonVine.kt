package dima.telegram.vine

import com.github.kotlintelegrambot.bot
import com.github.kotlintelegrambot.entities.ChatId
import com.github.kotlintelegrambot.entities.ParseMode
import dima.apps.amazonVine.AmazonVine
import dima.settings
import dima.utils.SimpleResult

fun AmazonVine.Article.sendToTelegramGroup(): SimpleResult {
    if (settings.telegram?.botToken == null) {
        return SimpleResult.Error("Telegram bot token is not set in settings")
    }
    if (settings.telegram.amazonVineGroupId == null) {
        return SimpleResult.Error("Telegram Amazon Vine group ID is not set in settings")
    }
    val bot = bot {
        token = settings.telegram.botToken
    }
    val price = if (taxPrice == "€0.00") {
        "✅ kostenlos ✅"
    } else taxPrice.ifBlank {
        "???"
    }
    val ratingText = if (this.ratingStar == null || this.ratingTotal == null) {
        ""
    } else {
        if (this.ratingTotal == 1) {
            "${this.ratingStar} / 5.0   ${this.ratingTotal} Bewertung"
        } else {
            "${this.ratingStar} / 5.0   ${this.ratingTotal} Bewertungen"
        }
    }
    val parts = listOf(
        "<b>$price</b>",
        ratingText,
        category,
        this.link
    ).filter { it.isNotBlank() }
    val telegramVineGroupId = ChatId.fromId(settings.telegram.amazonVineGroupId)
    bot.sendMessage(
        telegramVineGroupId,
        text = parts.joinToString("\n"),
        parseMode = ParseMode.HTML
    )
    return SimpleResult.Success
}