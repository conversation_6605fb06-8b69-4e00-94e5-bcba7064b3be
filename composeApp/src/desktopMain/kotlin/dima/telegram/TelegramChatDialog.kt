package dima.telegram

import Globals
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.unit.dp
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dialogs.completion.ChangeCompletionDialogCandidates
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.CompletionDialogCmdAction
import dima.dialogs.completion.openCompletionDialog
import dima.events.emitEasy
import dima.images.CustomAsyncImage
import dima.utils.Result
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

private fun sortContactsByRecency(contacts: List<TelegramContact>): List<TelegramContact> {
    val recentChatIds = TelegramSendRecencyCache.getRecentSendChatIds()
    if (recentChatIds.isEmpty()) {
        return contacts
    }
    return contacts.sortedWith(compareBy { contact ->
        val index = recentChatIds.indexOf(contact.id)
        if (index == -1) Int.MAX_VALUE else index // Cached items first, in order of recency
    })
}

fun openTelegramChatSelectionDialog(dialogTitle: String, onSelect: (TelegramContact) -> Unit) {
    Globals.coroutineScope.launch(Dispatchers.IO) {
        var contacts: List<TelegramContact> = TelegramDatabase.getContactsFromDatabase()
        if (contacts.isEmpty()) {
            val downloadedContacts = TelegramService.getDatabaseContactsOrDownloadWithNotification()
            when (downloadedContacts) {
                is Result.Error -> {
                    showErrorNotification("Failed to load contacts. Please try again.")
                    return@launch
                }

                is Result.Success -> contacts = downloadedContacts.value
            }
        }
        // Sort contacts after fetching or loading from DB
        contacts = sortContactsByRecency(contacts)

        val candidates = contacts.mapIndexed { _, contact -> // index is not used here, but kept for consistency
            CompletionDialogCandidate(
                text = contact.name,
                prefixView = { modifier ->
                    Row(modifier = Modifier.width(24.dp)) {
                        if (contact.profilePic != null) {
                            CustomAsyncImage(
                                fileSystemUrl = contact.profilePic,
                                modifier = modifier.size(24.dp).clip(CircleShape)
                            )
                        }
                    }
                },
                additionalSearchString = contact.id // Use ID for potential filtering if needed
            )
        }
        openCompletionDialog(
            title = dialogTitle,
            candidates = candidates,
            cmdActions = listOf(
                CompletionDialogCmdAction(Key.R, "Download new contacts", closeDialog = false) {
                    Globals.coroutineScope.launch(Dispatchers.IO) {
                        val loadingNotification = showLoadingNotification(
                            "Reloading Telegram Contacts...",
                            "This takes a while because downloading profile pictures is slow."
                        )
                        val downloadedContactsResult =
                            TelegramService.downloadContactsFromApi(downloadProfilePics = true)
                        loadingNotification.dismiss()
                        when (downloadedContactsResult) {
                            is Result.Error -> showErrorNotification(downloadedContactsResult.error)
                            is Result.Success -> {
                                // Sort the reloaded contacts
                                contacts = sortContactsByRecency(downloadedContactsResult.value)
                                // Update the dialog with new (and sorted) contacts
                                val newCandidates: List<CompletionDialogCandidate> =
                                    contacts.map { downloadedContact ->
                                        CompletionDialogCandidate(
                                            text = downloadedContact.name,
                                            prefixView = { modifier ->
                                                Row(modifier = Modifier.width(24.dp)) {
                                                    if (downloadedContact.profilePic != null) {
                                                        CustomAsyncImage(
                                                            fileSystemUrl = downloadedContact.profilePic,
                                                            modifier = modifier.size(24.dp).clip(CircleShape)
                                                        )
                                                    }
                                                }
                                            },
                                            additionalSearchString = downloadedContact.id
                                        )
                                    }
                                ChangeCompletionDialogCandidates(it.dialogId, newCandidates).emitEasy()
                            }
                        }
                    }
                }
            ),
            onAccept = {
                val selectedContact =
                    contacts.find { contact -> contact.name == it.text && contact.id == (candidates[it.index!!].additionalSearchString) }
                if (selectedContact == null) {
                    showErrorNotification("Error: Could not find selected contact.")
                } else {
                    TelegramSendRecencyCache.rememberSendChatId(selectedContact.id)
                    // The `rememberSendChatId` call is now in TelegramFileSenderDialog.kt itself,
                    // which is called after this `onSelect` callback.
                    onSelect(selectedContact)
                }
            }
        )
    }
}
