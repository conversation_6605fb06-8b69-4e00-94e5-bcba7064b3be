package dima.obsidianNotes

import androidx.compose.material.Text
import androidx.compose.ui.text.font.FontWeight
import dima.apps.dired.openFileInDired
import dima.apps.notifications.showErrorNotification
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.settings
import java.io.File
import java.nio.file.Files
import java.nio.file.Files.walk
import java.nio.file.Paths

object ObsidianNotes {

    fun browseTo() {
        val filePaths = mutableListOf<String>()
        val dirPath: java.nio.file.Path = Paths.get(settings.obsidianPath)
        val files: MutableList<java.nio.file.Path>? = walk(dirPath)
            .filter { Files.isRegularFile(it) && it.toString().endsWith(".md") }
            .toList()
        if (files == null) {
            showErrorNotification("No files found in Obsidian vault?")
            return
        }
        for (file in files) {
            filePaths.add(file.toString())
        }
        val candidates = filePaths.map { filePath ->
            val path = filePath.removePrefix(settings.obsidianPath)
            if (path.contains("/")) {
                CompletionDialogCandidate(
                    path.substringAfterLast("/"),
                    prefixView = {
                        Text(
                            path.substringBeforeLast("/"),
                            fontWeight = FontWeight.SemiBold,
                        )
                    })
            } else {
                CompletionDialogCandidate(
                    path.substringAfterLast("/"),
                )
            }
        }
        openCompletionDialog("Open note", candidates) {
            File(filePaths[it.index!!]).openFileInDired()
        }
    }
}