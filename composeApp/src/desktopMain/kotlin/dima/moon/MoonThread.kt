package dima.moon

import dima.globalState.GlobalState
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.time.Duration.Companion.minutes

// cross-checked for accuracy with
// https://www.webexhibits.org/calendars/moon.html
private fun currentMoonPhaseEmoji(date: LocalDate): String {
    val moonPhases = arrayOf("🌑", "🌒", "🌓", "🌔", "🌕", "🌖", "🌗", "🌘")
    // Lunar cycle: approximately 29.5 days
    val lunarCycleDays = 29.53
    // A known recent new moon date (example: January 2, 2022).
    // replace this with a recent new moon date for more accurate results.
    val recentNewMoon = LocalDate.of(2022, 1, 2)
    // calculate days since the last new moon
    val daysSinceNewMoon = ChronoUnit.DAYS.between(recentNewMoon, date).toDouble()
    // determine the current phase index based on days since last new moon
    val phaseIndex = (daysSinceNewMoon % lunarCycleDays / (lunarCycleDays / moonPhases.size)).toInt()
    return moonPhases.getOrNull(phaseIndex) ?: "🌑"
}

class MoonThread : Thread() {
    override fun run() {
        val sleepDuration = 30.minutes.inWholeMilliseconds
        while (true) {
            GlobalState.moon = currentMoonPhaseEmoji(LocalDate.now())
            sleep(sleepDuration)
        }
    }
}
