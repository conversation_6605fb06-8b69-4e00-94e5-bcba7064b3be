package dima.webdriver

import kotlin.time.Duration.Companion.minutes

/**
 * Kills zombie WebDriver related processes which happens on stopping the Kotlin Emacs process forcibly,
 * like when stopping inside IntelliJ.
 */
class WebDriverCleanupThread : Thread() {

    override fun run() {
        val sleepDuration = 10.minutes.inWholeMilliseconds
        while (true) {
            val processes = ProcessHandle.allProcesses()
            val processesToKill = mutableListOf<Long>()
            processes.forEach { process ->
                val info = process.info()
                val command = info.command()
                if (command.isEmpty) {
                    return@forEach
                }
                val parentProcessHandle = process.parent()
                if (parentProcessHandle.isEmpty) {
                    return@forEach
                }
                val parentPid = parentProcessHandle.get().pid()
                // zombie processes have parent PID 1
                if (parentPid != 1L) {
                    return@forEach
                }
                val name = info.command().get()
                if (!(name.contains("chromedriver")
                            || name.contains("geckodriver")
                            || name.contains("Firefox.app")
                            || name.contains("Google Chrome.app"))
                ) {
                    return@forEach
                }
                val pid = process.pid()
                processesToKill.add(pid)
            }
            if (processesToKill.isNotEmpty()) {
                ProcessBuilder("kill", "-9", *(processesToKill.map { it.toString() }.toTypedArray())).start().waitFor()
            }
            sleep(sleepDuration)
        }
    }
}