package dima.server

import Globals
import com.github.pgreze.process.process
import dima.ai.singlePromptApp.launchAiSinglePromptApp
import dima.ai.transcribe.GlobalTranscribe
import dima.apps.dired.openFileInDired
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.notifications.showErrorNotification
import dima.audio.Audio
import dima.audio.RustAudioEngine
import dima.audio.RustAudioStatus
import dima.settings
import dima.utils.JsonIgnoreUnknown
import dima.utils.abbreviatePath
import io.javalin.Javalin
import io.javalin.config.JavalinConfig
import io.javalin.plugin.Plugin
import io.javalin.plugin.bundled.DevLoggingPlugin.Config
import io.ktor.util.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import java.util.function.Consumer

private class NetworkActivityLoggingPlugin(userConfig: Consumer<Config>? = null) :
    Plugin<Config>(userConfig, Config()) {

    override fun onInitialize(config: JavalinConfig) {
        config.requestLogger.http { ctx, _ ->
            NetworkActivity.addFakedEntry(
                text = "Incoming request",
                status = FakedNetworkActivityStatus.Info,
                fullUrl = "http://localhost:" + settings.serverPort + ctx.path(),
                requestBody = ctx.body().ifBlank { null },
                responseBody = ctx.result(),
                requestHeaders = StringValues.build {
                    ctx.headerMap().forEach { (key, value) ->
                        append(key, value)
                    }
                },
                responseHeaders = StringValues.build {
                    val res = ctx.res()
                    res.headerNames.forEach {
                        append(it, res.getHeader(it))
                    }
                },
                statusCode = ctx.res().status
            )
        }
    }

}

/**
 * Use [Globals.coroutineScope] to avoid this crash (happened often in ChatGpt):
 * Reading a state that was created after the snapshot was taken or in a snapshot that has not yet been applied
 */
class JavalinServerThread : Thread() {

    override fun run() {
        Javalin
            .create {
                it.showJavalinBanner = false
                it.startupWatcherEnabled = false
                it.registerPlugin(NetworkActivityLoggingPlugin())
                it.http.maxRequestSize = 10 * 1024 * 1024 // Increase max request size to 10MB
            }
            .post("/chatgpt") { api ->
                launchAiSinglePromptApp(api.body().trim())
            }
            .post("/dired") { path ->
                Globals.coroutineScope.launch {
                    val file = File(path.body().trim())
                    if (!file.exists()) {
                        showErrorNotification("POST dired requested missing path", file.abbreviatePath())
                        return@launch
                    }
                    file.openFileInDired()
                }
            }
            .get("/focus-window") {
                runBlocking {
                    val pid = ProcessHandle.current().pid()
                    val p = process("macos-app-switcher-by-pid", pid.toString())
                    if (p.resultCode != 0) {
                        showErrorNotification("Failed to focus Kotlin Emacs window", p.output.joinToString("\n"))
                    }
                }
            }
            .get("/toggle-transcribe") {
                GlobalTranscribe.toggle()
            }
            .post("/audio-status") { ctx ->
                // This endpoint is called by the Rust audio server to notify status changes
                Globals.coroutineScope.launch {
                    if (settings.audio.engine == Audio.Engine.Rust) {
                        try {
                            val statusJson = ctx.body()
                            val status = JsonIgnoreUnknown.decodeFromString<RustAudioStatus>(statusJson)
                            RustAudioEngine.updateGlobalStateFromRustStatus(status)
                        } catch (_: Exception) {
                            // If parsing fails, fall back to polling the status endpoint
                            RustAudioEngine.update(showErrorNotification = false)
                        }
                    }
                }
                ctx.result("OK")
            }
            .start(settings.serverPort)
    }
}
