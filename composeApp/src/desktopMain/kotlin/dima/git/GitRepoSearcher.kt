package dima.git

import Globals.faBrandsFont
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.tclement.fonticons.FontIcon
import dev.tclement.fonticons.fa.FontAwesome
import dev.tclement.fonticons.fa.Github
import dev.tclement.fonticons.fa.Gitlab
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.color.TailwindCssColors
import dima.dialogs.DialogIdentifier
import dima.dialogs.completion.*
import dialogs
import dima.events.emitEasy
import dima.globalState.GlobalState
import dima.os.openUrl
import dima.utils.RecentHistory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

@Serializable
enum class GitSource {
    GitHub,
    GitLab
}

@Serializable
data class GitRepository(
    val name: String,
    val description: String?,
    val source: GitSource,
    val isPrivate: Boolean,
    val url: String
)

/**
 * Searches and displays Git repositories from GitHub and GitLab.
 * Maintains a complete list of repositories in sortedReposFile,
 * sorted based on user selection history. The file contains ALL repositories,
 * not just the ones the user has picked, with the most recently selected ones at the top.
 */
object GitRepoSearcher {

    /**
     * Stores the ID of the currently active search dialog, if any.
     */
    private var activeSearchDialogId: Long? = null

    /**
     * Display user's own repositories from GitHub and GitLab in a completion dialog.
     * If sorted repositories from previous sessions exist, they are displayed immediately
     * while fresh repositories are fetched in the background. When fresh repositories are available,
     * the dialog is updated. All repositories are saved to sortedReposFile in sorted order.
     */
    fun searchAndDisplayRepositories() {
        var currentDialogListSource = if (sortedReposFile.exists()) { // This list backs the dialog's candidates
            RecentHistory.readFromJsonFile<GitRepository>(sortedReposFile)
        } else {
            emptyList()
        }

        val onAcceptLogic: (CompletionDialogAccept) -> Unit = { result ->
            if (result.index != null) {
                val selectedRepo = currentDialogListSource[result.index] // Use the list backing the dialog
                val source = when (selectedRepo.source) {
                    GitSource.GitHub -> "GitHub"
                    GitSource.GitLab -> "GitLab"
                }
                val visibility = if (selectedRepo.isPrivate) "Private" else "Public"
                updateRepositoryPosition(selectedRepo) // This moves selectedRepo to top of file-persisted list
                openUrl(selectedRepo.url)
                showNotification(
                    "Opened repository in browser",
                    "${result.text} ($source, $visibility)",
                    durationMillis = 500
                )
            }
        }

        val commonDialogCmdActions = listOf(
            CompletionDialogCmdAction(Key.N, "Create GitHub repository", closeDialog = true) {
                openGitHubRepoCreatorDialog()
            }
        )

        if (currentDialogListSource.isNotEmpty()) {
            val candidates = createRepositoryCandidates(currentDialogListSource)
            openCompletionDialog(
                title = "Open repository in browser",
                candidates = candidates,
                keepDialogOpenOnAccept = true,
                allowChangingCandidatesViaEventToFixDialogHeight = true,
                allowMatchingLiterally = true,
                cmdActions = commonDialogCmdActions,
                onAccept = onAcceptLogic,
                onDismiss = { activeSearchDialogId = null }
            )
            dialogs.lastOrNull()?.let { activeSearchDialogId = it.id }
        }

        val loadingNotification = showLoadingNotification(
            "Loading your repositories...",
            "Fetching from GitHub and GitLab"
        )

        CoroutineScope(Dispatchers.IO).launch {
            val downloadedRepositories = downloadGitHubAndGitLabRepositories() // This also saves the fully sorted list
            loadingNotification.dismiss()

            if (downloadedRepositories.isEmpty()) {
                showNotification(
                    "No repositories found",
                    "Make sure your GitHub and GitLab tokens are set in settings"
                )
                // If a dialog was open, it might be good to close it or show "No repos found"
                activeSearchDialogId?.let {
                    if (dialogs.any { d -> d.id == it }) {
                        ChangeCompletionDialogCandidates(it, emptyList()).emitEasy()
                    } else {
                        activeSearchDialogId = null
                    }
                }
                return@launch
            }

            if (activeSearchDialogId == null) { // No dialog was open (e.g., cache was empty)
                currentDialogListSource = downloadedRepositories // Update the source list
                val candidates = createRepositoryCandidates(currentDialogListSource)
                // Need to run openCompletionDialog on the main thread or appropriate UI dispatcher
                withContext(Dispatchers.Main) {
                    openCompletionDialog(
                        title = "Open repository in browser",
                        candidates = candidates,
                        keepDialogOpenOnAccept = true,
                        allowChangingCandidatesViaEventToFixDialogHeight = true,
                        allowMatchingLiterally = true,
                        cmdActions = commonDialogCmdActions,
                        onAccept = onAcceptLogic,
                        onDismiss = { activeSearchDialogId = null }
                    )
                    dialogs.lastOrNull()?.let { activeSearchDialogId = it.id }
                }
            } else { // A dialog was already open
                val dialogStillExists =
                    dialogs.any { it.id == activeSearchDialogId && it.identifier == DialogIdentifier.Completion }
                if (!dialogStillExists) {
                    activeSearchDialogId = null
                } else if (!areRepositoryListsEqual(currentDialogListSource, downloadedRepositories)) {
                    currentDialogListSource = downloadedRepositories // Update the source list
                    val candidates = createRepositoryCandidates(currentDialogListSource)
                    ChangeCompletionDialogCandidates(activeSearchDialogId!!, candidates).emitEasy()
                }
            }
        }
    }

    /**
     * Reloads repositories in the background and updates the search dialog if it's open.
     */
    suspend fun reloadRepositoriesInBackground(showNotifications: Boolean = true) {
        val loadingNotification = if (showNotifications) {
            showLoadingNotification(
                "Reloading repositories...",
                "Fetching from GitHub and GitLab in background"
            )
        } else null

        try {
            val downloadedRepositories = downloadGitHubAndGitLabRepositories() // This already saves sorted list

            if (showNotifications) {
                loadingNotification?.toInfo(
                    "Repositories reloaded",
                    "${downloadedRepositories.size} repositories found."
                )
            }

            activeSearchDialogId?.let { dialogId ->
                if (dialogs.any { it.id == dialogId && it.identifier == DialogIdentifier.Completion }) {
                    val candidates = createRepositoryCandidates(downloadedRepositories)
                    ChangeCompletionDialogCandidates(dialogId, candidates).emitEasy()
                } else {
                    activeSearchDialogId = null // Dialog was closed
                }
            }
        } catch (e: Exception) {
            if (showNotifications) {
                loadingNotification?.toError("Failed to reload repositories", e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Creates completion dialog candidates from repository list
     */
    private fun createRepositoryCandidates(repositories: List<GitRepository>): List<CompletionDialogCandidate> {
        val width = 16.dp
        val iconModifier = Modifier.size(16.dp)

        return repositories.map { repo ->
            val source = when (repo.source) {
                GitSource.GitHub -> "GitHub"
                GitSource.GitLab -> "GitLab"
            }
            val description = repo.description ?: ""
            CompletionDialogCandidate(
                text = repo.name,
                additionalSearchString = "$source $description".trim(),
                prefixView = {
                    val icon: Char = when (repo.source) {
                        GitSource.GitHub -> FontAwesome.Brands.Github
                        GitSource.GitLab -> FontAwesome.Brands.Gitlab
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        FontIcon(
                            icon = icon,
                            contentDescription = null,
                            tint = when (repo.source) {
                                GitSource.GitHub -> if (GlobalState.isDarkMode) TailwindCssColors.white else TailwindCssColors.black
                                GitSource.GitLab -> TailwindCssColors.orange600
                            },
                            iconFont = faBrandsFont,
                            modifier = iconModifier,
                        )
                        if (repo.isPrivate) {
                            Text(
                                "🔒",
                                fontSize = 16.sp,
                                modifier = Modifier.width(width)
                            )
                        } else {
                            Box(Modifier.width(width))
                        }
                    }
                }
            )
        }
    }

    /**
     * Updates the position of a repository in the sorted list and saves the updated list.
     * The selected repository is moved to the front of the list.
     *
     * @param selectedRepo The repository that was selected by the user.
     */
    private fun updateRepositoryPosition(selectedRepo: GitRepository) {
        val currentSortedRepos = if (sortedReposFile.exists()) {
            RecentHistory.readFromJsonFile<GitRepository>(sortedReposFile).toMutableList()
        } else {
            mutableListOf()
        }
        currentSortedRepos.removeAll { it == selectedRepo }
        currentSortedRepos.add(0, selectedRepo)
        sortedReposFile.writeText(Json.encodeToString(currentSortedRepos))
    }

    private fun areRepositoryListsEqual(list1: List<GitRepository>, list2: List<GitRepository>): Boolean {
        if (list1.size != list2.size) {
            return false
        }
        val set1 = list1.toSet()
        val set2 = list2.toSet()
        return set1 == set2
    }
}