package dima.git

import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.postLogged
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.dialogs.generic.GenericDialogRow
import dima.dialogs.generic.getTextInputSingleLineContent
import dima.dialogs.generic.isCheckboxTicked
import dima.dialogs.generic.openGenericDialog
import dima.os.copyToClipboard
import dima.settings
import dima.utils.JsonIgnoreUnknown
import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString

@Serializable
private data class CreateRepoRequest(
    val name: String,
    val description: String?,
    val private: <PERSON><PERSON><PERSON>,
)

/**
 * Opens a dialog to create a new GitHub repository.
 * Uses the GitHub API to create the repository with the provided name, description, and privacy setting.
 */
fun openGitHubRepoCreatorDialog() {
    openGenericDialog(
        "GitHub Repo Creator",
        layout = listOf(
            GenericDialogRow.TextInputSingleLine("Name", isRequired = true),
            GenericDialogRow.TextInputSingleLine("Description"),
            GenericDialogRow.Checkbox(
                "🔒 Private",
                id = "private",
                isChecked = true
            ),
        )
    ) { dialogResult ->
        val repoName = dialogResult.getTextInputSingleLineContent("Name")
        val description = dialogResult.getTextInputSingleLineContent("Description")
        val isPrivate = dialogResult.isCheckboxTicked("private")
        createGitHubRepository(repoName, description, isPrivate)
    }
}

/**
 * Creates a new GitHub repository using the GitHub API.
 *
 * @param name The name of the repository
 * @param description Optional description for the repository
 * @param isPrivate Whether the repository should be private (true) or public (false)
 */
private fun createGitHubRepository(name: String, description: String?, isPrivate: Boolean) {
    if (settings.githubRepoCreatorToken.isNullOrBlank()) {
        NetworkActivity.addFakedEntry(
            text = "GitHub token is not set in settings",
            status = FakedNetworkActivityStatus.Error
        )
        showErrorNotification(
            "GitHub token not set",
            "Please set your GitHub token in settings to create repositories"
        )
        return
    }

    val title = if (isPrivate) {
        "Creating private GitHub repository: $name..."
    } else {
        "Creating public GitHub repository: $name..."
    }
    val loadingNotification = showLoadingNotification(title)

    CoroutineScope(Dispatchers.IO).launch {
        val client = createHttpClientWithLocalhostProxy()
        try {
            val request = CreateRepoRequest(
                name = name,
                description = description,
                private = isPrivate
            )

            val response = client.postLogged("https://api.github.com/user/repos") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(request))
                headers {
                    append(HttpHeaders.Authorization, "token ${settings.githubRepoCreatorToken}")
                    append(HttpHeaders.Accept, "application/vnd.github.v3+json")
                    append(HttpHeaders.UserAgent, "KotlinEmacs")
                }
            }
            loadingNotification.dismiss()
            if (response.statusCode == 201) {
                val repo = JsonIgnoreUnknown.decodeFromString<GitHubRepository>(response.body)
                val cloneCommand = "git clone ${repo.sshUrl}"
                copyToClipboard(cloneCommand, showNotification = false)
                val successTitle = if (isPrivate) {
                    "Created private GitHub repository"
                } else {
                    "Created public GitHub repository"
                }
                showNotification(successTitle, "Copied to clipboard:\n${cloneCommand}")
                CoroutineScope(Dispatchers.IO).launch {
                    GitRepoSearcher.reloadRepositoriesInBackground(showNotifications = false)
                }
            } else {
                showErrorNotification(
                    "Failed to create repository",
                    "Error: ${response.statusCode} - ${response.body}"
                )
            }
        } catch (e: Exception) {
            loadingNotification.dismiss()
            NetworkActivity.addFakedEntry(
                text = "Error creating GitHub repository: ${e.message}",
                status = FakedNetworkActivityStatus.Error
            )
            showErrorNotification(
                "Error creating repository",
                e.message ?: "Unknown error occurred"
            )
        }
    }
}