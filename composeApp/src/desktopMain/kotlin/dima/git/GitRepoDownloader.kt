package dima.git

import dima.utils.RecentHistory
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.sortedByHistory
import io.ktor.client.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

private val client: HttpClient = createHttpClientWithLocalhostProxy()

/**
 * Fetch user's own repositories from both GitHub and GitLab in parallel.
 * Sorts the repositories based on user selection history stored in sortedReposFile.
 *
 * @return Combined list of repositories from both sources, sorted by user selection history.
 */
internal suspend fun downloadGitHubAndGitLabRepositories(): List<GitRepository> {
    // Ensure cache directory exists
    if (!cacheDirectory.exists()) {
        cacheDirectory.mkdirs()
    }

    // Get repositories from both sources
    val repos = coroutineScope {
        var githubRepos = listOf<GitRepository>()
        var gitlabRepos = listOf<GitRepository>()
        val githubJob = launch(Dispatchers.IO) {
            githubRepos = client.downloadGitHubRepositories()
        }
        val gitlabJob = launch(Dispatchers.IO) {
            gitlabRepos = client.downloadGitLabRepositories()
        }
        githubJob.join()
        gitlabJob.join()
        githubRepos + gitlabRepos
    }
    // Get previously sorted repositories to maintain user's selection history
    val repoHistory = RecentHistory.readFromJsonFile<GitRepository>(sortedReposFile)
    val sortedRepos = repos.sortedByHistory(repoHistory)

    // Save all repositories in sorted order
    saveAllRepositoriesSorted(sortedRepos)

    return sortedRepos
}