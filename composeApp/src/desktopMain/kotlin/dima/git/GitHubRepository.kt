package dima.git

import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.getLogged
import dima.settings
import dima.utils.JsonIgnoreUnknown
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
internal data class GitHubRepository(
    val id: Long,
    val name: String,
    val fullName: String = "",
    val private: Boolean = false,
    val description: String? = null,
    @SerialName("html_url")
    val htmlUrl: String? = null,
    @SerialName("ssh_url")
    val sshUrl: String? = null
)

/**
 * Downloads user's own repositories from GitHub, handling pagination.
 *
 * @return List of GitRepository objects from GitHub.
 */
internal suspend fun HttpClient.downloadGitHubRepositories(): List<GitRepository> {
    if (settings.githubRepoSearcherToken == null) {
        return emptyList()
    }
    if (settings.githubRepoSearcherToken.isBlank()) {
        NetworkActivity.addFakedEntry(
            text = "GitHub token is not set in settings",
            status = FakedNetworkActivityStatus.Error
        )
        return emptyList()
    }

    try {
        val allRepos = mutableListOf<GitHubRepository>()
        var page = 1
        var hasMorePages = true

        // Fetch all pages
        while (hasMorePages) {
            val url = "https://api.github.com/user/repos?per_page=100&page=$page"
            val response = getLogged(url) {
                headers {
                    append(HttpHeaders.Authorization, "token ${settings.githubRepoSearcherToken}")
                    append(HttpHeaders.Accept, "application/vnd.github.v3+json")
                    append(HttpHeaders.UserAgent, "KotlinEmacs")
                }
            }

            if (response.statusCode != 200) {
                break
            }

            val pageRepos = JsonIgnoreUnknown.decodeFromString<List<GitHubRepository>>(response.body)
            allRepos.addAll(pageRepos)

            // Check if we've reached the last page
            hasMorePages = pageRepos.isNotEmpty() && pageRepos.size == 100
            page++
        }

        return allRepos.map { item ->
            GitRepository(
                name = item.name,
                description = item.description,
                source = GitSource.GitHub,
                isPrivate = item.private,
                url = item.htmlUrl ?: "https://github.com/${item.fullName}"
            )
        }
    } catch (e: Exception) {
        NetworkActivity.addFakedEntry(
            text = "Error fetching GitHub repositories: ${e.message}",
            status = FakedNetworkActivityStatus.Error
        )
        return emptyList()
    }
}