package dima.git

import Globals
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.os.copyToClipboard
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

fun openGitStatusDialog(path: File) {
    Globals.coroutineScope.launch(Dispatchers.IO) {
        val p = process(
            "git", "status",
            directory = path,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        val output = p.output.joinToString("\n")
        if (p.resultCode != 0) {
            showErrorNotification("Failed to get git status", output)
            return@launch
        }
        openConfirmationDialog("Git status", output, confirmButtonText = "Copy to clipboard") {
            copyToClipboard(output)
        }
    }
}