package dima.git

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showLoadingNotification
import java.io.File

object Git {

    /**
     * @param directory can be null in which case it uses the current working directory
     */
    suspend fun commitAndPushAllChanges(notificationSuffix: String? = null, directory: File? = null) {
        val notification = showLoadingNotification("git add .")
        val gitAdd = process(
            "git", "add", ".",
            directory = directory,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        var output = gitAdd.output.joinToString("\n").trim()
        if (gitAdd.resultCode != 0) {
            notification.toError(message = output)
            return
        }
        notification.update("git commit -m \"Commit all changes\"", output)
        val gitCommit = process(
            "git", "commit", "-m", "Commit all changes",
            directory = directory,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        output += ("\n" + gitCommit.output.joinToString("\n")).trim()
        if (gitCommit.resultCode != 0) {
            notification.toError(message = output)
            return
        }
        notification.update("git push...", message = output)
        val gitPush = process(
            "git", "push",
            directory = directory,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        output += ("\n" + gitPush.output.joinToString("\n")).trim()
        if (gitPush.resultCode != 0) {
            notification.toError(message = output)
            return
        }
        notification.toInfo(
            if (notificationSuffix == null) {
                "Pushed all changes"
            } else {
                "Pushed all changes for $notificationSuffix"
            },
            output.trim(),
        )
    }

}
