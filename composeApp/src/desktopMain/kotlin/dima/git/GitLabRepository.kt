package dima.git

import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.getLogged
import dima.settings
import dima.utils.JsonIgnoreUnknown
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
internal data class GitLabProject(
    val id: Long,
    val name: String,
    val description: String? = null,

    @SerialName("path_with_namespace")
    val pathWithNamespace: String = "",

    val public: Boolean? = null, // Changed to nullable

    @SerialName("web_url")
    val webUrl: String? = null
)

/**
 * Downloads user's own repositories from GitLab, handling pagination.
 *
 * @return List of GitRepository objects from GitLab.
 */
internal suspend fun HttpClient.downloadGitLabRepositories(): List<GitRepository> {
    if (settings.gitlabToken == null) {
        return emptyList()
    }
    if (settings.gitlabToken.isBlank()) {
        NetworkActivity.addFakedEntry(
            text = "GitLab token is not set in settings",
            status = FakedNetworkActivityStatus.Error
        )
        return emptyList()
    }

    try {
        val allProjects = mutableListOf<GitLabProject>()
        var page = 1
        var hasMorePages = true

        // Fetch all pages
        while (hasMorePages) {
            val url = "https://gitlab.com/api/v4/projects?owned=true&per_page=100&page=$page"
            val response = getLogged(url) {
                headers {
                    append(HttpHeaders.Authorization, "Bearer ${settings.gitlabToken}")
                }
            }

            if (response.statusCode != 200) {
                break
            }

            val pageProjects = JsonIgnoreUnknown.decodeFromString<List<GitLabProject>>(response.body)
            allProjects.addAll(pageProjects)

            // Check if we've reached the last page
            hasMorePages = pageProjects.isNotEmpty() && pageProjects.size == 100
            page++
        }

        return allProjects.map { project ->
            GitRepository(
                name = project.name,
                description = project.description,
                source = GitSource.GitLab,
                isPrivate = project.public != true, // Updated logic
                url = project.webUrl ?: "https://gitlab.com/${project.pathWithNamespace}"
            )
        }
    } catch (e: Exception) {
        NetworkActivity.addFakedEntry(
            text = "Error fetching GitLab repositories: ${e.message}",
            status = FakedNetworkActivityStatus.Error
        )
        return emptyList()
    }
}
