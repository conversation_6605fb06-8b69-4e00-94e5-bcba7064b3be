package dima.git

import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File

internal val cacheDirectory = File("cache/git")
internal val sortedReposFile = File(cacheDirectory, "sorted-repositories.json")

/**
 * Saves all repositories to the sortedReposFile in their sorted order.
 * This ensures the file contains ALL repositories, not just the ones the user has picked.
 *
 * @param sortedRepos The list of repositories already sorted by user selection history.
 */
internal fun saveAllRepositoriesSorted(sortedRepos: List<GitRepository>) {
    if (sortedRepos.isEmpty()) {
        return
    }
    sortedReposFile.writeText(Json.encodeToString(sortedRepos))
}