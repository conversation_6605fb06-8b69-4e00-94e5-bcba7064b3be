package dima.utils

import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp

fun Modifier.dashedBorder(
    strokeWidth: Dp,
    color: Color,
    shape: Shape,
    dashLength: Dp,
    gapLength: Dp,
    phase: Float = 0f
): Modifier = composed {
    val density = LocalDensity.current
    val strokeWidthPx = density.run { strokeWidth.toPx() }
    val dashLengthPx = density.run { dashLength.toPx() }
    val gapLengthPx = density.run { gapLength.toPx() }

    this.then(
        Modifier.drawWithCache {
            onDrawBehind {
                val outline: Outline = shape.createOutline(size, layoutDirection, density)
                val path = Path()
                path.addOutline(outline)
                drawPath(
                    path = path,
                    color = color,
                    style = Stroke(
                        width = strokeWidthPx,
                        pathEffect = PathEffect.dashPathEffect(
                            intervals = floatArrayOf(dashLengthPx, gapLengthPx),
                            phase = phase
                        )
                    )
                )
            }
        }
    )
}

fun Modifier.topBorder(strokeWidth: Dp, color: Color) = composed(
    factory = {
        val density = LocalDensity.current
        val strokeWidthPx = density.run { strokeWidth.toPx() }
        Modifier.drawBehind {
            val width = size.width
            drawLine(
                color = color,
                start = Offset(x = 0f, y = 0f),
                end = Offset(x = width, y = 0f),
                strokeWidth = strokeWidthPx
            )
        }
    }
)

fun Modifier.bottomBorder(strokeWidth: Dp, color: Color, dashed: Boolean = false) = composed {
    val density = LocalDensity.current
    val strokeWidthPx = with(density) { strokeWidth.toPx() }
    this.then(
        Modifier.drawBehind {
            val width = size.width
            val height = size.height
            drawLine(
                color = color,
                pathEffect = if (dashed) PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f) else null,
                start = Offset(x = 0f, y = height - strokeWidthPx / 2),
                end = Offset(x = width, y = height - strokeWidthPx / 2),
                strokeWidth = strokeWidthPx
            )
        }
    )
}

fun Modifier.rightBorder(strokeWidth: Dp, color: Color, dashed: Boolean = false) = composed {
    val density = LocalDensity.current
    val strokeWidthPx = with(density) { strokeWidth.toPx() }
    this.then(
        Modifier.drawBehind {
            val width = size.width
            val height = size.height
            drawLine(
                color = color,
                pathEffect = if (dashed) PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f) else null,
                start = Offset(x = width - strokeWidthPx / 2, y = 0f),
                end = Offset(x = width - strokeWidthPx / 2, y = height),
                strokeWidth = strokeWidthPx
            )
        }
    )
}

fun Modifier.leftBorder(strokeWidth: Dp, color: Color, dashed: Boolean = false) = composed {
    val density = LocalDensity.current
    val strokeWidthPx = with(density) { strokeWidth.toPx() }
    this.then(
        Modifier.drawBehind {
            drawLine(
                color = color,
                start = Offset(x = strokeWidthPx / 2, y = 0f),
                end = Offset(x = strokeWidthPx / 2, y = size.height),
                strokeWidth = strokeWidthPx,
                pathEffect = if (dashed) PathEffect.dashPathEffect(floatArrayOf(10f, 10f)) else null
            )
        }
    )
}