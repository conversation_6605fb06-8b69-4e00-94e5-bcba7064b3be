package dima.utils

import java.text.Normalizer

fun String.ensureSuffix(suffix: String): String {
    return if (this.endsWith(suffix)) this else this + suffix
}

fun String.slugify(): String {
    return lowercase()
        .replace(Regex("[^a-z0-9]+"), "-")
        .trim('-')
}

fun String.capitalizeWords(): String = split(" ").joinToString(" ") { word ->
    word.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
}

fun String.breakWithNewlines(maxLength: Int = 70): String {
    return this.chunked(maxLength).joinToString("\n")
}

/**
 * Normalize string to [Normalizer.Form.NFC].
 *
 * To convert ä to ä. Move cursor over first ä, to see that it is displayed differently inside IntelliJ or paste into Emacs.
 * This is required for [String.contains] to match correctly.
 */
fun String.normalize(): String {
    return Normalizer.normalize(this, Normalizer.Form.NFC)
}

fun String.truncateWithEllipsis(max: Int): String {
    if (this.length > max) {
        return this.substring(0, max) + "..."
    }
    return this
}

fun String.truncateWithEllipsisMiddle(max: Int): String {
    if (this.length <= max) {
        return this
    }
    val partLen = max / 2
    val startPart = this.substring(0, partLen)
    val endPart = this.substring(this.length - partLen, this.length)
    return "$startPart...$endPart"
}