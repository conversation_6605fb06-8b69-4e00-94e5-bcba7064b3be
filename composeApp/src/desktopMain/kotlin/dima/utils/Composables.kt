package dima.utils

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.configureSwingGlobalsForCompose
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.awaitApplication
import dima.dialogs.DialogIdentifier
import dialogs
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.globalState.PaneState
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.runBlocking
import java.awt.Desktop
import javax.swing.SwingUtilities
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext
import kotlin.system.exitProcess

@Composable
fun LaunchedEffectForAppKeys(paneState: PaneState, localAppKeys: List<AppKey>) {
    LaunchedEffect(paneState.id == GlobalState.activePaneId) {
        if (paneState.id == GlobalState.activePaneId) {
            appKeys = localAppKeys
        }
    }
}

@Composable
fun LaunchedEffectGlobalEventForApps(block: suspend CoroutineScope.() -> Unit) {
    LaunchedEffect(globalEvent) {
        if (globalEvent == null) {
            return@LaunchedEffect
        }
        val last = dialogs.lastOrNull()
        val isTransientAndKeepopen = last?.identifier == DialogIdentifier.Transient
        if (dialogs.isNotEmpty() && !isTransientAndKeepopen) {
            return@LaunchedEffect
        }
        block()
        globalEvent = null
    }
}

@Composable
fun LaunchedEffectGlobalEventForDialogs(dialogId: Long, block: suspend () -> Unit) {
    LaunchedEffect(globalEvent) {
        if (globalEvent == null || dialogs.isEmpty()) {
            return@LaunchedEffect
        }
        if (dialogs.last().id == dialogId) {
            block()
            globalEvent = null
        }
    }
}

/**
 * Prefer this to set items clickable, because the main focus is entirely on keyboard, and often the fancy 'animation' is just annoying,
 * depending on where the mouse pointer is on the screen.
 *
 * Make clickable without any indication, except for optionally setting the Hand pointer icon.
 */
@Composable
fun Modifier.clickableWithoutBackgroundRipple(useHandIcon: Boolean = true, onClick: () -> Unit) = this.then(
    if (useHandIcon) {
        Modifier.pointerHoverIcon(PointerIcon.Hand).then(
            clickableWrapper(onClick)
        )
    } else {
        clickableWrapper(onClick)
    }
)

@Composable
fun clickableWrapper(onClick: () -> Unit) = Modifier.clickable(
    enabled = true,
    onClickLabel = null,
    onClick = onClick,
    role = null,
    indication = null,
    interactionSource = remember { MutableInteractionSource() },
)

// from https://github.com/JetBrains/compose-multiplatform/issues/754
@OptIn(ExperimentalComposeUiApi::class)
fun applicationWithQuitListener(
    context: CoroutineContext = EmptyCoroutineContext,
    onQuit: (() -> Unit)? = null,
    content: @Composable ApplicationScope.() -> Unit
) {
    if (System.getProperty("compose.application.configure.swing.globals") == "true") {
        configureSwingGlobalsForCompose()
    }
    check(!SwingUtilities.isEventDispatchThread()) {
        "application can't run inside UI thread (Event Dispatch Thread)"
    }
    if (onQuit != null && Desktop.isDesktopSupported()) {
        val desktop = Desktop.getDesktop()
        desktop.setQuitHandler { _, response ->
            onQuit()
            response.performQuit()
        }
    }
    runBlocking(context) {
        awaitApplication(content = content)
    }
    exitProcess(0)
}

@Suppress("unused")
fun Modifier.grayScale(): Modifier {
    val saturationMatrix = ColorMatrix().apply { setToSaturation(0f) }
    val saturationFilter = ColorFilter.colorMatrix(saturationMatrix)
    val paint = Paint().apply { colorFilter = saturationFilter }
    return drawWithCache {
        val canvasBounds = Rect(Offset.Zero, size)
        onDrawWithContent {
            drawIntoCanvas {
                it.saveLayer(canvasBounds, paint)
                drawContent()
                it.restore()
            }
        }
    }
}
