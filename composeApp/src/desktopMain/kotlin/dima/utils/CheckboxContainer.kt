package dima.utils

import GlobalStyling
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun CheckboxContainer(text: String, checked: Boolean, onCheckedChange: (Boolean) -> Unit) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .clickableWithoutBackgroundRipple {
                onCheckedChange(!checked)
            }
    ) {
        Checkbox(
            checked = checked,
            // use null, so no ripple hover is displayed
            onCheckedChange = null,
            colors = CheckboxDefaults.colors(
                uncheckedColor = GlobalStyling.getCheckboxUncheckedColor(),
                checkedColor = GlobalStyling.getCheckboxCheckedColor()
            ),
        )
        Text(
            text,
            color = GlobalStyling.getTextColor()
        )
    }
}
