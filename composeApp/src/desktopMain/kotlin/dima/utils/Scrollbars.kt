package dima.utils

import androidx.compose.foundation.LocalScrollbarStyle
import androidx.compose.foundation.ScrollbarStyle
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

@Composable
fun scrollbarStyleThemed(): ScrollbarStyle {
    return if (GlobalState.isDarkMode) {
        scrollbarStyleForDarkBackground()
    } else {
        LocalScrollbarStyle.current
    }
}

/**
 * Use for dark backgrounds, tries to mimic the IntelliJ IDEA dark theme.
 *
 * If used, this applies to both light and dark mode.
 */
fun scrollbarStyleForDarkBackground() = ScrollbarStyle(
    minimalHeight = 16.dp,
    thickness = 8.dp,
    shape = RoundedCornerShape(4.dp),
    hoverDurationMillis = 300,
    unhoverColor = TailwindCssColors.gray500.copy(alpha = 0.8f),
    hoverColor = TailwindCssColors.gray500.copy(alpha = 0.9f)
)
