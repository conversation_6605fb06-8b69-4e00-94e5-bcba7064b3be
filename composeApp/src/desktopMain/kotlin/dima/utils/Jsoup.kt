package dima.utils

import org.jsoup.nodes.Element
import org.jsoup.nodes.Node

/**
 * @return the href of the anchor tag or null when no anchor tag is found
 */
fun Node.isInAnchor(): String? {
    var parent = this.parentNode()
    while (parent != null) {
        if (parent is Element && parent.tagName() == "a") {
            return parent.attr("href")
        }
        parent = parent.parentNode()
    }
    return null
}

fun Node.hasParentTag(tagName: String): Boolean {
    var node: Node? = this
    while (node != null) {
        if (node is Element && node.tagName() == tagName) {
            return true
        }
        node = node.parentNode()
    }
    return false
}
