package dima.utils

import com.sun.mail.util.BASE64DecoderStream
import java.util.*
import kotlin.math.ln
import kotlin.math.pow
import kotlin.math.roundToInt

/**
 * See the tree-sitter section in README.md for why this is required.
 *
 * @param byteStart the start position of the byte range (inclusive)
 * @param byteEnd the end position of the byte range (exclusive)
 * @return range with end exclusive which can be used in AnnotatedString's addStyle().
 *  This is specifically not [IntRange] because [IntRange] can be passed directly to [String.substring] and it would return 1 character too much.
 *  Merely a safety to avoid small bugs!
 */
fun String.getStringLengthRangesFromByteRange(byteStart: Int, byteEnd: Int): Pair<Int, Int> {
    var totalBytes = 0
    var stringStart = 0
    var stringEnd = 0
    for (codePoint in this.codePoints().toArray()) {
        val codePointByteSize = Character.toString(codePoint).toByteArray(Charsets.UTF_8).size
        if (totalBytes >= byteStart && stringStart == 0 && byteStart != 0) {
            stringStart = stringEnd
        }
        if (totalBytes + codePointByteSize <= byteEnd) {
            stringEnd += Character.toString(codePoint).length
            totalBytes += codePointByteSize
        } else {
            break
        }
    }
    return stringStart to stringEnd
}

fun BASE64DecoderStream.decodeToBytes(): ByteArray {
    var data = ByteArray(1024)
    var count = this.read(data)
    var startPos: Int
    while (count == 1024) {
        val addBuffer = ByteArray(data.size + 1024)
        System.arraycopy(data, 0, addBuffer, 0, data.size)
        startPos = data.size
        data = addBuffer
        count = this.read(data, startPos, 1024)
    }
    return data
}

/**
 * Display like in macOS Finder with decimal places.
 */
fun Long.toNiceHumanByteString(): String {
    if (this == 1L) return "1 Byte"
    if (this < 1000) return "$this Bytes" // Use 1000 for consistency with macOS Finder
    val k = 1000.0 // Use 1000 for KB, MB, GB as per macOS Finder style
    val units = listOf("Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
    val i = (ln(this.toDouble()) / ln(k)).toInt().coerceAtMost(units.size - 1) // Ensure index is within bounds
    val value = this / k.pow(i)

    return when (i) {
        0 -> String.format(Locale.US, "%d %s", this, units[i]) // Bytes (should be caught by the first if)
        1 -> String.format(Locale.US, "%d %s", value.roundToInt(), units[i]) // KB, no decimal
        2 -> String.format(Locale.US, "%.1f %s", value, units[i]) // MB, one decimal
        else -> String.format(Locale.US, "%.2f %s", value, units[i]) // GB and larger, two decimals
    }
}


fun Long.toCompactHumanByteString(): String {
    val units = listOf("B", "K", "M", "G")
    var bytes = this
    var unit = 0
    while (bytes >= 1024 && unit < units.size - 1) {
        bytes /= 1024
        unit++
    }
    return "${bytes.toString().format(0)}${units[unit]}"
}
