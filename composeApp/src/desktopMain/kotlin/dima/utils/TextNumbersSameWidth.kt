package dima.utils

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit

/**
 * @param widthOffset subtract a bit to keep numbers closer together, they are still fully rendered
 * @param modifier do not set width here
 */
@Composable
fun TextNumbersSameWidth(
    text: String,
    widthOffset: Int = 2,
    setAllCharactersToSameWidth: Boolean = false,
    fontSize: TextUnit? = null,
    textAlign: TextAlign? = null,
    totalWidth: Dp? = null,
    color: Color = Color.Unspecified,
    modifier: Modifier = Modifier
) {
    val widestWidthDp = remember {
        with(Globals.density) {
            if (fontSize == null) {
                (measureSingleWidestCharacterWidth() - widthOffset).toDp()
            } else {
                val style = TextStyle(fontSize = fontSize)
                (measureSingleWidestCharacterWidth(style = style) - widthOffset).toDp()
            }
        }
    }
    if (text.isNotEmpty()) {
        val newModifier = modifier
            .width(widestWidthDp)
        Row(
            modifier = Modifier.then(
                if (totalWidth == null) {
                    Modifier
                } else {
                    Modifier.width(totalWidth)
                }
            )
        ) {
            text.forEach {
                val s = it.toString()
                if (setAllCharactersToSameWidth || it.isDigit()) {
                    Text(
                        s,
                        color = color,
                        fontSize = fontSize ?: TextUnit.Unspecified,
                        textAlign = textAlign,
                        modifier = newModifier
                    )
                } else {
                    Text(
                        s,
                        color = color,
                        fontSize = fontSize ?: TextUnit.Unspecified,
                        textAlign = textAlign,
                        modifier = modifier
                    )
                }
            }
        }
    }
}

fun measureSingleWidestCharacterWidth(
    stringToCheckAllCharsIn: String = "0123456789",
    style: TextStyle = TextStyle.Default
): Int {
    var widest = 0
    stringToCheckAllCharsIn.forEach {
        val textLayoutResult: TextLayoutResult =
            TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
                .measure(
                    it.toString(),
                    style = style
                )
        val width = textLayoutResult.size.width
        if (width > widest) {
            widest = width
        }
    }
    return widest
}
