package dima.utils

import GlobalStyling
import Globals
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.Icon
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.Lucide
import com.composables.icons.lucide.Search
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

private val iconSize = 18.dp

data class SmartTextFieldCandidateCount(
    /**
     * Can be null to only display [maxCount].
     */
    val currentCount: Int?,
    val maxCount: Int?
)

/**
 * Mimicks [androidx.compose.material.TextField], but without the label animation,
 * while adding a candidate count.
 *
 * Make sure to apply padding not to this [modifier], but one of the parent composable.
 *
 * Implementation-wise, if the top label is too wide, the text field width is not set to the same width, it is really hard to implement,
 * and even Gemini Pro 2.5 fails to implement it.
 *
 * @param modifier use only .width() or .fillMaxWidth(), not .padding()
 */
@Composable
fun TextFieldWithCandidateCountWithoutAnimation(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    count: SmartTextFieldCandidateCount? = null,
    isFocused: ((Boolean) -> Unit)? = null,
    placeholder: String = "",
    topLabel: String? = null,
    singleLine: Boolean = true,
    focusRequester: FocusRequester? = null,
    modifier: Modifier = Modifier,
) {
    var isFocusedState by remember { mutableStateOf(false) }
    val maxCountWidth: Dp? by derivedStateOf {
        if (count == null) {
            null
        } else {
            with(Globals.density) {
                (measureSingleWidestCharacterWidth() * count.maxCount.toString().length).toDp()
            }
        }
    }
    Column {
        if (topLabel != null) {
            Text(
                topLabel,
                color = if (isFocusedState) {
                    GlobalStyling.TextField.selectedColor
                } else {
                    GlobalStyling.TextField.unfocusedColor
                },
                fontSize = 13.sp,
                modifier = Modifier
                    .then(
                        if (focusRequester == null) {
                            Modifier
                        } else {
                            Modifier.pointerHoverIcon(PointerIcon.Text).then(
                                clickableWrapper {
                                    focusRequester.requestFocus()
                                }
                            )
                        }
                    )
                    .padding(start = 18.dp + 8.dp, bottom = 2.dp)
            )
        }
        Row(
            modifier = Modifier
                .then(
                    if (topLabel == null) {
                        Modifier
                    } else {
                        Modifier.bottomBorder(
                            2.dp,
                            if (isFocusedState) {
                                GlobalStyling.TextField.selectedColor
                            } else {
                                GlobalStyling.TextField.unfocusedColor
                            },
                            dashed = !isFocusedState
                        )
                    }
                )
        ) {
            Icon(
                Lucide.Search,
                contentDescription = null,
                tint = if (isFocusedState) {
                    GlobalStyling.TextField.selectedColor
                } else {
                    GlobalStyling.TextField.unfocusedColor
                },
                modifier = Modifier
                    .padding(end = 8.dp)
                    .then(
                        if (focusRequester == null) {
                            Modifier
                        } else {
                            Modifier.pointerHoverIcon(PointerIcon.Text).then(
                                clickableWrapper {
                                    focusRequester.requestFocus()
                                }
                            )
                        }
                    )
                    .size(iconSize)
            )

            BasicTextField(
                value = value,
                onValueChange = onValueChange,
                singleLine = singleLine,
                cursorBrush = GlobalStyling.TextField.cursorBrush,
                textStyle = if (GlobalState.isDarkMode) {
                    LocalTextStyle.current.copy(color = GlobalStyling.Dark.textColor)
                } else {
                    TextStyle.Default
                },
                decorationBox = { innerTextField ->
                    if (placeholder == "") {
                        innerTextField()
                        return@BasicTextField
                    }
                    Box {
                        if (value.text.isEmpty()) {
                            Text(
                                text = placeholder,
                                maxLines = 1,
                                color = TailwindCssColors.gray500,
                            )
                        }
                        innerTextField()
                    }
                },
                modifier = modifier
                    .weight(1f, fill = false)
                    .onFocusChanged {
                        isFocusedState = it.isFocused
                        isFocused?.invoke(it.isFocused)
                    }
                    .then(
                        if (focusRequester == null) {
                            Modifier
                        } else {
                            Modifier.focusRequester(focusRequester)
                        }
                    )
                    .then(
                        if (topLabel == null) {
                            Modifier.border(
                                width = 2.dp,
                                color = if (isFocusedState) {
                                    GlobalStyling.TextField.selectedColor
                                } else {
                                    GlobalStyling.TextField.unfocusedColor
                                },
                                shape = RoundedCornerShape(6.dp)
                            )
                        } else {
                            Modifier
                        }
                    )
                    .then(
                        if (topLabel == null) {
                            Modifier.padding(10.dp)
                        } else {
                            Modifier.padding(bottom = 10.dp)
                        }
                    )
            )

            if (count != null) {
                Row(
                    horizontalArrangement = Arrangement.End,
                    modifier = Modifier
                        .then(
                            if (focusRequester == null) {
                                Modifier
                            } else {
                                Modifier.pointerHoverIcon(PointerIcon.Text).then(
                                    clickableWrapper {
                                        focusRequester.requestFocus()
                                    }
                                )
                            }
                        )
                        .widthIn(min = 80.dp)
                        .padding(start = 8.dp)
                ) {
                    if (count.currentCount != null) {
                        Row(
                            horizontalArrangement = Arrangement.End,
                            modifier = Modifier
                                .width(maxCountWidth!!)
                        ) {
                            Text(
                                count.currentCount.toString(),
                                color = GlobalStyling.TextField.unfocusedColor
                            )
                        }
                    }
                    Text(
                        if (count.currentCount == null) {
                            count.maxCount.toString()
                        } else {
                            " / " + count.maxCount
                        },
                        color = GlobalStyling.TextField.unfocusedColor
                    )
                }
            }
        }
    }
}