package dima.utils

class ResultException(message: String) : Exception(message)

/** Both [Success] and [Error] are strings. */
sealed class StringResult {
    data class Success(val value: String) : StringResult()
    data class Error(val error: String) : StringResult()
}

/** [Error] is string. */
sealed class Result<out T> {
    data class Success<out T>(val value: T) : Result<T>()
    data class Error(val error: String) : Result<Nothing>()

    fun expect(message: String = "Error unwrapping Result"): T {
        return when (this) {
            is Error -> throw ResultException("$message: $error")
            is Success -> value
        }
    }
}

/** Success is just a marker. */
sealed class SimpleResult {
    object Success : SimpleResult()
    data class Error(val error: String) : SimpleResult()
}
