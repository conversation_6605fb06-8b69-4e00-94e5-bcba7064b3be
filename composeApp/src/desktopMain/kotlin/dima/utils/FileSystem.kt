package dima.utils

import dima.apps.dired.Dired
import dima.apps.notifications.showErrorNotification
import dima.os.homeWithoutSlash
import java.io.*
import kotlin.text.Charsets.UTF_8

/**
 * Replace home with tilde.
 */
fun File.abbreviatePath(): String = absolutePath.replace(homeWithoutSlash, "~")

/**
 * Replace home with tilde.
 */
fun String.abbreviatePath(): String = this.replace(homeWithoutSlash, "~")

data class FileReadResult(
    val text: String,
    val isTruncated: Boolean
)

fun File.readTextUpTo(
    limit: Int = Dired.PREVIEW_FILE_SIZE_LIMIT_CHARS,
    showErrorNotification: Boolean = true
): Result<FileReadResult> {
    return try {
        val buffer = CharArray(limit + 1)
        BufferedReader(InputStreamReader(FileInputStream(this), UTF_8)).use { reader ->
            val charsRead = reader.read(buffer, 0, limit + 1)
            val result = when {
                charsRead == -1 -> FileReadResult("", false)
                charsRead <= limit -> FileReadResult(String(buffer, 0, charsRead), false)
                else -> {
                    val text = String(buffer, 0, limit)
                    FileReadResult(text, true)
                }
            }
            Result.Success(result)
        }
    } catch (_: FileNotFoundException) {
        if (showErrorNotification) {
            showErrorNotification("File does not exist anymore", File(absolutePath).name)
        }
        Result.Error("File does not exist anymore")
    }
}

object FileSystem {

    val disallowedChars = listOf('\\', '/', ':', '*', '?', '\"', '<', '>', '|')

    /**
     * The extension is checked case-insensitively.
     */
    fun isAudioExtension(extensionWithoutDot: String): Boolean {
        return when (extensionWithoutDot.lowercase()) {
            "opus", "mp3", "wav", "flac", "ogg", "m4a" -> true
            else -> false
        }
    }

    private fun sanitizeFileName(fileName: String): String {
        // 1. replace all characters ':', ' ', '.', '/', '\', ''', '"' with a hyphen
        // 2. replace consecutive hyphens
        // 3. remove hyphens from the beginning and the end
        return fileName
            .replace(Regex("[: /.\\\\'\"]"), "-")
            .replace(Regex("-+"), "-")
            .replace(Regex("^-+|-+$"), "")
    }

    /**
     * @return a guaranteed non-existing file with a unique name in [dir]
     */
    fun getUniqueFileByFileName(dir: File, originalName: String, checkIfInitialExists: Boolean = true): File {
        if (checkIfInitialExists) {
            val initialFile = File(dir, originalName)
            if (!initialFile.exists()) {
                return initialFile
            }
        }
        val baseName = originalName.substringBeforeLast(".")
        val extension = originalName.substringAfterLast(".", "")
        val hasExtension = extension.isNotEmpty() && originalName != extension
        val fullExtension = if (hasExtension) ".$extension" else ""
        var i = 1
        var file = File(dir, originalName)
        while (file.exists()) {
            file = File(dir, "$baseName-$i$fullExtension")
            i++
        }
        return file
    }

    /**
     * This creates a new directory with a unique name in [dir].
     *
     * @param name the name of the directory will be sanitized
     */
    fun createUniqueDirectory(dir: File, name: String): File {
        val sanitizedName = sanitizeFileName(name)
        val startDir = File(dir, sanitizedName)
        if (!startDir.exists()) {
            startDir.mkdirs()
            return startDir
        }
        var i = 1
        var file = File(dir, sanitizedName)
        while (file.exists()) {
            i++
            file = File(dir, "$sanitizedName-$i")
        }
        file.mkdirs()
        return file
    }

}