package dima.utils

import GlobalStyling
import androidx.compose.animation.animateColor
import androidx.compose.animation.core.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

@Composable
fun TextLoadingWithAnimatedForeground(text: String) {
    val infiniteTransition = rememberInfiniteTransition()
    val color by infiniteTransition.animateColor(
        initialValue = GlobalStyling.getFuchsiaColor(),
        targetValue = if (GlobalState.isDarkMode) TailwindCssColors.violet400 else TailwindCssColors.violet600,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        )
    )
    Text(
        text = text,
        color = color,
    )
}