package dima.utils

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.unit.dp
import handleLostFocus

/**
 * Use for apps to hold focus state without any visual indicator.
 */
@Composable
fun DummyFocusable(focusRequester: FocusRequester? = null) {
    Box(
        modifier = Modifier
            .size(0.dp)
            .focusable()
            .then(
                if (focusRequester == null) {
                    Modifier
                } else {
                    Modifier.focusRequester(focusRequester)
                }
            )
            .handleLostFocus()
    )
}
