package dima.utils

import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.text.MultiParagraph

/**
 * Use this since my text editor methods crash when on the last newline of a line.
 */
fun MultiParagraph.getBoundingBoxSafe(index: Int): Rect {
    return try {
        getBoundingBox(index)
    } catch (e: IllegalArgumentException) {
        if (index == 0) {
            Rect(0f, 0f, 0f, 0f)
        } else {
            getBoundingBox(index - 1)
        }
    }
}