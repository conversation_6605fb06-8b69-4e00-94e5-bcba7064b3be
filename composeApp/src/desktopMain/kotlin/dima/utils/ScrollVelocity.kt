package dima.utils

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.gestures.scrollBy
import kotlinx.coroutines.delay

class ScrollVelocity(
    private val scrollStateToModify: ScrollState,
    private val maxVelocity: Float = 80f,
    private val increasePerTick: Float = 6f,
    private val decreasePercentagePerTick: Float = 25f
) {
    private var isDownDirection: Boolean = true
    private var isKeyHeldDown: Boolean = false
    private var velocity: Float = 0f
    private var wasLastScrollMore: Boolean = false
    private val maxVelocityForScrollLess: Float = maxVelocity / 3f

    suspend fun loopForeverAndTick() {
        // update every 16 milliseconds
        val sleepDuration = 16L
        while (true) {
            if (shouldTickBeSkipped()) {
                delay(sleepDuration)
                continue
            }
            tick()
            delay(sleepDuration)
        }
    }

    suspend fun callTickInLoop() {
        if (!shouldTickBeSkipped()) {
            tick()
        }
    }

    fun resetVelocity() {
        velocity = 0f
    }

    private fun shouldTickBeSkipped(): Boolean {
        return velocity <= 0.1f
    }

    suspend fun scrollToTop(instantly: Boolean = false) {
        velocity = 0f
        if (instantly) {
            scrollStateToModify.scrollTo(0)
        } else {
            scrollStateToModify.animateScrollTo(0)
        }
    }

    suspend fun scrollToBottom() {
        velocity = 0f
        scrollStateToModify.animateScrollTo(scrollStateToModify.maxValue)
    }

    fun onScrollUpKeyPressed() {
        doScroll(toUpDirection = true, scrollMore = false)
    }

    fun onScrollUpMoreKeyPressed() {
        doScroll(toUpDirection = true, scrollMore = true)
    }

    fun onScrollDownKeyPressed() {
        doScroll(toUpDirection = false, scrollMore = false)
    }

    fun onScrollDownMoreKeyPressed() {
        doScroll(toUpDirection = false, scrollMore = true)
    }

    private fun doScroll(toUpDirection: Boolean, scrollMore: Boolean) {
        wasLastScrollMore = scrollMore
        if (toUpDirection) {
            if (isDownDirection) {
                velocity *= 0.01f
            }
            isDownDirection = false
        } else {
            if (!isDownDirection) {
                velocity *= 0.01f
            }
            isDownDirection = true
        }
        isKeyHeldDown = true
        velocity += if (scrollMore) {
            increasePerTick * 6f
        } else {
            increasePerTick
        }
    }

    fun onKeyReleased() {
        isKeyHeldDown = false
        wasLastScrollMore = false
    }

    private suspend fun tick() {
        if (isKeyHeldDown) {
            velocity = if (wasLastScrollMore) {
                (velocity + increasePerTick).coerceAtMost(maxVelocity)
            } else {
                (velocity + increasePerTick).coerceAtMost(maxVelocityForScrollLess)
            }
        } else {
            val decreaseAmount = velocity * (decreasePercentagePerTick / 100f)
            velocity = (velocity - decreaseAmount).coerceIn(0f, maxVelocity)
        }
        if (isDownDirection) {
            scrollStateToModify.scrollBy(velocity)
        } else {
            scrollStateToModify.scrollBy(-velocity)
        }
    }
}
