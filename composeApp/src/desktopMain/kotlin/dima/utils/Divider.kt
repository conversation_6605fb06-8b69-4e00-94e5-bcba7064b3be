package dima.utils

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * A divider is a thin line that groups content in lists and layouts.
 *
 * @param modifier Modifier to be applied to the divider line
 * @param thickness thickness of the divider line, 1 dp is used by default. Using [Dp.Hairline] will
 *   produce a single pixel divider regardless of screen density.
 * @param startIndent start offset of this line, no offset by default
 */
@Composable
fun DarkLightDivider(
    modifier: Modifier = Modifier,
    thickness: Dp = 1.dp,
    startIndent: Dp = 0.dp
) {
    val indentMod =
        if (startIndent.value != 0f) {
            Modifier.padding(start = startIndent)
        } else {
            Modifier
        }
    val targetThickness =
        if (thickness == Dp.Hairline) {
            (1f / LocalDensity.current.density).dp
        } else {
            thickness
        }
    Box(
        modifier.then(indentMod)
            .fillMaxWidth()
            .height(targetThickness)
            .background(color = GlobalStyling.getDividerColor())
    )
}
