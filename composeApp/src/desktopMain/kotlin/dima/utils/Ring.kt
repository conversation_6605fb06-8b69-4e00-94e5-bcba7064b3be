package dima.utils

/**
 * A thread-safe ring with a max capacity where the order of the elements is significant.
 */
class Ring<T>(private val maxCapacity: Int) {
    private val elements = ArrayDeque<T>(maxCapacity)
    private val lock = Any()

    /**
     * Make sure that the element to be added is not already in the ring.
     */
    fun add(element: T) {
        synchronized(lock) {
            if (elements.size >= maxCapacity) {
                elements.removeFirst()
            }
            elements.add(element)
        }
    }

    fun clear() {
        synchronized(lock) {
            elements.clear()
        }
    }

    /**
     * Replace the elements with the given list, but consider the max capacity.
     */
    fun replace(list: List<T>) {
        synchronized(lock) {
            elements.clear()
            if (list.size <= maxCapacity) {
                elements.addAll(list)
            } else {
                elements.addAll(list.subList(list.size - maxCapacity, list.size))
            }
        }
    }

    fun toList(): List<T> {
        synchronized(lock) {
            return elements.toList()
        }
    }

    /**
     * @return the element that matches the predicate, or null if no such element exists.
     */
    fun find(predicate: (T) -> Boolean): T? {
        synchronized(lock) {
            return elements.find(predicate)
        }
    }

    /**
     * @return null if the list is empty, otherwise the most recent element.
     */
    fun getMostRecentOrNull(): T? {
        synchronized(lock) {
            return elements.lastOrNull()
        }
    }

    /**
     * @return null if the list is empty, otherwise the most recent element.
     */
    fun popMostRecentOrNull(): T? {
        synchronized(lock) {
            return elements.removeLastOrNull()
        }
    }
}