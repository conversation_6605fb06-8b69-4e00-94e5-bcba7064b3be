package dima.utils

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

val JsonIgnoreUnknown = Json {
    ignoreUnknownKeys = true
}

@OptIn(ExperimentalSerializationApi::class)
private val json = Json {
    prettyPrint = true
    prettyPrintIndent = "  "
}

/**
 * @return pretty printed JSON string or the original string on any exception
 */
fun String.prettyPrintJson(): String {
    return try {
        json.encodeToString(json.parseToJsonElement(this))
    } catch (e: Exception) {
        this
    }
}