package dima.utils

import GlobalStyling
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.VectorPainter
import androidx.compose.ui.unit.dp

/**
 * Generic tray icon that draws a vector painter with a round background.
 * Used by EmailTray and TranscribeTray.
 */
class TrayIcon(private val painter: VectorPainter) : Painter() {
    override val intrinsicSize = Size(256f, 256f)

    override fun DrawScope.onDraw() {
        drawRoundBackground()
        drawIntoCanvas {
            translate(left = 4f, top = 4f) {
                with(painter) {
                    draw(
                        size = Size(18.dp.toPx(), 18.dp.toPx()),
                        colorFilter = ColorFilter.tint(GlobalStyling.Tray.foregroundColor)
                    )
                }
            }
        }
    }
}

fun DrawScope.drawRoundBackground() {
    drawRoundRect(
        color = GlobalStyling.Tray.backgroundColor,
        cornerRadius = GlobalStyling.Tray.cornerRadius
    )
}
