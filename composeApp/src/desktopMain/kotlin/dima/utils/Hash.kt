package dima.utils

import java.io.File
import java.security.MessageDigest

fun String.asSha256Hash(): String {
    // note that the sha256 variable is not thread safe
    val sha256 = MessageDigest.getInstance("SHA-256")
    val sha256Bytes = sha256.digest(this.toByteArray())
    return sha256Bytes.joinToString("") { "%02x".format(it) }
}

fun File.sha256Hash(): String {
    // note that the sha256 variable is not thread safe
    val digest = MessageDigest.getInstance("SHA-256")
    val fileBytes = this.readBytes()
    val hashBytes = digest.digest(fileBytes)
    return hashBytes.joinToString("") { "%02x".format(it) }
}