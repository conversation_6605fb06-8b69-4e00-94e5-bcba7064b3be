package dima.utils

import androidx.compose.ui.input.key.*
import dima.dialogs.help.appKeys

fun Key.isModifierKey(): Boolean =
    this in setOf(
        Key.<PERSON>ft<PERSON>eft,
        Key.ShiftRight,
        Key.<PERSON>,
        Key.<PERSON>l<PERSON>,
        <PERSON>.<PERSON>,
        <PERSON>.<PERSON>,
        Key.MetaLeft,
        Key.MetaRight
    )

/**
 * Converts a Key to a display string
 */
fun Key.toDisplayString(): String {
    return when (this) {
        Key.NumPadAdd -> "+"
        Key.Spacebar -> "␣"
        Key.Enter -> "⏎"
        Key.Escape -> "⎋"
        Key.Backspace -> "⌫"
        Key.Tab -> "⇥"
        else -> this.asLowerCase()
    }
}

/**
 * Handle keys explicitly since the Compose implementation is buggy around what key is really used.
 */
fun KeyEvent.toLowerKey(): String {
//    println("toLowerKey() for key: ${this.key}, utf16: ${this.utf16CodePoint}")
    return when {
        this.isVerticalBar() -> "|"
        this.isColon() -> ":"
        this.isSemicolon() -> ";"
        this.isAmpersand() -> "&"
        this.isAtSign() -> "@"
        this.isOpenBrace() -> "{"
        this.isCloseBrace() -> "}"
        this.isOpenBracket() -> "("
        this.isCloseBracket() -> ")"
        this.isDoubleQuote() -> "\""
        this.isQuestionMark() -> "?"
        this.key.isPlus() -> "+"
        this.key == Key.Spacebar -> " "
        else -> {
            val result = this.key.asLowerCase()
            println("  -> converted to: $result")
            result
        }
    }
}

/**
 * Pick either this or [isQuestionMarkByKeyCode], otherwise it will trigger twice, for instance in the
 * [dima.dialogs.confirmation.ConfirmationDialog] the mini help dialog will be opened twice.
 */
fun KeyEvent.isQuestionMark(): Boolean =
    this.key == Key.Slash && this.isShiftPressed

/**
 * No idea why in CalculatorApp only the keyCode works.
 */
fun KeyEvent.isQuestionMarkByKeyCode(): Boolean =
    this.key.keyCode == 8589934608 && this.isShiftPressed

fun Key.isPlus(): Boolean = this.keyCode == 17179869291

// "
fun KeyEvent.isDoubleQuote(): Boolean = this.utf16CodePoint == 34

// &
fun KeyEvent.isAmpersand(): Boolean = this.utf16CodePoint == 38

// :
fun KeyEvent.isColon(): Boolean = this.utf16CodePoint == 58

// ;
fun KeyEvent.isSemicolon(): Boolean = this.utf16CodePoint == 59

// |
fun KeyEvent.isVerticalBar(): Boolean = this.utf16CodePoint == 124

// @
fun KeyEvent.isAtSign(): Boolean = this.utf16CodePoint == 64

// {
fun KeyEvent.isOpenBrace(): Boolean = this.utf16CodePoint == 123

// }
fun KeyEvent.isCloseBrace(): Boolean = this.utf16CodePoint == 125

// (
fun KeyEvent.isOpenBracket(): Boolean = this.utf16CodePoint == 40

// =
fun KeyEvent.isEquals(): Boolean = this.utf16CodePoint == 61

// )
fun KeyEvent.isCloseBracket(): Boolean = this.utf16CodePoint == 41

fun Key.asLowerCase(): String = java.awt.event.KeyEvent.getKeyText(this.nativeKeyCode).lowercase()

fun Key.asUpperCase(): String = java.awt.event.KeyEvent.getKeyText(this.nativeKeyCode).uppercase()

/**
 * To have entries only in the help dialog, but not handled by [handleAppMap], leave [onKeyDown] and [onKeyUp] null.
 */
data class AppKey(
    /**
     * Consider as lower case.
     */
    val key: Key,
    val text: String,
    val isShift: Boolean = false,
    val isCtrl: Boolean = false,
    val isCmd: Boolean = false,
    /**
     * If [onKeyDown] and [[onKeyDown]] are null, this is ignored.
     */
    val stopPropagationToChild: Boolean = true,
    val onKeyUp: (() -> Unit)? = null,
    /**
     * If null, then no automatic key mapping is done.
     */
    val onKeyDown: (() -> Unit)? = null
)

fun List<AppKey>.sorted(): List<AppKey> = this.sortedBy { it.key.asLowerCase() }

/**
 * @param blacklist is used for scenarios where holding multiple keys is required, like in DiredApp image moving
 * @return true to stop propagation to the children
 */
fun KeyEvent.handleAppMap(keys: List<AppKey> = appKeys, blacklist: List<Key> = emptyList()): Boolean {
    if (this.isAltPressed) {
        return false
    }
    keys.forEach {
        if ((it.onKeyDown == null && it.onKeyUp == null) ||
            this.isShiftPressed != it.isShift || this.isMetaPressed != it.isCmd || this.isCtrlPressed != it.isCtrl
        ) {
            return@forEach
        }
        if (blacklist.contains(it.key)) {
            return@forEach
        }
        // not sure if this is a bug since 1.8.x in jetpack compose, but the Equals sign is always KeyEventType.KeyDown
        val isEquals = (this.isEquals() && it.key == Key.Equals)
        val isCorrectKey = isEquals ||
                (this.isOpenBracket() && it.key == Key.NumPadLeftParenthesis) ||
                (it.key == this.key)
        if (!isCorrectKey) {
            return@forEach
        }
        if (isEquals) {
            it.onKeyDown?.invoke()
            return it.stopPropagationToChild
        }
        if (this.type == KeyEventType.KeyDown) {
            it.onKeyDown?.invoke()
            return it.stopPropagationToChild
        } else if (this.type == KeyEventType.KeyUp) {
            it.onKeyUp?.invoke()
            return it.stopPropagationToChild
        }
    }
    return false
}

fun KeyEvent.utf16CodePointToString(): String {
    val codePoint = this.utf16CodePoint
    return if (codePoint < 0 || codePoint > 0x10FFFF) {
        throw IllegalArgumentException("Invalid Unicode code point: $codePoint")
    } else if (codePoint <= 0xFFFF) {
        // for BMP characters, simply convert the code point to a char
        codePoint.toChar().toString()
    } else {
        // for supplementary characters, convert the code point to a char array
        String(Character.toChars(codePoint))
    }
}
