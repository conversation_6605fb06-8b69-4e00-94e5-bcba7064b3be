package dima.utils

import dima.settings
import io.ktor.client.*
import io.ktor.client.engine.*
import io.ktor.client.engine.cio.*
import io.ktor.client.request.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.security.cert.X509Certificate
import javax.net.ssl.X509TrustManager

inline fun <reified T> HttpRequestBuilder.setJsonBody(body: T) {
    header("Content-Type", "application/json")
    setBody(Json.encodeToString(body))
}

fun HttpRequestBuilder.setJsonBody(vararg values: Pair<String, String>) {
    header("Content-Type", "application/json")
    setBody(Json.encodeToString(values.toMap()))
}

/**
 * Always use this instead of: HttpClient(CIO) because this sets a proxy.
 *
 * If a proxy is set in the settings, SSL verification is disabled.
 */
fun createHttpClientWithLocalhostProxy(): HttpClient {
    return HttpClient(CIO) {
        if (settings.networkRequestLocalhostProxyPort != null) {
            engine {
                proxy = ProxyBuilder.http("http://localhost:${settings.networkRequestLocalhostProxyPort}")

                https {
                    trustManager = object : X509TrustManager {
                        override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {}
                        override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {}
                        override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
                    }
                }
            }
        }
    }
}