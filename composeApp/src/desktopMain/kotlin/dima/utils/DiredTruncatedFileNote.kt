package dima.utils

import GlobalStyling
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
internal fun DiredTruncatedFileNote(useDarkTextColor: Boolean) {
    val red = if (useDarkTextColor) {
        GlobalStyling.Dark.redTextColor
    } else {
        GlobalStyling.getRedTextColor()
    }
    Box(
        Modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = 12.dp)
            .drawBehind {
                val y = 0f
                drawLine(
                    color = red,
                    start = Offset(0f, y),
                    end = Offset(size.width - 20.dp.toPx(), y),
                    strokeWidth = 2.dp.toPx(),
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(12f, 8f))
                )
            }
    ) {
        Text(
            "File content truncated for preview",
            color = red,
            textAlign = TextAlign.Center,
            fontSize = 16.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 10.dp)
        )
    }
}