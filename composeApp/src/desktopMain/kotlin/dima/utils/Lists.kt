package dima.utils

/**
 * Same as the other [filterHuman] function without a map function since this is already a list of strings.
 * Making the mapper parameter in the other [filterHuman] function nullable, fails in the for loop.
 */
fun List<String>.filterHuman(query: String): List<String> = filterHuman(query) { it }

/**
 * Filter list by query case insensitively.
 * Splits the query by spaces and checks if all parts are contained.
 * Parts starting with '-' are treated as exclusions.
 */
fun <T> List<T>.filterHuman(query: String, mapper: (T) -> String): List<T> {
    if (this.isEmpty()) {
        return this
    }
    val trimmed = query.trim()
    if (trimmed.isEmpty()) {
        return this
    }
    val result = mutableListOf<T>()
    val queryParts = trimmed.lowercase().split(" ").filter { it.isNotEmpty() }.sortedBy {
        if (it.startsWith("-")) {
            -1 // exclusion parts first
        } else {
            0
        }
    }
    for (item in this) {
        val mapped = mapper(item).lowercase()
        var pass = true
        for (queryPart in queryParts) {
            // check length, so a single minus is still matched
            if (queryPart.length >= 2 && queryPart.startsWith("-")) {
                if (mapped.contains(queryPart.drop(1))) {
                    pass = false
                    break
                }
            } else { // Also handles single '-' if not starting with it (e.g. "a-b")
                if (!mapped.contains(queryPart)) {
                    pass = false
                    break
                }
            }
        }
        if (pass) {
            result.add(item)
        }
    }
    return result
}

object Lists {

    /**
     * @return a positive integer or null. 0 if [fromSetting] is null, null when not found in [entries] and
     * otherwise an index starting from 0 in the [entries]
     */
    fun <T> getInitialSelected(entries: List<T>, fromSetting: String?, mapper: (T) -> String): Int? {
        if (fromSetting != null) {
            val index = entries.indexOfFirst {
                mapper(it) == fromSetting
            }
            if (index == -1) {
                return null
            }
            return index
        }
        return if (entries.isEmpty()) null else 0
    }

    @Suppress("FoldInitializerAndIfToElvis")
    fun <T> getSelectedOrNext(selected: Int?, entries: List<T>, fromSetting: String?, mapper: (T) -> String): Int {
        val newSelected = getInitialSelected(entries, fromSetting, mapper)
        if (newSelected == null) {
            return selected?.coerceAtMost(entries.size - 1) ?: (entries.size - 1)
        }
        return newSelected
    }

}