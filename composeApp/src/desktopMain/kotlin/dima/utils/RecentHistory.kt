package dima.utils

import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import jdk.internal.org.jline.utils.Colors.s
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File

/**
 * Sorts the list by the passed history.
 *
 * @param history if empty, the original list is returned.
 */
fun <T> List<T>.sortedByHistory(history: List<T>): List<T> {
    if (history.isEmpty()) {
        return this
    }
    val (inHistory, notInHistory) = this.partition { history.contains(it) }
    val sortedInHistory = inHistory.sortedWith(compareBy { history.indexOf(it) })
    return sortedInHistory + notInHistory
}

fun Array<File>.sortedByHistory(): List<String> {
    val stringFiles = map { it.absolutePath }
    val recentFiles: List<String> = RecentHistory.getRecentFiles()
    val (filesInHistory: List<String>, filesNotInHistory: List<String>) = stringFiles.partition {
        recentFiles.contains(it)
    }
    val sortedFilesInHistory: List<String> = filesInHistory.sortedByHistory(recentFiles)
    val sortedFilesNotInHistory: List<String> = filesNotInHistory.sortedBy { it }
    return sortedFilesInHistory + sortedFilesNotInHistory
}

fun openCopyRecentTextHistoryDialog() {
    val recentTexts = RecentHistory.getRecentTexts()
    openCompletionDialog("Copy recent text to clipboard", recentTexts, hideCopyCmdActionInBottomBar = true) {
        RecentHistory.rememberText(it.text)
        copyToClipboard(it.text)
    }
}

/**
 * Prescient is the name of the Emacs package where I have this idea from.
 */
object RecentHistory {

    // both files contain one JSON array of strings
    private val recentTextsFile = File("cache", "recent-texts.json")
    private val recentFilesFile = File("cache", "recent-files.json")

    // The mutexes are required to not corrupt the files. I had crashes in the dired threads with manual DiredApp edits.
    private val recentTextsMutex = Mutex()
    private val recentFilesMutex = Mutex()

    inline fun <reified T> readFromJsonFile(file: File): List<T> {
        return if (file.exists()) {
            Json.decodeFromString<List<T>>(file.readText())
        } else {
            listOf()
        }
    }

    fun getRecentTexts(): List<String> {
        return runBlocking {
            recentTextsMutex.withLock {
                if (recentTextsFile.exists()) {
                    Json.decodeFromString<List<String>>(recentTextsFile.readText())
                } else {
                    listOf()
                }
            }
        }
    }

    /**
     * @return list of absolute paths
     */
    fun getRecentFiles(): List<String> {
        return runBlocking {
            recentFilesMutex.withLock {
                if (recentFilesFile.exists()) {
                    Json.decodeFromString<List<String>>(recentFilesFile.readText())
                } else {
                    listOf()
                }
            }
        }
    }

    fun rememberText(text: String, ignoreShortText: Boolean = false, trimText: Boolean = false) {
        runBlocking {
            recentTextsMutex.withLock {
                rememberAndPersistStringToFileAsJson(
                    text = text,
                    ignoreShortText = ignoreShortText,
                    trimText = trimText,
                    file = recentTextsFile
                )
            }
        }
    }

    fun rememberFile(file: File) {
        rememberFile(file.absolutePath)
    }

    fun rememberFile(absolutePath: String) {
        runBlocking {
            recentFilesMutex.withLock {
                rememberAndPersistToFileAsJson(
                    absolutePath,
                    recentFilesFile
                )
            }
        }
    }

    /**
     * The passed [candidate] is added to the first position of the list, so on viewing [file], the first element
     * is the most recently used.
     */
    inline fun <reified T> rememberAndPersistToFileAsJson(candidate: T, file: File) {
        val initialList = readFromJsonFile<T>(file)
        val list = initialList.toMutableList()
        if (initialList.isNotEmpty() && initialList.first() == candidate) {
            return
        }
        // Remove any existing occurrence and add to front
        list.removeAll { it == candidate }
        list.add(0, candidate)
        file.writeText(Json.encodeToString(list))
    }

    /**
     * The passed [text] is added to the first position of the list, so on viewing [file], the first element
     * is the most recently used.
     */
    private fun rememberAndPersistStringToFileAsJson(
        text: String,
        ignoreShortText: Boolean = true,
        trimText: Boolean = true,
        @Suppress("SameParameterValue") file: File
    ) {
        val trimmed = if (trimText) text.trim() else text
        if (ignoreShortText && trimmed.length <= 3) {
            return
        }
        val initialList = readFromJsonFile<String>(file)
        // If it's already the most recent, don't do anything
        if (initialList.isNotEmpty() && initialList.first() == text) {
            return
        }
        val list = initialList.toMutableList()
        list.removeAll { it == text }
        list.add(0, text)
        file.writeText(Json.encodeToString(list))
    }

}