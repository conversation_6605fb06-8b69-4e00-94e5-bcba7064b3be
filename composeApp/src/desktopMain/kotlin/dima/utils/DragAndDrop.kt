package dima.utils

import Globals
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.draganddrop.dragAndDropSource
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draganddrop.DragAndDropTransferAction
import androidx.compose.ui.draganddrop.DragAndDropTransferData
import androidx.compose.ui.draganddrop.DragAndDropTransferable
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.withStyle
import dima.color.TailwindCssColors
import java.awt.datatransfer.DataFlavor
import java.awt.datatransfer.Transferable
import java.nio.file.Path

// References:
// https://github.com/dragossusi/ktcnc/blob/c59df2d426f841f04c1b5bf7fe0590ad82db156d/frontend/filesystem/src/jvmMain/kotlin/components/filesystem/FileSystemItemView.jvm.kt
// https://stackoverflow.com/a/78973741/6908755

/**
 * Draw a red rectangle with the same elements size with a dummy text in the middle.
 */
private fun fileNameDrawDragDecoration(paths: List<Path>): DrawScope.() -> Unit {
    return {
        drawRoundRect(
            color = TailwindCssColors.blue600,
            topLeft = Offset(x = 0f, y = 0f),
            cornerRadius = CornerRadius(24f),
            size = Size(size.width, size.height)
        )
        val textToShow = if (paths.size == 1) {
            paths.first().fileName.toString()
        } else {
            "${paths.size} files"
        }
        val textLayoutResult = Globals.textMeasurer.measure(
            text = AnnotatedString.Builder().apply {
                withStyle(style = SpanStyle(color = Color.White)) {
                    append(textToShow)
                }
            }.toAnnotatedString(),
            layoutDirection = layoutDirection,
            density = this
        )
        drawText(
            textLayoutResult = textLayoutResult,
            topLeft = Offset(
                x = (size.width - textLayoutResult.size.width) / 2,
                y = (size.height - textLayoutResult.size.height) / 2,
            )
        )
    }
}

/**
 * Allows to drag the component out into other apps to copy/move/link a file.
 *
 * Note that on 'regular' mouse drag & drop into macOS Finder, it moves the file.
 *
 * @param onTransferCompleted null, when the drag is aborted.
 * @param drawDragDecoration These are drawn as a visual representation of the component being dragged.
 */
@OptIn(ExperimentalFoundationApi::class, ExperimentalComposeUiApi::class)
fun Modifier.fileDragSource(
    paths: List<Path>,
    onTransferCompleted: ((userAction: DragAndDropTransferAction?) -> Unit)? = null,
    drawDragDecoration: (DrawScope.() -> Unit)? = fileNameDrawDragDecoration(paths)
): Modifier = dragAndDropSource(
    drawDragDecoration = {
        drawDragDecoration?.invoke(this)
    },
    transferData = { offset ->
        DragAndDropTransferData(
            DragAndDropTransferable(
                PathTransferable(paths)
            ),
            supportedActions = listOf(
                DragAndDropTransferAction.Copy,
                DragAndDropTransferAction.Move,
                DragAndDropTransferAction.Link,
            ),
            onTransferCompleted = { action ->
                onTransferCompleted?.invoke(action)
            },
            dragDecorationOffset = offset
        )
    }
)

private class PathTransferable(private val paths: List<Path>) : Transferable {
    override fun getTransferDataFlavors(): Array<DataFlavor> {
        return arrayOf(DataFlavor.javaFileListFlavor)
    }

    override fun isDataFlavorSupported(p0: DataFlavor?): Boolean {
        return p0?.isFlavorJavaFileListType == true
    }

    override fun getTransferData(p0: DataFlavor?): Any {
        return paths.map { it.toFile() }
    }
}