package dima.database

import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.transaction
import java.sql.Connection

fun <T> transactionToAvoidBusySqlite(
    db: Database? = null,
    statement: Transaction.() -> T
): T {
    return transaction(db) {
        TransactionManager.manager.defaultIsolationLevel = Connection.TRANSACTION_SERIALIZABLE
        TransactionManager.manager.defaultMaxAttempts = 20
        TransactionManager.manager.defaultMinRetryDelay = 20
        TransactionManager.manager.defaultMaxRetryDelay = 20
        statement()
    }
}