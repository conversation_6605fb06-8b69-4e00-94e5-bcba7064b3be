package dima.timers

import dima.dateTime.DateTimeFormat
import dima.os.Osascript
import dima.os.playMp3File
import dima.globalState.GlobalState
import dima.dateTime.DateTimeUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.seconds

class TimerThread : Thread() {
    override fun run() {
        val sleepDuration = 1.seconds.inWholeMilliseconds
        while (true) {
            if (GlobalState.timers.isEmpty()) {
                sleep(sleepDuration)
                continue
            }
            val toDelete = GlobalState.timers.filter { timer ->
                val dateTime = DateTimeUtils.fromIsoToLocalDateTime(timer.start)
                    .plusSeconds(timer.durationSeconds.toLong())
                return@filter java.time.LocalDateTime.now() > dateTime
            }
            toDelete.forEach {
                playMp3File("alert.mp3")
                val time = DateTimeUtils.fromIsoToLocalDateTime(it.start).format(DateTimeFormat.hourColonMinute)
                val relativeDuration = startTimeToRelative(it, addDuration = false)
                CoroutineScope(Dispatchers.IO).launch {
                    val prefix = if (it.name == null) {
                        "$relativeDuration timer"
                    } else {
                        "$relativeDuration '${it.name}' timer"
                    }
                    Osascript.showOsNotification("$prefix. Started at $time")
                    Osascript.showDismissableDialog("$prefix\n\nStarted at $time")
                }
                GlobalState.timers = GlobalState.timers.filter { timer -> timer != it }
            }
            GlobalState.timers = GlobalState.timers.map {
                it.copy(displayText = startTimeToRelative(it, addDuration = true))
            }
            sleep(sleepDuration)
        }
    }
}
