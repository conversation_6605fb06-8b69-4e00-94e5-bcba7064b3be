package dima.timers

import Globals
import androidx.compose.runtime.Composable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.rememberTrayState
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.utils.drawRoundBackground

private class TextTrayIcon(val text: String) : <PERSON>() {
    override val intrinsicSize = Size(256f, 256f)

    override fun DrawScope.onDraw() {
        drawRoundBackground()
        drawText(
            TextMeasurer(Globals.fontFamilyResolver, Density(100f), LayoutDirection.Ltr),
            style = TextStyle(
                fontSize = 10.sp,
                textAlign = TextAlign.Center,
                lineHeight = 8.sp,
                color = GlobalStyling.Tray.foregroundColor
            ),
            overflow = TextOverflow.Visible,
            topLeft = Offset(0f, 4f),
            text = text,
            size = Size(45f, 45f)
        )
    }
}

@Composable
fun ApplicationScope.TimerTray() {
    GlobalState.timers.forEach {
        // not sure why this can happen
        if (it.displayText.isNotEmpty()) {
            val state = rememberTrayState()
            // I tried adding a menu to display the timer name and a Quit item, but it always crashes the application with:
            // Use instances of NSMenu and NSMenuItem directly instead.
            Tray(
                state = state,
                icon = TextTrayIcon(it.displayText.take(it.displayText.length - 1) + "\n" + it.displayText.last()),
            )
        }
    }
}
