package dima.timers

import dima.apps.notifications.NotificationType
import dima.apps.notifications.showNotification
import dima.dateTime.DateTimeFormat
import dima.dateTime.DateTimeUtils
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.confirmation.openConfirmationDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState
import dima.globalState.TimerState

internal fun startTimeToRelative(timer: TimerState, addDuration: Boolean): String {
    val end = DateTimeUtils.fromIsoToLocalDateTime(timer.start)
    val duration = if (addDuration) {
        val endPlusDuration = end.plusSeconds(timer.durationSeconds.toLong())
        java.time.Duration.between(java.time.LocalDateTime.now(), endPlusDuration)
    } else {
        val endPlusDuration = end.plusSeconds(timer.durationSeconds.toLong())
        java.time.Duration.between(end, endPlusDuration)
    }
    if (duration.seconds <= 59) {
        return duration.seconds.toString() + "s"
    }
    val minutes = duration.toMinutes()
    if (minutes <= 59) {
        return minutes.toString() + "m"
    }
    return duration.toHours().toString() + "h"
}

object Timers {

    fun addNew() {
        openTextInputDialog(
            "Add new timer",
            "15 → 15 minutes\n6:30 → at next 6:30\n9 Tee → 9 minutes with name 'Tee'",
            minLines = 1
        ) {

            fun createNewTimerState(durationSeconds: Int, name: String): TimerState {
                return TimerState(
                    DateTimeUtils.nowAsIsoDateTime(),
                    durationSeconds = durationSeconds,
                    name = name.ifBlank { null }
                )
            }

            fun doMinutesOnly(time: String, name: String) {
                val minutes = try {
                    time.trim().toInt()
                } catch (_: NumberFormatException) {
                    showNotification("Invalid time", type = NotificationType.Error)
                    return
                }
                val new = createNewTimerState(minutes * 60, name)
                GlobalState.timers += new.copy(displayText = startTimeToRelative(new, addDuration = true))
                showNotification("Started timer with $minutes minutes")
            }

            val trimmedParts = it.trim().split(" ")
            val time = trimmedParts.first()
            val name = trimmedParts.drop(1).joinToString(" ")
            if (time.all { char -> char.isDigit() }) {
                doMinutesOnly(time, name)
                return@openTextInputDialog TextInputDialogConfirmAction.Close
            }
            val timeParts = time.split(":")
            if (timeParts.size != 2) {
                showNotification("Invalid time", type = NotificationType.Error)
                return@openTextInputDialog TextInputDialogConfirmAction.Close
            }
            val hour = timeParts[0].toIntOrNull() ?: -1
            val minute = timeParts[1].toIntOrNull() ?: -1
            if (hour !in 0..23 || minute !in 0..59) {
                showNotification("Invalid time", type = NotificationType.Error)
                return@openTextInputDialog TextInputDialogConfirmAction.Close
            }
            val givenTime = hour * DateTimeUtils.SECONDS_IN_HOUR + minute * DateTimeUtils.SECONDS_IN_MINUTE
            val currentTime = DateTimeUtils.nowInSeconds()
            val durationSeconds = if (givenTime > currentTime) {
                givenTime - currentTime
            } else {
                DateTimeUtils.SECONDS_IN_DAY - currentTime + givenTime
            }
            val new = createNewTimerState(durationSeconds, name)
            GlobalState.timers += new.copy(displayText = startTimeToRelative(new, addDuration = true))
            showNotification("Started timer to $hour:${minute.toString().padStart(2, '0')}")
            return@openTextInputDialog TextInputDialogConfirmAction.Close
        }
    }

    fun pickTimerToStop() {
        when (GlobalState.timers.size) {
            0 -> {
                showNotification("No currently running timer")
            }

            1 -> {
                val timer = GlobalState.timers.first()
                val duration = startTimeToRelative(timer, addDuration = false)
                val title = if (timer.name == null) {
                    "Stop $duration timer?"
                } else {
                    "Stop $duration ${timer.name} timer?"
                }
                openConfirmationDialog(
                    title,
                    "Started at " + DateTimeUtils.fromIsoToLocalDateTime(timer.start)
                        .format(DateTimeFormat.hourColonMinute)
                ) {
                    GlobalState.timers = GlobalState.timers.drop(1)
                }
            }

            else -> {
                val copiedTimers = GlobalState.timers.toList()
                val candidates = copiedTimers.map {
                    val total = startTimeToRelative(it, addDuration = false)
                    val prefix = "Done in $total. Started at " + DateTimeUtils.fromIsoToLocalDateTime(it.start)
                        .format(DateTimeFormat.hourColonMinute)
                    if (it.name == null) {
                        prefix
                    } else {
                        "$prefix. ${it.name}"
                    }
                }
                openCompletionDialog("Pick timer to delete", candidates) {
                    // do not drop by index because it could have changed at the time this was confirmed
                    val timer = copiedTimers[it.index!!]
                    GlobalState.timers = GlobalState.timers.filter {
                        !(it.durationSeconds == timer.durationSeconds && it.start == timer.start)
                    }
                }
            }
        }
    }
}