package dima.youtube

import dialogs
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dialogs.closeDialogWithId
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.feeds.FeedChannel
import dima.os.homeWithoutSlash
import dima.process.LoggedProcess
import dima.settings
import dima.utils.Result
import dima.utils.abbreviatePath
import dima.utils.truncateWithEllipsisMiddle
import dima.youtube.YouTubeChannelIdInfo.getYouTubeChannelIdFromHandleUrl
import java.io.File

object YouTube {

    /**
     * Builds yt-dlp arguments with cookies support if configured in settings.
     */
    private fun buildYtDlpArgs(baseArgs: List<String>): List<String> {
        val args = baseArgs.toMutableList()
        settings.feed.youTubeCookieFile?.let { cookieFile ->
            args.addAll(0, listOf("--cookies", cookieFile.absolutePath))
        }
        return args
    }

    suspend fun getRssFeedUrlsForFeed(): List<FeedChannel> {
        return settings.feed.youTube.channelsToCheckForNewVideos.mapNotNull {
            val result = getYouTubeChannelIdFromHandleUrl(it.url)
            when (result) {
                is Result.Error -> {
                    showErrorNotification("Failed to get YouTube channel ID", result.error)
                    return@mapNotNull null
                }

                is Result.Success -> {
                    FeedChannel(
                        title = "YouTube: " + it.title,
                        url = "https://www.youtube.com/feeds/videos.xml?channel_id=${result.value}"
                    )
                }
            }
        }
    }

    fun openDownloadDialog() {
        val candidates = listOf(
            "Audio to ~/Downloads",
            "Video to ~/Downloads",
            "Audio to ~/Downloads (playlist)",
            "Video to ~/Downloads (playlist)",
            "Audio with cover to audio database (no playlist)",
        )
        openCompletionDialog("YouTube download option", candidates, hideCopyCmdActionInBottomBar = true) {
            when (it.text) {
                "Audio to ~/Downloads" -> downloadAudioToDownloads(downloadAllInPlaylist = false)
                "Audio to ~/Downloads (playlist)" -> downloadAudioToDownloads(downloadAllInPlaylist = true)
                "Audio with cover to audio database (no playlist)" -> openDialogToPickFileNameAndDownloadAudioAndCover()
                "Video to ~/Downloads" -> downloadVideoToDownloads(downloadAllInPlaylist = false)
                "Video to ~/Downloads (playlist)" -> downloadVideoToDownloads(downloadAllInPlaylist = true)
                else -> throw Exception("Unhandled ${it.text}")
            }
        }
    }

    fun downloadVideoToDownloads(url: String, downloadAllInPlaylist: Boolean) {
        val notification = showLoadingNotification("Downloading YouTube video to ~/Downloads...")
        LoggedProcess(
            command = "yt-dlp",
            args = buildYtDlpArgs(
                if (downloadAllInPlaylist) {
                    listOf(url)
                } else {
                    listOf("--no-playlist", url)
                }
            ),
            showErrorNotifications = false,
            workingDirectory = File(homeWithoutSlash, "Downloads"),
            onOutputLine = { line ->
                notification.update(
                    message = line.trim().truncateWithEllipsisMiddle(200)
                )
            },
            onFinish = { process ->
                if (process.exitCode.value == 0) {
                    notification.toInfo("Finished downloading from YouTube")
                } else {
                    notification.toError("yt-dlp error", process.getOutput().trim())
                }
            }
        ).startAsync()
    }

    private fun downloadVideoToDownloads(downloadAllInPlaylist: Boolean) {
        openTextInputDialog(
            "Paste YouTube URL to download video file to ~/Downloads",
        ) { url ->
            downloadVideoToDownloads(url, downloadAllInPlaylist)
            return@openTextInputDialog TextInputDialogConfirmAction.Close
        }
    }

    fun downloadAudioToDownloads(url: String, downloadAllInPlaylist: Boolean) {
        val notification = showLoadingNotification("Downloading YouTube audio to ~/Downloads...")
        val args = mutableListOf(
            "--extract-audio",
            "--audio-quality", "0"
        ).apply {
            if (!downloadAllInPlaylist) {
                add("--no-playlist")
            }
        }
        LoggedProcess(
            command = "yt-dlp",
            args = buildYtDlpArgs(args + url),
            workingDirectory = File(homeWithoutSlash, "Downloads"),
            showErrorNotifications = false,
            onOutputLine = { line ->
                notification.update(
                    message = line.trim().truncateWithEllipsisMiddle(200)
                )
            },
            onFinish = { process ->
                if (process.exitCode.value == 0) {
                    notification.toInfo("Finished downloading from YouTube")
                } else {
                    notification.toError("yt-dlp error", process.getOutput().trim())
                }
            }
        ).startAsync()
    }

    private fun downloadAudioToDownloads(downloadAllInPlaylist: Boolean) {
        openTextInputDialog(
            "Paste YouTube URL to download audio file to ~/Downloads",
        ) { url ->
            downloadAudioToDownloads(url, downloadAllInPlaylist)
            return@openTextInputDialog TextInputDialogConfirmAction.Close
        }
    }

    fun openDialogToPickFileNameAndDownloadAudioAndCover(url: String? = null) {
        if (settings.audio.databasePath == null) {
            showErrorNotification("The audio database path is not set")
            return
        }
        openTextInputDialog(
            "Paste YouTube URL to download audio file and cover",
            subTitle = settings.audio.databasePath.abbreviatePath(),
            initialText = url
        ) first@{ currentUrl ->
            val firstDialogId = dialogs.last().id
            openTextInputDialog(
                "Enter base file name without file extension",
                subTitle = settings.audio.databasePath.abbreviatePath()
            ) { newFileName ->
                if (newFileName.trim().isBlank()) {
                    showErrorNotification("File name should not be blank")
                    return@openTextInputDialog TextInputDialogConfirmAction.KeepOpen
                }
                val dbPathFile = settings.audio.databasePath
                val files = dbPathFile.listFiles()
                if (files == null) {
                    closeDialogWithId(firstDialogId)
                    return@openTextInputDialog TextInputDialogConfirmAction.Close
                }
                val existing = files.map { it.name }.toSet()
                if (existing.contains(newFileName)) {
                    showErrorNotification("A file with the name '$newFileName' already exists")
                    return@openTextInputDialog TextInputDialogConfirmAction.KeepOpen
                }
                val baseMessage = "$newFileName\n$currentUrl"
                val notification = showLoadingNotification(
                    "Downloading YouTube audio and cover",
                    baseMessage
                )
                LoggedProcess(
                    command = "yt-dlp",
                    args = buildYtDlpArgs(
                        listOf(
                            "--write-thumbnail",
                            "--extract-audio",
                            "--audio-quality", "0",
                            "--no-playlist",
                            "--output", newFileName,
                            currentUrl
                        )
                    ),
                    workingDirectory = dbPathFile,
                    onOutputLine = { line ->
                        notification.update(
                            message = baseMessage + "\n" + line.trim().truncateWithEllipsisMiddle(200)
                        )
                    },
                    showErrorNotifications = false,
                    onFinish = { process ->
                        if (process.exitCode.value == 0) {
                            notification.toInfo("Finished downloading from YouTube")
                        } else {
                            notification.toError("yt-dlp error", process.getOutput().trim())
                        }
                    }
                ).startAsync()
                closeDialogWithId(firstDialogId)
                return@openTextInputDialog TextInputDialogConfirmAction.Close
            }
            // keep open for now, on confirming the next text input dialog, both dialogs will be closed
            return@first TextInputDialogConfirmAction.KeepOpen
        }
    }

    fun isYouTubeUrl(url: String): Boolean {
        return url.contains("youtube.com") || url.contains("youtu.be")
    }
}
