package dima.youtube

import dima.apps.networkActivity.getLogged
import dima.settings
import dima.utils.JsonIgnoreUnknown
import dima.utils.Result
import dima.utils.createHttpClientWithLocalhostProxy
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import java.io.File

/**
 * @return  handle without the "@", or null.
 */
internal fun extractAtHandle(url: String): String? {
    return try {
        if (url.startsWith("@")) {
            return url
        }
        val uri = java.net.URI(url)
        uri.path?.split("/")?.find { it.startsWith("@") }?.removePrefix("@")
    } catch (_: Exception) {
        null
    }
}

@Serializable
private data class YouTubeSearchResponse(val items: List<Item>) {

    @Serializable
    data class Item(val id: Id) {

        @Serializable
        data class Id(
            @SerialName("channelId")
            val channelId: String? = null
        )
    }
}

object YouTubeChannelIdInfo {

    internal const val RELATIVE_CACHE_DIRECTORY = "cache/feed"
    private val cacheFile = File(RELATIVE_CACHE_DIRECTORY, "url_to_id_cache.json")

    @Volatile
    private var cache: MutableMap<String, String> = mutableMapOf()

    init {
        if (cacheFile.exists()) {
            try {
                val json = cacheFile.readText()
                cache = JsonIgnoreUnknown.decodeFromString(json)
            } catch (e: Exception) {
                println("Failed to load cache: ${e.message}")
                cache = mutableMapOf()
            }
        }
    }

    private fun saveCache() {
        try {
            val json = JsonIgnoreUnknown.encodeToString(cache)
            cacheFile.writeText(json)
        } catch (e: Exception) {
            println("Failed to save cache: ${e.message}")
        }
    }

    /**
     * @param urlOrId can be:
     *   https://www.youtube.com/@fireship
     *   https://www.youtube.com/@fireship/videos
     *   https://www.youtube.com/channel/UCTiNWm_Qfiulu45VYWXrHsA
     *   https://www.youtube.com/channel/UCTiNWm_Qfiulu45VYWXrHsA/videos
     *   UCTiNWm_Qfiulu45VYWXrHsA
     */
    internal suspend fun getYouTubeChannelIdFromHandleUrl(urlOrId: String): Result<String> {
        cache[urlOrId]?.let {
            return Result.Success(it)
        }
        val result = getYouTubeChannelIdFromHandleUrlImpl(urlOrId)
        if (result is Result.Success) {
            cache[urlOrId] = result.value
            saveCache()
        }
        return result
    }

    /**
     * @param urlOrId can be:
     *   https://www.youtube.com/@fireship
     *   https://www.youtube.com/@fireship/videos
     *   https://www.youtube.com/channel/UCTiNWm_Qfiulu45VYWXrHsA
     *   https://www.youtube.com/channel/UCTiNWm_Qfiulu45VYWXrHsA/videos
     *   UCTiNWm_Qfiulu45VYWXrHsA
     */
    private suspend fun getYouTubeChannelIdFromHandleUrlImpl(urlOrId: String): Result<String> {
        // Directly parse URLs of the shape https://www.youtube.com/channel/CHANNEL_ID
        try {
            val uri = java.net.URI(urlOrId)
            val pathSegments = uri.path?.split("/")?.filter { it.isNotEmpty() } ?: emptyList()
            if (pathSegments.size >= 2 && pathSegments[0] == "channel") {
                val channelId = pathSegments[1]
                return Result.Success(channelId)
            }
        } catch (_: Exception) {
            // ignore, will fall through to other mechanisms
        }
        if (settings.feed.youTube.apiKeyToExtractChannelIds == null) {
            return Result.Error("YouTube API key is not set in settings")
        }
        if (!urlOrId.trim().startsWith("http")) {
            return Result.Success(urlOrId)
        }
        val handle = extractAtHandle(urlOrId)
        if (handle == null) {
            return Result.Error("Invalid URL format: can't find a handle segment in path.")
        }
        val client = createHttpClientWithLocalhostProxy()
        try {
            val response = client.getLogged("https://www.googleapis.com/youtube/v3/search") {
                url {
                    parameters.append("part", "snippet")
                    parameters.append("type", "channel")
                    parameters.append("q", handle)
                    parameters.append("key", settings.feed.youTube.apiKeyToExtractChannelIds)
                    parameters.append("maxResults", "1")
                }
            }
            if (response.statusCode != 200) {
                return Result.Error("Failed to query YouTube API: ${response.body}")
            }
            val apiResp: YouTubeSearchResponse =
                JsonIgnoreUnknown.decodeFromString<YouTubeSearchResponse>(response.body)
            val channelId = apiResp.items.firstOrNull()?.id?.channelId
            return if (channelId == null) {
                Result.Error("Channel ID not found (API response OK but no item with channelId)")
            } else {
                Result.Success(channelId)
            }
        } catch (e: Exception) {
            return Result.Error("Error querying YouTube API: ${e.message}")
        } finally {
            client.close()
        }
    }
}
