package dima.feeds

import GlobalEvent
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import dialogs
import dima.apps.notifications.showErrorNotification
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.help.appKeys
import dima.globalState.PaneState
import dima.utils.LaunchedEffectForAppKeys
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.os.openUrl
import dima.utils.AppKey
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.Lists
import dima.youtube.YouTube
import globalEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun FeedsApp(paneState: dima.globalState.PaneState) {
    remember {
        FeedsDatabase.setup()
    }
    var newOnly by remember { mutableStateOf(GlobalState.feed.newOnly) }
    var entries by remember { mutableStateOf<List<FeedEntry>>(emptyList()) }
    var selected by remember { mutableStateOf<Int?>(null) }
    var nextScrollAnimated by remember { mutableStateOf(false) }
    val listState = rememberLazyListState()
    val appFocusRequester = remember { FocusRequester() }
    val coroutineScope = rememberCoroutineScope()

    suspend fun scrollTo() {
        val offset = (-GlobalState.mainWindow.heightInDp / 2).toInt()
        if (nextScrollAnimated) {
            listState.animateScrollToItem(selected!!, scrollOffset = offset)
            nextScrollAnimated = false
        } else {
            listState.scrollToItem(selected!!, scrollOffset = offset)
        }
    }

    suspend fun updateSelectedAfterEntriesChange() {
        selected = Lists.getInitialSelected(entries, GlobalState.feed.selectedLink) { it.link }
        selected = if (selected == null) {
            0
        } else {
            selected!!
        }
        // scrollTo() here can lead to issues if entries list is empty
        if (entries.isNotEmpty() && selected != null && selected!! < entries.size) {
            scrollTo()
        }
    }

    fun updateSelected(article: FeedEntry) {
        FeedsDatabase.markAsRead(article, isRead = true)
        val oldIndex = entries.indexOfFirst { it.link == article.link }
        entries = FeedsDatabase.fetchEntries(newOnly)
        selected = if (oldIndex < entries.size) {
            oldIndex
        } else {
            nextScrollAnimated = true
            entries.size - 1
        }
    }

    fun preview(article: FeedEntry) {
        openUrl(article.link)
        updateSelected(article)
    }

    val localAppKeys = remember {
        listOf(
            AppKey(Key.Z, "Copy all visible entries") { copyAllEntries(newOnly = newOnly) },
            AppKey(Key.J, "Copy link to clipboard") {
                if (entries.isNotEmpty()) {
                    val article = entries.getOrNull(selected ?: -1)
                    if (article != null) {
                        copyToClipboard(article.link)
                    }
                }
            },
            AppKey(Key.Enter, "Open selected article in browser and jump to last entry") {
                if (entries.isEmpty()) {
                    return@AppKey
                }
                val article = entries.getOrNull(selected ?: -1)
                if (article == null) {
                    return@AppKey
                }
                preview(article)
            },
            AppKey(Key.Y, "Download YouTube video/audio") {
                if (entries.isEmpty() || selected == null) {
                    YouTube.openDownloadDialog()
                    return@AppKey
                }
                val article = entries.getOrNull(selected!!) ?: return@AppKey
                if (!YouTube.isYouTubeUrl(article.link)) {
                    showErrorNotification("Not a YouTube video", article.title)
                    return@AppKey
                }
                val candidates = listOf(
                    "Video to ~/Downloads",
                    "Audio to ~/Downloads",
                )
                openCompletionDialog(
                    "Download YouTube video:\n" + article.title,
                    candidates,
                    hideCopyCmdActionInBottomBar = true
                ) {
                    when (it.text) {
                        "Audio to ~/Downloads" -> YouTube.downloadAudioToDownloads(
                            article.link,
                            downloadAllInPlaylist = false
                        )

                        "Video to ~/Downloads" -> YouTube.downloadVideoToDownloads(
                            article.link,
                            downloadAllInPlaylist = false
                        )

                        else -> throw Exception("Unhandled ${it.text}")
                    }
                }
            },
            AppKey(Key.G, "Transcribe YouTube video and mark read") {
                if (entries.isEmpty() || selected == null) return@AppKey
                val article = entries.getOrNull(selected!!) ?: return@AppKey
                transcribeYouTubeEntry(
                    article = article,
                    coroutineScope = coroutineScope,
                    newOnly = newOnly,
                    changeEntries = { entries = it },
                    updateSelectedAfterEntriesChange = ::updateSelectedAfterEntriesChange
                )
            },
            AppKey(Key.D, "Mark as read") {
                if (entries.isEmpty() || selected == null) {
                    return@AppKey
                }
                val article = entries.getOrNull(selected!!)
                if (article == null) {
                    return@AppKey
                }
                updateSelected(article)
            },
            AppKey(Key.F, "Toggle new only") {
                newOnly = !newOnly
            },
            AppKey(Key.T, "Go one article down") {
                if (selected != null && selected!! + 1 < entries.size) {
                    nextScrollAnimated = true
                    selected = selected!! + 1
                }
            },
            AppKey(Key.M, "Go 6 articles down") {
                if (selected == null) {
                    return@AppKey
                }
                val newIndex = selected!! + 6
                nextScrollAnimated = true
                selected = newIndex.coerceAtMost(entries.size - 1)
            },
            AppKey(Key.V, "Go 6 articles up") {
                if (selected == null) {
                    return@AppKey
                }
                val newIndex = selected!! - 6
                nextScrollAnimated = true
                selected = newIndex.coerceAtLeast(0)

            },
            AppKey(Key.C, "Go one article up") {
                if (selected != null && selected!! >= 1) {
                    nextScrollAnimated = true
                    selected = selected!! - 1
                }
            }
        )
    }

    LaunchedEffectForAppKeys(paneState, localAppKeys)

    FeedsAppUi(
        appFocusRequester = appFocusRequester,
        newOnly = newOnly,
        listState = listState,
        selected = selected,
        onNewOnlyChange = { newOnly = it },
        onSelectedChange = { selected = it },
        entries = entries,
        preview = ::preview
    )

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }
    LaunchedEffect(newOnly) {
        GlobalState.feed = GlobalState.feed.copy(newOnly = newOnly)
        entries = FeedsDatabase.fetchEntries(newOnly)
        updateSelectedAfterEntriesChange()
    }
    
    // React to database changes by watching the database change timestamp
    LaunchedEffect(FeedsDatabase.lastDatabaseChange, newOnly) {
        // Refresh entries when the database changes
        val newEntries = FeedsDatabase.fetchEntries(newOnly)
        if (newEntries != entries) {
            entries = newEntries
            updateSelectedAfterEntriesChange()
        }
    }
    LaunchedEffect(selected) {
        if (selected == null) {
            return@LaunchedEffect
        }
        GlobalState.feed = if (entries.isNotEmpty() && selected!! >= 0) {
            if (selected!! < entries.size) { // Check bounds before accessing entries
                scrollTo()
                GlobalState.feed.copy(selectedLink = entries.getOrNull(selected!!)?.link)
            } else { // Selected index is out of bounds, possibly due to list refresh
                GlobalState.feed.copy(selectedLink = null) // Or select first/last valid item
            }
        } else {
            GlobalState.feed.copy(selectedLink = null)
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                if (selected != 0) {
                    nextScrollAnimated = true
                    selected = 0
                }
            }

            GlobalEvent.ScrollToBottom -> {
                if (selected != entries.size - 1) {
                    nextScrollAnimated = true
                    selected = entries.size - 1
                }
            }

            GlobalEvent.Reload -> {
                coroutineScope.launch(Dispatchers.Default) {
                    Feeds.downloadRssYouTubeAndInstagramToDatabase(showNotification = true)
                    entries = FeedsDatabase.fetchEntries(newOnly)
                    selected = Lists.getInitialSelected(entries, GlobalState.feed.selectedLink) { it.link }
                    coroutineScope.launch {
                        if (selected == null) {
                            selected = 0
                        }
                        if (entries.isNotEmpty() && selected != null && selected!! < entries.size) {
                            scrollTo()
                        }
                    }
                }
            }

            else -> {}
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
}
