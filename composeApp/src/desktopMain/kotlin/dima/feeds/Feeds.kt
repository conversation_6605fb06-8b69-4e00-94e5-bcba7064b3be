package dima.feeds

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showLoadingNotification
import dima.dateTime.DateTimeUtils
import dima.globalState.GlobalState
import dima.instagram.Instagram.loadInstagramFeedEntries
import dima.settings
import dima.youtube.YouTube
import kotlin.math.abs

object Feeds {
    var hasUnread by mutableStateOf(false)

    fun needsUpdate(): Bo<PERSON>an {
        val lastUpdateString = GlobalState.feed.lastUpdate ?: return true
        return abs(DateTimeUtils.differenceToNowInIso(lastUpdateString).toMinutes()) >= 180
    }

    /**
     * Downloading the RSS channels is so quick, that is not worth making it in parallel with the Instagram downloading.
     */
    suspend fun downloadRssYouTubeAndInstagramToDatabase(showNotification: Boolean) {
        // update at start to not trigger too often when often recompiling the app
        GlobalState.feed = GlobalState.feed.copy(lastUpdate = DateTimeUtils.nowAsIsoDateTime())
        var notification: LoadingNotification? = null
        if (showNotification) {
            if (settings.feed.instagram.isNotEmpty()) {
                notification = showLoadingNotification("Loading Instagram...")
            }
        }
        val mm = loadInstagramFeedEntries(notification)
        notification?.update("Loading RSS...")
        val rssLinks = settings.feed.channels + YouTube.getRssFeedUrlsForFeed()
        val all: List<FeedEntry> = rssLinks.flatMap {
            Rss.downloadUrl(it)
        } + mm
        notification?.dismiss()
        FeedsDatabase.insert(all)
    }
}
