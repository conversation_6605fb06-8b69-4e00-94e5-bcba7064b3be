package dima.feeds

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.database.transactionToAvoidBusySqlite
import dima.dateTime.DateTimeFormat
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File

internal object FeedsDatabase {
    
    // Add a reactive state to track database changes
    var lastDatabaseChange by mutableStateOf(System.currentTimeMillis())
        private set

    private object Table : org.jetbrains.exposed.sql.Table("Entries") {
        val title = text("title")
        val link = text("link").uniqueIndex()
        val imageUrl = text("imageUrl").nullable()
        val isRead = bool("isRead").default(false)
        val feedTitle = text("feedTitle")

        /**
         * In [DateTimeFormat.isoDateTimeWithTandZ].
         */
        val date = text("date")
    }

    internal const val RELATIVE_CACHE_DIRECTORY = "cache/feeds"
    internal val cacheDirectory = File(RELATIVE_CACHE_DIRECTORY)
    internal var database by mutableStateOf<Database?>(null)

    fun setup() {
        if (database != null) {
            return
        }
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        val dbFile = File(cacheDirectory, "feeds.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
        transactionToAvoidBusySqlite(database) {
            SchemaUtils.create(Table)
        }
        updateHasUnread()
    }

    private fun updateHasUnread() {
        transaction(database) {
            val count = Table.selectAll().where { Table.isRead eq false }.count()
            Feeds.hasUnread = count > 0
        }
    }

    fun insert(all: List<FeedEntry>) {
        var hasChanges = false
        transaction(database) {
            val links = Table.selectAll().map {
                it[Table.link]
            }
            all.forEach { entry ->
                if (!links.contains(entry.link)) {
                    Table.insert {
                        it[this.title] = entry.title
                        it[this.link] = entry.link
                        it[this.imageUrl] = entry.imageUrl
                        it[this.date] = entry.date
                        it[this.isRead] = false
                        it[this.feedTitle] = entry.feedTitle
                    }
                    hasChanges = true
                }
            }
        }
        if (hasChanges) {
            lastDatabaseChange = System.currentTimeMillis()
        }
        updateHasUnread()
    }

    fun markAsRead(article: FeedEntry, isRead: Boolean = true) {
        transaction(database) {
            Table.update({ Table.link eq article.link }) {
                it[Table.isRead] = isRead
            }
        }
        lastDatabaseChange = System.currentTimeMillis()
        updateHasUnread()
    }

    /**
     * @return sorted with index 0 being the most recent entry
     */
    fun fetchEntries(newOnly: Boolean): List<FeedEntry> {
        return transaction(database) {
            val query = Table.selectAll()
            if (newOnly) {
                query.andWhere { Table.isRead eq false }
            }
            query.map {
                FeedEntry(
                    title = it[Table.title],
                    link = it[Table.link],
                    imageUrl = it[Table.imageUrl],
                    date = it[Table.date],
                    isRead = it[Table.isRead],
                    feedTitle = it[Table.feedTitle]
                )
            }.sortedBy {
                it.date
            }.reversed()
        }
    }


}