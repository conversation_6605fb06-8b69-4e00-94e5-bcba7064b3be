package dima.feeds

import dima.os.copyToClipboard

internal fun copyAllEntries(newOnly: Boolean) {
    val sb = StringBuilder()
    FeedsDatabase.fetchEntries(newOnly).forEach {
        sb
            .append(it.title)
            .append("\n")
            .append(it.imageUrl ?: "[NO IMAGE URL]")
            .append("\n")
            .append(it.link)
            .append("\n\n")
    }
    copyToClipboard(sb.toString())
}