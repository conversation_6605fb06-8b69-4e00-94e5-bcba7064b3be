package dima.feeds

import dima.apps.notifications.showErrorNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dima.os.homeWithoutSlash
import dima.process.LoggedProcess
import dima.settings
import dima.utils.truncateWithEllipsisMiddle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

internal fun transcribeYouTubeEntry(
    article: FeedEntry, coroutineScope: CoroutineScope,
    newOnly: <PERSON>olean,
    changeEntries: (List<FeedEntry>) -> Unit,
    updateSelectedAfterEntriesChange: suspend () -> Unit
) {
    if (!article.link.contains("youtube.com/watch")) {
        showErrorNotification("Not a YouTube video", article.title)
        return
    }
    openConfirmationDialog(
        title = "Transcribe YouTube video and mark as read?",
        confirmButtonText = "Transcribe",
        subTitle = article.title
    ) {
        val notification = dima.apps.notifications.showLoadingNotification(
            "Transcribing YouTube video...",
            article.title.truncateWithEllipsisMiddle(100)
        )
        val args = buildList {
            add("--output-directory")
            add(File(homeWithoutSlash, "Downloads").absolutePath)
            if (settings.feed.youTubeCookieFile != null) {
                add("--cookie-file")
                add(settings.feed.youTubeCookieFile.absolutePath)
            }
            add(article.link)
        }
        LoggedProcess(
            command = "youtube-summary",
            args = args,
            showErrorNotifications = false,
            onFinish = { processResult ->
                if (processResult.exitCode.value == 0) {
                    notification.toInfo("Wrote transcript to ~/Downloads", durationMillis = 3000)
                    coroutineScope.launch(Dispatchers.IO) {
                        FeedsDatabase.markAsRead(article)
                        changeEntries(FeedsDatabase.fetchEntries(newOnly))
                        coroutineScope.launch {
                            updateSelectedAfterEntriesChange()
                        }
                    }
                } else {
                    notification.toError(
                        "Failed to transcribe video",
                        processResult.getOutput().trim()
                    )
                }
            }
        ).startAsync()
    }
}