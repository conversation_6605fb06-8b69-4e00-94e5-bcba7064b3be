package dima.feeds

import com.prof18.rssparser.RssParser
import com.prof18.rssparser.model.RssItem
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.NetworkActivity.updateFaked
import dima.dateTime.DateTimeFormat
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.time.LocalDateTime
import java.time.format.DateTimeParseException

private fun RssItem.toLocalDateTime(): LocalDateTime {
    val date = pubDate!!
    return try {
        LocalDateTime.parse(date, DateTimeFormat.isoDateTimeWithTandPlus00_00Timezone)
    } catch (_: DateTimeParseException) {
        LocalDateTime.parse(date, DateTimeFormat.isoDateTimeWithTandZ)
    }
}

object Rss {

    private val parser: RssParser = RssParser()

    internal suspend fun downloadUrl(channel: FeedChannel): List<FeedEntry> {
        val title = "Download RSS: ${channel.title}"
        val log = NetworkActivity.addFakedEntry(title, FakedNetworkActivityStatus.InTransit, fullUrl = channel.url)
        val rssChannel = try {
            parser.getRssChannel(channel.url)
        } catch (e: SocketTimeoutException) {
            log.updateFaked(FakedNetworkActivityStatus.Error, responseBody = "SocketTimeoutException: ${e.message}")
            return emptyList()
        } catch (e: UnknownHostException) {
            log.updateFaked(FakedNetworkActivityStatus.Error, responseBody = "UnknownHostException: ${e.message}")
            return emptyList()
        }
        log.updateFaked(FakedNetworkActivityStatus.Success)
        val parsedFromRss = rssChannel.items.map {
            val imageUrl = if (it.link!!.contains("www.youtube.com")) {
                val parts = it.link!!.split("=")
                val id = parts[parts.size - 1]
                "https://img.youtube.com/vi/$id/hqdefault.jpg"
            } else {
                it.image
            }
            FeedEntry(
                title = it.title!!,
                link = it.link!!,
                imageUrl = imageUrl,
                date = it.toLocalDateTime().format(DateTimeFormat.isoDateTimeWithTandZ),
                feedTitle = channel.title
            )
        }
        return parsedFromRss
    }

}