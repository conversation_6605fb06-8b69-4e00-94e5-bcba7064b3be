package dima.feeds

import GlobalStyling
import androidx.compose.foundation.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.images.CachedInternetImage
import dima.utils.*
import handleLostFocus

@Composable
internal fun FeedsAppUi(
    appFocusRequester: FocusRequester,
    newOnly: Boolean,
    onNewOnlyChange: (Boolean) -> Unit,
    listState: LazyListState,
    selected: Int?,
    onSelectedChange: (Int) -> Unit,
    entries: List<FeedEntry>,
    preview: (entry: FeedEntry) -> Unit,
) {
    val color = GlobalStyling.getTextColor()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        DummyFocusable()
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxSize()
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(24.dp, Alignment.CenterHorizontally),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp)
            ) {
                Text(
                    "Feeds",
                    textAlign = TextAlign.Center,
                    color = color,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                CheckboxContainer("New only", newOnly, onCheckedChange = { onNewOnlyChange(it) })
            }
            Box(
                contentAlignment = Alignment.TopCenter,
                modifier = Modifier
            ) {
                LazyColumn(
                    contentPadding = PaddingValues(bottom = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier
                        .widthIn(max = 900.dp)
                        .fillMaxWidth()
                        .fillMaxHeight(),
                    state = listState,
                ) {
                    val selectedLink = if (selected == null) "" else entries.getOrNull(selected)?.link
                    items(entries, key = { it.link }) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier
                                .padding(end = 12.dp)
                                .fillMaxWidth()
                                .background(
                                    color = if (it.link == selectedLink) {
                                        GlobalStyling.getSelectedBackgroundColor()
                                    } else {
                                        GlobalStyling.getWindowBackgroundColor()
                                    },
                                    shape = RoundedCornerShape(GlobalStyling.smallRoundedCornersDp)
                                )
                                .hoverable(interactionSource = remember { MutableInteractionSource() })
                                .animateItem(fadeInSpec = null, fadeOutSpec = null)
                                .pointerHoverIcon(PointerIcon.Hand)
                                .clickableWithoutBackgroundRipple {
                                    onSelectedChange(entries.indexOf(it))
                                    preview(it)
                                }
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                if (it.imageUrl != null) {
                                    // use same size for stable size when scrolling, without it there is animation and it looks weird
                                    val imageSize = 180.dp
                                    Row(
                                        modifier = Modifier
                                            .padding(start = 12.dp, top = 12.dp, bottom = 12.dp)
                                            .size(imageSize)
                                    ) {
                                        CachedInternetImage(
                                            imageUrl = it.imageUrl,
                                            cacheDir = FeedsDatabase.cacheDirectory,
                                            cacheFilePrefix = it.date + " ",
                                            logCacheDir = FeedsDatabase.RELATIVE_CACHE_DIRECTORY,
                                            contentScale = ContentScale.Fit,
                                            modifier = Modifier
                                                .clip(shape = GlobalStyling.smallRoundedCorners)
                                                .size(imageSize)
                                        )
                                    }
                                }
                                Column(
                                    modifier = Modifier
                                        .padding(12.dp)
                                        .fillMaxWidth()
                                ) {
                                    Text(
                                        it.feedTitle,
                                        color = color,
                                        fontSize = 15.sp,
                                        modifier = Modifier.padding(bottom = 12.dp)
                                    )
                                    Text(
                                        it.title.truncateWithEllipsis(600),
                                        color = color,
                                        fontWeight = FontWeight.SemiBold,
                                        fontSize = if (it.title.length > 100) 14.sp else 17.sp,
                                    )
                                }
                            }
                        }
                    }
                }
                VerticalScrollbar(
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = listState),
                    modifier = Modifier
                        .padding(bottom = 12.dp)
                        .align(Alignment.TopEnd),
                )
            }
        }
    }
}
