package dima.feeds

import dima.dateTime.DateTimeFormat
import kotlinx.serialization.Serializable

data class FeedEntry(
    val title: String,
    val feedTitle: String,
    val link: String,
    val imageUrl: String?,
    /**
     * In [DateTimeFormat.isoDateTimeWithTandZ].
     */
    val date: String,
    val isRead: Boolean = false
)

@Serializable
data class FeedChannel(
    val title: String,
    val url: String,
)