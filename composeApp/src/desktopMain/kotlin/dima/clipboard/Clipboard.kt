package dima.clipboard

import java.awt.Toolkit
import java.awt.datatransfer.DataFlavor
import java.awt.datatransfer.StringSelection
import java.awt.datatransfer.UnsupportedFlavorException

private val clipboard = Toolkit.getDefaultToolkit().systemClipboard

/**
 * This is faster than invoking pbcopy on macOS, simply because no process needs to be spawned.
 */
fun setClipboard(text: String) {
    val selection = StringSelection(text)
    clipboard.setContents(/* contents = */ selection, /* owner = */ selection)
}

/**
 * @return null if clipboard is empty
 */
fun readClipboard(): String? {
    return try {
        val s = clipboard.getData(DataFlavor.stringFlavor) as String
        if (s == "") {
            null
        }
        s
    } catch (_: UnsupportedFlavorException) {
        // for UnsupportedFlavorException: Unicode String
        null
    }
}

fun clearClipboard() {
    clipboard.setContents(/* contents = */ StringSelection(""), /* owner = */ null)
}