package dima.color

import androidx.compose.ui.graphics.Color
import dima.utils.Result

fun String.parseToColor(): Result<Color> {
    val hex = this.removePrefix("#")
    val colorLong = hex.toLongOrNull(16) ?: return Result.Error("Invalid hex color string: '$this'")

    return when (hex.length) {
        6 -> Result.Success(
            Color( // RRGGBB
                red = ((colorLong shr 16) and 0xFF) / 255f,
                green = ((colorLong shr 8) and 0xFF) / 255f,
                blue = (colorLong and 0xFF) / 255f,
                alpha = 1f
            )
        )

        8 -> Result.Success(
            Color( // AARRGGBB
                alpha = ((colorLong shr 24) and 0xFF) / 255f,
                red = ((colorLong shr 16) and 0xFF) / 255f,
                green = ((colorLong shr 8) and 0xFF) / 255f,
                blue = (colorLong and 0xFF) / 255f
            )
        )

        3 -> { // RGB short form (e.g., #F03)
            val r = ((colorLong shr 8 and 0xF).toFloat() / 15f)
            val g = ((colorLong shr 4 and 0xF).toFloat() / 15f)
            val b = ((colorLong and 0xF).toFloat() / 15f)
            Result.Success(Color(r, g, b, 1f))
        }

        4 -> { // ARGB short form (e.g., #AF03)
            val a = ((colorLong shr 12 and 0xF).toFloat() / 15f)
            val r = ((colorLong shr 8 and 0xF).toFloat() / 15f)
            val g = ((colorLong shr 4 and 0xF).toFloat() / 15f)
            val b = ((colorLong and 0xF).toFloat() / 15f)
            Result.Success(Color(r, g, b, a))
        }

        else -> Result.Error("Invalid hex color string length: '$this'. Expected #RGB, #ARGB, #RRGGBB or #AARRGGBB")
    }
}

fun Color.toHexRGB(): String {
    return "#%02x%02x%02x".format(
        (this.red * 255).toInt().coerceIn(0, 255),
        (this.green * 255).toInt().coerceIn(0, 255),
        (this.blue * 255).toInt().coerceIn(0, 255)
    )
}