package dima.color

import androidx.compose.ui.graphics.Color
import kotlin.math.abs

/**
 * Set default values for good contrast in the calendar app.
 */
fun String.toHashedComposeColor(
    lightness: List<Float> = listOf(0.5f),
    saturation: List<Float> = listOf(0.5f),
    minHue: Int = 0,
    maxHue: Int = 360
) = ColorHash(this, lightness, saturation, minHue, maxHue).toComposeColor()

class ColorHash(
    val string: String,
    val lightness: List<Float> = listOf(0.35f, 0.5f, 0.65f),
    val saturation: List<Float> = listOf(0.35f, 0.5f, 0.65f),
    val minHue: Int = 0,
    val maxHue: Int = 360
) {
    companion object {
        private const val SEED = 131L
        private const val SEED2 = 137L
        private const val MAX_SAFE_LONG = 65745979961613L
    }

    fun toHSL(): FloatArray {
        var hash = bkdrHash(string)

        // Hue
        val hue = ((hash % 359) / 359f) * (maxHue - minHue) + minHue
        hash /= 360

        // Saturation
        val sat = saturation[(hash % saturation.size).toInt()]
        hash /= saturation.size

        // Lightness
        val light = lightness[(hash % lightness.size).toInt()]

        return floatArrayOf(hue, sat, light)
    }

    fun toComposeColor(): Color {
        val hsl = toHSL()
        val colorInt = hslToColor(hsl)
        return Color(colorInt)
    }

    private fun bkdrHash(str: String): Long {
        var acc = 0L
        for (ch in (str + 'x')) {
            acc = if (acc > MAX_SAFE_LONG)
                acc / SEED2
            else
                acc * SEED + ch.code
        }
        return acc
    }
}

/**
 * @return ARGB int, like Android's Color/HSLToColor
 */
private fun hslToColor(hsl: FloatArray): Int {
    val h = hsl[0]
    val s = hsl[1]
    val l = hsl[2]

    val c = (1f - abs(2 * l - 1f)) * s
    val m = l - 0.5f * c
    val x = c * (1f - abs((h / 60f) % 2 - 1f))

    val (r1, g1, b1) = when {
        h < 60 -> Triple(c, x, 0f)
        h < 120 -> Triple(x, c, 0f)
        h < 180 -> Triple(0f, c, x)
        h < 240 -> Triple(0f, x, c)
        h < 300 -> Triple(x, 0f, c)
        else -> Triple(c, 0f, x)
    }

    val r = ((r1 + m) * 255f + 0.5f).toInt().coerceIn(0, 255)
    val g = ((g1 + m) * 255f + 0.5f).toInt().coerceIn(0, 255)
    val b = ((b1 + m) * 255f + 0.5f).toInt().coerceIn(0, 255)

    return (0xFF shl 24) or (r shl 16) or (g shl 8) or b
}