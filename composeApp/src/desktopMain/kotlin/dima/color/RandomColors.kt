package dima.color

import androidx.compose.ui.graphics.Color
import com.github.ajalt.colormath.extensions.android.composecolor.toComposeColor
import com.github.ajalt.colormath.model.HSL

object ColorHelper {

    private val randomColorCache = mutableMapOf<String, Color>()

    /**
     * Result is cached.
     *
     * @param saturation [0f, 1f]
     */
    fun getRandomByString(text: String, saturation: Float): Color {
        if (randomColorCache.containsKey(text)) {
            return randomColorCache[text]!!
        }

        fun adjustToRange(value: Float): Float {
            // Adjust to range [0, 360)
            var adjustedValue = value % 360
            if (adjustedValue < 0) {
                adjustedValue += 360
            }
            return adjustedValue
        }

        val hue = adjustToRange(text.hashCode().toFloat())
        val color = HSL(hue, saturation, 0.5f).toComposeColor()
        randomColorCache[text] = color
        return color
    }

}

