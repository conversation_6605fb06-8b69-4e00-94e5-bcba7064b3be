package dima.terminal

import Globals
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.sp
import com.jediterm.terminal.*
import com.jediterm.terminal.emulator.JediEmulator
import com.jediterm.terminal.emulator.mouse.MouseFormat
import com.jediterm.terminal.emulator.mouse.MouseMode
import com.jediterm.terminal.model.*
import dima.apps.notifications.showNotification
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.nio.charset.Charset
import java.util.concurrent.atomic.AtomicBoolean

@Composable
fun Terminal(
    modifier: Modifier = Modifier,
    command: Array<String>,
    rows: Int = 24,
    cols: Int = 80
) {
    val scope = rememberCoroutineScope()

    // Terminal state
    val terminalState = remember {
        val styleState = StyleState()
        styleState.setDefaultStyle(TextStyle(TerminalColor.WHITE, TerminalColor.BLACK))

        // Create the text buffer first
        val textBuffer = TerminalTextBuffer(cols, rows, styleState)

        object {
            val isRunning = AtomicBoolean(true)
            val display = ComposeTerminalDisplay(textBuffer, null, 2f) // Pass null for DrawScope initially
            // Initialize the terminal inside the display
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            terminalState.isRunning.set(false)
        }
    }

    LaunchedEffect(Unit) {
        scope.launch(Dispatchers.IO) {
            val process = ProcessBuilder(command.toList())
                .redirectErrorStream(true)
                .start()

            val ttyConnector = object : ProcessTtyConnector(process, Charset.defaultCharset()) {
                override fun getName(): String = "Compose Terminal"
            }

            val emulator = JediEmulator(
                TtyBasedArrayDataStream(ttyConnector),
                terminalState.display.terminal
            )

            while (terminalState.isRunning.get() && emulator.hasNext()) {
                try {
                    emulator.next()
                    terminalState.display.requestUpdate()
                } catch (_: Exception) {
                    showNotification("EXCEPTION")
                    break
                }
            }

            showNotification("Process done?")
            process.destroy()
        }
    }

    Canvas(modifier = modifier.fillMaxSize()) {
        // Update the DrawScope in the display
        terminalState.display.updateDrawScope(this)
        // Render terminal content
        terminalState.display.render()
    }
}

private class ComposeTerminalDisplay(
    private val buffer: TerminalTextBuffer,
    private var drawScope: DrawScope?,
    density: Float
) : TerminalDisplay {

    private var needsUpdate by mutableStateOf(false)
    val terminal = JediTerminal(this, buffer, StyleState())

    fun updateDrawScope(scope: DrawScope) {
        drawScope = scope
    }

    fun requestUpdate() {
        needsUpdate = true
    }

    fun render() {
        val currentDrawScope = drawScope ?: return

        buffer.lock()
        try {
            buffer.processScreenLines(0, buffer.height, object : StyledTextConsumer {
                override fun consume(
                    x: Int,
                    y: Int,
                    style: TextStyle,
                    chars: CharBuffer,
                    startRow: Int
                ) {
                    with(currentDrawScope) {
                        // Draw characters using Canvas
                        val text = chars.toString()
                        println("X: $x, Y: $y, Text: $text, Style: $style, StartRow: $startRow, chars: $chars")
                        val fontMeasurer =
                            TextMeasurer(Globals.fontFamilyResolver, Globals.density, LayoutDirection.Ltr)
                        val textLayoutResult = fontMeasurer.measure(
                            text = AnnotatedString(text),
                            style = androidx.compose.ui.text.TextStyle(
                                fontSize = 14.sp,
                                fontFamily = FontFamily.Monospace,
                            ),
                        )
                        currentDrawScope.drawText(
                            textLayoutResult,
                            topLeft = Offset(
                                x = x * charWidth,
                                y = y * charHeight
                            )
                        )
                    }
                }

                override fun consumeNul(
                    x: Int,
                    y: Int,
                    nulIndex: Int,
                    style: TextStyle,
                    chars: CharBuffer,
                    startRow: Int
                ) {
                }

                override fun consumeQueue(x: Int, y: Int, nulIndex: Int, startRow: Int) {}
            })
        } finally {
            buffer.unlock()
            needsUpdate = false
        }
    }

    private val charWidth = 8f * density
    private val charHeight = 16f * density

    override fun setCursor(x: Int, y: Int) {}

    override fun setCursorShape(cursorShape: CursorShape?) {}

    override fun beep() {
        showNotification("Terminal beeped")
    }

    override fun scrollArea(scrollRegionTop: Int, scrollRegionSize: Int, dy: Int) {}

    override fun setCursorVisible(isCursorVisible: Boolean) {}

    override fun useAlternateScreenBuffer(useAlternateScreenBuffer: Boolean) {}

    override fun getWindowTitle(): String? = "test"

    override fun setWindowTitle(windowTitle: String) {}

    override fun getSelection(): TerminalSelection? = null

    override fun terminalMouseModeSet(mouseMode: MouseMode) {}

    override fun setMouseFormat(mouseFormat: MouseFormat) {}

    override fun ambiguousCharsAreDoubleWidth(): Boolean = false
}