package dima.modeline

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.composables.icons.lucide.*
import dima.apps.textEditor.TextEditor
import dima.apps.textEditor.TextEditorStyling
import dima.apps.todoist.Todoist
import dima.audio.Audio
import dima.color.TailwindCssColors
import dima.dialogs.audio.openAudioDialog
import dima.dialogs.help.openHelpDialog
import dima.feeds.Feeds
import dima.globalState.GlobalState
import dima.utils.*
import dima.vero.VeroHourBooking

object Modeline {
    /**
     * The weight compared to the main app content above.
     */
    const val HEIGHT_WEIGHT = 0.04f
}

@Composable
private fun AudioTrack() {
    val fileName = GlobalState.audioPlayer.currentTrack?.substringAfterLast("/")?.substringBeforeLast(".")
    if (fileName == null) {
        return
    }
    val color = if (GlobalState.audioPlayer.isPlaying) {
        GlobalStyling.getFuchsiaColor()
    } else {
        GlobalStyling.getTextColor()
    }
    Text(
        fileName.truncateWithEllipsisMiddle(45),
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        color = color
    )
    val isFromDatabase = Audio.isCurrentlyPlayedTrackFromDatabase()
    if (isFromDatabase) {
        return
    }
    val currentPositionFormatted = Audio.formatMsDuration(GlobalState.audioPlayer.positionMs)
    val totalDuration = Audio.formatMsDuration(GlobalState.audioPlayer.totalDurationMs)
    TextNumbersSameWidth(currentPositionFormatted, color = color)
    Text("/", color = color)
    TextNumbersSameWidth(totalDuration, color = color)
}

@Composable
private fun tasks() {
    if (Todoist.randomTask == null) {
        return
    }
    Text(
        Todoist.randomTask!!.truncateWithEllipsis(60),
        color = GlobalStyling.getTextColor()
    )
}

@Composable
private fun feeds() {
    if (!Feeds.hasUnread) {
        return
    }
    Icon(
        Lucide.Rss,
        contentDescription = null,
        tint = GlobalStyling.getTextColor(),
        modifier = Modifier.padding(6.dp),
    )
}

@Composable
private fun timers() {
    val color = GlobalStyling.getTextColor()
    GlobalState.timers.forEach {
        if (it.name == null) {
            Text(it.displayText, color = color)
        } else {
            Text(it.name + ": " + it.displayText, color = color)
        }
    }
}

@Composable
private fun coldWeather() {
    if (GlobalState.coldWeather == null) {
        return
    }
    Text(
        GlobalState.coldWeather.toString() + "°C",
        color = GlobalStyling.getBlueColor()
    )
}

@Composable
private fun moon() {
    // no color because this just contains one unicode emoji
    Text(GlobalState.moon)
}

@Composable
private fun help() {
    Icon(
        Lucide.CircleHelp,
        contentDescription = null,
        tint = GlobalStyling.getTextColor(),
        modifier = Modifier
            .clickableWithoutBackgroundRipple {
                openHelpDialog()
            }
            .fillMaxHeight(0.6f)
    )
}

@Composable
private fun textEditorVisualInfo() {
    if (TextEditor.state.visualSelectedCharacters != null) {
        val chars = TextEditor.state.visualSelectedCharacters!!.chars
        var text = if (chars == 1) "$chars char" else "$chars chars"
        val lines = TextEditor.state.visualSelectedCharacters!!.lines
        if (lines == 1) {
            text += "   1 line"
        } else if (lines >= 2) {
            text += "   $lines lines"
        }
        Text(
            text,
            color = TailwindCssColors.white,
            modifier = Modifier
                .background(TextEditorStyling.selectedCharLineBackgroundColor, RoundedCornerShape(4.dp))
                .padding(horizontal = 4.dp, vertical = 2.dp)
        )
    }
}

@Composable
private fun vero() {
    val modeline = VeroHourBooking.todayHoursModeline ?: return
    val color = if (GlobalState.vero.isWorking) {
        GlobalStyling.getFuchsiaColor()
    } else {
        GlobalStyling.getTextColor()
    }
    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .then(
                if (GlobalState.vero.isWorking) {
                    Modifier
                } else {
                    Modifier.alpha(GlobalStyling.DISABLED_ALPHA)
                }
            )
    ) {
        Text(
            modeline.projectName,
            color = color
        )
        Text(
            modeline.hourText,
            color = color
        )
    }
}

@Composable
private fun audio() {
    val iconColor = GlobalStyling.getFuchsiaColor()
    val textColor = GlobalStyling.getTextColor()
    if (GlobalState.audioPlayer.isPlaying) {
        Icon(
            if (GlobalState.audioPlayer.quietVolume) Lucide.Volume1 else Lucide.Volume2,
            contentDescription = null,
            tint = iconColor,
            modifier = Modifier
                .size(20.dp)
        )
    } else {
        Icon(
            Lucide.CirclePause,
            contentDescription = null,
            tint = textColor,
            modifier = Modifier
                .size(20.dp)
        )
    }
    AudioTrack()
    if (GlobalState.audioPlayer.randomMode) {
        Icon(
            Lucide.Shuffle,
            contentDescription = null,
            tint = if (GlobalState.audioPlayer.isPlaying) {
                iconColor
            } else {
                textColor
            },
            modifier = Modifier
                .size(14.dp)
        )
    }
    if (GlobalState.audioPlayer.loopTrack) {
        Icon(
            Lucide.RefreshCw,
            contentDescription = null,
            tint = if (GlobalState.audioPlayer.isPlaying) {
                iconColor
            } else {
                textColor
            },
            modifier = Modifier
                .size(16.dp)
        )
    }
}

@Composable
fun ColumnScope.Modeline() {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .weight(Modeline.HEIGHT_WEIGHT)
            .fillMaxWidth()
            .topBorder(1.dp, GlobalStyling.getBorderColor())
            .background(GlobalStyling.getWindowBackgroundColor())
            .padding(horizontal = 8.dp)
    ) {
        ModelineLeftSide()
        ModelineRightSide()
    }
}

@Composable
private fun ModelineRightSide() {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(24.dp),
    ) {
        textEditorVisualInfo()
        vero()
        tasks()
        timers()
        feeds()
        coldWeather()
        help()
        moon()
    }
}

@Composable
private fun ModelineLeftSide() {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(6.dp),
        modifier = Modifier
            .clickableWithoutBackgroundRipple {
                openAudioDialog()
            }
            .then(
                if (GlobalState.audioPlayer.isPlaying) {
                    Modifier
                } else {
                    Modifier.alpha(GlobalStyling.DISABLED_ALPHA)
                }
            )

    ) {
        audio()
    }
}