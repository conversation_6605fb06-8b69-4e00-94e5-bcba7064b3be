package dima.ai

import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.mikepenz.markdown.m2.markdownColor
import dima.ai.singlePromptApp.TreeSitterCodeFenceText
import dima.apps.networkActivity.postLogged
import dima.color.TailwindCssColors
import dima.dialogs.help.appKeys
import dima.markdownRendererLibraryPatches.MyMarkdown
import dima.markdownRendererLibraryPatches.myMarkdownComponents
import dima.os.copyToClipboard
import dima.utils.AppKey
import dima.utils.DummyFocusable
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.handleAppMap
import io.ktor.client.request.*
import isTextFieldFocused
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import handleLostFocus

// Ideas, I think Emacs with sonnet-3.5 is totally sufficient
//  this here is the idea for a proper chat with textfield at the bottom
//  do not show failed models, just a little overview with the failed models in an own dialog
//  keys to switch models with responses left and right

@Serializable
private data class Message(val role: String, val content: String)

@Serializable
private data class RequestBody(val model: String, val messages: List<Message>)

/**
 * Represents a response from the DuckDuckGo AI chat service.
 *
 * This class encapsulates both successful responses and error states:
 * - For successful responses, [message] will contain the AI's response and [error] will be null
 * - For error cases, [error] will contain the error message and [message] will be null
 *
 * @property message The AI's response message when the request is successful
 * @property error An error message describing what went wrong, if an error occurred
 */
data class ChatResponse(
    val message: String? = null,
    val error: String? = null
)

class DuckDuckGoAiChat {
    private val client = createHttpClientWithLocalhostProxy()

    fun sendMessage(messageContent: String): ChatResponse = runBlocking {
        try {
            val response = client.postLogged("https://duckduckgo.com/duckchat/v1/chat") {
                headers.append("Content-Type", "application/json")
                val requestBody = RequestBody(
                    model = "gpt-4o-mini",
                    messages = listOf(
                        Message(role = "user", content = messageContent)
                    )
                )
                setBody(Json.encodeToString(requestBody))
            }
            return@runBlocking if (response.statusCode == 200) {
                ChatResponse(message = response.body)
            } else {
                ChatResponse(error = "Failed to get a response from DuckDuck AI (${response.statusCode}):\n${response.body}")
            }
        } catch (e: Exception) {
            return@runBlocking ChatResponse(error = "Error: ${e.message}")
        }
    }
}

data class ChatMessage(
    val content: String,
    val isAssistant: Boolean
)

@Composable
fun AiChatApp() {
    val duckDuckGoAiChat = DuckDuckGoAiChat()
    val appFocusRequester = remember { FocusRequester() }
    var textFieldValue by remember { mutableStateOf(TextFieldValue()) }
    var messages by remember { mutableStateOf(listOf<ChatMessage>()) }
    var selected by remember { mutableStateOf<Int?>(null) }
    val listState = rememberLazyListState()
    var isLoading by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    remember {
        appKeys = listOf(
            AppKey(Key.C, "Go one message up") {
                if (selected != null && selected!! >= 1) {
                    selected = selected!! - 1
                }
            },
            AppKey(Key.T, "Go one message down") {
                if (selected != null && selected!! + 1 < messages.size) {
                    selected = selected!! + 1
                }
            }
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                if (isTextFieldFocused) {
                    if (it.type == KeyEventType.KeyDown) {
                        when (it.key) {
                            Key.Escape -> {
                                appFocusRequester.requestFocus()
                                return@onPreviewKeyEvent true
                            }

                            Key.Enter -> {
                                val userMessage = textFieldValue.text
                                if (userMessage.isNotBlank() && !isLoading) {
                                    messages = messages + ChatMessage(userMessage, false)
                                    textFieldValue = TextFieldValue() // Clear input first

                                    isLoading = true
                                    coroutineScope.launch {
                                        val response = duckDuckGoAiChat.sendMessage(userMessage)
                                        isLoading = false
                                        if (response.error != null) {
                                            messages = messages + ChatMessage(response.error, true)
                                        } else if (response.message != null) {
                                            messages = messages + ChatMessage(response.message, true)
                                        }
                                        selected = messages.size - 1
                                    }
                                }
                                return@onPreviewKeyEvent true
                            }

                            else -> return@onPreviewKeyEvent false
                        }
                    }
                    return@onPreviewKeyEvent false
                }
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        DummyFocusable()
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            state = listState,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(messages.size) { index ->
                val message = messages[index]
                if (message.isAssistant) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        // Removed Robot Emoji
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .background(TailwindCssColors.blue100, RoundedCornerShape(8.dp))
                                .padding(8.dp)
                        ) {
                            MyMarkdown(
                                content = message.content,
                                colors = markdownColor(text = TailwindCssColors.blue900),
                                components = myMarkdownComponents(
                                    codeFence = { model ->
                                        TreeSitterCodeFenceText(
                                            content = model.content,
                                            node = model.node,
                                            onClick = { code ->
                                                copyToClipboard(
                                                    code.trim(),
                                                    notificationTitleSubstring = "code"
                                                )
                                            }
                                        )
                                    }
                                )
                            )
                        }
                    }
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.End
                    ) {
                        Box(
                            modifier = Modifier
                                .background(TailwindCssColors.gray200, RoundedCornerShape(8.dp))
                                .padding(8.dp)
                        ) {
                            Text(
                                text = message.content,
                                color = Color.Black
                            )
                        }
                    }
                }
            }
            if (isLoading) {
                item {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Start // Align spinner to the start like an assistant message
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp,
                            color = TailwindCssColors.blue900 // Consistent with assistant text
                        )
                    }
                }
            }
        }

        TextField(
            value = textFieldValue,
            onValueChange = { textFieldValue = it },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
                .onFocusChanged { isTextFieldFocused = it.isFocused },
            placeholder = { Text("Type your message...") },
            colors = TextFieldDefaults.textFieldColors(backgroundColor = TailwindCssColors.transparent)
        )
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }

    LaunchedEffect(messages.size, isLoading) {
        val targetIndex = if (isLoading) {
            messages.size // This will be the index of the loading indicator item
        } else {
            if (messages.isEmpty()) -1 else messages.size - 1 // Last actual message
        }

        if (targetIndex >= 0) {
            listState.animateScrollToItem(targetIndex)
        }
    }
}

