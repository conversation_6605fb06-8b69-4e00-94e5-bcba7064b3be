package dima.ai

import dima.apps.email.models.EmailMessage
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.utils.truncateWithEllipsis
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jsoup.Jsoup

internal fun copyExtractedCodeToClipboard(
    selected: Int?,
    filteredEntries: List<EmailMessage>,
    coroutineScope: CoroutineScope
) {
    if (selected == null) {
        return
    }
    val email = filteredEntries[selected]
    val plainText = if (email.html != null) {
        Jsoup.parse(email.html).text()
    } else {
        email.plainText
    } ?: ""
    if (plainText.isBlank()) {
        showErrorNotification("No text content found in email")
        return
    }
    val notification = showLoadingNotification(
        "Extracting verification code...",
        email.subject.truncateWithEllipsis(100)
    )
    coroutineScope.launch(Dispatchers.IO) {
        AiTextExtraction.extractVerificationCodeAndCopyToClipboard(
            plainText,
            notification = notification
        )
    }
}
