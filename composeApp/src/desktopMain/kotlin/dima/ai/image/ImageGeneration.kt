package dima.ai.image

import dima.apps.networkActivity.getLogged
import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.apps.notifications.showNotification
import dima.dialogs.generic.*
import dima.globalState.GlobalState
import dima.globalState.ImageGenerationState
import dima.os.homeWithoutSlash
import dima.process.GlobalThreads
import dima.utils.abbreviatePath
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.truncateWithEllipsis
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.random.Random

object ImageGeneration {

    fun openDialogToEnterParameters() {
        openGenericDialog(
            "Generate PollinationsAI image and save to ~/Downloads",
            layout = listOf(
                GenericDialogRow.TextInputSingleLine(
                    "Prompt",
                    content = GlobalState.imageGeneration.prompt,
                    isRequired = true
                ),
                GenericDialogRow.TextInputSingleLine(
                    "Number of images",
                    content = GlobalState.imageGeneration.numImages.toString(),
                    validator = ::GenericDialogNumberValidator
                ),
                GenericDialogRow.Checkbox(
                    "Use slow and high quality GptImage model",
                    id = "gptImage",
                    isChecked = GlobalState.imageGeneration.useSlowHighQualityGptImageModel
                ),
            )
        ) {
            val prompt = it.getTextInputSingleLineContent("Prompt")
            val n = it.getTextInputSingleLineContent("Number of images").toIntOrNull() ?: 1
            val useGptImage = it.isCheckboxTicked("gptImage")
            val model = if (useGptImage) ImageGenerationModel.GptImage else ImageGenerationModel.Flux
            GlobalState.imageGeneration = ImageGenerationState(
                prompt = prompt,
                numImages = n,
                useSlowHighQualityGptImageModel = useGptImage
            )
            val notification =
                showLoadingNotification("Generating $n image(s) via ${model.name} to ~/Downloads...")
            generatePollinationsImagesInBackground(
                n,
                prompt,
                File(homeWithoutSlash, "Downloads"),
                model,
                notification = notification
            )
        }
    }
}

enum class ImageGenerationModel {
    Flux,

    /**
     * High quality, but slow generation times.
     */
    GptImage
}

/**
 * Copied from ImageGen network requests from https://mirexa.vercel.app
 *
 * @param n Number of images to request (API currently always responds with a single image, so n is used for compatibility)
 * @param prompt Prompt string for the image generation
 * @param directory Local directory where the resulting image(s) should be saved
 */
private fun generatePollinationsImagesInBackground(
    n: Int,
    prompt: String,
    directory: File,
    model: ImageGenerationModel,
    notification: LoadingNotification
) {
    GlobalThreads.register("Generating $n image(s) via ${model.name} for $prompt") {
        try {
            runBlocking {
                val client = createHttpClientWithLocalhostProxy()
                if (!directory.exists()) {
                    directory.mkdirs()
                }
                val savedFiles = mutableListOf<File>()
                repeat(n) { i ->
                    if (Thread.currentThread().isInterrupted) {
                        showErrorNotification("Image generation killed", prompt)
                        return@repeat
                    }
                    val seed = Random.nextInt(0, 999999)
                    val url = buildString {
                        append("https://image.pollinations.ai/prompt/")
                        append(encodePrompt(prompt))
                        append(
                            "?model=" + when (model) {
                                ImageGenerationModel.Flux -> "flux"
                                ImageGenerationModel.GptImage -> "gptimage"
                            }
                        )
                        append("&private=true")
                        append("&nologo=true")
                        append("&seed=$seed")
                        append("&quality=high")
                        append("&nofeed=true")
                        append("&safe=false")
                    }

                    notification.update(
                        message = buildString {
                            append("Requesting image ${i + 1} / $n...\n")
                            if (n > 1) append("Progress: ${i + 1} of $n\n")
                            append("Prompt: ")
                            append(prompt.truncateWithEllipsis(40))
                        }
                    )

                    val response = client.getLogged(url, binaryResponse = true) {
                        timeout {
                            requestTimeoutMillis = 150_000
                            connectTimeoutMillis = 150_000
                        }
                        headers {
                            append("accept", "*/*")
                            append("accept-language", "en-US,en;q=0.9,de;q=0.8")
                            append("cache-control", "no-cache")
                            append("dnt", "1")
                            append("origin", "https://mirexa.vercel.app")
                            append("pragma", "no-cache")
                            append("priority", "u=1, i")
                            append("referer", "https://mirexa.vercel.app/")
                            append("sec-ch-ua", "\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"")
                            append("sec-ch-ua-mobile", "?0")
                            append("sec-ch-ua-platform", "\"macOS\"")
                            append("sec-fetch-dest", "empty")
                            append("sec-fetch-mode", "cors")
                            append("sec-fetch-site", "cross-site")
                            append(
                                "user-agent",
                                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
                            )
                        }
                    }
                    if (response.statusCode == 200) {
                        val fileName = "pollinations_${model.name}_${System.currentTimeMillis()}_${seed}.png"
                        val file = File(directory, fileName)
                        file.writeBytes(response.bodyBytes!!)
                        notification.update(
                            message = "Saved: $fileName\n(${i + 1} / $n)"
                        )
                        showNotification("Generated image in ${directory.abbreviatePath()}", file.name)
                        savedFiles += file
                    } else {
                        if (model == ImageGenerationModel.GptImage) {
                            delay(5000)
                        }
                        notification.toError(
                            "Failed to generate image ${i + 1}/$n",
                            response.body
                        )
                        showErrorNotification("Failed to generate image", response.body)
                    }
                }
            }
        } catch (throwable: Throwable) {
            notification.toError("Image generation failed", throwable.message)
        } finally {
            notification.dismiss()
        }
    }
}

/**
 * Encode a prompt for use in the Pollinations API URL path component.
 * Pollinations expects spaces as %20, not +, and special characters percent-encoded.
 */
private fun encodePrompt(prompt: String): String =
    java.net.URLEncoder.encode(prompt, "UTF-8").replace("+", "%20")

