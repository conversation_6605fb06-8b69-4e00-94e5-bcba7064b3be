package dima.ai.image

import Globals
import dima.ai.ChatGptApi
import dima.apps.networkActivity.postLogged
import dima.apps.notifications.showLoadingNotification
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.confirmation.openConfirmationDialog
import dima.os.Emacs
import dima.os.copyToClipboard
import dima.utils.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.*
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.io.File
import java.util.*
import dima.settings

object AiImage {

    @Serializable
    private data class ImageUrl(
        val url: String
    )

    @Serializable
    private data class TextContent(val type: String, val text: String)

    @Serializable
    private data class ImageContent(
        val type: String,
        @SerialName("image_url")
        val imageUrl: ImageUrl
    )

    @Serializable
    private data class MessageWithImage(
        val role: String,
        val content: List<@Serializable(with = ContentSerializer::class) Any>
    )

    @Serializable
    private data class RequestBodyWithImage(
        val model: String,
        val messages: List<MessageWithImage>
    )

    private object ContentSerializer : KSerializer<Any> {
        override val descriptor: SerialDescriptor = buildClassSerialDescriptor("ContentSerializer")

        override fun serialize(encoder: Encoder, value: Any) {
            when (value) {
                is TextContent -> encoder.encodeSerializableValue(serializer(), value)
                is ImageContent -> encoder.encodeSerializableValue(serializer(), value)
                else -> throw SerializationException("Unknown content type: ${value::class}")
            }
        }

        override fun deserialize(decoder: Decoder): Any {
            throw SerializationException("Deserializing content is not supported")
        }
    }

    /** This is public so other dialogs can use it. */
    suspend fun askAboutImage_inner(
        userContent: String,
        image: File
    ): StringResult {
        val client = createHttpClientWithLocalhostProxy()
        client.use {
            try {
                // Read and encode the image
                val imageBytes = image.readBytes()
                val base64Image = Base64.getEncoder().encodeToString(imageBytes)
                val imageUrl = "data:image/png;base64,$base64Image"

                // Create message content with text and image
                val textContent = TextContent(type = "text", text = userContent)
                val imageContent = ImageContent(type = "image_url", imageUrl = ImageUrl(url = imageUrl))

                // Create request body
                val requestBody = RequestBodyWithImage(
                    model = "pixtral-large-2411",
                    messages = listOf(
                        MessageWithImage(
                            role = "user",
                            content = listOf(textContent, imageContent)
                        )
                    )
                )

                val response = client.postLogged("https://api.llm7.io/v1/chat/completions") {
                    setJsonBody(requestBody)
                }

                if (response.statusCode != 200) {
                    return StringResult.Error("Failed to get a response (${response.statusCode}):\n${response.body}")
                }

                val parsed = JsonIgnoreUnknown.decodeFromString<ChatGptApi.Response>(response.body)
                val choice = parsed.choices.firstOrNull()
                if (choice == null) {
                    return StringResult.Error("No choices in response")
                }

                return StringResult.Success(choice.message.content)
            } catch (e: Exception) {
                return StringResult.Error("Error processing image: ${e.message}")
            }
        }
    }

    fun askAboutImage(file: File) {
        val recentTexts = RecentHistory.getRecentTexts()
        openCompletionDialog(
            "Ask AI about " + file.abbreviatePath(),
            recentTexts,
            matchRequired = false
        ) {
            RecentHistory.rememberText(it.text)
            val notification = showLoadingNotification("Asking AI...", it.text)
            Globals.coroutineScope.launch(Dispatchers.IO) {
                val answer = askAboutImage_inner(it.text, file)
                when (answer) {
                    is StringResult.Error -> notification.toError(
                        "Failed to ask question via AI",
                        answer.error
                    )

                    is StringResult.Success -> {
                        RecentHistory.rememberText(answer.value)
                        notification.dismiss()
                        copyToClipboard(answer.value, showNotification = false)
                        openConfirmationDialog(
                            "Open in Emacs?",
                            confirmButtonText = "Open in Emacs",
                            subTitle = answer.value
                        ) {
                            Globals.coroutineScope.launch(Dispatchers.IO) {
                                val tempFile = createTemporaryFile("gpt4-image-response", ".txt")
                                tempFile.writeText(answer.value)
                                Emacs.openFileContentAndFocus(tempFile)
                            }
                        }
                    }
                }
            }
        }
    }

}
