package dima.ai.singlePromptApp

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.treeSitter.IntellijDarculaTheme
import dima.treeSitter.TreeSitterLanguage
import dima.treeSitter.TreeSitterText
import dima.utils.clickableWithoutBackgroundRipple
import org.intellij.markdown.MarkdownTokenTypes
import org.intellij.markdown.ast.ASTNode
import org.intellij.markdown.ast.findChildOfType
import org.intellij.markdown.ast.getTextInNode

/**
 * Note that content and node are not reactive, because onCode() would be called every time.
 *
 * @param onClick called with the extracted code string from this code fence node
 * @param onCode called with the extracted code string from this code fence node
 */
@Composable
internal fun TreeSitterCodeFenceText(
    content: String,
    node: ASTNode,
    isHighlighted: Boolean = false,
    onClick: (String) -> Unit,
    onCode: (String) -> Unit = {},
) {
    var text by remember { mutableStateOf<String?>(null) }
    var language by remember { mutableStateOf<TreeSitterLanguage?>(null) }

    if (text != null) {
        // set higher value, so the inner borders of the outline border are also rounded
        val roundedCornerShape = RoundedCornerShape(8.dp)
        TreeSitterText(
            text!!, language,
            modifier = Modifier
                .padding(vertical = 8.dp)
                .background(
                    if (isHighlighted) {
                        GlobalStyling.getSelectedBorderColor()
                    } else {
                        IntellijDarculaTheme.background
                    },
                    roundedCornerShape
                )
                .padding(6.dp)
                .background(
                    IntellijDarculaTheme.background,
                    roundedCornerShape
                )
                .clickableWithoutBackgroundRipple {
                    onClick(text!!)
                }
                .padding(4.dp)
                .fillMaxWidth()
        )
    }

    LaunchedEffect(Unit) {
        // copied and adjusted from MarkdownCodeFence()
        val languageInMarkdown = node.findChildOfType(MarkdownTokenTypes.FENCE_LANG)?.getTextInNode(content)?.toString()
        if (node.children.size >= 3) {
            val start = node.children[2].startOffset
            val end = node.children[(node.children.size - 2).coerceAtLeast(2)].endOffset
            text = content.subSequence(start, end).toString().replaceIndent()
            onCode(text!!)
        } else {
            // invalid code block, skipping
            text = null
            return@LaunchedEffect
        }
        if (languageInMarkdown == null) {
            language = null
            return@LaunchedEffect
        }
        language = TreeSitterLanguage.getForMarkdown(languageInMarkdown)
    }
}