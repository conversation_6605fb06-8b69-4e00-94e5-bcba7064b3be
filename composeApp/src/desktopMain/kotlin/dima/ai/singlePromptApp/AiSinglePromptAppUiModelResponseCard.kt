package dima.ai.singlePromptApp

import GlobalStyling
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.input.pointer.PointerIcon
import androidx.compose.ui.input.pointer.pointerHoverIcon
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dima.apps.dired.addSelectedBorder
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.markdownRendererLibraryPatches.MyMarkdown
import dima.markdownRendererLibraryPatches.myMarkdownComponents
import dima.os.copyToClipboard
import dima.utils.TextLoadingWithAnimatedForeground
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleThemed
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

internal enum class CardState {
    Loading,
    Succeeded,
    Failed,
}

@Composable
internal fun AiSinglePromptAppUiModelResponseCard(
    prompt: String,
    model: LlmModel,
    isSelected: Boolean,
    selectedCodeBlock: Int?,
    width: Dp,
    maxTokens: Int? = null,
    onStateChange: (model: LlmModel, state: CardState) -> Unit,
    onCodeBlockSizeUpdate: (model: LlmModel, size: Int) -> Unit,
    onMouseClickToSelect: () -> Unit,
    onScrollStateCreated: (model: LlmModel, scrollState: ScrollState) -> Unit,
    onGloballyPositioned: (LayoutCoordinates) -> Unit
) {
    var response by remember { mutableStateOf<LlmSinglePrompt.DatabaseEntry?>(null) }
    var endpoint by remember { mutableStateOf<String?>(null) }
    var isRetrying by remember { mutableStateOf(false) }
    var codeBlocks by remember { mutableStateOf<List<String>>(emptyList()) }
    var elapsedTime by remember { mutableStateOf<String?>(null) }
    var currentJob by remember { mutableStateOf<Job?>(null) }
    val scrollState = rememberScrollState()
    val coroutineScope = rememberCoroutineScope()

    fun copyToClipboard() {
        if (response == null) {
            return
        }

        fun copyAll(s: String) {
            copyToClipboard(s.trim(), notificationTitleSubstring = "all")
        }

        val r = response!!
        when {
            r.error != null -> {
                copyAll(r.error)
                return
            }

            r.response == null -> return
            selectedCodeBlock == null -> copyAll(r.response)

            else -> {
                val codeBlock = codeBlocks.getOrNull(selectedCodeBlock)
                if (codeBlock != null) {
                    copyToClipboard(codeBlock.trim(), notificationTitleSubstring = "code")
                }
            }
        }
    }

    val modifier = Modifier
        .widthIn(
            min = if (response != null && response!!.error != null) (width / 2f) else Dp.Unspecified,
            max = if (response == null || response?.error != null) (width / 2f) else width
        )
        .addSelectedBorder(shouldDrawBorder = isSelected)
        .pointerHoverIcon(PointerIcon.Hand)
        .clickableWithoutBackgroundRipple {
            if (isSelected) {
                copyToClipboard()
            } else {
                onMouseClickToSelect()
            }
        }
        .padding(start = 12.dp, top = 12.dp)
        .onGloballyPositioned { layoutCoordinates ->
            onGloballyPositioned(layoutCoordinates)
        }
    Column(
        modifier = modifier
    ) {
        LlmSinglePromptAppUiModelResponseHeader(
            model = model,
            isRetrying = isRetrying,
            endpoint = endpoint,
            elapsedTime = elapsedTime,
            showTiming = response != null
        )
        when {
            response == null -> {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp)
                ) {
                    if (endpoint != null) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            TextLoadingWithAnimatedForeground(endpoint!!)
                            if (elapsedTime != null) {
                                Text(
                                    elapsedTime!!,
                                    color = GlobalStyling.getGrayColor()
                                )
                            }
                        }
                    }
                }
            }

            response!!.error != null -> {
                Text(
                    "$endpoint\n\n" + response!!.error!!,
                    color = GlobalStyling.getRedTextColor(),
                    modifier = Modifier
                        .padding(top = 6.dp, bottom = 24.dp, end = 12.dp)
                )
            }

            response!!.response != null -> {
                Box(
                    modifier = Modifier
                        // add padding for scrollbar
                        .padding(end = 6.dp)
                ) {
                    var codeBlockIndex = 0
                    MyMarkdown(
                        LlmSinglePrompt.patchResponse(response!!.response!!),
                        components = myMarkdownComponents(
                            codeFence = {
                                TreeSitterCodeFenceText(
                                    it.content, it.node,
                                    onCode = { code ->
                                        codeBlocks = codeBlocks + code
                                    },
                                    onClick = { code ->
                                        copyToClipboard(code.trim(), notificationTitleSubstring = "code")
                                    },
                                    isHighlighted = isSelected && selectedCodeBlock != null && selectedCodeBlock == codeBlockIndex++
                                )
                            }
                        ),
                        modifier = Modifier
                            .verticalScroll(state = scrollState)
                            .padding(end = 16.dp, bottom = 4.dp)
                    )
                    // onCodeBlockSize(codeBlocks.size) // Old call, replaced by LaunchedEffect
                    // wrap with Box, so the scroll bar does not take the full height
                    Box(modifier = Modifier.matchParentSize()) {
                        VerticalScrollbar(
                            style = scrollbarStyleThemed(),
                            adapter = rememberScrollbarAdapter(scrollState = scrollState),
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .offset(x = 6.dp)
                                .padding(
                                    bottom = GlobalStyling.ScrollBar.outerPadding,
                                    end = if (isSelected) GlobalStyling.ScrollBar.outerPadding else 0.dp
                                )
                                .alpha(if (isSelected) 1f else GlobalStyling.DISABLED_ALPHA)
                        )
                    }
                }
            }
        }
    }

    LaunchedEffect(codeBlocks.size) {
        onCodeBlockSizeUpdate(model, codeBlocks.size)
    }

    suspend fun requestFromService() {
        codeBlocks = emptyList()
        onStateChange(model, CardState.Loading)
        try {
            val result = AiSinglePromptAppService.requestResponse(prompt, model, maxTokens)
            response = result
            if (result.error == null) {
                onStateChange(model, CardState.Succeeded)
            } else {
                onStateChange(model, CardState.Failed)
            }
        } catch (e: Exception) {
            response = LlmSinglePrompt.DatabaseEntry(
                prompt = prompt,
                model = model,
                maxTokens = maxTokens,
                error = e.message ?: "Unknown error"
            )
            onStateChange(model, CardState.Failed)
        } finally {
            isRetrying = false
        }
    }

    LaunchedEffect(Unit) {
        onScrollStateCreated(model, scrollState)
    }

    // Monitor service status updates for this specific model
    LaunchedEffect(prompt, model, maxTokens) {
        val requestKey = AiSinglePromptAppService.RequestKey(prompt, model, maxTokens)
        launch {
            AiSinglePromptAppService.requestStatusFlow.collect { status ->
                if (status.requestKey == requestKey) {
                    when (status) {
                        is AiSinglePromptAppService.RequestStatus.Started -> {
                            endpoint = status.providerName
                            elapsedTime = null
                        }

                        is AiSinglePromptAppService.RequestStatus.Progress -> {
                            endpoint = status.providerName
                            elapsedTime = "(${status.elapsedSeconds}s)"
                        }

                        is AiSinglePromptAppService.RequestStatus.Completed -> {
                            // Response will be updated by the main request
                        }

                        is AiSinglePromptAppService.RequestStatus.Failed -> {
                            // Error will be updated by the main request
                        }
                    }
                }
            }
        }
    }
    LaunchedEffect(prompt) {
        currentJob?.cancel()
        response = null
        endpoint = null
        isRetrying = false
        codeBlocks = emptyList()
        elapsedTime = null
        currentJob = coroutineScope.launch {
            requestFromService()
        }
    }
    LaunchedEffect(cardEvent) {
        if (cardEvent == null || cardEvent!!.model != model) {
            return@LaunchedEffect
        }
        when (cardEvent!!.event) {
            ChatGptCardEventType.CopyToClipboard -> copyToClipboard()

            ChatGptCardEventType.Retry -> {
                currentJob?.cancel()
                if (response != null && response!!.response != null) {
                    showErrorNotification("Can not retry since there is already a response")
                } else {
                    isRetrying = true
                    showNotification("Retrying $model...", durationMillis = 1000L)
                    coroutineScope.launch {
                        requestFromService()
                    }
                }
            }
        }
    }
}

