package dima.ai.singlePromptApp

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.database.transactionToAvoidBusySqlite
import dima.globalState.GlobalState
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.transaction
import java.io.File

internal enum class ChatGptCardEventType {
    CopyToClipboard,
    Retry,
}

internal data class ChatGptCardEvent(
    val event: ChatGptCardEventType,
    val model: LlmModel,
)

internal fun getInitialPrompt(): MutableState<String> {
    val lastQueries = LlmSinglePrompt.getLastQueries()
    return if (lastQueries.isEmpty()) {
        mutableStateOf("hi")
    } else {
        mutableStateOf(lastQueries.last())
    }
}

internal object LlmSinglePrompt {

    internal const val RELATIVE_CACHE_DIRECTORY = "cache/chatgpt"
    internal val cacheDirectory = File("cache/chatgpt")
    private val lastQueryFile = File(cacheDirectory, "lastQueries.json")
    private var database by mutableStateOf<Database?>(null)

    internal fun getLastQueries(): List<String> {
        if (lastQueryFile.exists()) {
            return Json.decodeFromString<List<String>>(lastQueryFile.readText())
        }
        return emptyList()
    }

    internal fun patchResponse(response: String): String {
        return if (GlobalState.llmSinglePromptApp.codeModeLanguage == null || response.lines().any { it.startsWith("```") }) {
            response
        } else {
            "```${GlobalState.llmSinglePromptApp.codeModeLanguage}\n${response}\n```"
        }
    }

    internal fun rememberQuery(query: String) {
        val lastQueries = getLastQueries().toMutableList()
        if (lastQueries.contains(query)) {
            lastQueries.remove(query)
        }
        lastQueries.add(query)
        lastQueryFile.writeText(Json.encodeToString(lastQueries))
    }

    internal fun setupChatGptDatabase() {
        if (database != null) {
            return
        }
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        val dbFile = File(cacheDirectory, "chatgpt.db")
        database = Database.connect("jdbc:sqlite:$dbFile", "org.sqlite.JDBC")
        transactionToAvoidBusySqlite(database) {
            SchemaUtils.create(Table)
        }
    }

    object Table : org.jetbrains.exposed.sql.Table("Results") {
        val prompt = text("prompt")
        val model = text("model")
        val response = text("response").nullable()
        val maxTokens = integer("maxTokens").nullable()
        val error = text("error").nullable()
    }

    data class DatabaseEntry(
        val prompt: String,
        val model: LlmModel,
        val response: String? = null,
        val error: String? = null,
        val maxTokens: Int? = null
    )

    /**
     * @return null when not in database
     */
    fun hasResultWithoutError(prompt: String, model: LlmModel, maxTokens: Int?): DatabaseEntry? {
        val rowResult = transaction(database) {
            Table.selectAll()
                .where {
                    (Table.prompt eq prompt) and
                            (Table.model eq model.apiName) and
                            (Table.maxTokens eq maxTokens) and
                            (Table.error.isNull())
                }
                .firstOrNull()
        }
        if (rowResult == null) {
            return null
        }
        return DatabaseEntry(
            prompt = prompt,
            model = model,
            response = rowResult[Table.response],
            maxTokens = maxTokens
        )
    }

    internal fun insert(prompt: String, model: LlmModel, response: String, maxTokens: Int?) {
        transaction(database) {
            Table.insert {
                it[this.prompt] = prompt
                it[this.model] = model.apiName
                it[this.response] = response
                it[this.maxTokens] = maxTokens
            }
        }
    }

    internal fun insertError(prompt: String, model: LlmModel, error: String, maxTokens: Int?) {
        val has = transaction(database) {
            Table.selectAll().where {
                (Table.prompt eq prompt) and (Table.model eq model.apiName) and (Table.maxTokens eq maxTokens)
            }.firstOrNull()
        }
        transaction(database) {
            if (has == null) {
                Table.insert {
                    it[this.prompt] = prompt
                    it[this.model] = model.apiName
                    it[this.maxTokens] = maxTokens
                    it[this.error] = error
                }
            } else {
                Table.update({
                    (Table.prompt eq prompt) and (Table.model eq model.apiName) and (Table.maxTokens eq maxTokens)
                }) {
                    it[this.error] = error
                }
            }
        }
    }

}