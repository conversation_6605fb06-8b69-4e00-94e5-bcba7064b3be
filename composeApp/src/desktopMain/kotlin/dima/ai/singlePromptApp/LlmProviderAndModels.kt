package dima.ai.singlePromptApp

import dima.ai.providers.CohereProvider
import dima.ai.providers.GeminiProvider
import dima.ai.providers.OpenAiProvider
import dima.settings
import dima.utils.StringResult
import kotlinx.serialization.Serializable

sealed interface LlmProvider {
    val providerName: String
    val apiKey: String
    val models: List<LlmModel>
}

sealed class LlmSinglePromptAppProvider : LlmProvider {

    data class OpenAi(
        override val providerName: String,
        val urlWithoutTrailingSlash: String,
        override val apiKey: String,
        override val models: List<LlmModel>
    ) : LlmSinglePromptAppProvider() {

        suspend fun generate(model: LlmModel, prompt: String, maxTokens: Int?): StringResult {
            return OpenAiProvider.queryApi(
                model = model,
                prompt = prompt,
                maxTokens = maxTokens,
                endpoint = this,
                retriedOnFirstError = false
            )
        }
    }

    data class GeminiFlash2Dot0(override val apiKey: String) : LlmSinglePromptAppProvider() {

        suspend fun generate(prompt: String): StringResult {
            return GeminiProvider.queryFlash_2_0(prompt = prompt, endpoint = this)
        }

        override val providerName: String
            get() = "Google Gemini 2.0 Flash"

        override val models: List<LlmModel>
            get() = listOf(LlmModel.Gemini_2_0_Flash)
    }

    data class CohereCommandA(override val apiKey: String) : LlmSinglePromptAppProvider() {
        suspend fun generate(prompt: String): StringResult {
            return CohereProvider.queryCohereCommandA(prompt = prompt, endpoint = this)
        }

        override val providerName: String
            get() = "Cohere Command A"

        override val models: List<LlmModel>
            get() = listOf(LlmModel.CohereCommandA)
    }
}

internal fun getProvidersForModel(model: LlmModel): List<LlmSinglePromptAppProvider> {
    return settings.llmSinglePromptAppProviders.filter { it.models.contains(model) }
}

internal fun getModelsFromProviders(): List<LlmModel> {
    val models = mutableSetOf<LlmModel>()
    settings.llmSinglePromptAppProviders.forEach {
        models.addAll(it.models)
    }
    return models.toList()
}

@Suppress("EnumEntryName")
@Serializable
enum class LlmModel(val apiName: String, val displayName: String, val logoImageUrl: String? = null) {
    Gpt3_5_Turbo(
        "gpt-3.5-turbo", "GPT-3.5 Turbo",
        "https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/ChatGPT_logo.svg/768px-ChatGPT_logo.svg.png"
    ),
    Gpt4_1(
        "gpt-4.1", "GPT-4.1",
        "https://static.vecteezy.com/system/resources/previews/021/059/827/non_2x/chatgpt-logo-chat-gpt-icon-on-white-background-free-vector.jpg"
    ),
    Gpt4(
        "gpt-4", "GPT-4",
        "https://static.vecteezy.com/system/resources/previews/021/059/827/non_2x/chatgpt-logo-chat-gpt-icon-on-white-background-free-vector.jpg"
    ),
    Gpt_4o_mini(
        "gpt-4o-mini", "GPT-4o mini",
        "https://static.vecteezy.com/system/resources/previews/021/059/827/non_2x/chatgpt-logo-chat-gpt-icon-on-white-background-free-vector.jpg"
    ),
    Grok_3_mini(
        "grok", "Grok 3 mini",
        "https://byothe.fr/wp-content/uploads/2023/11/grok-intelligence-artificielle-1392x783.jpg"
    ),
    o3_mini(
        "openai-reasoning", "o3 mini",
        "https://static.vecteezy.com/system/resources/previews/021/059/827/non_2x/chatgpt-logo-chat-gpt-icon-on-white-background-free-vector.jpg"
    ),
    O4_mini_high(
        "o4-mini-high", "o4-mini-high",
        "https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/ChatGPT_logo.svg/768px-ChatGPT_logo.svg.png"
    ),
    Claude_3_5_Haiku(
        "claude-3.5-haiku", "Claude 3.5 Haiku",
        "https://uxwing.com/wp-content/themes/uxwing/download/brands-and-social-media/claude-ai-icon.png"
    ),
    Phi_4(
        "phi-4", "Phi-4",
        "https://i.ytimg.com/vi/H85F0vib85Y/hq720.jpg?sqp=-oaymwEnCK4FEIIDSFryq4qpAxkIARUAAAAAGAElAADIQj0AgKJDuAK33r8Y&rs=AOn4CLB5qHOjcGJIYfzn-w6je0H8Vg1uvQ"
    ),
    Gemini_2_0_Flash_Thinking_Exp(
        "gemini-2.0-flash-thinking-exp-01-21", "Gemini 2.0 Flash",
        "https://kodexolabs.com/wp-content/uploads/2024/05/Gemini-1.5-Flash-Blog-Thumbnail_1.webp"
    ),
    Gemini_2_0_Flash(
        "gemini-2.0-flash", "Gemini 2.0 Flash",
        "https://kodexolabs.com/wp-content/uploads/2024/05/Gemini-1.5-Flash-Blog-Thumbnail_1.webp"
    ),
    CohereCommandA(
        "command-r-plus", "Command A",
        "https://cdn.sanity.io/images/rjtqmwfu/web3-prod/0750efbc3db33b1a67bc77575525b076f0137f26-1200x630.jpg?w=1200&h=630"
    ),
    Sonar_Pro_Web_Search(
        "sonar-pro", "Sonar Pro (Web Search)",
        "https://framerusercontent.com/images/RSdckef1II88WpcyGuF13FGxO8.png"
    ),
    OpenRouter_Deepseek_R1_Distill_Llama_70B(
        "deepseek/deepseek-r1-distill-llama-70b:free", "Deepseek R1 Distill Llama 70B",
        "https://substackcdn.com/image/fetch/w_1456,c_limit,f_webp,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb41161e2-7139-4c15-84ba-0fff53b18561_452x370.png"
    ),
    OpenRouter_Deepseek_R1(
        "deepseek/deepseek-r1:free",
        "Deepseek R1",
        "https://substackcdn.com/image/fetch/w_1456,c_limit,f_webp,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb41161e2-7139-4c15-84ba-0fff53b18561_452x370.png"
    ),
    OpenRouter_Deepseek_V3(
        "deepseek/deepseek-chat:free",
        "Deepseek V3",
        "https://substackcdn.com/image/fetch/w_1456,c_limit,f_webp,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fb41161e2-7139-4c15-84ba-0fff53b18561_452x370.png"
    ),
}
