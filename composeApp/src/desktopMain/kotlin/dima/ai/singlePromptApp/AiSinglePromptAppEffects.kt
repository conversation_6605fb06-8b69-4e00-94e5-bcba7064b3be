package dima.ai.singlePromptApp

import GlobalEvent
import androidx.compose.foundation.ScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.focus.FocusRequester
import dialogs
import dima.globalState.GlobalState
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
internal fun AiSinglePromptAppEffects(
    appFocusRequester: FocusRequester,
    prompt: String,
    selectedCodeBlock: Int?,
    changeSelectedCodeBlock: (Int?) -> Unit,
    selectedModel: LlmModel?,
    scrollXState: Map<LlmModel, XScrollState>,
    cardYScrollState: Map<LlmModel, ScrollVelocity>,
    listState: ScrollState,
    codeBlockSizes: Map<LlmModel, Int>
) {
    LaunchedEffect(Unit) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
        CoroutineScope(Dispatchers.IO).launch {
            val sleepDurationMs = 16L
            while (true) {
                cardYScrollState.forEach { (_, scrollVelocity) ->
                    scrollVelocity.callTickInLoop()
                }
                delay(sleepDurationMs)
            }
        }
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(prompt) {
        LlmSinglePrompt.rememberQuery(prompt)
        changeSelectedCodeBlock(null)
    }
    // This effect primarily handles state changes when selectedModel changes
    LaunchedEffect(selectedModel) {
        changeSelectedCodeBlock(null)
        // The actual scrolling and GlobalState.chatGpt.selectedModel update
        // will be handled by the LaunchedEffect(scrollXState, selectedModel)
        // once the position of the newly selected model is known.
    }
    LaunchedEffect(scrollXState, selectedModel) {
        if (selectedModel != null && scrollXState.containsKey(selectedModel)) {
            GlobalState.llmSinglePromptApp = GlobalState.llmSinglePromptApp.copy(selectedModel = selectedModel)
            scrollToModelResponse(
                models = getModelsFromProviders(),
                animateScroll = true, // Always animate when selection changes and position is known
                selectedModel = selectedModel,
                listState = listState,
                scrollXState = scrollXState
            )
        }
    }
    LaunchedEffect(codeBlockSizes) {
        if (selectedModel != null && codeBlockSizes.isNotEmpty() && selectedCodeBlock != null) {
            val size = codeBlockSizes[selectedModel]
            changeSelectedCodeBlock(
                if (size != null && size > 0) { // Ensure size is greater than 0
                    selectedCodeBlock % size
                } else {
                    null // No code blocks, so nothing to select
                }
            )
        }
    }
    LaunchedEffectGlobalEventForApps {
        when (globalEvent) {
            GlobalEvent.ScrollToTop -> {
                if (selectedModel != null) {
                    cardYScrollState[selectedModel]?.scrollToTop()
                }
            }

            GlobalEvent.ScrollToBottom -> {
                if (selectedModel != null) {
                    cardYScrollState[selectedModel]?.scrollToBottom()
                }
            }

            else -> {}
        }
    }
}
