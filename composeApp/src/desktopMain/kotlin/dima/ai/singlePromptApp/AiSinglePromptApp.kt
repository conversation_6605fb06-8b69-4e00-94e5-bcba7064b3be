package dima.ai.singlePromptApp

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import dima.ai.singlePromptApp.LlmSinglePrompt.Table.prompt
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.utils.AppKey
import dima.utils.DummyFocusable
import dima.utils.ScrollVelocity
import dima.utils.handleAppMap
import handleLostFocus
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Mutex for synchronizing global chat state updates.
 *
 * This ensures thread-safe updates to the cardStateMap map and prevents race conditions
 * when multiple cards try to update their states simultaneously, or when a new prompt
 * arrives while state updates are in progress.
 */
internal val cardStateUpdateMutex = Mutex()

internal var cardEvent by mutableStateOf<ChatGptCardEvent?>(null)

internal data class XScrollState(val x: Float, val width: Int)

/**
 * It flickers on new response contents since the horizontal content increases, I currently instantly set the scroll position back,
 * but the little jump is visible.
 *
 * There is also very slight flicker on launching this app fresh since it loads the old response from the file system async, but
 * the UI is already displayed with the 'in progress' state.
 *
 * Fixing those 2 flicker issues is not easy and not really worth it, since they are just minimal issues.
 */
@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AiSinglePromptApp() {
    remember {
        LlmSinglePrompt.setupChatGptDatabase()
    }
    val cardStateMap = remember { mutableStateMapOf<LlmModel, CardState>() }
    var prompt by remember { getInitialPrompt() }
    var selectedModel by remember { mutableStateOf(GlobalState.llmSinglePromptApp.selectedModel) }
    val listState = rememberScrollState()
    val appFocusRequester = remember { FocusRequester() }
    val endpointModelNames = remember { getModelsFromProviders() }
    var selectedCodeBlock by remember { mutableStateOf<Int?>(null) }
    val coroutineScope = rememberCoroutineScope()

    /**
     * Use this since smooth scrolling inside [AiSinglePromptAppUiModelResponseCard] did not work.
     */
    val cardYScrollState = remember { mutableStateMapOf<LlmModel, ScrollVelocity>() }
    var scrollXState by remember { mutableStateOf(mapOf<LlmModel, XScrollState>()) }
    var codeBlockSizes by remember { mutableStateOf(mapOf<LlmModel, Int>()) }

    fun goToPreviousModel() {
        if (endpointModelNames.isEmpty()) {
            selectedModel = null
            return
        }
        if (selectedModel == null) {
            selectedModel = endpointModelNames.firstOrNull()
            return
        }
        val currentIndex = selectedModel?.let { endpointModelNames.indexOf(it) } ?: 0
        if (currentIndex > 0) {
            selectedModel = endpointModelNames[currentIndex - 1]
        }
    }

    fun goToNextModel() {
        if (endpointModelNames.isEmpty()) {
            selectedModel = null
            return
        }
        if (selectedModel == null) {
            selectedModel = endpointModelNames.firstOrNull()
            return
        }
        val currentIndex = selectedModel?.let { endpointModelNames.indexOf(it) } ?: (endpointModelNames.size - 1)
        if (currentIndex < endpointModelNames.size - 1) {
            selectedModel = endpointModelNames[currentIndex + 1]
        }
    }

    remember {
        appKeys = listOf(
            AppKey(Key.R, "Retry model") { retryModel(selectedModel) },
            AppKey(Key.F, "Set code mode language") { setCodeModeLanguage() },
            AppKey(Key.Escape, "Exit out of code selection") { selectedCodeBlock = null },
            AppKey(Key.DirectionDown, "Select code block below") {
                if (selectedModel == null) {
                    return@AppKey
                }
                val size = codeBlockSizes[selectedModel] ?: return@AppKey
                selectedCodeBlock = if (selectedCodeBlock == null) {
                    0
                } else {
                    (selectedCodeBlock!! + 1).coerceAtMost(size - 1)
                }
            },
            AppKey(Key.DirectionUp, "Select code block above") {
                if (selectedModel == null) {
                    return@AppKey
                }
                if (selectedCodeBlock == null) {
                    selectedCodeBlock = 0
                } else {
                    if (selectedCodeBlock!! >= 1) {
                        selectedCodeBlock = selectedCodeBlock!! - 1
                    }
                }
            },
            AppKey(Key.J, "Copy prompt to clipboard") { copyToClipboard(prompt) },
            AppKey(Key.U, "Change prompt") { changePrompt { prompt = it } },
            AppKey(Key.O, "Use old prompt from history") { switchToExistingPrompt { prompt = it } },
            AppKey(Key.H, "Go to previous model") { goToPreviousModel() },
            AppKey(Key.N, "Go to next model") { goToNextModel() },
            AppKey(
                Key.T, "Scroll model response down",
                onKeyUp = {
                    if (selectedModel != null) {
                        cardYScrollState[selectedModel]?.onKeyReleased()
                    }
                },
                onKeyDown = {
                    if (selectedModel == null) {
                        selectedModel = endpointModelNames.firstOrNull()
                    } else {
                        cardYScrollState[selectedModel]?.onScrollDownKeyPressed()
                    }
                }),
            AppKey(
                Key.C, "Scroll model response up",
                onKeyUp = {
                    if (selectedModel != null) {
                        cardYScrollState[selectedModel]?.onKeyReleased()
                    }
                },
                onKeyDown = {
                    if (selectedModel == null) {
                        selectedModel = endpointModelNames.firstOrNull()
                    } else {
                        cardYScrollState[selectedModel]?.onScrollUpKeyPressed()
                    }
                }),
            AppKey(
                Key.V, "Scroll model response up a lot",
                onKeyUp = {
                    if (selectedModel != null) {
                        cardYScrollState[selectedModel]?.onKeyReleased()
                    }
                },
                onKeyDown = {
                    if (selectedModel == null) {
                        selectedModel = endpointModelNames.firstOrNull()
                    } else {
                        cardYScrollState[selectedModel]?.onScrollUpMoreKeyPressed()
                    }
                }),
            AppKey(
                Key.M, "Scroll model response down a lot",
                onKeyUp = {
                    if (selectedModel != null) {
                        cardYScrollState[selectedModel]?.onKeyReleased()
                    }
                },
                onKeyDown = {
                    if (selectedModel == null) {
                        selectedModel = endpointModelNames.firstOrNull()
                    } else {
                        cardYScrollState[selectedModel]?.onScrollDownMoreKeyPressed()
                    }
                }),
            AppKey(Key.Enter, "Copy model response/error to clipboard") {
                if (selectedModel != null) {
                    cardEvent = ChatGptCardEvent(ChatGptCardEventType.CopyToClipboard, selectedModel!!)
                }
            },
        )
    }

    Column(
        modifier = Modifier
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                return@onPreviewKeyEvent it.handleAppMap()
            },
    ) {
        DummyFocusable()
        AiSinglePromptAppUiTopRow(
            prompt = prompt,
            cardStateMap = cardStateMap,
            onGoToNextModel = ::goToNextModel,
            onGoToPreviousModel = ::goToPreviousModel,
            onSwitchToExistingPrompt = {
                switchToExistingPrompt {
                    prompt = it
                }
            },
            openChangePromptDialog = {
                changePrompt {
                    prompt = it
                }
            }
        )
        AiSinglePromptAppUiRowCards(
            listState = listState,
            selectedModel = selectedModel,
            selectedCodeBlock = selectedCodeBlock,
            onCardYScrollStateEntryChange = { modelName, scrollVelocity ->
                cardYScrollState[modelName] = scrollVelocity
            },
            onScrollXStateEntryChange = { modelName, xState ->
                coroutineScope.launch {
                    scrollXState = scrollXState + (modelName to xState)
                }
            },
            onCodeBlockSizeEntryChange = { modelName, size ->
                coroutineScope.launch {
                    codeBlockSizes = codeBlockSizes + (modelName to size)
                }
            },
            onCardStateChange = { model, state ->
                coroutineScope.launch {
                    cardStateUpdateMutex.withLock {
                        cardStateMap[model] = state
                    }
                }
            },
            prompt = prompt,
            onSelectedModelChange = { selectedModel = it },
        )
    }
    AiSinglePromptAppEffects(
        appFocusRequester = appFocusRequester,
        cardYScrollState = cardYScrollState,
        prompt = prompt,
        selectedCodeBlock = selectedCodeBlock,
        selectedModel = selectedModel,
        scrollXState = scrollXState,
        listState = listState,
        codeBlockSizes = codeBlockSizes,
        changeSelectedCodeBlock = { selectedCodeBlock = it }
    )
}
