package dima.ai.singlePromptApp

import GlobalStyling
import androidx.compose.foundation.layout.*
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.onPointerEvent
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.composables.icons.lucide.*
import dima.globalState.GlobalState
import dima.utils.clickableWithoutBackgroundRipple

/**
 * Displays history icon, the current prompt and accumulated model state.
 */
@OptIn(ExperimentalComposeUiApi::class) // for .onPointerEvent()
@Composable
internal fun AiSinglePromptAppUiTopRow(
    prompt: String,
    cardStateMap: Map<LlmModel, CardState>,
    onGoToNextModel: () -> Unit,
    onGoToPreviousModel: () -> Unit,
    onSwitchToExistingPrompt: () -> Unit,
    openChangePromptDialog: () -> Unit,
) {
    val textColor = GlobalStyling.getTextColor()
    Row(
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .padding(8.dp)
    ) {
        Column {
            Icon(
                Lucide.History,
                contentDescription = null,
                tint = GlobalStyling.getGrayColor(),
                modifier = Modifier
                    .clickableWithoutBackgroundRipple {
                        onSwitchToExistingPrompt()
                    }
                    .padding(8.dp)
            )
            if (GlobalState.llmSinglePromptApp.codeModeLanguage != null) {
                Text(
                    "Patched ```" + GlobalState.llmSinglePromptApp.codeModeLanguage,
                    color = GlobalStyling.getBlueColor(),
                    modifier = Modifier
                        .padding(top = 4.dp, start = 8.dp)
                )
            }
        }

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .weight(1f)
                .padding(bottom = 12.dp)
                .clickableWithoutBackgroundRipple {
                    openChangePromptDialog()
                }
                .fillMaxWidth()
                .onPointerEvent(PointerEventType.Scroll) {
                    it.changes.firstOrNull()?.let { scrollEvent ->
                        if (scrollEvent.scrollDelta.y > 0) {
                            onGoToNextModel()
                        } else if (scrollEvent.scrollDelta.y < 0) {
                            onGoToPreviousModel()
                        }
                    }
                }) {
            Text(
                prompt.replace(Regex("\n+"), "\n"),
                textAlign = TextAlign.Start,
                maxLines = 4,
                color = textColor,
                overflow = TextOverflow.Ellipsis,
                fontSize = 12.sp,
                modifier = Modifier
                    .padding(bottom = 4.dp)
                    .widthIn(max = 600.dp)
            )
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(start = 8.dp, top = 12.dp)
        ) {
            Icon(
                Lucide.Check,
                contentDescription = null,
                tint = GlobalStyling.getGreenColor(),
                modifier = Modifier.size(20.dp)
            )
            Text(
                cardStateMap.values.filter { it == CardState.Succeeded }.size.toString(),
                color = textColor,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(top = 12.dp)
        ) {
            Icon(
                Lucide.X,
                contentDescription = null,
                tint = GlobalStyling.getRedTextColor(),
                modifier = Modifier.size(20.dp)
            )
            Text(
                cardStateMap.values.filter { it == CardState.Failed }.size.toString(),
                color = textColor,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
        val inProgress = cardStateMap.values.filter { it == CardState.Loading }.size
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(top = 12.dp)
        ) {
            Icon(
                Lucide.Loader,
                contentDescription = null,
                tint = GlobalStyling.getGrayColor(),
                modifier = Modifier.size(20.dp)
            )
            Text(
                inProgress.toString(),
                color = textColor,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }

}
