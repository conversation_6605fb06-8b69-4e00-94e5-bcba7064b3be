package dima.ai.singlePromptApp

import androidx.compose.foundation.ScrollState
import dima.globalState.GlobalState

internal suspend fun scrollToModelResponse(
    models: List<LlmModel>,
    selectedModel: LlmModel,
    listState: ScrollState,
    scrollXState: Map<LlmModel, XScrollState>,
    animateScroll: Boolean
) {
    val index = models.indexOf(selectedModel)
    if (index == 0) {
        if (animateScroll) {
            listState.animateScrollTo(0)
        } else {
            listState.scrollTo(0)
        }
    } else {
        val xState = scrollXState[selectedModel]
        if (xState == null) {
            if (animateScroll) {
                listState.animateScrollTo(0)
            } else {
                listState.scrollTo(0)
            }
        } else {
            val windowWidth = GlobalState.mainWindow.widthInDp.toInt()
            val scrollTo = xState.x.toInt() - (windowWidth / 2)
            if (animateScroll) {
                listState.animateScrollTo(scrollTo)
            } else {
                listState.scrollTo(scrollTo)
            }
        }
    }
}
