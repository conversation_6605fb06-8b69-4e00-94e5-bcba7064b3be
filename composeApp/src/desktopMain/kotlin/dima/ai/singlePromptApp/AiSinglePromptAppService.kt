package dima.ai.singlePromptApp

import dima.ai.singlePromptApp.AiSinglePromptAppService.requestStatusFlow
import dima.utils.StringResult
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap

/**
 * Service that manages AI API calls for the Single Prompt App.
 *
 * Features:
 * - Prevents duplicate API calls for the exact same request (prompt, model, maxTokens) while one is in transit.
 * - Ensures that API calls for the *same model* are executed sequentially, not in parallel.
 * - Allows API calls for *different models* to run in parallel.
 * - Stores results in a database, even if the app is in the background.
 * - Centralizes API call management.
 */
internal object AiSinglePromptAppService {

    // Background scope that survives app lifecycle, using a SupervisorJob so one failing child doesn't cancel others.
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Map to hold a Mutex for each LlmModel. Used to serialize API calls per model.
    private val modelExecutionLocks = ConcurrentHashMap<LlmModel, Mutex>()

    // Tracks in-progress requests (by exact RequestKey) to prevent duplicate API calls for the same prompt/model/tokens.
    // The Deferred allows multiple callers for the same key to await the same underlying API call.
    private val inProgressExactRequests = ConcurrentHashMap<RequestKey, Deferred<LlmSinglePrompt.DatabaseEntry>>()

    // Flow for broadcasting request status updates
    private val _requestStatusFlow = MutableSharedFlow<RequestStatus>(replay = 100)
    val requestStatusFlow: SharedFlow<RequestStatus> = _requestStatusFlow.asSharedFlow()

    /**
     * Unique key for identifying exact requests (prompt, model, and maxTokens combination).
     */
    data class RequestKey(
        val prompt: String,
        val model: LlmModel,
        val maxTokens: Int?
    )

    /**
     * Status of a request, used for UI updates via [requestStatusFlow].
     */
    sealed class RequestStatus {
        abstract val requestKey: RequestKey

        data class Started(
            override val requestKey: RequestKey,
            val providerName: String
        ) : RequestStatus()

        data class Progress(
            override val requestKey: RequestKey,
            val providerName: String,
            val elapsedSeconds: Long
        ) : RequestStatus()

        data class Completed(
            override val requestKey: RequestKey,
            val result: LlmSinglePrompt.DatabaseEntry
        ) : RequestStatus()

        data class Failed(
            override val requestKey: RequestKey,
            val error: String
        ) : RequestStatus()
    }

    /**
     * Request a response for the given prompt and model.
     *
     * - Checks the database for a cached successful result first.
     * - If not in DB, checks if an identical request (same prompt, model, maxTokens) is already in progress.
     *   If so, awaits that existing request.
     * - Otherwise, initiates a new API request. This new request will be serialized with other requests
     *   for the *same model* using a model-specific mutex. Requests for *different models* can proceed in parallel.
     *
     * @param prompt The prompt to send to the AI.
     * @param model The AI model to use.
     * @param maxTokens Maximum tokens for the response (optional).
     * @return [LlmSinglePrompt.DatabaseEntry] containing the response or an error.
     */
    suspend fun requestResponse(
        prompt: String,
        model: LlmModel,
        maxTokens: Int? = null
    ): LlmSinglePrompt.DatabaseEntry {
        val requestKey = RequestKey(prompt, model, maxTokens)

        // 1. Check DB for an existing successful result for this exact request.
        val dbResult = LlmSinglePrompt.hasResultWithoutError(prompt, model, maxTokens)
        if (dbResult != null) {
            _requestStatusFlow.emit(RequestStatus.Completed(requestKey, dbResult))
            return dbResult
        }

        // 2. Get or create a Deferred for this exact requestKey.
        // `computeIfAbsent` on ConcurrentHashMap is atomic for the key. This ensures that for a given `requestKey`,
        // the lambda to create the Deferred is only called once if the key is not present.
        val deferred = inProgressExactRequests.computeIfAbsent(requestKey) { _ ->
            // This Deferred will execute the API request. The execution itself is wrapped
            // with a model-specific lock to ensure serialization per model.
            serviceScope.async {
                // Obtain a mutex specific to this model. Creates one if not present.
                val modelMutex = modelExecutionLocks.computeIfAbsent(model) { Mutex() }
                modelMutex.withLock {
                    // Re-check database INSIDE the model-specific lock.
                    // This is a safeguard: if another request for the same model (but perhaps a different prompt)
                    // somehow led to this exact requestKey's result being populated in the DB
                    // by a concurrent operation that just released the modelMutex, we'd pick it up.
                    // More critically, this lock serializes calls to `executeApiRequest` for the same `model`.
                    val innerDbResult = LlmSinglePrompt.hasResultWithoutError(prompt, model, maxTokens)
                    if (innerDbResult != null) {
                        _requestStatusFlow.emit(RequestStatus.Completed(requestKey, innerDbResult))
                        innerDbResult // Return from DB if found inside lock
                    } else {
                        // If not in DB, execute the actual API request.
                        // This call is serialized for other requests using the same 'model'.
                        executeApiRequest(requestKey)
                    }
                }
            }
        }

        // 3. Await the Deferred and handle its completion or cancellation.
        return try {
            val result = deferred.await()
            result // Return the successful (or error-containing) result from executeApiRequest
        } catch (e: CancellationException) {
            // If the Deferred was cancelled (e.g., by cancelRequest or service shutdown)
            _requestStatusFlow.emit(RequestStatus.Failed(requestKey, "Request cancelled: ${e.message}"))
            // Create and save an error entry to the database
            LlmSinglePrompt.DatabaseEntry(
                prompt,
                model,
                error = "Request cancelled: ${e.message}",
                maxTokens = maxTokens
            ).also {
                LlmSinglePrompt.insertError(prompt, model, it.error!!, maxTokens)
            }
        } catch (e: Exception) {
            // Catch any other unexpected exception from executeApiRequest if not handled within it.
            _requestStatusFlow.emit(RequestStatus.Failed(requestKey, "Request failed: ${e.message}"))
            LlmSinglePrompt.DatabaseEntry(prompt, model, error = "Request failed: ${e.message}", maxTokens = maxTokens)
                .also {
                    LlmSinglePrompt.insertError(prompt, model, it.error!!, maxTokens)
                }
        } finally {
            // Once awaited (successfully, with error, or cancelled), remove the Deferred from the map.
            // This ensures that future identical requests will start fresh (checking DB, then new API call if needed).
            inProgressExactRequests.remove(requestKey)
        }
    }


    /**
     * Executes the API request by trying available providers for the given model.
     * This function is intended to be called under a model-specific lock.
     */
    private suspend fun executeApiRequest(requestKey: RequestKey): LlmSinglePrompt.DatabaseEntry {
        val (prompt, model, maxTokens) = requestKey
        val providers = getProvidersForModel(model)
        var lastError: String? = null

        for (provider in providers) {
            _requestStatusFlow.emit(RequestStatus.Started(requestKey, provider.providerName))
            val startTime = System.currentTimeMillis()

            // Start progress tracking for this provider attempt
            val progressJob = serviceScope.launch {
                while (isActive) { // Check isActive in case the outer scope or this job gets cancelled
                    delay(1000) // Update every second
                    val elapsed = (System.currentTimeMillis() - startTime) / 1000
                    _requestStatusFlow.emit(RequestStatus.Progress(requestKey, provider.providerName, elapsed))
                }
            }

            try {
                val result = when (provider) {
                    is LlmSinglePromptAppProvider.OpenAi -> provider.generate(model, prompt, maxTokens)
                    is LlmSinglePromptAppProvider.CohereCommandA -> provider.generate(prompt)
                    is LlmSinglePromptAppProvider.GeminiFlash2Dot0 -> provider.generate(prompt)
                }

                progressJob.cancelAndJoin() // Ensure progress job is fully stopped

                when (result) {
                    is StringResult.Error -> {
                        lastError = result.error
                        // Try next provider
                        continue
                    }

                    is StringResult.Success -> {
                        val databaseEntry = LlmSinglePrompt.DatabaseEntry(
                            prompt = prompt,
                            model = model,
                            maxTokens = maxTokens,
                            response = result.value
                        )
                        // Save successful response to database
                        LlmSinglePrompt.insert(prompt, model, result.value, maxTokens)
                        _requestStatusFlow.emit(RequestStatus.Completed(requestKey, databaseEntry))
                        return databaseEntry
                    }
                }
            } catch (e: CancellationException) {
                progressJob.cancelAndJoin() // Also cancel progress on cancellation
                throw e // Re-throw cancellation to be handled by the calling coroutine
            } catch (e: Exception) {
                progressJob.cancelAndJoin() // Cancel progress on other exceptions
                lastError = e.message ?: "Unknown error with provider ${provider.providerName}"
                // Log or handle specific provider error, then try next provider
            }
        }

        // All providers failed or no providers were available
        val finalError = lastError ?: "No providers available or all failed for model ${model.displayName}"
        val errorEntry = LlmSinglePrompt.DatabaseEntry(
            prompt = prompt,
            model = model,
            maxTokens = maxTokens,
            error = finalError
        )
        // Save error to database
        LlmSinglePrompt.insertError(prompt, model, errorEntry.error!!, maxTokens)
        _requestStatusFlow.emit(RequestStatus.Failed(requestKey, errorEntry.error))
        return errorEntry
    }

    /**
     * Shutdown the service (call when app is closing).
     * This cancels the serviceScope, which in turn cancels all coroutines launched within it,
     * including any in-progress API calls and progress jobs.
     */
    fun shutdown() {
        serviceScope.cancel() // This will propagate cancellation to all children coroutines
        // Clear maps, though cancellation should make Deferreds inactive/completed
        inProgressExactRequests.clear()
        modelExecutionLocks.clear()
    }
}
