package dima.ai.singlePromptApp

import GlobalStyling
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.images.CachedInternetImage

@Composable
internal fun LlmSinglePromptAppUiModelResponseHeader(
    model: LlmModel,
    isRetrying: Boolean,
    endpoint: String?,
    elapsedTime: String?,
    showTiming: Boolean
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(bottom = 12.dp)
    ) {
        CachedInternetImage(
            // a sad emoji if not set
            imageUrl = model.logoImageUrl ?: "https://static.thenounproject.com/png/82078-200.png",
            cacheDir = LlmSinglePrompt.cacheDirectory,
            loadImageOnMainThread = true,
            logCacheDir = LlmSinglePrompt.RELATIVE_CACHE_DIRECTORY,
            modifier = Modifier
                .size(20.dp)
        )
        Text(
            model.displayName,
            color = GlobalStyling.getBoldTextColor(),
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .padding(horizontal = 8.dp)
        )
        if (isRetrying) {
            Text(
                "Retrying...",
                color = GlobalStyling.getFuchsiaColor(),
                modifier = Modifier
                    .padding(start = 12.dp)
            )
        }
        if (showTiming && elapsedTime != null) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                modifier = Modifier.padding(horizontal = 6.dp)
            ) {
                val fontSize = 12.sp
                val gray = GlobalStyling.getGrayColor()
                Text(
                    endpoint!!,
                    color = gray,
                    fontSize = fontSize
                )
                Text(
                    elapsedTime,
                    color = gray,
                    fontSize = fontSize
                )
            }
        }
    }
}
