package dima.ai.singlePromptApp

import dima.apps.AppType
import dima.events.emitEasy
import dima.globalState.GlobalState
import kotlinx.coroutines.runBlocking
import setCurrentApp

fun launchAiSinglePromptApp(prompt: String) {
    if (GlobalState.app == AppType.AiSinglePrompt) {
        runBlocking {
            ChangeAiSinglePromptAppEvent(prompt).emitEasy()
        }
    } else {
        LlmSinglePrompt.rememberQuery(prompt)
        setCurrentApp(AppType.AiSinglePrompt)
    }
}
