package dima.ai.singlePromptApp

import Globals.density
import androidx.compose.foundation.HorizontalScrollbar
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.unit.dp
import dima.globalState.GlobalState
import dima.utils.ScrollVelocity
import dima.utils.scrollbarStyleThemed

@Composable
internal fun AiSinglePromptAppUiRowCards(
    listState: ScrollState,
    selectedModel: LlmModel?,
    selectedCodeBlock: Int?,
    onCardYScrollStateEntryChange: (model: LlmModel, scrollVelocity: ScrollVelocity) -> Unit,
    onScrollXStateEntryChange: (model: LlmModel, xState: XScrollState) -> Unit,
    onCodeBlockSizeEntryChange: (model: LlmModel, size: Int) -> Unit,
    onCardStateChange: (modelName: LlmModel, state: CardState) -> Unit,
    prompt: String,
    onSelectedModelChange: (LlmModel) -> Unit
) {
    Box {
        Row(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .horizontalScroll(state = listState)
                .padding(start = 12.dp, bottom = 24.dp, end = 12.dp)
        ) {
            getModelsFromProviders().forEach { model ->
                AiSinglePromptAppUiModelResponseCard(
                    prompt = prompt,
                    model = model,
                    isSelected = model == selectedModel,
                    selectedCodeBlock = selectedCodeBlock,
                    width = with(density) {
                        GlobalState.mainWindow.widthInDp.toDp()
                    },
                    onScrollStateCreated = { modelName, scrollState ->
                        onCardYScrollStateEntryChange(modelName, ScrollVelocity(scrollState))
                    },
                    onCodeBlockSizeUpdate = { modelName, size ->
                        onCodeBlockSizeEntryChange(modelName, size)
                    },
                    onGloballyPositioned = { layoutCoordinates ->
                        val width = layoutCoordinates.size.width
                        val newXState = XScrollState(layoutCoordinates.positionInParent().x, width)
                        onScrollXStateEntryChange(model, newXState)
                    },
                    onStateChange = onCardStateChange,
                    onMouseClickToSelect = {
                        onSelectedModelChange(model)
                    })
            }
        }
        HorizontalScrollbar(
            style = scrollbarStyleThemed().copy(thickness = 6.dp),
            adapter = rememberScrollbarAdapter(scrollState = listState),
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(horizontal = 12.dp),
        )
    }
}
