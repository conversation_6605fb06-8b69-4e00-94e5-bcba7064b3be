package dima.ai.singlePromptApp

import dima.apps.notifications.showErrorNotification
import dima.dialogs.completion.openCompletionDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.globalState.GlobalState

internal fun changePrompt(onPromptChange: (String) -> Unit) {
    openTextInputDialog("Change prompt", minLines = 15, dialogWidth = 0.7f) {
        val trimmed = it.trim()
        if (trimmed.isEmpty()) {
            return@openTextInputDialog TextInputDialogConfirmAction.Close
        }
        onPromptChange(trimmed)
        return@openTextInputDialog TextInputDialogConfirmAction.Close
    }
}

internal fun switchToExistingPrompt(onPromptChange: (String) -> Unit) {
    val lastQueries = LlmSinglePrompt.getLastQueries().reversed()
    openCompletionDialog("Use old prompt", lastQueries, maxLinesPerCandidate = 3) {
        onPromptChange(it.text)
    }
}

internal fun retryModel(model: LlmModel?) {
    if (model == null) {
        showErrorNotification("No model selected to retry!")
    } else {
        cardEvent = ChatGptCardEvent(ChatGptCardEventType.Retry, model)
    }
}

internal fun setCodeModeLanguage() {
    val disable = "[disable]"
    val withoutLanguage = "[``` without language]"
    openCompletionDialog(
        "Enter language for response or pick action",
        listOf(disable, withoutLanguage),
        matchRequired = false
    ) {
        val new = when (it.text) {
            disable -> null
            withoutLanguage -> ""
            else -> it.text
        }
        GlobalState.llmSinglePromptApp = GlobalState.llmSinglePromptApp.copy(codeModeLanguage = new)
    }
}