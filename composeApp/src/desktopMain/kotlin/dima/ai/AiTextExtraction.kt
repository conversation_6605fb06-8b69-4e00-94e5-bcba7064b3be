package dima.ai

import dima.ai.text.AiText
import dima.apps.notifications.LoadingNotification
import dima.os.copyToClipboard
import dima.utils.StringResult

object AiTextExtraction {

    /**
     * Extracts verification codes from any text, like emails.
     */
    suspend fun extractVerificationCodeAndCopyToClipboard(
        plainText: String,
        notification: LoadingNotification
    ) {
        val response = AiText.generate(
            """Extract the verification code from this email content. Respond ONLY with the code itself, nothing else:

$plainText""",
            listOf(AiText.Provider.GroqLlama_3_3_70B_Versatile, AiText.Provider.Gpt4free_Any_Model)
        )
        when (response) {
            is StringResult.Error -> notification.toError("Error extracting verification code", response.error)
            is StringResult.Success -> {
                copyToClipboard(response.value, showNotification = false)
                notification.toInfo("Copied verification code to clipboard", response.value)
            }
        }
    }

}