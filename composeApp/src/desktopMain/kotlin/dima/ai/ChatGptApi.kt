package dima.ai

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

object ChatGptApi {

    @Serializable
    data class Message(val content: String)

    @Serializable
    data class Choice(val message: Message)

    @Serializable
    data class Response(val choices: List<Choice>)

    @Serializable
    data class RequestMessage(
        /** user, assistant, system */
        val role: String,
        val content: String
    )

    @Serializable
    data class RequestBody(
        val model: String,
        val messages: List<RequestMessage>,
        val temperature: Int = 0,
        val stream: Boolean = false,
        @SerialName("max_tokens")
        val maxTokens: Int? = null
    )

    fun prepareRequestBody(
        userContent: String,
        model: String? = null,
        systemContent: String? = null,
        maxTokens: Int? = null,
        stream: Boolean = false
    ): RequestBody {
        return RequestBody(
            model = model ?: "foo",
            maxTokens = maxTokens,
            stream = stream,
            messages = buildList {
                if (systemContent != null) {
                    add(
                        RequestMessage(
                            role = "system",
                            content = systemContent
                        )
                    )
                }
                add(
                    RequestMessage(
                        role = "user",
                        content = userContent
                    )
                )
            }
        )
    }

}
