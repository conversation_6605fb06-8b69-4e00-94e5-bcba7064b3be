package dima.ai.text

import dima.ai.ChatGptApi
import dima.apps.email.database.EmailTable.plainText
import dima.apps.networkActivity.postLogged
import dima.settings
import dima.utils.JsonIgnoreUnknown
import dima.utils.StringResult
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.setJsonBody
import io.ktor.client.request.*

object AiText {

    @Suppress("EnumEntryName")
    enum class Provider {
        GroqLlama_3_3_70B_Versatile,

        /**
         * Uses [Gpt4freeText.models] in order.
         */
        Gpt4free_Any_Model,
    }

    suspend fun generate(
        userContent: String,
        providers: List<Provider> = listOf(Provider.GroqLlama_3_3_70B_Versatile),
        maxTokens: Int? = null,
        systemContent: String? = null
    ): StringResult {
        val requestBody =
            ChatGptApi.prepareRequestBody(
                userContent = userContent,
                systemContent = systemContent,
                maxTokens = maxTokens
            )
        return generate(
            request = requestBody,
            providers = providers
        )
    }

    /**
     * @param request the model will be replaced, depending on the [providers]
     */
    suspend fun generate(
        request: ChatGptApi.RequestBody,
        providers: List<Provider> = listOf(Provider.GroqLlama_3_3_70B_Versatile),
    ): StringResult {
        if (settings.llmApiKeys.groq == null) {
            return StringResult.Error("Groq API key is not set in settings")
        }
        if (providers.isEmpty()) {
            return StringResult.Error("No providers passed")
        }
        var lastError: StringResult.Error? = null
        providers.forEach { provider ->
            when (provider) {
                Provider.GroqLlama_3_3_70B_Versatile -> {
                    val client = createHttpClientWithLocalhostProxy()
                    client.use {
                        val response = it.postLogged("https://api.groq.com/openai/v1/chat/completions") {
                            headers {
                                bearerAuth(settings.llmApiKeys.groq)
                            }
                            setJsonBody(request.copy(model = "llama-3.3-70b-versatile"))
                        }
                        if (response.statusCode != 200) {
                            lastError =
                                StringResult.Error("Failed to get a response from Groq (${response.statusCode}):\n${response.body}")
                            return@forEach
                        }
                        val parsed = JsonIgnoreUnknown.decodeFromString<ChatGptApi.Response>(response.body)
                        val choice = parsed.choices.firstOrNull()
                        if (choice == null) {
                            lastError = StringResult.Error("No choices in response")
                            return@forEach
                        }
                        return StringResult.Success(choice.message.content)
                    }
                }

                Provider.Gpt4free_Any_Model -> {
                    return Gpt4freeText.downloadResponseByAnyModel(
                        ChatGptApi.prepareRequestBody(
                            """Extract the verification code from this email content. Respond ONLY with the code itself, nothing else:

        $plainText"""
                        )
                    )
                }
            }
        }
        return StringResult.Error("All providers failed. Last error: ${lastError?.error}")
    }

}