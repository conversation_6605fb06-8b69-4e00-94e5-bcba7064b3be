package dima.ai.text

import dima.ai.ChatGptApi.RequestBody
import dima.ai.ChatGptApi.Response
import dima.apps.networkActivity.postLogged
import dima.settings
import dima.utils.JsonIgnoreUnknown
import dima.utils.StringResult
import dima.utils.setJsonBody
import io.ktor.client.*

object Gpt4freeText {

    // same as in git-committer:
    // https://github.com/Dima-369/ai-git-committer
    val models = listOf("gemini-1.5-pro", "sonar,", "deepseek-v3", "claude-3-haiku", "gpt-4o-mini")

    /**
     * @param request the model is patched in
     * @return the response text or null if there was an error
     */
    suspend fun downloadResponseByAnyModel(
        request: RequestBody,
    ): StringResult {
        if (settings.gpt4freeBaseApiUrl == null) {
            return StringResult.Error("Gpt4free base API URL is not set in settings")
        }
        for (model in models) {
            val client = HttpClient()
            try {
                val response = client.postLogged("${settings.gpt4freeBaseApiUrl}chat/completions") {
                    setJsonBody(request.copy(model = model))
                }
                if (response.statusCode == 200) {
                    val decoded = JsonIgnoreUnknown.decodeFromString<Response>(response.body)
                    if (decoded.choices.isNotEmpty()) {
                        return StringResult.Success(decoded.choices.first().message.content)
                    }
                }
                // If we get here, either status wasn't 200 or choices was empty - try next model
            } catch (_: Exception) {
                // On exception, try next model
            } finally {
                client.close()
            }
        }
        return StringResult.Error("All models failed to generate a response")
    }

}