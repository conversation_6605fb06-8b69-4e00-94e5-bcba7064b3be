package dima.ai.standaloneChatDialog

import GlobalStyling
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mikepenz.markdown.m2.markdownColor
import dima.ai.singlePromptApp.TreeSitterCodeFenceText
import dima.color.TailwindCssColors
import dima.dialogs.DialogBottomKey
import dima.dialogs.DialogBottomKeyInfo
import dima.markdownRendererLibraryPatches.MyMarkdown
import dima.markdownRendererLibraryPatches.myMarkdownComponents
import dima.os.copyToClipboard
import dima.utils.TextFieldWithCandidateCountWithoutAnimation
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleThemed
import isTextFieldFocused

@Composable
internal fun AiStandaloneDialogUi(
    dialogFocusRequester: FocusRequester,
    imageBitmap: ImageBitmap?,
    messagesScrollState: ScrollState,
    messages: List<AiChatMessage>,
    isInMessageSelectionMode: Boolean,
    changeIsInMessageSelectionMode: (Boolean) -> Unit,
    selectedMessageIndex: Int?,
    changeSelectedMessageIndex: (Int?) -> Unit,
    isLoading: Boolean,
    inputValue: TextFieldValue,
    onInputValueChange: (TextFieldValue) -> Unit,
    textFieldFocusRequester: FocusRequester
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .focusRequester(dialogFocusRequester)
            .focusable()
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row {
                Text(
                    "pixtral-large-2411 via LLM7",
                    fontSize = 18.sp,
                    color = GlobalStyling.getTextColor(),
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 12.dp, bottom = 12.dp)
                )
            }
            Row(modifier = Modifier.weight(1f)) {
                if (imageBitmap != null) {
                    Row(modifier = Modifier.weight(1f)) {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier.fillMaxSize(),
                        ) {
                            Image(bitmap = imageBitmap, contentDescription = null)
                        }
                    }
                }
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Box(
                        contentAlignment = Alignment.TopCenter,
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier
                                .widthIn(max = 600.dp)
                                .fillMaxSize()
                                .padding(start = 8.dp, end = 12.dp)
                                .verticalScroll(messagesScrollState)
                        ) {
                            messages.forEachIndexed { index, msg ->
                                val isCurrentMessageSelected = isInMessageSelectionMode && selectedMessageIndex == index
                                Box(
                                    modifier = Modifier
                                        .then(
                                            if (isCurrentMessageSelected) {
                                                Modifier.border(
                                                    width = 2.dp,
                                                    color = GlobalStyling.getSelectedBorderColor(),
                                                    shape = GlobalStyling.smallRoundedCorners
                                                )
                                            } else {
                                                Modifier
                                            }
                                        )
                                        .padding(horizontal = 4.dp)
                                ) {
                                    when (msg.type) {
                                        MessageType.User -> {
                                            Row(
                                                Modifier
                                                    .fillMaxWidth()
                                                    .padding(vertical = 4.dp),
                                                horizontalArrangement = Arrangement.End
                                            ) {
                                                Box(
                                                    modifier = Modifier
                                                        .background(
                                                            color = TailwindCssColors.gray200,
                                                            shape = GlobalStyling.smallRoundedCorners
                                                        )
                                                        .padding(horizontal = 16.dp, vertical = 10.dp)
                                                ) {
                                                    Text(msg.content)
                                                }
                                            }
                                        }

                                        MessageType.Assistant -> {
                                            Row(
                                                Modifier
                                                    .fillMaxWidth()
                                                    .padding(vertical = 4.dp)
                                                    .clickableWithoutBackgroundRipple {
                                                        copyToClipboard(msg.content)
                                                    },
                                                verticalAlignment = Alignment.Top,
                                                horizontalArrangement = Arrangement.Start
                                            ) {
                                                MyMarkdown(
                                                    content = msg.content,
                                                    colors = markdownColor(text = GlobalStyling.getTextColor()),
                                                    components = myMarkdownComponents(
                                                        codeFence = { model ->
                                                            TreeSitterCodeFenceText(
                                                                content = model.content,
                                                                node = model.node,
                                                                onClick = { code ->
                                                                    copyToClipboard(
                                                                        code.trim(),
                                                                        notificationTitleSubstring = "code"
                                                                    )
                                                                }
                                                            )
                                                        }
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            if (isLoading) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp),
                                    horizontalArrangement = Arrangement.Start,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(26.dp),
                                        strokeWidth = 3.dp,
                                        color = GlobalStyling.getTextColor()
                                    )
                                }
                            }
                        }
                        VerticalScrollbar(
                            style = scrollbarStyleThemed(),
                            adapter = rememberScrollbarAdapter(messagesScrollState),
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .fillMaxHeight(),
                        )
                    }
                }
            }
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = 12.dp,
                        end = 12.dp,
                        bottom = 8.dp,
                        top = 8.dp
                    )
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .widthIn(max = 600.dp)
                ) {
                    TextFieldWithCandidateCountWithoutAnimation(
                        value = inputValue,
                        onValueChange = { onInputValueChange(it) },
                        topLabel = "Type your message",
                        singleLine = false,
                        focusRequester = textFieldFocusRequester,
                        modifier = Modifier
                            .fillMaxWidth()
                            .onFocusChanged { focusState ->
                                // Assuming isTextFieldFocused is a global var, it's updated here.
                                // If it's a local var, pass the lambda `(Boolean) -> Unit`
                                isTextFieldFocused = focusState.isFocused
                                if (focusState.isFocused && isInMessageSelectionMode) {
                                    // If text field gains focus while we thought we were in selection mode, exit selection mode.
                                    changeIsInMessageSelectionMode(false)
                                    changeSelectedMessageIndex(null)
                                }
                            }
                            .heightIn(max = 200.dp)
                    )
                }
            }
            DialogBottomKeyInfo(
                keys = listOf(
                    DialogBottomKey("⇥", "Select messages"),
                    DialogBottomKey("c", "Scroll up", isCtrl = true),
                    DialogBottomKey("t", "Scroll down", isCtrl = true),
                ),
                modifier = Modifier.padding(start = 12.dp, end = 12.dp, bottom = 12.dp)
            )
        }
    }
}
