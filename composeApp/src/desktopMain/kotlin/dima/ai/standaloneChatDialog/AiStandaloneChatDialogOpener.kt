package dima.ai.standaloneChatDialog

import dima.dialogs.DialogIdentifier
import dima.dialogs.openDialog
import java.io.File

internal data class AiStandaloneDialogParams(
    val initialPrompt: String = "",
    val imageFile: File? = null,
    val copyFirstResponseToClipboard: Boolean = false
)

internal enum class MessageType { User, Assistant }

internal data class AiChatMessage(
    val type: MessageType,
    val content: String
)

fun openAiStandaloneDialog(
    initialPrompt: String = "",
    imageFile: File? = null,
    copyFirstResponseToClipboard: Boolean = false
) {
    openDialog(
        identifier = DialogIdentifier.AiTextImageDialog,
        data = AiStandaloneDialogParams(
            initialPrompt = initialPrompt,
            imageFile = imageFile,
            copyFirstResponseToClipboard = copyFirstResponseToClipboard
        ),
        composable = { id: Long, data: Any? -> AiStandaloneChatDialog(id, data) }
    )
}