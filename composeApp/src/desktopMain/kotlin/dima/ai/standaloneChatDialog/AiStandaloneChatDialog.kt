package dima.ai.standaloneChatDialog

import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.toComposeImageBitmap
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import dima.ai.image.AiImage
import dima.ai.text.AiText
import dima.apps.notifications.showNotification
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dialogs
import dima.dialogs.help.MiniHelpDialogKey
import dima.dialogs.help.openMiniHelpDialog
import dima.os.copyToClipboard
import dima.utils.RecentHistory
import dima.utils.ScrollVelocity
import dima.utils.StringResult
import dima.utils.isQuestionMark
import isTextFieldFocused
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.imageio.ImageIO

@Composable
fun AiStandaloneChatDialog(id: Long, data: Any?) {
    val params = data as? AiStandaloneDialogParams ?: AiStandaloneDialogParams()
    var messages by remember { mutableStateOf(listOf<AiChatMessage>()) }
    var inputValue by remember {
        mutableStateOf(
            TextFieldValue(
                text = params.initialPrompt,
                selection = TextRange(params.initialPrompt.length, params.initialPrompt.length)
            )
        )
    }
    val textFieldFocusRequester = remember { FocusRequester() }
    val dialogFocusRequester = remember { FocusRequester() } // For overall dialog focus management

    var isInMessageSelectionMode by remember { mutableStateOf(false) }
    var selectedMessageIndex by remember { mutableStateOf<Int?>(null) }

    val coroutineScope = rememberCoroutineScope()
    var currentJob by remember { mutableStateOf<Job?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var firstAiResponseCopiedInSession by remember { mutableStateOf(false) }

    val imageBitmap: ImageBitmap? = remember(params.imageFile) {
        params.imageFile?.let {
            try {
                ImageIO.read(it).toComposeImageBitmap()
            } catch (_: Exception) {
                null
            }
        }
    }

    val messagesScrollState = rememberScrollState()
    val scrollVelocity = remember { ScrollVelocity(messagesScrollState) }

    fun sendMessage() {
        if (isLoading) return
        val userInput = inputValue.text
        if (userInput.isBlank() && params.imageFile == null) {
            isLoading = false
            return
        }
        RecentHistory.rememberText(userInput)
        messages = messages + AiChatMessage(MessageType.User, userInput)
        isLoading = true
        currentJob?.cancel()
        coroutineScope.launch {
            scrollVelocity.scrollToBottom()
        }
        currentJob = coroutineScope.launch {
            val result = if (params.imageFile == null) {
                AiText.generate(userInput)
            } else {
                AiImage.askAboutImage_inner(userInput, params.imageFile)
            }
            val aiText = when (result) {
                is StringResult.Success -> result.value
                is StringResult.Error -> "Error: ${result.error}"
            }
            RecentHistory.rememberText(aiText)
            isLoading = false
            messages = messages + AiChatMessage(MessageType.Assistant, aiText)

            if (params.copyFirstResponseToClipboard && !firstAiResponseCopiedInSession) {
                copyToClipboard(aiText, showNotification = false)
                showNotification("Copied AI response to clipboard", durationMillis = 1000)
                firstAiResponseCopiedInSession = true
            }
            // After sending, if in selection mode, update selection or exit
            if (isInMessageSelectionMode) {
                selectedMessageIndex = messages.size - 1
            }
        }
        inputValue = TextFieldValue("", selection = TextRange(0, 0))
    }

    DialogOverlay(
        dialogId = id,
        dialogPadding = null,
        widthFraction = if (imageBitmap == null) 0.7f else 0.9f,
        showHelpIcon = true,
        alpha = 1f,
        onPreviewKeyEvent = { event ->
            if (event.type == KeyEventType.KeyDown) {
                if (isInMessageSelectionMode) {
                    if (event.isQuestionMark()) {
                        openMiniHelpDialog(
                            "AI Chat Dialog", keys = listOf(
                                MiniHelpDialogKey(listOf(Key.Tab), "Switch to message selection mode"),
                                MiniHelpDialogKey(listOf(Key.C), "Move up"),
                                MiniHelpDialogKey(listOf(Key.T), "Move down"),
                                MiniHelpDialogKey(listOf(Key.J), "Copy selected message"),
                                MiniHelpDialogKey(listOf(Key.Escape), "Exit message selection mode or exit dialog"),
                            )
                        )
                        return@DialogOverlay true
                    }
                    when (event.key) {
                        Key.Escape -> {
                            isInMessageSelectionMode = false
                            selectedMessageIndex = null
                            textFieldFocusRequester.requestFocus()
                            return@DialogOverlay true
                        }

                        Key.C -> {
                            selectedMessageIndex?.let {
                                if (it > 0) selectedMessageIndex = it - 1
                            }
                            return@DialogOverlay true
                        }

                        Key.T -> {
                            selectedMessageIndex?.let {
                                if (it < messages.size - 1) selectedMessageIndex = it + 1
                            }
                            return@DialogOverlay true
                        }

                        Key.J -> {
                            selectedMessageIndex?.let { index ->
                                if (index in messages.indices) {
                                    copyToClipboard(messages[index].content)
                                    showNotification("Copied message", durationMillis = 1000)
                                }
                            }
                            return@DialogOverlay true
                        }
                    }
                } else { // Not in message selection mode
                    when {
                        event.key == Key.Tab && isTextFieldFocused -> {
                            isInMessageSelectionMode = true
                            selectedMessageIndex = if (messages.isNotEmpty()) messages.size - 1 else null
                            dialogFocusRequester.requestFocus() // Shift focus away from TextField
                            return@DialogOverlay true
                        }

                        event.key == Key.Enter && !event.isShiftPressed -> {
                            if (isLoading) return@DialogOverlay true
                            sendMessage()
                            return@DialogOverlay true
                        }

                        event.key == Key.Enter && event.isShiftPressed -> {
                            inputValue = inputValue.copy(
                                text = inputValue.text + "\n",
                                selection = TextRange(inputValue.text.length + 1, inputValue.text.length + 1)
                            )
                            return@DialogOverlay true
                        }

                        event.key == Key.Escape -> {
                            closeDialogWithId(id)
                            return@DialogOverlay true
                        }

                        event.isCtrlPressed && event.key == Key.C -> {
                            scrollVelocity.onScrollUpKeyPressed()
                            return@DialogOverlay true
                        }

                        event.isCtrlPressed && event.key == Key.T -> {
                            scrollVelocity.onScrollDownKeyPressed()
                            return@DialogOverlay true
                        }
                    }
                }
            } else if (event.type == KeyEventType.KeyUp) {
                if (isInMessageSelectionMode) {
                    // No specific KeyUp handling for selection mode for now
                } else {
                    if (event.isCtrlPressed && (event.key == Key.C || event.key == Key.T)) {
                        scrollVelocity.onKeyReleased()
                        return@DialogOverlay true
                    }
                }
            }
            false
        }
    ) {
        AiStandaloneDialogUi(
            dialogFocusRequester = dialogFocusRequester,
            imageBitmap = imageBitmap,
            messagesScrollState = messagesScrollState,
            messages = messages,
            isInMessageSelectionMode = isInMessageSelectionMode,
            changeIsInMessageSelectionMode = { isInMessageSelectionMode = it },
            selectedMessageIndex = selectedMessageIndex,
            changeSelectedMessageIndex = { selectedMessageIndex = it },
            isLoading = isLoading,
            inputValue = inputValue,
            onInputValueChange = { inputValue = it },
            textFieldFocusRequester = textFieldFocusRequester
        )
    }

    LaunchedEffect(Unit) {
        if (params.initialPrompt.isNotBlank()) {
            sendMessage()
        }
        textFieldFocusRequester.requestFocus()
        scrollVelocity.loopForeverAndTick()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.lastOrNull()?.id == id && !isInMessageSelectionMode) {
            textFieldFocusRequester.requestFocus()
        } else if (dialogs.lastOrNull()?.id == id && isInMessageSelectionMode) {
            dialogFocusRequester.requestFocus()
        }
    }
    LaunchedEffect(messages.size, isLoading, selectedMessageIndex, isInMessageSelectionMode) {
        if (messages.isNotEmpty() || isLoading) {
            val targetIndex = if (isInMessageSelectionMode && selectedMessageIndex != null) {
                selectedMessageIndex!!
            } else if (isLoading) {
                messages.size // Scroll to loading indicator if it's the last item
            } else {
                messages.size - 1 // Scroll to the last actual message
            }
            if (targetIndex >= 0) {
                messagesScrollState.animateScrollTo(messagesScrollState.maxValue) // More reliable for bottom
            }
        }
    }
}
