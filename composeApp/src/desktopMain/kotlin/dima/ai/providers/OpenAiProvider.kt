package dima.ai.providers

import dima.ai.ChatGptApi
import dima.ai.singlePromptApp.LlmModel
import dima.ai.singlePromptApp.LlmSinglePrompt.insert
import dima.ai.singlePromptApp.LlmSinglePrompt.insertError
import dima.ai.singlePromptApp.LlmSinglePromptAppProvider
import dima.apps.networkActivity.postLogged
import dima.utils.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import kotlinx.coroutines.delay
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException

@Serializable
internal data class RequestModelApi(
    val model: String,
    val messages: List<RequestModelApiMessage>,
    @SerialName("max_tokens")
    val maxTokens: Int? = null
)

@Serializable
internal data class RequestModelApiMessage(
    val role: String,
    val content: String
)

object OpenAiProvider {

    private fun buildRequest(model: LlmModel, prompt: String, maxTokens: Int? = null): RequestModelApi {
        if (maxTokens == null) {
            return RequestModelApi(
                model = model.apiName,
                messages = listOf(
                    RequestModelApiMessage(
                        role = "user",
                        content = prompt
                    )
                )
            )
        }
        return RequestModelApi(
            model = model.apiName,
            maxTokens = maxTokens,
            messages = listOf(
                RequestModelApiMessage(
                    role = "user",
                    content = prompt
                )
            )
        )
    }

    /**
     * Updates the database on error and success.
     */
    internal suspend fun queryApi(
        model: LlmModel,
        prompt: String,
        maxTokens: Int?,
        endpoint: LlmSinglePromptAppProvider.OpenAi,
        retriedOnFirstError: Boolean = false
    ): StringResult {
        val url = endpoint.urlWithoutTrailingSlash + "/chat/completions"
        createHttpClientWithLocalhostProxy().use {
            val apiResponse = it.postLogged(url) {
                timeout {
                    requestTimeoutMillis = 60_000
                    connectTimeoutMillis = 60_000
                }
                setJsonBody(buildRequest(model, prompt, maxTokens))
                bearerAuth(endpoint.apiKey)
            }
            /*
                        } catch (e: IOException) {
                            val error = e.message ?: "IOException without message"
                            insertError(prompt, model, error, maxTokens)
                            return@runBlocking ChatGptApiResult(error = error)
                        }
            */
            val body = apiResponse.body
            // this is only for zukijourney
            if (!retriedOnFirstError && apiResponse.statusCode == 429 && body.contains("Rate limit exceeded: 2 per 1 second")) {
                delay(timeMillis = 2000)
                return queryApi(model, prompt, maxTokens, endpoint, retriedOnFirstError = true)
            }
            if (apiResponse.statusCode == 200) {
                if (body.trim().isEmpty()) {
                    val error = "200 status code but empty response"
                    insertError(prompt, model, error, maxTokens)
                    return StringResult.Error(error)
                }
                val decoded = try {
                    JsonIgnoreUnknown.decodeFromString<ChatGptApi.Response>(body)
                } catch (e: SerializationException) {
                    val error = "Failed to decode response: ${e.message}"
                    insertError(prompt, model, error, maxTokens)
                    return StringResult.Error(error)
                }
                val content = decoded.choices.first().message.content
                if (content.trim().isEmpty()) {
                    val error = "200 status code with valid response format, but empty message content"
                    insertError(prompt, model, error, maxTokens)
                    return StringResult.Error(error)
                } else {
                    insert(prompt, model, content, maxTokens)
                    return StringResult.Success(content)
                }
            } else {
                val error = "Failed with ${apiResponse.statusCode} status code\n\n${body.prettyPrintJson()}"
                insertError(prompt, model, error, maxTokens)
                return StringResult.Error(error)
            }
        }
    }

}

