package dima.ai.providers

import dima.ai.singlePromptApp.LlmModel
import dima.ai.singlePromptApp.LlmSinglePrompt
import dima.ai.singlePromptApp.LlmSinglePromptAppProvider
import dima.apps.networkActivity.postLogged
import dima.utils.JsonIgnoreUnknown
import dima.utils.StringResult
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.setJsonBody
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException

@Serializable
private data class CohereRequestMessage(val role: String, val content: String)

@Serializable
private data class CohereRequest(val model: String, val messages: List<CohereRequestMessage>)

@Serializable
private data class CohereContentItem(val text: String)

@Serializable
private data class CohereResponseMessage(val content: List<CohereContentItem>)

@Serializable
private data class CohereResponse(val message: CohereResponseMessage)

object CohereProvider {

    private fun buildRequest(model: LlmModel, prompt: String): CohereRequest {
        return CohereRequest(
            model = model.apiName,
            messages = listOf(
                CohereRequestMessage(role = "user", content = prompt)
            )
        )
    }

    internal suspend fun queryCohereCommandA(
        prompt: String,
        endpoint: LlmSinglePromptAppProvider.CohereCommandA
    ): StringResult {
        val model = LlmModel.CohereCommandA
        createHttpClientWithLocalhostProxy().use { client ->
            try {
                val response = client.postLogged("https://api.cohere.com/v2/chat") {
                    timeout {
                        requestTimeoutMillis = 60_000
                        connectTimeoutMillis = 60_000
                    }
                    bearerAuth(endpoint.apiKey)
                    setJsonBody(buildRequest(model, prompt))
                }

                if (response.statusCode != 200) {
                    val error = "Cohere API request failed with status ${response.statusCode}: ${response.body}"
                    LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                    return StringResult.Error(error)
                }

                val responseData = try {
                    JsonIgnoreUnknown.decodeFromString<CohereResponse>(response.body)
                } catch (e: SerializationException) {
                    val error = "Failed to parse JSON response from Cohere API: ${e.message}"
                    LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                    return StringResult.Error(error)
                }

                val text = responseData.message.content.firstOrNull()?.text
                if (text.isNullOrBlank()) {
                    val error = "No text content found in Cohere API response"
                    LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                    return StringResult.Error(error)
                }

                LlmSinglePrompt.insert(prompt, model, text, maxTokens = null)
                return StringResult.Success(text)

            } catch (e: Exception) {
                val error = "Request to Cohere API failed: ${e.message}"
                LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                return StringResult.Error(error)
            }
        }
    }
}
