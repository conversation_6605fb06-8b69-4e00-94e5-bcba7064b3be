package dima.ai.providers

import dima.ai.singlePromptApp.LlmModel
import dima.ai.singlePromptApp.LlmSinglePrompt
import dima.ai.singlePromptApp.LlmSinglePromptAppProvider
import dima.apps.networkActivity.postLogged
import dima.utils.JsonIgnoreUnknown
import dima.utils.StringResult
import dima.utils.createHttpClientWithLocalhostProxy
import dima.utils.setJsonBody
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException

@Serializable
private data class GeminiPart(val text: String)

@Serializable
private data class GeminiContent(val parts: List<GeminiPart>, val role: String = "user")

@Serializable
private data class GeminiRequest(val contents: List<GeminiContent>)

@Serializable
private data class GeminiResponse(val candidates: List<GeminiCandidate>)

@Serializable
private data class GeminiCandidate(val content: GeminiContent)

object GeminiProvider {

    private fun buildRequest(prompt: String): GeminiRequest {
        return GeminiRequest(
            contents = listOf(
                GeminiContent(
                    parts = listOf(GeminiPart(text = prompt))
                )
            )
        )
    }

    internal suspend fun queryFlash_2_0(prompt: String, endpoint: LlmSinglePromptAppProvider.GeminiFlash2Dot0): StringResult {
        val model = LlmModel.Gemini_2_0_Flash
        val url = "https://generativelanguage.googleapis.com/v1beta/models/${model.apiName}:generateContent"
        createHttpClientWithLocalhostProxy().use { client ->
            try {
                val response = client.postLogged(url) {
                    timeout {
                        requestTimeoutMillis = 60_000
                        connectTimeoutMillis = 60_000
                    }
                    header("x-goog-api-key", endpoint.apiKey)
                    setJsonBody(buildRequest(prompt))
                }

                if (response.statusCode != 200) {
                    val error =
                        "Google Gemini API request failed with status ${response.statusCode}: ${response.body}"
                    LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                    return StringResult.Error(error)
                }

                val responseData = try {
                    JsonIgnoreUnknown.decodeFromString<GeminiResponse>(response.body)
                } catch (e: SerializationException) {
                    val error = "Failed to parse JSON response from Gemini API: ${e.message}"
                    LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                    return StringResult.Error(error)
                }

                val text = responseData.candidates.firstOrNull()?.content?.parts?.firstOrNull()?.text
                if (text.isNullOrBlank()) {
                    val error = "No text content found in Gemini API response"
                    LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                    return StringResult.Error(error)
                }

                LlmSinglePrompt.insert(prompt, model, text, maxTokens = null)
                return StringResult.Success(text)

            } catch (e: Exception) {
                val error = "Request to Gemini API failed: ${e.message}"
                LlmSinglePrompt.insertError(prompt, model, error, maxTokens = null)
                return StringResult.Error(error)
            }
        }
    }
}
