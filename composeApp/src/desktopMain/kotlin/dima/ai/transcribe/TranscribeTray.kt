package dima.ai.transcribe

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.rememberTrayState
import dima.utils.TrayIcon

@Composable
fun ApplicationScope.TranscribeTray() {
    if (GlobalTranscribe.isRecording) {
        val painter = rememberVectorPainter(Icons.Default.Mic)
        val trayState = rememberTrayState()
        Tray(
            state = trayState,
            icon = TrayIcon(painter)
        )
    }
}