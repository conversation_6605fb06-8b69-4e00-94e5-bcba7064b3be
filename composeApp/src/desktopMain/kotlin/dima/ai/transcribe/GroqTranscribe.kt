package dima.ai.transcribe

import dima.apps.networkActivity.submitFormWithBinaryDataLogged
import dima.apps.notifications.showErrorNotification
import dima.settings
import dima.utils.Result
import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.http.*
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.io.File

@Serializable
data class TranscriptionResponse(
    val text: String,
    val task: String,
    val language: String,
    val duration: Double,
    val segments: List<Segment>,
    @SerialName("x_groq")
    val xGroq: XGroq
)

@Serializable
data class Segment(
    val id: Int,
    val seek: Int,
    val start: Double,
    val end: Double,
    val text: String,
    val tokens: List<Int>,
    val temperature: Double,
    @SerialName("avg_logprob")
    val avgLogprob: Double,
    @SerialName("compression_ratio")
    val compressionRatio: Double,
    @SerialName("no_speech_prob")
    val noSpeechProb: Double
)

@Serializable
data class XGroq(
    val id: String
)

object Transcribe {

    suspend fun viaGroqWhisperLargeV3(audioFile: File, language: String? = null): Result<TranscriptionResponse> {
        if (settings.llmApiKeys.groq == null) {
            showErrorNotification("Groq API key is not set in settings")
            return Result.Error("Groq API key is not set in settings")
        }
        val client = createHttpClientWithLocalhostProxy()
        client.use {
            val response = it.submitFormWithBinaryDataLogged(
                "https://api.groq.com/openai/v1/audio/transcriptions",
                formData {
                    append("model", "whisper-large-v3")
                    if (language != null) {
                        append("language", language)
                    }
                    append("response_format", "verbose_json")
                    append("file", audioFile.readBytes(), Headers.build {
                        append(HttpHeaders.ContentType, ContentType("audio", "*"))
                        append(HttpHeaders.ContentDisposition, "filename=\"${audioFile.name}\"")
                    })
                }
            ) {
                headers {
                    bearerAuth(settings.llmApiKeys.groq)
                    append(HttpHeaders.ContentType, "multipart/form-data")
                }
            }
            if (response.statusCode != 200) {
                return Result.Error("Failed to transcribe: ${response.body}")
            }
            return Result.Success(Json.decodeFromString<TranscriptionResponse>(response.body))
        }
    }

}
