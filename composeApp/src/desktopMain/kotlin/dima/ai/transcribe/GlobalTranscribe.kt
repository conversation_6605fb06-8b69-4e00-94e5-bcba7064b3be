package dima.ai.transcribe

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.globalState.GlobalState
import dima.os.Hammerspoon
import dima.os.copyToClipboard
import dima.utils.Result
import dima.utils.truncateWithEllipsis
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import java.time.Instant

object GlobalTranscribe {
    private var ffmpegProcess: Process? = null
    private var currentAudioFile: File? = null
    private const val MAX_RECORDING_DURATION_SECONDS_STR = "600" // 10 minutes

    init {
        Runtime.getRuntime().addShutdownHook(Thread {
            ffmpegProcess?.destroy()
        })
    }

    private var _isRecording by mutableStateOf(false)
    var isRecording: Boolean
        get() = _isRecording
        private set(value) {
            _isRecording = value
        }

    // State for transcribed texts, initialized from GlobalState
    // This ensures that any pre-existing texts in GlobalState are respected.
    private var _transcribedTexts by mutableStateOf(GlobalState.transcribe.transcribedTexts)
    val transcribedTexts: List<String>
        get() = _transcribedTexts

    fun deleteTranscribedTexts() {
        GlobalState.transcribe = GlobalState.transcribe.copy(transcribedTexts = emptyList())
        _transcribedTexts = emptyList()
    }

    fun toggle() {
        if (isRecording || ffmpegProcess?.isAlive == true) {
            stopRecording()
        } else {
            startRecording()
        }
    }

    private fun startRecording() {
        if (isRecording) {
            return
        }

        Hammerspoon.showAlert("Starting transcribe...")
        val downloadsDir = System.getProperty("user.home") + "/Downloads"
        val timestamp = Instant.now().epochSecond
        val audioFile = File("$downloadsDir/recording_$timestamp.flac")
        currentAudioFile = audioFile

        val command = arrayOf(
            "ffmpeg",
            "-y",              // Overwrite output file if it exists
            "-t", MAX_RECORDING_DURATION_SECONDS_STR,       // Record for max duration
            "-f", "avfoundation",
            "-i", ":1",        // Input device (":1" is usually the default microphone)
            "-ar", "16000",    // Sample rate
            audioFile.absolutePath
        )

        try {
            ffmpegProcess = ProcessBuilder(*command)
                .redirectErrorStream(true)
                .start()

            // Set recording state after process successfully starts
            if (ffmpegProcess?.isAlive == true) {
                isRecording = true
            } else {
                throw Exception("Failed to start recording process")
            }
        } catch (e: Exception) {
            showErrorNotification("Failed to start recording: ${e.message}")
            currentAudioFile = null
            _isRecording = false // Explicitly set back
            ffmpegProcess = null
        }
    }

    private fun stopRecording() {
        Hammerspoon.showAlert("Stopped global transcription", durationSeconds = 1)
        val audioFile = currentAudioFile
        ffmpegProcess?.destroy()
        ffmpegProcess = null
        _isRecording = false

        if (audioFile != null) {
            CoroutineScope(Dispatchers.IO).launch {
                delay(500)
                if (audioFile.exists() && audioFile.length() > 0) {
                    Hammerspoon.showAlert("Transcribing...", durationSeconds = 1)
                    transcribeAudio(audioFile)
                } else {
                    Hammerspoon.showAlert("Recording failed: No audio file was created or file is empty?")
                    showErrorNotification("Recording failed: No audio file was created or file is empty.")
                    audioFile.delete()
                }
            }
        }
        currentAudioFile = null
    }

    private suspend fun transcribeAudio(audioFile: File) {
        val notification = showLoadingNotification("Transcribing audio...")
        var fileToKeep: File? = null
        try {
            val response = Transcribe.viaGroqWhisperLargeV3(audioFile)
            when (response) {
                is Result.Error -> {
                    Hammerspoon.showAlert("Transcription failed: ${response.error}")
                    notification.toError("Transcription failed", response.error)
                }

                is Result.Success -> {
                    Hammerspoon.showAlert("Transcription completed: ${response.value.text.truncateWithEllipsis(100)}")
                    copyToClipboard(response.value.text, notificationTitleSubstring = "transcription")
                    val newText = response.value.text.trimStart()
                    val updatedGlobalStateTexts = GlobalState.transcribe.transcribedTexts + newText
                    GlobalState.transcribe = GlobalState.transcribe.copy(
                        transcribedTexts = updatedGlobalStateTexts
                    )
                    _transcribedTexts = updatedGlobalStateTexts
                    val newFileName = audioFile.nameWithoutExtension + "_transcribed." + audioFile.extension
                    val renamedFile = File(audioFile.parentFile, newFileName)
                    if (audioFile.renameTo(renamedFile)) {
                        fileToKeep = renamedFile
                    } else {
                        showErrorNotification("Failed to rename ${audioFile.name} to ${renamedFile.name}. Original file will be deleted.")
                    }
                    notification.dismiss()
                }
            }
        } finally {
            if (fileToKeep == null || fileToKeep.absolutePath != audioFile.absolutePath) {
                audioFile.delete()
            }
        }
    }
}