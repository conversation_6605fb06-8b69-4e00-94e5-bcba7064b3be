package dima.ai.transcribe

import dima.apps.notifications.showLoadingNotification
import dima.os.copyToClipboard
import dima.utils.Result
import dima.utils.truncateWithEllipsis
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

fun File.transcribeAudioToClipboard() {
    CoroutineScope(Dispatchers.IO).launch {
        val notification = showLoadingNotification("Starting transcription...", name)
        val response = Transcribe.viaGroqWhisperLargeV3(this@transcribeAudioToClipboard)
        when (response) {
            is Result.Success -> {
                copyToClipboard(response.value.text, showNotification = false)
                notification.toInfo(
                    "Transcription completed and copied to clipboard",
                    response.value.text.truncateWithEllipsis(100),
                )
            }

            is Result.Error -> {
                notification.toError("Transcription failed", response.error)
            }
        }
    }
}
