package dima.ai.transcribe.app

import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.input.key.Key
import dima.ai.transcribe.GlobalTranscribe
import dima.dialogs.confirmation.openConfirmationDialog
import dialogs
import dima.dialogs.help.appKeys
// GlobalState might still be needed if appKeys or other configurations depend on it,
// but for transcribedTexts, we now use GlobalTranscribe.
// import dima.globalState.GlobalState
import dima.utils.AppKey


@Composable
fun TranscribeApp() {
    val appFocusRequester = remember { FocusRequester() }
    // Observe transcribedTexts from GlobalTranscribe, which now holds Compose-aware state
    val transcribedTexts = GlobalTranscribe.transcribedTexts
    val scrollState = rememberScrollState()

    remember {
        appKeys = listOf(
            A<PERSON><PERSON>ey(Key.Enter, "Start or stop recording") {
                GlobalTranscribe.toggle()

            },
            <PERSON><PERSON><PERSON><PERSON>(Key.D, "Delete all transcribed text") {
                openConfirmationDialog("Delete all transcribed text?") {
                    GlobalTranscribe.deleteTranscribedTexts()
                }
            }
        )
    }
    Ui(
        appFocusRequester = appFocusRequester,
        scrollState = scrollState,
        transcribedTexts = transcribedTexts,
        toggleRecording = { GlobalTranscribe.toggle() }
    )
    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }
    // Optional: Scroll to the latest transcript when new text is added
    // The list is reversed in Ui, so scrolling to 0 shows the newest.
    LaunchedEffect(transcribedTexts.size) {
        if (transcribedTexts.isNotEmpty()) {
            scrollState.animateScrollTo(0)
        }
    }
}