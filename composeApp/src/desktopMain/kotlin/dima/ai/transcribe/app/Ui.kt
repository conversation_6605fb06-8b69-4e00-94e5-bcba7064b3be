package dima.ai.transcribe.app

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.ai.transcribe.GlobalTranscribe
import dima.color.TailwindCssColors
import dima.utils.DummyFocusable
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.handleAppMap

@Composable
internal fun Ui(
    appFocusRequester: FocusRequester,
    scrollState: ScrollState,
    transcribedTexts: List<String>,
    toggleRecording: () -> Unit,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .onPreviewKeyEvent {
                return@onPreviewKeyEvent it.handleAppMap()
            },
    ) {
        val isRecordingActive = GlobalTranscribe.isRecording
        DummyFocusable()
        Column {
            Text(
                "Audio Transcription using whisper-large-v3",
                fontSize = 22.sp,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
            )
            Text(
                "10 minutes limit",
                textAlign = TextAlign.Center,
                color = TailwindCssColors.gray600,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp)
            )
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(vertical = 30.dp)
                .background(
                    if (isRecordingActive) TailwindCssColors.orange600 else TailwindCssColors.blue600,
                    RoundedCornerShape(12.dp)
                )
                .clickableWithoutBackgroundRipple {
                    toggleRecording()
                }
                .padding(8.dp)
        ) {
            if (isRecordingActive) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.size(80.dp)
                ) {
                    CircularProgressIndicator(
                        color = TailwindCssColors.white,
                        modifier = Modifier.size(40.dp)
                    )
                }
            } else {
                Icon(
                    imageVector = Icons.Default.Mic,
                    contentDescription = "Mic",
                    tint = TailwindCssColors.white,
                    modifier = Modifier
                        .size(80.dp)
                )
            }
            Text(
                if (isRecordingActive) "Stop recording" else "Start recording",
                fontSize = 16.sp,
                color = TailwindCssColors.white,
                modifier = Modifier
                    .padding(8.dp)
            )
        }
        Box {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier
                    .verticalScroll(scrollState)
                    .fillMaxHeight()
            ) {
                transcribedTexts.reversed().forEach { text -> // Show newest first
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .widthIn(max = 600.dp)
                            .background(TailwindCssColors.gray100, RoundedCornerShape(8.dp))
                            .padding(8.dp)
                    ) {
                        Text(text, modifier = Modifier.fillMaxWidth())
                    }
                }
            }
            VerticalScrollbar(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .fillMaxHeight(),
                adapter = rememberScrollbarAdapter(scrollState = scrollState)
            )
        }
    }
}