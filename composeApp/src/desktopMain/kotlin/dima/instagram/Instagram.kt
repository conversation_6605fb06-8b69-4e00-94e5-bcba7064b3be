package dima.instagram

import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.NetworkActivity.updateFaked
import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.clipboard.readClipboard
import dima.dateTime.DateTimeUtils
import dima.feeds.FeedEntry
import dima.process.LoggedProcess
import dima.settings
import org.openqa.selenium.By
import org.openqa.selenium.WebDriver
import org.openqa.selenium.WebDriverException
import org.openqa.selenium.chrome.ChromeDriver
import org.openqa.selenium.chrome.ChromeOptions
import org.openqa.selenium.support.ui.WebDriverWait
import java.time.Duration

private data class InstagramEntry(val imageUrl: String, val link: String, val title: String)

object Instagram {

    /**
     * Launches a headless WebDriver and loads the last 9 posts (plus 3 pinned ones) per channel.
     */
    fun loadInstagramFeedEntries(notification: LoadingNotification?): List<FeedEntry> {
        if (settings.feed.instagram.isEmpty()) {
            return emptyList()
        }
        return settings.feed.instagram.mapNotNull { channel ->
            var driver: WebDriver? = null
            val log = NetworkActivity.addFakedEntry(
                "Download Instagram posts from ${channel.url}",
                FakedNetworkActivityStatus.InTransit,
                fullUrl = channel.url
            )
            try {
                val options = ChromeOptions()
                options.addArguments("user-data-dir=selenium", "--headless")
                driver = ChromeDriver(options)
                notification?.update(message = "${channel.title}: Loading...")
                driver.get(channel.url)
                var images = driver.findElements(By.tagName("img"))
                val wait = WebDriverWait(driver, Duration.ofSeconds(30))
                notification?.update(message = "${channel.title}: Loading all images...")
                wait.until {
                    images = driver.findElements(By.tagName("img"))
                    return@until images.size >= 30
                }
                notification?.update(message = "${channel.title}: Processing post images")
                val entries = images.mapNotNull {
                    val src = it.getDomAttribute("src")!!
                    if (src.startsWith("data:")) {
                        return@mapNotNull null
                    }
                    val title = it.getDomAttribute("alt")!!
                    if (title.endsWith("'s profile picture") || title.endsWith("'s highlight story picture")) {
                        return@mapNotNull null
                    }
                    val aElement = it.findElement(By.xpath("ancestor::a[1]"))
                    val href = "https://www.instagram.com" + aElement.getDomAttribute("href")!!
                    InstagramEntry(src, href, title)
                }
                log.updateFaked(FakedNetworkActivityStatus.Success)
                return entries.map {
                    FeedEntry(
                        title = it.title,
                        link = it.link,
                        imageUrl = it.imageUrl,
                        date = DateTimeUtils.nowAsIsoDateTime(),
                        feedTitle = channel.title
                    )
                }
            } catch (e: WebDriverException) {
                log.updateFaked(FakedNetworkActivityStatus.Error, responseBody = "WebDriverException: ${e.message}")
                // show notification only when there is internet
                if (notification != null && e.message == null ||
                    (e.message != null && !e.message!!.contains("ERR_INTERNET_DISCONNECTED"))
                ) {
                    showErrorNotification("Instagram WebDriver error", e.message ?: "Unknown error")
                }
                return@mapNotNull null
            } catch (e: Exception) {
                log.updateFaked(FakedNetworkActivityStatus.Error, responseBody = "Exception: ${e.message}")
                if (notification != null) {
                    showErrorNotification("Instagram WebDriver error", e.message ?: "Unknown error")
                }
                return@mapNotNull null
            } finally {
                try {
                    driver?.quit()
                } catch (_: Exception) {
                }
            }
        }
    }

    /**
     * Downloads an image from an Instagram URL in the clipboard.
     */
    fun downloadVideoOrImageFromUrlInClipboard() {
        if (settings.python3BinaryFile == null || settings.pythonInstagramDownloaderScriptFile == null) {
            showErrorNotification("Python3 binary and Instagram downloader script are not set")
            return
        }
        var clipboard = readClipboard()
        if (clipboard == null) {
            showErrorNotification("Clipboard is empty")
            return
        }
        clipboard = clipboard.trim()
        if (!clipboard.startsWith("http")) {
            showErrorNotification(
                "Invalid Instagram URL",
                "Clipboard does not contain a URL: $clipboard"
            )
            return
        }
        val notification = showLoadingNotification("Downloading from Instagram to home directory...", clipboard)
        try {
            LoggedProcess(
                command = settings.python3BinaryFile.absolutePath,
                args = listOf(
                    settings.pythonInstagramDownloaderScriptFile.absolutePath,
                    clipboard
                ),
                showErrorNotifications = false, // Custom handling in onFinish
                onFinish = { processResult ->
                    if (processResult.exitCode.value == 0) {
                        notification.toInfo(
                            "Downloaded from Instagram",
                            processResult.getOutput().trim()
                        )
                    } else {
                        notification.toError(
                            "Failed to download from Instagram",
                            processResult.getOutput()
                        )
                    }
                }
            ).startAsync()
        } catch (e: Exception) {
            notification.toError(
                "Failed to start download process from Instagram",
                e.message ?: "Unknown error"
            )
        }
    }
}
