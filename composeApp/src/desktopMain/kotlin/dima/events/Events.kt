package dima.events

import events
import io.sellmair.evas.Event
import io.sellmair.evas.collectEvents
import io.sellmair.evas.emit
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Added to quickly find implementations.
 */
interface DimaEvent : Event

/**
 * Collect events on [Dispatchers.IO], otherwise it locks up the main thread.
 */
inline fun <reified T : Event> collectEvent(crossinline block: (T) -> Unit) {
    CoroutineScope(Dispatchers.IO).launch {
        withContext(events) {
            collectEvents<T> {
                block(it)
            }
        }
    }
}

suspend fun Event.emitEasy() {
    withContext(events) {
        <EMAIL>()
    }
}

