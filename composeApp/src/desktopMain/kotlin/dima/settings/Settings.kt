package dima.settings

import dima.ai.singlePromptApp.LlmSinglePromptAppProvider
import dima.apps.email.models.EmailFolderName
import dima.apps.email.models.EmailFolderType
import dima.apps.email.models.EmailFolderTypeInfoPill
import dima.audio.Audio
import dima.feeds.FeedChannel
import dima.os.homeWithoutSlash
import dima.vero.VeroRootProject
import java.io.File

object Settings {

    data class All(
        val serverPort: Int = 5000,
        val kleinanzeigenBaseDirectory: File? = null,
        val vineDatabasePath: String = "${homeWithoutSlash}/Developer/amazon-vine-rust-cli/data.db",
        /**
         * For copying compile errors.
         */
        val kotlinEmacsGradlewPath: File? = null,
        val todoistToken: String = "TODO",
        val feed: Feed = Feed(),
        val obsidianPath: String = "${homeWithoutSlash}/Documents/Obsidian Vault",
        /**
         * A list of strings relative to the home directory like "Library/".
         */
        /**
         * Not [File], because the excluded directories here are relative to the home directory like "Library/".
         * The leading slash is also important.
         */
        val findFileDialogExcludeDirectoriesForHome: List<String> = emptyList(),
        val dired: Dired = Dired(),
        /**
         * The directory where I store my scanned letters and other papers.
         */
        val scannedDirectory: File? = null,
        /**
         * GitHub Personal Access Token for accessing the GitHub API.
         * Used by [dima.git.GitRepoSearcher] to search for repositories.
         */
        val githubRepoSearcherToken: String? = null,
        val githubRepoCreatorToken: String? = null,
        /**
         * GitLab Personal Access Token for accessing the GitLab API.
         * Used by GitRepoSearcher to search for repositories.
         */
        val gitlabToken: String? = null,
        val audio: AudioPlayer = AudioPlayer(),
        val cli: CliApplications = CliApplications(),
        /**
         * Set to null to disable.
         */
        val networkRequestLocalhostProxyPort: Int? = null,
        val email: Email = Email(),
        val googleCalendar: GoogleCalendar? = null,
        val vero: Vero = Vero(),
        val weather: Weather = Weather(),
        val llmApiKeys: LlmApiKeys = LlmApiKeys(),
        val llmSinglePromptAppProviders: List<LlmSinglePromptAppProvider> = emptyList(),
        val telegram: Telegram? = null,
        val addressesToCopy: List<String> = emptyList(),
        /**
         * Mainly added for my system-wide Python venv.
         * I hate Python, but some special packages only exist in the Python universe.
         */
        val python3BinaryFile: File? = null,
        val pythonInstagramDownloaderScriptFile: File? = null,
        /**
         * Like "http://localhost:1337/v1/"
         * Should end with /.
         */
        val gpt4freeBaseApiUrl: String? = null
    )

    /**
     * Settings for [dima.apps.email.EmailApp].
     */
    data class Email(
        val address: String = "<EMAIL>",
        val password: String = "TODO",
        val imapServer: String = "imap.web.de",
        val folderTypes: List<EmailFolderType> = emptyList(),
        /**
         * Note that [dima.apps.email.InfoPills] already defines a few pills, depending on email flags.
         *
         * Could be moved into [EmailFolderType] like [EmailFolderType.syncRegularly],
         * but it is complicated for [EmailFolderType.MultiServerFolder], so this is easier.
         */
        val infoPillsForServerNames: Map<EmailFolderName, EmailFolderTypeInfoPill> = emptyMap(),
        /**
         * If the email is from one of these senders, pressing Key.Y will mark or unmark the entire thread,
         * and move the cursor down to the last non-marked thread email (or to the last email in the thread).
         * Default: "<EMAIL>"
         */
        val emailAddressesThreadMarkingSenders: List<String> = listOf("<EMAIL>")
    )

    data class Vero(
        val hourBooking: HourBooking = HourBooking(),
        /** This is used for both the hour booking and the token downloading in POST login. */
        val basicAuthTokenForLogin: String = "dGVzdHdlYnBvcnRhbDp0ZXN0cGFzczRwb3J0YWw=",
        val loginData: List<LoginData> = emptyList()
    ) {
        data class LoginData(
            val principal: String,
            val pnr: Int,
            /** Like "https://api.baubuddy.de/index.php" **/
            val baseUrlWithoutTrailingSlash: String,
            val password: String,
        )

        data class HourBooking(
            val pnr: Int = -999,
            val password: String = "TODO",
            val baseUrlWithoutTrailingSlash: String = "https://api.baubuddy.de/index.php",
            val principal: String = "vero_digital_solutions_ood",
            /**
             * The only projects to consider for booking on.
             * Note that all projects are always downloaded by GET v3/projects.
             *
             * Make sure that only one project has [VeroRootProject.isDefaultForBooking] set to true.
             */
            val rootProjects: List<VeroRootProject> = listOf(
                VeroRootProject(41506, "Printouts", isDefaultForBooking = true),
            ),
        )

        fun getDefaultProject(): VeroRootProject? {
            return hourBooking.rootProjects.find { it.isDefaultForBooking }
        }
    }

    data class CliApplications(
        /**
         * https://github.com/Dima-369/tree-sitter-cli-via-rust
         * Clone with all git submodules, since it needs the "highlights.scm" file from it.
         */
        val treeSitterCliRootDirectory: File? = File(homeWithoutSlash, "Developer/rust/tree-sitter-cli-via-rust"),
        /**
         * https://github.com/Dima-369/PDF-to-PNG-via-Rust
         * For PDF previews in Dired. The directory where the libpdfium.dylib file is located.
         */
        val libpdfiumDylibDirectory: File? = File(homeWithoutSlash, "Developer/rust/PDF-to-PNG"),
    )

    data class AudioPlayer(
        val engine: Audio.Engine = Audio.Engine.VLC,
        /**
         * The directory which is non-recursively searched for audio files for the [dima.dialogs.audio.AudioDialog].
         *
         * In the same directory, the YouTube audio files are downloaded to.
         */
        val databasePath: File? = null,
        /**
         * Port for the Rust audio engine server which is only used when [engine] is [Audio.Engine.Rust].
         */
        val rustPort: Int = 5001,
    )

    data class Dired(
        /**
         * Your most often used directory which can be quickly opened via a leader sequence.
         */
        val workingProjectPath: File? = null,
    )

    data class Telegram(
        /**
         * Currently only used to send messages in the Amazon Vine Telegram group.
         */
        val botToken: String? = null,
        /**
         * This is the global group which contains all.
         * Is usually negative.
         */
        val amazonVineGroupId: Long? = 0,
        val ownId: Long,
        val pythonFileSenderScriptFile: File? = File(
            homeWithoutSlash,
            "Developer/telegram-file-sender-cli/telegram_file_sender.py"
        ),
    )

    data class Weather(
        val latitude: Double? = null,
        val longitude: Double? = null,
    )

    /**
     * For use like in AI single prompt app or groq for transcriptions.
     */
    data class LlmApiKeys(
        val groq: String? = null,
        val googleGemini: String? = null,
        val cohere: String? = null,
        val pollinationsAi: String? = null,
    )

    data class GoogleCalendar(
        /**
         * From https://console.cloud.google.com
         */
        val secretCredentialsJson: String,
        /**
         * The full email <NAME_EMAIL>. This is used to open the calendar in the browser.
         */
        val emailAccount: String
    )

    data class Feed(
        /**
         * Can also be used for YouTube channels, but prefer to use [youTube].
         */
        val channels: List<FeedChannel> = emptyList(),
        /**
         * Pass URLs like https://www.instagram.com/medicalmedium
         */
        val instagram: List<FeedChannel> = emptyList(),
        val youTube: YouTubeFeed = YouTubeFeed(),
        /**
         * For downloading videos or subtitles via yt-dlp. There was a change in mid 2025 to make this mandatory.
         */
        val youTubeCookieFile: File? = null,
    )

    data class YouTubeFeed(
        val apiKeyToExtractChannelIds: String? = null,
        val channelsToCheckForNewVideos: List<FeedChannel> = emptyList(),
    )

}
