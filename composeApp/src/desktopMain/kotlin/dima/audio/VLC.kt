package dima.audio

import dima.apps.notifications.showErrorNotification
import dima.audio.Audio.QUIET_VOLUME
import dima.audio.Audio.playTrack
import dima.audio.Audio.updateTrackPositionInSettings
import dima.dialogs.DialogIdentifier
import dialogs
import dima.globalState.GlobalState
import dima.utils.randomFloat
import uk.co.caprica.vlcj.factory.MediaPlayerFactory
import uk.co.caprica.vlcj.media.*
import uk.co.caprica.vlcj.player.base.MediaPlayer
import uk.co.caprica.vlcj.player.base.State
import java.lang.Thread.sleep
import kotlin.time.Duration.Companion.milliseconds

/**
 * Updates the settings with its mutable state for the UI.
 */
private class VlcAudioStateThread : Thread() {
    override fun run() {
        val sleepDuration = 500.milliseconds.inWholeMilliseconds
        var lastUpdate = System.currentTimeMillis()
        while (true) {
            if (VLC.player == null) {
                sleep(sleepDuration)
                continue
            }
            val isInAudioDialog = dialogs.lastOrNull()?.identifier == DialogIdentifier.Audio
            if (isInAudioDialog) {
                lastUpdate = System.currentTimeMillis()
                updateTrackPositionInSettings()
                sleep(sleepDuration)
            } else {
                if (System.currentTimeMillis() - lastUpdate > 5000) {
                    updateTrackPositionInSettings()
                    lastUpdate = System.currentTimeMillis()
                }
                sleep(sleepDuration)
            }
        }
    }
}

object VLC {

    internal var factory: MediaPlayerFactory? = null
    internal var player: MediaPlayer? = null

    fun setup() {
        VlcAudioStateThread().start()
        if (player != null) {
            throw Exception("VLC MediaPlayer already setup!")
        }
        if (GlobalState.audioPlayer.isPlaying) {
            setupPlayerWhenNull()
            if (GlobalState.audioPlayer.currentTrack != null) {
                playTrack(
                    GlobalState.audioPlayer.currentTrack!!,
                    startPlaying = GlobalState.audioPlayer.isPlaying,
                    positionMs = GlobalState.audioPlayer.positionMs
                )
            }
        }
    }

    private fun setupPlayerWhenNull() {
        if (player == null) {
            factory = MediaPlayerFactory()
            player = factory!!.mediaPlayers().newMediaPlayer()
        }
    }

    internal fun positionInMs(): Long = player!!.status().time()

    fun toggleBetweenQuietAndNormalVolume() {
        if (player == null) {
            showErrorNotification("No track is playing")
            return
        }
        if (GlobalState.audioPlayer.quietVolume) {
            GlobalState.audioPlayer = GlobalState.audioPlayer.copy(quietVolume = false)
            player!!.audio().setVolume(100)
        } else {
            GlobalState.audioPlayer = GlobalState.audioPlayer.copy(quietVolume = true)
            player!!.audio().setVolume(QUIET_VOLUME)
        }
    }

    fun clearTrack() {
        if (player == null) {
            return
        }
        player!!.controls().stop()
    }

    fun seekForward(offsetMs: Long) {
        if (player == null) {
            return
        }
        val current = player!!.status().time()
        val next = (current + offsetMs).coerceAtLeast(0)
        player!!.controls().setTime(next)
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(positionMs = next)
    }

    fun seek(positionInMs: Long) {
        player!!.controls().setTime(positionInMs)
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(positionMs = positionInMs)
    }

    /**
     * Just for debugging.
     */
    fun seekToEnd() {
        player!!.controls().setPosition(0.999f)
    }

    fun shutdown() {
        if (factory == null) {
            return
        }
        updateTrackPositionInSettings()
        player!!.release()
    }

    fun isPlaying(): Boolean {
        return player != null && player!!.status().isPlaying
    }

    /**
     * @param startPlaying if false, prepares the track but does not play it
     */
    fun playTrack(
        path: String,
        startPlaying: Boolean,
        positionMs: Long? = null,
        startAtRandomPosition: Boolean = false,
    ) {
        setupPlayerWhenNull()
        val success = if (startPlaying) {
            player!!.media().play(path)
        } else {
            player!!.media().prepare(path)
        }
        if (!success) {
            if (startPlaying) {
                showErrorNotification("Failed to play track", path)
            } else {
                showErrorNotification("Failed to prepare track", path)
            }
            GlobalState.audioPlayer =
                GlobalState.audioPlayer.copy(isPlaying = false, currentTrack = null)
            return
        }
        player!!.media().events().addMediaEventListener(MyMediaEventListener())
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(
            currentTrack = path,
            isPlaying = startPlaying,
            positionMs = positionMs ?: 0
        )
        if (startAtRandomPosition) {
            seekToRandomTime()
            GlobalState.audioPlayer = GlobalState.audioPlayer.copy(positionMs = positionInMs())
        }
        if (positionMs != null) {
            player!!.controls().setTime(positionMs)
        }
    }

    fun togglePlayPause() {
        if (player == null) {
            setupPlayerWhenNull()
            playTrack(
                GlobalState.audioPlayer.currentTrack!!,
                startPlaying = true,
                positionMs = GlobalState.audioPlayer.positionMs
            )
        } else {
            player!!.controls().pause()
        }
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(isPlaying = !isPlaying())
    }

    fun seekToRandomTime() {
        player!!.controls().setPosition(randomFloat(0f, 0.8f))
    }

}

private class MyMediaEventListener : MediaEventListener {

    override fun mediaMetaChanged(media: Media?, metaType: Meta?) {
//        println("meta changed")
    }

    override fun mediaSubItemAdded(media: Media?, newChild: MediaRef?) {
//        println("media sub item added")
    }

    override fun mediaDurationChanged(media: Media?, newDuration: Long) {
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(totalDurationMs = newDuration)
    }

    override fun mediaParsedChanged(media: Media?, newStatus: MediaParsedStatus?) {
//        println("media parsed changed: $newStatus")
    }

    override fun mediaFreed(media: Media?, mediaFreed: MediaRef?) {
//        println("media freed")
    }

    override fun mediaStateChanged(media: Media?, newState: State?) {
        when (newState) {
            State.PLAYING -> {
                if (GlobalState.audioPlayer.quietVolume) {
                    VLC.factory!!.submit {
                        // wait a bit since playback needs to start
                        sleep(50)
                        VLC.player!!.audio().setVolume(QUIET_VOLUME)
                    }
                }
            }

            State.ENDED, State.STOPPED -> {
                if (GlobalState.audioPlayer.loopTrack) {
                    VLC.factory!!.submit {
                        playTrack(GlobalState.audioPlayer.currentTrack!!, startPlaying = true)
                    }
                } else if (GlobalState.audioPlayer.randomMode) {
                    VLC.factory!!.submit {
                        Audio.playRandomTrack()
                    }
                } else {
                    GlobalState.audioPlayer = GlobalState.audioPlayer.copy(
                        isPlaying = false,
                        currentTrack = null,
                        positionMs = 0,
                    )
                }
            }

            else -> {}
        }
    }

    override fun mediaSubItemTreeAdded(media: Media?, item: MediaRef?) {
//        println("media sub item tree added")
    }

    override fun mediaThumbnailGenerated(media: Media?, picture: Picture?) {
//        println("media thumbnail generated")
    }

}
