package dima.audio

import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.globalState.GlobalState
import dima.settings
import dima.utils.FileSystem
import java.nio.file.Path
import java.nio.file.Paths
import kotlin.io.path.absolutePathString
import kotlin.io.path.extension
import kotlin.io.path.listDirectoryEntries

object Audio {

    enum class Engine {
        VLC,
        Cmus,

        /**
         * https://github.com/Dima-369/rust-cli-audio-player
         */
        Rust
    }

    /**
     * I think this scales logarithmically where 30 is way quieter than 50.
     */
    internal const val QUIET_VOLUME: Int = 30

    fun isCurrentlyPlayedTrackFromDatabase(): Boolean {
        val path = settings.audio.databasePath?.absolutePath ?: return false
        val currentTrack = GlobalState.audioPlayer.currentTrack ?: return false
        return currentTrack.startsWith(path)
    }

    fun formatMsDuration(ms: Long): String {
        val secondsInMinute = 60
        val secondsInHour = 3600
        val totalSeconds = ms / 1000
        val hours = totalSeconds / secondsInHour
        val minutes = (totalSeconds % secondsInHour) / secondsInMinute
        val seconds = totalSeconds % secondsInMinute
        return if (hours >= 1) {
            String.format("%d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format("%02d:%02d", minutes, seconds)
        }
    }

    fun updateTrackPositionInSettings() {
        if (isPlaying()) {
            GlobalState.audioPlayer = GlobalState.audioPlayer.copy(positionMs = positionInMs())
        }
    }

    fun getAllAudioFilesInAudioDirectory(): List<Path> {
        val path = settings.audio.databasePath?.absolutePath ?: return emptyList()
        return Paths.get(path).listDirectoryEntries().mapNotNull { dirEntry ->
            val extension = dirEntry.extension
            if (FileSystem.isAudioExtension(extension)) {
                dirEntry
            } else {
                null
            }
        }
    }

    fun toggleBetweenQuietAndNormalVolume() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.toggleBetweenQuietAndNormalVolume()
            Engine.Cmus -> {
                // this works to control the volume, but may degrade audio quality
                // set softvol=true
                showErrorNotification("Cmus has no proper volume control on macOS")
            }

            Engine.Rust -> RustAudioEngine.toggleBetweenQuietAndNormalVolume()
        }
    }

    fun setup() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.setup()
            Engine.Cmus -> Cmus.setup()
            Engine.Rust -> RustAudioEngine.setup()
        }
    }

    fun clearTrack() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.clearTrack()
            Engine.Cmus -> Cmus.clearTrack()
            Engine.Rust -> RustAudioEngine.clearTrack()
        }
    }

    /**
     * @param startPlaying if false, prepares the track but does not play it
     */
    fun playTrack(
        path: String,
        startPlaying: Boolean,
        positionMs: Long? = null,
        startAtRandomPosition: Boolean = false,
    ) {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.playTrack(path, startPlaying, positionMs, startAtRandomPosition)
            Engine.Cmus -> Cmus.playTrack(path, startPlaying, positionMs, startAtRandomPosition)
            Engine.Rust -> RustAudioEngine.playTrack(path, startPlaying, positionMs, startAtRandomPosition)
        }
    }

    private fun positionInMs(): Long {
        return when (settings.audio.engine) {
            Engine.VLC -> VLC.positionInMs()
            Engine.Cmus -> 0
            Engine.Rust -> RustAudioEngine.positionInMs()
        }
    }

    /**
     * @return true if the track was played
     */
    fun playRandomTrack(): Boolean {
        val tracks = getAllAudioFilesInAudioDirectory()
            .map { it.absolutePathString() }
            .filter { it != GlobalState.audioPlayer.currentTrack }
        if (tracks.isEmpty()) {
            showNotification("No tracks found")
            return false
        }
        val newTrack = tracks.random()
        showNotification("Playing ${newTrack.substringAfterLast("/").substringBeforeLast(".")}")
        playTrack(newTrack, startAtRandomPosition = true, startPlaying = true)
        return true
    }

    fun seek(positionInMs: Long) {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.seek(positionInMs)
            Engine.Cmus -> Cmus.seek(positionInMs)
            Engine.Rust -> RustAudioEngine.seek(positionInMs)
        }
    }

    fun seekForward(offsetMs: Long) {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.seekForward(offsetMs)
            Engine.Cmus -> Cmus.seekForward(offsetMs)
            Engine.Rust -> RustAudioEngine.seekForward(offsetMs)
        }
    }

    fun seekBackward(offsetMs: Long) {
        seekForward(-offsetMs)
    }

    /**
     * Just for debugging.
     */
    fun seekToEnd() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.seekToEnd()
            Engine.Cmus -> Cmus.seekToEnd()
            Engine.Rust -> RustAudioEngine.seekToEnd()
        }
    }

    fun togglePlayPause() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.togglePlayPause()
            Engine.Cmus -> Cmus.togglePlayPause()
            Engine.Rust -> RustAudioEngine.togglePlayPause()
        }
    }

    fun seekToRandomTime() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.seekToRandomTime()
            Engine.Cmus -> Cmus.seekToRandomTime()
            Engine.Rust -> RustAudioEngine.seekToRandomTime()
        }
    }

    private fun isPlaying(): Boolean {
        return when (settings.audio.engine) {
            Engine.VLC -> VLC.isPlaying()
            Engine.Cmus -> GlobalState.audioPlayer.isPlaying
            Engine.Rust -> RustAudioEngine.isPlaying()
        }
    }

    fun shutdown() {
        when (settings.audio.engine) {
            Engine.VLC -> VLC.shutdown()
            Engine.Cmus -> Cmus.shutdown()
            Engine.Rust -> RustAudioEngine.shutdown()
        }
    }
}