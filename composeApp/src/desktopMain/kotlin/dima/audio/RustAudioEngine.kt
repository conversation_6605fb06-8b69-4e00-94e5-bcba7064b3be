package dima.audio

import dialogs
import dima.apps.networkActivity.postLogged
import dima.apps.notifications.showErrorNotification
import dima.dialogs.DialogIdentifier
import dima.globalState.GlobalState
import dima.settings
import dima.utils.JsonIgnoreUnknown
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlin.time.Duration.Companion.seconds

/**
 * Updates the settings with its mutable state for the UI.
 */
private class RustAudioStateThread : Thread() {

    override fun run() {
        val sleepDuration = 3.seconds.inWholeMilliseconds
        while (true) {
            try {
                runBlocking {
                    RustAudioEngine.update(showErrorNotification = false)
                }
                if (dialogs.lastOrNull()?.identifier == DialogIdentifier.Audio) {
                    // update quicker when dialog is open to see the progress update in realtime
                    sleep(1.seconds.inWholeMilliseconds)
                } else {
                    sleep(sleepDuration)
                }
            } catch (_: InterruptedException) {
                break
            }
        }
    }
}

object RustAudioEngine {

    private var thread: RustAudioStateThread? = null
    private val client = HttpClient(CIO) {
        install(HttpTimeout) {
            requestTimeoutMillis = 5000
            connectTimeoutMillis = 3000
        }
    }

    private fun getBaseUrl(): String = "http://localhost:${settings.audio.rustPort}"

    fun setup() {
        // Launch a coroutine to perform the initial status update
        CoroutineScope(Dispatchers.IO).launch {
            update(showErrorNotification = true)
        }
        // Start the thread for periodic updates
        thread = RustAudioStateThread()
        thread!!.start()
    }

    /**
     * Do not log in network activity since it is spammed.
     */
    suspend fun update(showErrorNotification: Boolean) {
        val response: io.ktor.client.statement.HttpResponse = client.get("${getBaseUrl()}/status")
        val body = response.bodyAsText()
        if (response.status == HttpStatusCode.OK) {
            val status = JsonIgnoreUnknown.decodeFromString<RustAudioStatus>(body)
            updateGlobalStateFromRustStatus(status)
        } else if (showErrorNotification) {
            showErrorNotification("Rust audio server error: ${response.status}", body)
        }
    }

    fun updateGlobalStateFromRustStatus(status: RustAudioStatus) {
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(
            isPlaying = status.status == "playing",
            positionMs = status.positionInMs,
            totalDurationMs = status.totalDurationInMs,
            currentTrack = status.file,
            loopTrack = status.loopTrack,
            quietVolume = status.volume == Audio.QUIET_VOLUME
        )
    }

    fun clearTrack() {
        CoroutineScope(Dispatchers.IO).launch {
            val response = client.postLogged("${getBaseUrl()}/stop")
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to stop track", response.body)
            }
        }
    }

    fun togglePlayPause() {
        CoroutineScope(Dispatchers.IO).launch {
            val response = client.postLogged("${getBaseUrl()}/pause")
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to toggle play/pause", response.body)
            }
        }
    }

    /**
     * @param startPlaying if false, prepares the track but does not play it
     * @param positionMs takes precedence over [startAtRandomPosition]
     * @param startAtRandomPosition ignored when [positionMs] is set
     */
    fun playTrack(
        path: String,
        startPlaying: Boolean,
        positionMs: Long? = null,
        startAtRandomPosition: Boolean = false,
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            val request = RustPlayTrackRequest(
                file = path,
                startPlaying = startPlaying,
                position = positionMs,
                startAtRandomPosition = startAtRandomPosition
            )
            val response = client.postLogged("${getBaseUrl()}/play") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(RustPlayTrackRequest.serializer(), request))
            }
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to play track", response.body)
            }
        }
    }

    fun seekForward(offsetMs: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            val request = RustSeekRelativeRequest(offset = offsetMs)
            val response = client.postLogged("${getBaseUrl()}/seek-relative") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(RustSeekRelativeRequest.serializer(), request))
            }
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to seek forward", response.body)
            }
        }
    }

    fun seek(positionInMs: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            val request = RustSeekRequest(position = positionInMs)
            val response = client.postLogged("${getBaseUrl()}/seek") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(RustSeekRequest.serializer(), request))
            }
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to seek", response.body)
            }
        }
    }

    fun seekToEnd() {
        CoroutineScope(Dispatchers.IO).launch {
            // Seek to 99.9% of the track duration to avoid ending the track
            val endPosition = (GlobalState.audioPlayer.totalDurationMs * 0.999).toLong()
            val request = RustSeekRequest(position = endPosition)
            val response = client.postLogged("${getBaseUrl()}/seek") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(RustSeekRequest.serializer(), request))
            }
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to seek to end", response.body)
            }
        }
    }

    fun shutdown() {
        if (thread != null) {
            thread!!.interrupt()
        }
        client.close()
    }

    fun seekToRandomTime() {
        CoroutineScope(Dispatchers.IO).launch {
            // Seek to a random position between 0 and 80% of track duration
            val randomPosition =
                kotlin.random.Random.nextLong(0L, (GlobalState.audioPlayer.totalDurationMs * 0.8).toLong())
            val request = RustSeekRequest(position = randomPosition)
            val response = client.postLogged("${getBaseUrl()}/seek") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(RustSeekRequest.serializer(), request))
            }
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to seek to random time", response.body)
            }
        }
    }

    fun isPlaying(): Boolean {
        return GlobalState.audioPlayer.isPlaying
    }

    fun positionInMs(): Long {
        return GlobalState.audioPlayer.positionMs
    }

    fun toggleBetweenQuietAndNormalVolume() {
        CoroutineScope(Dispatchers.IO).launch {
            val newVolume = if (GlobalState.audioPlayer.quietVolume) 100 else Audio.QUIET_VOLUME
            val request = RustVolumeRequest(volume = newVolume)
            val response = client.postLogged("${getBaseUrl()}/volume") {
                contentType(ContentType.Application.Json)
                setBody(JsonIgnoreUnknown.encodeToString(RustVolumeRequest.serializer(), request))
            }
            if (response.statusCode == 200) {
                update(showErrorNotification = true)
            } else {
                showErrorNotification("Failed to toggle volume", response.body)
            }
        }
    }
}