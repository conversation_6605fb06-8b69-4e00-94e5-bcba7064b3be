package dima.audio

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MusicNote
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.rememberTrayState
import dima.globalState.GlobalState
import dima.utils.TrayIcon

@Composable
fun ApplicationScope.AudioTray() {
    if (GlobalState.audioPlayer.isPlaying) {
        val painter = rememberVectorPainter(Icons.Default.MusicNote)
        val trayState = rememberTrayState()
        Tray(
            state = trayState,
            icon = TrayIcon(painter)
        )
    }
}