package dima.audio

import com.github.pgreze.process.ProcessResult
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.dialogs.DialogIdentifier
import dialogs
import dima.globalState.GlobalState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import kotlin.random.Random
import kotlin.time.Duration.Companion.seconds

/**
 * Updates the settings with its mutable state for the UI.
 */
private class CmusAudioStateThread : Thread() {

    override fun run() {
        val sleepDuration = 3.seconds.inWholeMilliseconds
        while (true) {
            try {
                runBlocking {
                    Cmus.update(showErrorNotification = false)
                }
                if (dialogs.lastOrNull()?.identifier == DialogIdentifier.Audio) {
                    // update quicker when dialog is open to see the progress update in realtime
                    sleep(1.seconds.inWholeMilliseconds)
                } else {
                    sleep(sleepDuration)
                }
            } catch (_: InterruptedException) {
                break
            }
        }
    }
}

object Cmus {

    private enum class Status {
        Playing,
        Paused,
        Stopped
    }

    private var thread: CmusAudioStateThread? = null

    /**
     * Store to loop track.
     */
    private var lastPlayedFile: String? = null

    fun setup() {
        thread = CmusAudioStateThread()
        thread!!.start()
    }

    suspend fun update(showErrorNotification: Boolean) {
        val p = process(
            "cmus-remote", "--query",
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        if (p.resultCode == 1) {
            if (showErrorNotification) {
                showErrorNotification("No cmus process is running?", p.output.joinToString("\n"))
            }
            return
        }
        var status = Status.Playing
        var positionS: Int? = null
        var durationS: Int? = null
        var file: String? = null
        p.output.forEach {
            if (it.startsWith("status ")) {
                when (it) {
                    "status paused" -> status = Status.Paused
                    "status stopped" -> status = Status.Stopped
                    "status playing" -> status = Status.Playing
                }
            } else if (it.startsWith("file ")) {
                file = it.substringAfter("file ")
                lastPlayedFile = file
            } else if (it.startsWith("duration ")) {
                durationS = it.substringAfter("duration ").toInt()
            } else if (it.startsWith("position ")) {
                positionS = it.substringAfter("position ").toInt()
            }
        }
        GlobalState.audioPlayer = GlobalState.audioPlayer.copy(
            isPlaying = status == Status.Playing,
            positionMs = (positionS?.toLong()?.times(1000)) ?: 0L,
            totalDurationMs = (durationS?.toLong()?.times(1000)) ?: 0L,
            currentTrack = file
        )
        if (GlobalState.audioPlayer.loopTrack && status == Status.Stopped && lastPlayedFile != null) {
            if (File(lastPlayedFile!!).exists()) {
                Audio.playTrack(lastPlayedFile!!, startPlaying = true)
            }
        } else if (GlobalState.audioPlayer.randomMode && status == Status.Stopped) {
            Audio.playRandomTrack()
        }
    }

    /**
     * Cmus has no concept of track clearing, so playback is just stopped.
     */
    fun clearTrack() {
        CoroutineScope(Dispatchers.IO).launch {
            val result = executeCmusRemote("--stop")
            if (result != null) {
                update(showErrorNotification = true)
            }
        }
    }

    fun togglePlayPause() {
        CoroutineScope(Dispatchers.IO).launch {
            val result = executeCmusRemote("--pause")
            if (result != null) {
                update(showErrorNotification = true)
            }
        }
    }

    /**
     * @return null on any error and shows an error notification
     */
    private suspend fun executeCmusRemote(vararg args: String): ProcessResult? {
        val p = process(
            "cmus-remote", *args,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
        if (p.resultCode == 0) {
            return p
        }
        val joined = args.joinToString(" ")
        showErrorNotification(
            "cmus-remote $joined failed: ${p.resultCode}",
            p.output.joinToString("\n")
        )
        return null
    }

    /**
     * @param startPlaying if false, prepares the track but does not play it
     * @param positionS takes precedence over [startAtRandomPosition]
     * @param startAtRandomPosition ignored when [positionS] is set
     */
    fun playTrack(
        path: String,
        startPlaying: Boolean,
        positionS: Long? = null,
        startAtRandomPosition: Boolean = false,
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            if (positionS == null) {
                executeCmusRemote("--file", path)
            } else {
                executeCmusRemote("--file", path, "--seek", positionS.toString())
            }
            if (!startPlaying) {
                executeCmusRemote("--pause")
            }
            update(showErrorNotification = true)
            if (positionS == null && startAtRandomPosition) {
                val position = getRandomPositionS()
                executeCmusRemote(
                    "--seek",
                    position.toString()
                )
            }
        }
    }

    private fun getRandomPositionS(): Long {
        return Random.nextLong(0L, (((GlobalState.audioPlayer.totalDurationMs / 1000) * 0.8f).toLong()))
    }

    fun seekForward(offsetMs: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            val newPosition = (GlobalState.audioPlayer.positionMs + offsetMs) / 1000
            val result = executeCmusRemote("--seek", newPosition.toString())
            if (result != null) {
                update(showErrorNotification = true)
            }
        }
    }

    fun seek(positionInMs: Long) {
        CoroutineScope(Dispatchers.IO).launch {
            val result = executeCmusRemote("--seek", (positionInMs / 1000).toString())
            if (result != null) {
                update(showErrorNotification = true)
            }
        }
    }

    fun seekToEnd() {
        CoroutineScope(Dispatchers.IO).launch {
            val result = executeCmusRemote("--seek", ((GlobalState.audioPlayer.totalDurationMs / 1000) - 1).toString())
            if (result != null) {
                update(showErrorNotification = true)
            }
        }
    }

    fun shutdown() {
        if (thread != null) {
            thread!!.interrupt()
        }
    }

    fun seekToRandomTime() {
        CoroutineScope(Dispatchers.IO).launch {
            val result = executeCmusRemote("--seek", getRandomPositionS().toString())
            if (result != null) {
                update(showErrorNotification = true)
            }
        }
    }

}