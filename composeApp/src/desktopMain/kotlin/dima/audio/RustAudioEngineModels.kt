package dima.audio

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RustAudioStatus(
    /** "playing" | "paused" | "stopped" **/
    val status: String,
    /** Current file path (null if no track loaded) **/
    val file: String?,
    @SerialName("position")
    val positionInMs: Long,
    @SerialName("duration")
    val totalDurationInMs: Long,
    /** Current volume level (0-100) **/
    val volume: Int,
    @SerialName("loop_track")
    val loopTrack: Boolean,
)

@Serializable
data class RustPlayTrackRequest(
    val file: String,
    @SerialName("start_playing")
    val startPlaying: Boolean,
    val position: Long? = null,
    @SerialName("start_at_random_position")
    val startAtRandomPosition: Boolean = false
)

@Serializable
data class RustSeekRequest(
    val position: Long
)

@Serializable
data class RustSeekRelativeRequest(
    val offset: Long
)

@Serializable
data class RustVolumeRequest(
    val volume: Int
)
