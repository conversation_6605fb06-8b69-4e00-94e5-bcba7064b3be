package dima.os

import com.github.pgreze.process.*
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.os.Osascript.execute
import kotlinx.coroutines.*
import java.io.File

/**
 * Reveals the file in Finder.app
 */
fun File.revealInFinderViaOsascript() {
    suspend fun executeOsascript() {
        execute(
            """
            set thePath to POSIX file "$absolutePath"
            tell application "Finder"
                set frontmost to true
                reveal thePath
            end tell
            """.trimIndent()
        )
    }

    runBlocking {
        executeOsascript()
        CoroutineScope(Dispatchers.IO).launch {
            // do not set too high, otherwise when you alt-tab away from Finder, it just jumps back
            delay(timeMillis = 500)
            val active = Hammerspoon.getActiveMacOsApp()
            if (active == null || active != "Finder") {
                executeOsascript()
            }
        }
    }
}

/**
 * Copies an image file to the clipboard. This only works for image files.
 */
suspend fun File.copyImageToClipboard() {
    val result = execute(
        """
set the clipboard to (read (POSIX file "$absolutePath") as JPEG picture)
            """.trimIndent()
    )
    if (result.resultCode == 0) {
        showNotification("Copied image to clipboard", name, durationMillis = 1000)
    } else {
        showErrorNotification("Failed to copy image to clipboard", result.output.joinToString("\n"))
    }
}

object Osascript {

    internal suspend fun execute(osaScript: String): ProcessResult {
        return process(
            "osascript",
            "-e",
            osaScript,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE,
        )
    }

    /**
     * @param title should not have quotes to not mess with osascript quotes
     */
    suspend fun showDismissableDialog(dialogText: String, title: String = "Kotlin Emacs") {
        process(
            "osascript",
            "-e",
            """
            display dialog "$dialogText" buttons {"Dismiss"} default button 1 with title "$title"
        """.trimIndent(),
            stdin = InputSource.fromString(dialogText),
            stdout = Redirect.SILENT,
            stderr = Redirect.SILENT,
        ).unwrap()
    }

    suspend fun showOsNotification(notificationText: String, title: String = "Kotlin Emacs") {
        process(
            "osascript",
            "-e",
            """
            display notification "$notificationText" with title "$title"
        """.trimIndent(),
            stdin = InputSource.fromString(notificationText),
            stdout = Redirect.SILENT,
            stderr = Redirect.SILENT,
        ).unwrap()
    }
}
