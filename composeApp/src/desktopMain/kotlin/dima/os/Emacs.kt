package dima.os

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.utils.abbreviatePath
import java.io.File

object Emacs {

    suspend fun openFileInDiredAndFocus(file: File) {
        val p = process(
            "emacsclient",
            "-e",
            "(progn (find-file \"${file.absolutePath}\") (dired-jump-save) (hl-line-highlight) (recenter) (dima-place-emacs-in-foreground))",
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE
        )
        if (p.resultCode == 0) {
            showNotification("Opened file in Emacs Dired", file.abbreviatePath(), durationMillis = 1000)
            return
        }
        showErrorNotification("Opening file in Emacs Dired failed", p.output.joinToString("\n"))
    }

    suspend fun openDirectoryInDiredAndFocus(file: File) {
        val p = process(
            "emacsclient", "-e", "(progn (find-file \"${file.absolutePath}\") (hl-line-highlight) (dima-place-emacs-in-foreground))",
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE
        )
        if (p.resultCode == 0) {
            showNotification("Opened directory in Emacs", file.abbreviatePath(), durationMillis = 1000)
            return
        }
        showErrorNotification("Opening directory in Emacs failed", p.output.joinToString("\n"))
    }

    /**
     * @param lineNumber starts at 1
     * @param column note that in Emacs every combined character is counted as 1, regardless of emoji
     */
    suspend fun openFileContentAndFocus(file: File, lineNumber: Int? = null, column: Int? = null) {
        val arg = buildString {
            append("(progn (find-file \"${file.absolutePath}\") (dima-place-emacs-in-foreground)")
            if (lineNumber != null) {
                append(" (goto-line $lineNumber)")
            }
            if (column != null) {
                val clampedColumn = (column - 1).coerceAtLeast(0)
                append(" (move-to-column $clampedColumn)")
            }
            // add one closing paren for the starting (progn)
            append("(run-at-time 0.12 nil (lambda () (beacon-blink))))")
        }
        val p = process(
            "emacsclient", "-e", arg,
            stdout = Redirect.CAPTURE,
            stderr = Redirect.CAPTURE
        )
        if (p.resultCode == 0) {
            showNotification("Opened file in Emacs", file.abbreviatePath(), durationMillis = 1000)
            return
        }
        showErrorNotification("Opening file in Emacs failed", p.output.joinToString("\n"))
    }

    /**
     * Calculates the column number (1-based) for a given byte position in a file.
     * This is used to determine where to place the cursor in Emacs.
     * The function finds the start of the line containing the byte position,
     * then counts the number of code points (characters) up to that position.
     *
     * @param file the file to analyze
     * @param byteStart the byte position in the file (0-based)
     * @return the number of code points from the start of the line to the byte position
     */
    fun calculateColumnFromByteStart(file: File, byteStart: Int): Int {
        val fileBytes = file.readBytes()
        val lineStartByte = fileBytes.take(byteStart).lastIndexOf('\n'.code.toByte()).let { n ->
            if (n == -1) 0 else n + 1
        }
        val stringLine = fileBytes.slice(lineStartByte until byteStart).toByteArray().toString(Charsets.UTF_8)
        return stringLine.codePointCount(0, stringLine.length) + 1
    }

}