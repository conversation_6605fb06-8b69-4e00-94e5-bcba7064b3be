package dima.os

import com.github.pgreze.process.InputSource
import com.github.pgreze.process.ProcessResult
import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

object Hammerspoon {

    /**
     * Always run this like this since it takes more than a second for some reason.
     * In the shell, it is a lot faster!
     *
     * withContext(Dispatchers.Default) { ... }
     *
     * @return the application title of the currently focused window like "Kolin Emacs"
     */
    fun getActiveMacOsApp(): String? {
        val lua = """
        focused = hs.window.focusedWindow()
        if focused ~= nil then
          return focused:application():title()
        end
    """.trimIndent()
        return runBlocking {
            val p = process(
                "hs",
                stdin = InputSource.fromString(lua),
                stdout = Redirect.CAPTURE
            )
            if (p.resultCode != 0 || p.output.isEmpty()) {
                return@runBlocking null
            }
            return@runBlocking p.output[0].trim()
        }
    }

    /**
     * @param [windowName] should not have quotes to not mess with hs quotes
     */
    suspend fun focusWindow(windowName: String): ProcessResult {
        val lua = """
            win = hs.window.find("$windowName")
            if focused ~= nil then
                win:focus()
            end
        """.trimIndent()
        return process(
            "hs", "-c", lua,
            stdout = Redirect.SILENT,
            stderr = Redirect.SILENT,
        )
    }

    fun focusApp(appName: String) {
        CoroutineScope(Dispatchers.IO).launch {
            process(
                "hs",
                "-c",
                """hs.application.launchOrFocus("$appName")""",
                stdout = Redirect.SILENT,
                stderr = Redirect.SILENT,
            )
        }
    }

    /**
     * Note that newlines work correctly, but long lines are not correctly wrapped, use
     * [dima.utils.breakWithNewlines] to break a long string up with newlines.
     */
    fun showAlert(text: String, durationSeconds: Int = 4) {
        CoroutineScope(Dispatchers.IO).launch {
            process(
                "hs",
                "-c",
                """hs.alert.show([===[$text]===], { textStyle = { paragraphStyle = { alignment = "center" } } }, hs.screen.mainScreen(), $durationSeconds)""",
                stdout = Redirect.SILENT,
                stderr = Redirect.SILENT,
            )
        }
    }

}