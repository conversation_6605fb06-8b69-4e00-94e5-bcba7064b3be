@file:Suppress("unused")

package dima.os

import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.clipboard.setClipboard
import dima.utils.truncateWithEllipsis
import kotlinx.coroutines.runBlocking
import java.awt.Robot
import java.awt.event.KeyEvent

val homeWithoutSlash: String = System.getenv("HOME")

/**
 * Send Cmd+V keystroke to insert text from clipboard.
 */
fun insertStringViaClipboardViaRobot(text: String) {
    try {
        copyToClipboard(text, showNotification = false)
        val robot = Robot()
        robot.keyPress(KeyEvent.VK_META)
        robot.keyPress(KeyEvent.VK_V)
        robot.keyRelease(KeyEvent.VK_V)
        robot.keyRelease(KeyEvent.VK_META)
    } catch (e: Exception) {
        showErrorNotification("Failed to send keystrokes: ${e.message}")
    }
}

fun playMp3File(fileName: String) {
    ProcessBuilder("afplay", fileName).start()
}

fun openUrl(url: String) {
    runBlocking {
        val p = process("open", url)
        if (p.resultCode != 0) {
            showErrorNotification("Failed to open URL via shell", url)
        }
    }
}

/**
 * Copy text to clipboard.
 */
fun copyToClipboard(
    text: String,
    showNotification: Boolean = true,
    notificationTitleSubstring: String? = null
) {
    setClipboard(text)
    if (showNotification) {
        if (notificationTitleSubstring == null) {
            showNotification("Copied to clipboard", text.truncateWithEllipsis(900), durationMillis = 1000)
        } else {
            showNotification(
                "Copied $notificationTitleSubstring to clipboard",
                text.truncateWithEllipsis(900),
                durationMillis = 1000
            )
        }
    }
}
