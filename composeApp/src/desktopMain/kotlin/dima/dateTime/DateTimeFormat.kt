package dima.dateTime

import java.util.*

object DateTimeFormat {

    /** 23:01 */
    val hourColonMinute by lazy {
        dateTimeFormat {
            hour()
            char(':')
            minute()
        }
    }

    /** 23:01:49 */
    val hourMinuteSecondSeperatedByColon by lazy {
        dateTimeFormat {
            hourMinuteSecondsSeperatedByColon()
        }
    }

    /** 2024-07-27 23:01:49 */
    val dateTimeSpaceSeparated by lazy {
        dateTimeFormat {
            isoDate()
            char(' ')
            hourMinuteSecondsSeperatedByColon()
        }
    }

    /**
     * 18. März 2025 um 10:28
     */
    val humanDateMonthYearTime by lazy {
        dateTimeFormat(Locale.GERMAN) {
            dayOfMonthNoPadding()
            chars(". ")
            monthName()
            char(' ')
            year()
            chars(" um ")
            hour()
            char(':')
            minute()
        }
    }

    /**
     * 15. Februar 2024
     */
    val germanDateWithMonth by lazy {
        dateTimeFormat(Locale.GERMAN) {
            dayOfMonthNoPadding()
            chars(". ")
            monthName()
            char(' ')
            year()
        }
    }

    /**
     * 10.30.2024
     */
    val shortDateWithDots by lazy {
        dateTimeFormat {
            dayOfMonth()
            char('.')
            monthNumberShort()
            char('.')
            year()
        }
    }

    /**
     * 2024-10-17
     */
    val isoDate by lazy {
        dateTimeFormat {
            year()
            char('-')
            monthNumber()
            char('-')
            dayOfMonth()
        }
    }

    /**
     * 2024-10-07T17:30:17Z
     */
    val isoDateTimeWithTandZ by lazy {
        dateTimeFormat {
            isoDate()
            char('T')
            hourMinuteSecondsSeperatedByColon()
            chars("Z")
        }
    }

    /**
     * 2024-11-01T19:43:46.115517
     */
    val isoDateTimeWith6Milliseconds by lazy {
        dateTimeFormat {
            isoDate()
            char('T')
            hourMinuteSecondsSeperatedByColon()
            char('.')
            secondFraction(6)
        }
    }

    /**
     * 12. Juli
     */
    val germanDateWithMonthAbbreviated by lazy {
        dateTimeFormat(Locale.GERMAN) {
            dayOfMonthNoPadding()
            chars(". ")
            monthNameAbbrev()
        }
    }

    /**
     * 2024-10-07T17:30:17+00:00
     */
    val isoDateTimeWithTandPlus00_00Timezone by lazy {
        dateTimeFormat {
            isoDate()
            char('T')
            hourMinuteSecondsSeperatedByColon()
            chars("+00:00")
        }
    }

}
