package dima.dateTime

import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime

fun com.google.api.client.util.DateTime.toKotlinInstant(): Instant {
    return Instant.ofEpochMilli(this.value)
}

fun com.google.api.client.util.DateTime.toZonedDateTime(): ZonedDateTime {
    return this.toKotlinInstant().atZone(ZoneOffset.ofTotalSeconds(this.timeZoneShift * 60))
}

fun com.google.api.client.util.DateTime.toLocalDate(): LocalDate {
    return Instant.ofEpochMilli(this.value)
        .atZone(ZoneOffset.UTC)
        .toLocalDate()
}