package dima.dateTime

import java.time.format.DateTimeFormatter
import java.util.Locale.*

class DateTimeFormatterBuilder {
    private val pattern = StringBuilder()

    /** Adds ISO date format (yyyy-MM-dd) */
    fun isoDate() {
        year()
        char('-')
        monthNumber()
        char('-')
        dayOfMonth()
    }

    /** Adds 24-hour format (00-23) */
    fun hour() {
        pattern.append("HH")
    }

    /** Adds minutes (00-59) */
    fun minute() {
        pattern.append("mm")
    }

    /** Adds seconds (00-59) */
    fun second() {
        pattern.append("ss")
    }

    /** Adds fractional seconds with specified number of digits */
    fun secondFraction(digits: Int) {
        pattern.append(".")
        pattern.append("S".repeat(digits))
    }

    /** Adds 4-digit year */
    fun year() {
        pattern.append("yyyy")
    }

    /** Adds 2-digit month (01-12) */
    fun monthNumber() {
        pattern.append("MM")
    }

    /** Adds 1 or 2-digit month (1-12) */
    fun monthNumberShort() {
        pattern.append("M")
    }

    /** Adds full month name */
    fun monthName() {
        pattern.append("MMMM")
    }

    /** Adds 2-digit day of month (01-31) */
    fun dayOfMonth() {
        pattern.append("dd")
    }

    /** Adds day of month without padding (1-31) */
    fun dayOfMonthNoPadding() {
        pattern.append("d")
    }

    /** Adds abbreviated month name */
    fun monthNameAbbrev() {
        pattern.append("MMM")
    }

    /** Adds a single literal character */
    fun char(c: Char) {
        pattern.append("'").append(c).append("'")
    }

    /** Adds a literal string */
    fun chars(s: String) {
        pattern.append("'").append(s).append("'")
    }

    /** Adds time in HH:mm:ss format */
    fun hourMinuteSecondsSeperatedByColon() {
        hour()
        char(':')
        minute()
        char(':')
        second()
    }

    /** Builds the DateTimeFormatter with the configured pattern */
    fun build(): DateTimeFormatter = DateTimeFormatter.ofPattern(pattern.toString())
}

/** Creates a DateTimeFormatter using the builder DSL */
fun dateTimeFormat(
    language: java.util.Locale = getDefault(),
    init: DateTimeFormatterBuilder.() -> Unit
): DateTimeFormatter {
    return DateTimeFormatterBuilder()
        .apply(init)
        .build()
        .withLocale(language)
}