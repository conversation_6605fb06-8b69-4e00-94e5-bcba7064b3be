package dima.dateTime

import dima.dateTime.DateTimeUtils.formatYesterdayOrRelative
import dima.dateTime.DateTimeUtils.isSameDay
import java.time.*

fun LocalDateTime.isToday(): Boolean {
    return isSameDay(this, LocalDate.now())
}

/**
 * @return "heute 13:00", "gestern 13:00", "vorgestern", "vor 3 Tagen", "vor 1 Jahr" or "vor 3 Monaten"
 */
fun LocalDateTime.formatDateTimeRelative(): String {

    fun formatTime(): String = this.format(DateTimeFormat.hourColonMinute)

    val today = LocalDateTime.now()
    if (isSameDay(today, this)) {
        return "heute " + formatTime()
    }
    val yesterday = today.toLocalDate().minusDays(1)
    if (isSameDay(yesterday, this)) {
        return "gestern " + formatTime()
    }
    val twoDaysAgo = yesterday.minusDays(1)
    if (isSameDay(twoDaysAgo, this)) {
        return "vorgestern"
    }
    val period = Period.between(this.toLocalDate(), today.toLocalDate())
    if (period.years >= 1) {
        if (period.years == 1) {
            return "vor 1 Jahr"
        }
        return "vor ${period.years} Jahren"
    }
    if (period.months >= 1) {
        if (period.months == 1) {
            return "vor 1 Monat"
        }
        return "vor ${period.months} Monaten"
    }
    // is at least 3
    return "vor ${period.days} Tagen"
}

/**
 * @return "jetzt", "vor 3 Minuten" or "vor 3 Tagen"
 */
fun LocalDateTime.toHumanRelativeInPastShort(): String {
    val today = LocalDate.now()
    if (isSameDay(today, this)) {
        val duration = Duration.between(this, LocalDateTime.now())
        return when {
            duration.toMinutes() <= 2 -> "jetzt"
            duration.toMinutes() in 3..59 -> "vor ${duration.toMinutes()} Minuten"
            duration.toHours() <= 23 -> "vor ${duration.toHours()} Stunden"
            else -> "vor ${duration.toDays()} Tagen"
        }
    }
    return formatYesterdayOrRelative(this, today)
}

object DateTimeUtils {

    const val SECONDS_IN_MINUTE = 60
    const val SECONDS_IN_HOUR = SECONDS_IN_MINUTE * 60
    const val SECONDS_IN_DAY = SECONDS_IN_HOUR * 24

    fun nowInSeconds(): Int {
        val now = LocalTime.now()
        return now.hour * SECONDS_IN_HOUR + now.minute * SECONDS_IN_MINUTE + now.second
    }

    fun nowInUtc(): ZonedDateTime = ZonedDateTime.now(ZoneId.of("UTC"))

    /**
     * yyyy-MM-dd
     */
    fun nowAsIsoDate(): String = LocalDateTime.now().format(DateTimeFormat.isoDate)

    /**
     * 2024-11-01T19:43:46.115517
     */
    fun nowAsIsoDateTime(): String = LocalDateTime.now().format(DateTimeFormat.isoDateTimeWithTandZ)

    fun fromIsoToLocalDateTime(s: String): LocalDateTime = LocalDateTime.parse(s, DateTimeFormat.isoDateTimeWithTandZ)

    /**
     * @return if the passed String is 5 minutes ago, [Duration.toMinutes] will return a positive 5.
     */
    fun differenceToNowInIso(dateTime: LocalDateTime): Duration {
        val now = LocalDateTime.now()
        return Duration.between(dateTime, now)
    }

    /**
     * @return if the passed String is 5 minutes ago, [Duration.toMinutes] will return a positive 5.
     */
    fun differenceToNowInIso(iso: String): Duration {
        val now = LocalDateTime.now()
        val dateTime = LocalDateTime.parse(iso, DateTimeFormat.isoDateTimeWithTandZ)
        return Duration.between(dateTime, now)
    }

    fun isSameDay(date1: LocalDate, date2: LocalDateTime): Boolean {
        return date1.year == date2.year
                && date1.month == date2.month
                && date1.dayOfMonth == date2.dayOfMonth
    }

    @Suppress("unused")
    fun isSameDay(date1: LocalDateTime, date2: LocalDate): Boolean {
        return date1.year == date2.year
                && date1.month == date2.month
                && date1.dayOfMonth == date2.dayOfMonth
    }

    fun isSameDay(date1: LocalDateTime, date2: LocalDateTime): Boolean {
        return date1.year == date2.year
                && date1.month == date2.month
                && date1.dayOfMonth == date2.dayOfMonth
    }

    fun isSameDay(date1: LocalDate, date2: LocalDate): Boolean {
        return date1.year == date2.year
                && date1.month == date2.month
                && date1.dayOfMonth == date2.dayOfMonth
    }

    /**
     * @param today is just added to not calculate the today's date again
     */
    fun formatYesterdayOrRelative(date: LocalDateTime, today: LocalDate): String {
        val yesterday = today.minusDays(1)
        if (isSameDay(yesterday, date)) {
            return "gestern"
        }
        val period = Period.between(date.toLocalDate(), today)
        var periodString = periodToGermanString(period)
        if (periodString.endsWith("e")) {
            periodString += "n"
        }
        return "vor $periodString"
    }

    /**
     * @return "1 Jahr, 2 Monate, 3 Tage"
     */
    private fun periodToGermanString(period: Period): String {
        val parts = mutableListOf<String>()
        if (period.years != 0) {
            if (period.years == 1) {
                parts.add("1 Jahr")
            } else {
                parts.add("${period.years} Jahren")
            }
        }
        if (period.months != 0) {
            if (period.months == 1) {
                parts.add("1 Monat")
            } else {
                parts.add("${period.months} Monaten")
            }
        }
        if (period.days != 0) {
            if (period.days == 1) {
                parts.add("1 Tag")
            } else {
                parts.add("${period.days} Tagen")
            }
        }
        return parts.joinToString(", ")
    }

}