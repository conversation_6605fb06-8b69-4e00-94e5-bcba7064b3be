package dima.process.app

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Block
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.process.GlobalThreadEntry
import dima.process.ThreadStatus

@Composable
internal fun ProcessListUiThreadRow(
    threadEntry: GlobalThreadEntry,
    isSelected: Boolean,
    isRightSideSelected: Boolean
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .alpha(if (!isSelected && isRightSideSelected) 0.5f else 1f)
            .then(
                if (isSelected && isRightSideSelected) {
                    Modifier.background(
                        GlobalStyling.getSelectedInactiveBackgroundColor(),
                        GlobalStyling.smallRoundedCorners
                    )
                } else if (isSelected) {
                    Modifier.background(GlobalStyling.getSelectedBackgroundColor(), GlobalStyling.smallRoundedCorners)
                } else {
                    Modifier
                }
            )
            .padding(12.dp)
    ) {
        val width = 28.dp
        Row(
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.width(width)
        ) {
            when (threadEntry.status) {
                ThreadStatus.Running -> {
                    CircularProgressIndicator(
                        color = GlobalStyling.getGrayColor(),
                        strokeWidth = 3.dp,
                        modifier = Modifier
                            .size(20.dp)
                    )
                }

                ThreadStatus.Completed -> {
                    ProcessListUiGreenCheckmark()
                }

                ThreadStatus.Failed -> {
                    Icon(
                        imageVector = Icons.Default.Block,
                        contentDescription = "Failed",
                        tint = TailwindCssColors.red600,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
        SelectionContainer {
            Text(
                buildAnnotatedString {
                    append(
                        AnnotatedString(
                            "[Thread]",
                            spanStyle = SpanStyle(fontWeight = FontWeight.SemiBold)
                        )
                    )
                    append(" " + threadEntry.name)
                },
                color = GlobalStyling.getTextColor(),
                modifier = Modifier
                    .padding(start = 8.dp)
                    .weight(1f)
            )
        }
    }
}
