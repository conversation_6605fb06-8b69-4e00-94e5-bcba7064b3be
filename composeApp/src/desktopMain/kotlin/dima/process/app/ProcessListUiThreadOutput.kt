package dima.process.app

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.dired.addSelectedBorder
import dima.process.GlobalThreadEntry
import dima.utils.scrollbarStyleThemed

@Composable
internal fun RowScope.ProcessListUiThreadOutput(
    isRightSideSelected: Boolean,
    rightScrollState: ScrollState,
    color: Color,
    process: GlobalThreadEntry
) {
    Box(
        modifier = Modifier.weight(1f)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 12.dp)
                .addSelectedBorder(isRightSideSelected)
                .verticalScroll(state = rightScrollState)
                .padding(12.dp)
        ) {
            Text(
                "Thread",
                color = color,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            Text(
                "Name: ${process.name}",
                color = color,
            )
        }
        VerticalScrollbar(
            style = scrollbarStyleThemed(),
            adapter = rememberScrollbarAdapter(scrollState = rightScrollState),
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(
                    top = GlobalStyling.ScrollBar.outerPadding,
                    bottom = GlobalStyling.ScrollBar.outerPadding,
                    end = if (isRightSideSelected) GlobalStyling.ScrollBar.outerPadding else 0.dp
                ),
        )
    }
}
