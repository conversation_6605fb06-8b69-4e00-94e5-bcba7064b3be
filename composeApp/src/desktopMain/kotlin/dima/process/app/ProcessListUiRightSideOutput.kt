package dima.process.app

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.RowScope
import androidx.compose.runtime.Composable
import dima.process.GlobalThreadEntry
import dima.process.LoggedProcess

@Composable
internal fun RowScope.ProcessListUiRightSideOutput(
    process: Any,
    isRightSideSelected: Boolean,
    rightScrollState: ScrollState
) {
    val color = GlobalStyling.getTextColor()
    when (process) {
        is LoggedProcess -> {
            ProcessListUiLoggedProcessOutput(
                loggedProcess = process,
                isRightSideSelected = isRightSideSelected,
                rightScrollState = rightScrollState
            )
        }

        is GlobalThreadEntry -> {
            ProcessListUiThreadOutput(
                isRightSideSelected = isRightSideSelected,
                rightScrollState = rightScrollState,
                color = color,
                process = process
            )
        }
    }
}