package dima.process.app.tester

import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog

internal fun openCompletionDialogForCommands(
    onCommandChange: (String) -> Unit,
    appendToCommandLog: (String) -> Unit,
    startCurrentProcess: () -> Unit
) {
    // List of preset commands with descriptions
    val commandCandidates = listOf(
        // System information commands
        CompletionDialogCandidate("top -l 1", additionalSearchString = "system process info cpu memory"),
        CompletionDialogCandidate("ps aux | grep java", additionalSearchString = "java processes"),
        CompletionDialogCandidate("df -h", additionalSearchString = "disk space usage"),
        CompletionDialogCandidate("ls -la", additionalSearchString = "list files details"),

        // Network commands
        CompletionDialogCandidate("ping -c 10 google.com", additionalSearchString = "network ping google"),
        CompletionDialogCandidate("ping -c 50 google.com", additionalSearchString = "network ping google"),
        CompletionDialogCandidate("curl wttr.in", additionalSearchString = "weather forecast terminal"),
        CompletionDialogCandidate(
            "curl -s https://api.ipify.org",
            additionalSearchString = "show public ip address"
        ),
        CompletionDialogCandidate(
            "netstat -an | grep LISTEN",
            additionalSearchString = "network ports listening"
        ),

        // Fun visual commands
        CompletionDialogCandidate(
            "find / -name \"*.txt\" 2>/dev/null | head -n 20",
            additionalSearchString = "search text files"
        ),
        CompletionDialogCandidate("echo {1..100}", additionalSearchString = "print numbers sequence"),
        CompletionDialogCandidate(
            "seq 1 1000 | sort -R | head -n 9999",
            additionalSearchString = "random numbers"
        ),

        // Long-running commands
        CompletionDialogCandidate(
            "yes \"Hello, Process Tester!\"",
            additionalSearchString = "continuous output test"
        ),
        CompletionDialogCandidate(
            "ping google.com",
            additionalSearchString = "continuous ping until stopped"
        ),
        CompletionDialogCandidate(
            "while true; do date; sleep 1; done",
            additionalSearchString = "print date every second"
        ),

        // Commands with both stdout and stderr
        CompletionDialogCandidate(
            "ls /nonexistent 2>&1 && echo \"Success\"",
            additionalSearchString = "error output test"
        ),
        CompletionDialogCandidate(
            "find / -type f -name \"*.log\" 2>&1 | head -n 10",
            additionalSearchString = "find logs with errors"
        )
    )

    openCompletionDialog(
        title = "Select or enter a command to run",
        candidates = commandCandidates,
        allowMatchingLiterally = true,
        matchRequired = false,
        maxLinesPerCandidate = 1
    ) {
        onCommandChange(it.text)
        appendToCommandLog("\nCommand changed to: ${it.text}")
        // Start the process immediately after setting the new command
        startCurrentProcess()
    }
}
