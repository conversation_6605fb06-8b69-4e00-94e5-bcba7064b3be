package dima.process.app.tester

import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.sharp.Refresh
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.notifications.showErrorNotification
import dima.color.TailwindCssColors
import dialogs
import dima.process.LoggedProcess
import dima.utils.AppKey
import dima.utils.DummyFocusable
import dima.utils.scrollbarStyleThemed
import handleLostFocus
import kotlinx.coroutines.launch

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun ProcessTesterApp() {
    val coroutineScope = rememberCoroutineScope()
    val appFocusRequester = remember { FocusRequester() }

    // State for the process tester
    var command by remember { mutableStateOf("ls -la") }
    var isProcessRunning by remember { mutableStateOf(false) }
    var commandLog by remember { mutableStateOf("") }
    var processOutput by remember { mutableStateOf("") }

    // Process wrapper reference
    var loggedProcess by remember { mutableStateOf<LoggedProcess?>(null) }

    // Scroll state for the output
    val outputScrollState = rememberScrollState()
    val logScrollState = rememberScrollState()

    // Animation for the spinner
    val infiniteTransition = rememberInfiniteTransition()
    val angle by infiniteTransition.animateFloat(
        initialValue = 0F,
        targetValue = 360F,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing)
        )
    )

    // Function to append to command log
    fun appendToCommandLog(message: String) {
        commandLog += "$message\n"
        coroutineScope.launch {
            logScrollState.scrollTo(logScrollState.maxValue)
        }
    }

    fun startCurrentProcess() {
        if (isProcessRunning) {
            showErrorNotification("Process is already running")
            return
        }
        isProcessRunning = true
        processOutput = ""
        appendToCommandLog("Starting process: $command")

        coroutineScope.launch {
            loggedProcess = LoggedProcess(
                command = command,
                shell = true,
                onStdoutLine = { line: String ->
                    processOutput += "$line\n"
                    coroutineScope.launch {
                        outputScrollState.scrollTo(outputScrollState.maxValue)
                    }
                },
                onStderrLine = { line: String ->
                    processOutput += "[stderr] $line\n"
                    coroutineScope.launch {
                        outputScrollState.scrollTo(outputScrollState.maxValue)
                    }
                },
                onFinish = {
                    isProcessRunning = false
                    appendToCommandLog("Process completed with exit code: ${it.exitCode.value}")
                }
            )
            loggedProcess = loggedProcess!!.startAsync()
        }
    }

    // App keys for keyboard shortcuts
    val appKeys = remember {
        listOf(
            AppKey(Key.S, "Stop process") {
                if (isProcessRunning && loggedProcess != null) {
                    loggedProcess!!.kill()
                    appendToCommandLog("Process stopped by user")
                }
            },
            AppKey(Key.N, "New command and start") {
                openCompletionDialogForCommands(
                    onCommandChange = { command = it },
                    appendToCommandLog = ::appendToCommandLog,
                    startCurrentProcess = ::startCurrentProcess
                )
            }
        )
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxSize()
            .padding(12.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                it.handleAppMap(appKeys)
            }
    ) {
        DummyFocusable()
        // Title
        Text(
            "Process Tester",
            fontSize = 20.sp,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier
                .fillMaxWidth()
        )

        // Current command display
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                "Command:",
                fontWeight = FontWeight.SemiBold
            )
            Text(command)

            Spacer(modifier = Modifier.weight(1f))

            // Process status and spinner
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.height(24.dp) // Fixed height to prevent layout shifts
            ) {
                Text(
                    if (isProcessRunning) "Running" else "Stopped",
                    color = if (isProcessRunning) TailwindCssColors.green600 else TailwindCssColors.red600,
                    fontWeight = FontWeight.SemiBold
                )

                // Always include the icon space, but only show it when running
                Box(modifier = Modifier.size(20.dp)) {
                    if (isProcessRunning) {
                        Icon(
                            Icons.Sharp.Refresh,
                            contentDescription = "Loading",
                            tint = TailwindCssColors.gray600,
                            modifier = Modifier
                                .size(20.dp)
                                .rotate(angle)
                        )
                    }
                }
            }
        }

        // Command log
        Column(modifier = Modifier.weight(0.3f)) {
            Text(
                "Command Log:",
                fontWeight = FontWeight.SemiBold
            )
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .background(TailwindCssColors.gray100, RoundedCornerShape(5.dp))
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp)
                ) {
                    Text(
                        commandLog,
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(logScrollState)
                    )
                }
                VerticalScrollbar(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .fillMaxHeight()
                        .padding(end = 4.dp),
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = logScrollState)
                )
            }
        }

        // Process output
        Column(modifier = Modifier.weight(0.7f)) {
            Text(
                "Process Output:",
                fontWeight = FontWeight.SemiBold
            )
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight()
                    .background(TailwindCssColors.gray100, RoundedCornerShape(5.dp))
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp)
                ) {
                    Text(
                        processOutput,
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(outputScrollState)
                    )
                }
                VerticalScrollbar(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .fillMaxHeight()
                        .padding(end = 4.dp),
                    style = scrollbarStyleThemed(),
                    adapter = rememberScrollbarAdapter(scrollState = outputScrollState)
                )
            }
        }

        // Keyboard shortcuts help
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Keys: s (Stop) | n (New command & start)", color = TailwindCssColors.gray600)
        }
    }

    LaunchedEffect(Unit) {
        appFocusRequester.requestFocus()
        appendToCommandLog("Process Tester initialized")
        appendToCommandLog("Default command: $command")
    }
    LaunchedEffect(dialogs) {
        if (dialogs.isEmpty()) {
            appFocusRequester.requestFocus()
        }
    }

    // Auto-scroll command log and process output when content changes
    LaunchedEffect(commandLog) {
        coroutineScope.launch {
            logScrollState.scrollTo(logScrollState.maxValue)
        }
    }

    LaunchedEffect(processOutput) {
        coroutineScope.launch {
            outputScrollState.scrollTo(outputScrollState.maxValue)
        }
    }
}
