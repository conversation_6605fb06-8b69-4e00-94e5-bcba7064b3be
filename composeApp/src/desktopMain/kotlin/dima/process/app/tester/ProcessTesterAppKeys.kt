package dima.process.app.tester

import androidx.compose.ui.input.key.KeyEvent
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import dima.utils.AppKey

/**
 * Handle app key events.
 */
internal fun KeyEvent.handleAppMap(keys: List<AppKey>): Boolean {
    if (this.type != KeyEventType.KeyDown) {
        return false
    }
    keys.forEach {
        if (it.key == this.key) {
            it.onKeyDown?.invoke()
            return true
        }
    }
    return false
}
