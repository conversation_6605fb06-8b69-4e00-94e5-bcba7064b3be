package dima.process.app

import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import dima.process.LoggedProcess

private fun String.escapeForShell(): String =
    "'${this.replace("'", "'\"'\"'")}'"

internal fun openCopyFromProcessDialogForLoggedProcess(loggedProcess: LoggedProcess) {
    val modifier = Modifier.width(90.dp)
    val color = GlobalStyling.getTextColor()
    val parts: List<CompletionDialogCandidate> = buildList {
        add(
            CompletionDialogCandidate(
                "${loggedProcess.command.escapeForShell()} ${loggedProcess.args.joinToString(" ") { it.escapeForShell() }}",
                prefixView = {
                    Text(
                        "Full: ",
                        color = color,
                        fontWeight = FontWeight.Bold,
                        modifier = modifier
                    )
                },
                additionalSearchString = "Full"
            )
        )
        add(
            CompletionDialogCandidate(
                loggedProcess.command,
                prefixView = {
                    Text(
                        "Command: ",
                        color = color,
                        fontWeight = FontWeight.Bold,
                        modifier = modifier
                    )
                },
                additionalSearchString = "Command"
            )
        )
        if (loggedProcess.args.isNotEmpty()) {
            add(
                CompletionDialogCandidate(
                    loggedProcess.args.joinToString(" "),
                    prefixView = {
                        Text(
                            "Arguments: ",
                            color = color,
                            fontWeight = FontWeight.Bold,
                            modifier = modifier
                        )
                    },
                    additionalSearchString = "Arguments"
                )
            )
        }
        if (loggedProcess.exitCode.value != null) {
            add(
                CompletionDialogCandidate(
                    loggedProcess.exitCode.value.toString(),
                    prefixView = {
                        Text(
                            "Exit Code: ",
                            color = color,
                            fontWeight = FontWeight.Bold,
                            modifier = modifier
                        )
                    },
                    additionalSearchString = "Exit Code"
                )
            )
        }
        val stdout = loggedProcess.stdoutLines.value.joinToString("\n")
        val stderr = loggedProcess.stderrLines.value.joinToString("\n")
        var hasStdout = false
        var hasStderr = false
        if (stdout.isNotBlank()) {
            hasStdout = true
            add(
                CompletionDialogCandidate(
                    stdout,
                    prefixView = {
                        Text(
                            "Stdout: ",
                            color = color,
                            fontWeight = FontWeight.Bold,
                            modifier = modifier
                        )
                    },
                    additionalSearchString = "Stdout"
                )
            )
        }
        if (stderr.isNotBlank()) {
            hasStderr = true
            add(
                CompletionDialogCandidate(
                    stderr,
                    prefixView = {
                        Text(
                            "Stderr: ",
                            color = color,
                            fontWeight = FontWeight.Bold,
                            modifier = modifier
                        )
                    },
                    additionalSearchString = "Stderr"
                )
            )
        }
        if (hasStdout && hasStderr) {
            add(
                CompletionDialogCandidate(
                    loggedProcess.outputLines.value.joinToString("\n"),
                    prefixView = {
                        Text(
                            "Stdout + Stderr: ",
                            color = color,
                            fontWeight = FontWeight.Bold,
                            modifier = modifier
                        )
                    },
                    additionalSearchString = "Stdout + Stderr"
                )
            )
        }
    }

    openCompletionDialog(
        "Copy from process",
        parts
    ) { accept ->
        when (parts[accept.index!!].additionalSearchString) {
            "Full" -> copyToClipboard(
                "${loggedProcess.command.escapeForShell()} ${loggedProcess.args.joinToString(" ") { it.escapeForShell() }}"
            )

            "Command" -> copyToClipboard(loggedProcess.command)
            "Arguments" -> copyToClipboard(loggedProcess.args.joinToString(" "))
            "Exit Code" -> copyToClipboard(loggedProcess.exitCode.value.toString())
            "Stdout" -> copyToClipboard(loggedProcess.stdoutLines.value.joinToString("\n"))
            "Stderr" -> copyToClipboard(loggedProcess.stderrLines.value.joinToString("\n"))
            "Stdout + Stderr" -> copyToClipboard(loggedProcess.outputLines.value.joinToString("\n"))
        }
    }
}
