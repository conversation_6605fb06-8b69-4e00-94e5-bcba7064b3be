package dima.process.app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

@Composable
internal fun ProcessListUiGreenCheckmark() {
    Box(contentAlignment = Alignment.Center) {
        Box(
            modifier = Modifier
                // to set a white background below the checkmark
                .size(18.dp)
                .background(TailwindCssColors.white, CircleShape)
        )
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = "Completed",
            tint = if (GlobalState.isDarkMode) TailwindCssColors.green600 else TailwindCssColors.green700,
            modifier = Modifier
                .size(24.dp)
        )
    }
}