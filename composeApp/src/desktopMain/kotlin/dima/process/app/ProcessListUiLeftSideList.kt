package dima.process.app

import GlobalStyling
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import dima.process.GlobalThreadEntry
import dima.process.LoggedProcess
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.scrollbarStyleThemed

@Composable
internal fun RowScope.ProcessListUiLeftSideList(
    processes: List<Any>,
    selected: Int?,
    isRightSideSelected: Boolean,
    leftScrollState: LazyListState,
    onRowClick: (Int) -> Unit = {}
) {
    Box(
        modifier = Modifier
            .weight(1f)
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            state = leftScrollState,
            modifier = Modifier
                .fillMaxSize()
                .then(
                    if (leftScrollState.canScrollBackward || leftScrollState.canScrollForward) {
                        Modifier.padding(start = 12.dp)
                    } else {
                        Modifier
                    }
                )
        ) {
            items(processes.size) { index ->
                val process = processes[index]
                val processRowModifier = Modifier
                    .clickableWithoutBackgroundRipple {
                        onRowClick(index)
                    }
                when (process) {
                    is LoggedProcess -> Box(
                        modifier = processRowModifier
                    ) {
                        ProcessListUiLoggedProcessRow(
                            loggedProcess = process,
                            isSelected = index == selected,
                            isRightSideSelected = isRightSideSelected
                        )
                    }

                    is GlobalThreadEntry -> Box(
                        modifier = processRowModifier
                    ) {
                        ProcessListUiThreadRow(
                            threadEntry = process,
                            isSelected = index == selected,
                            isRightSideSelected = isRightSideSelected
                        )
                    }
                }
            }
        }
        VerticalScrollbar(
            style = scrollbarStyleThemed(),
            adapter = rememberScrollbarAdapter(scrollState = leftScrollState),
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(
                    top = GlobalStyling.ScrollBar.outerPadding,
                    bottom = GlobalStyling.ScrollBar.outerPadding,
                    end = GlobalStyling.ScrollBar.outerPadding
                ),
        )
    }
}
