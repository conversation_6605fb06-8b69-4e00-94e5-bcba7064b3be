package dima.process.app

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.process.LoggedProcess

@Composable
internal fun ProcessListUiLoggedProcessRow(
    loggedProcess: LoggedProcess,
    isSelected: Boolean,
    isRightSideSelected: Boolean
) {
    val isLoading by loggedProcess.isRunning.collectAsState()
    val statusColor = when (loggedProcess.exitCode.value) {
        null -> TailwindCssColors.gray400
        else -> TailwindCssColors.white
    }
    val statusBackground = when (loggedProcess.exitCode.value) {
        null -> TailwindCssColors.gray100
        else -> TailwindCssColors.red600
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .alpha(if (isSelected || !isRightSideSelected) 1f else GlobalStyling.DISABLED_ALPHA)
            .then(
                if (isSelected && isRightSideSelected) {
                    Modifier.background(
                        GlobalStyling.getSelectedInactiveBackgroundColor(),
                        GlobalStyling.smallRoundedCorners
                    )
                } else if (isSelected) {
                    Modifier.background(GlobalStyling.getSelectedBackgroundColor(), GlobalStyling.smallRoundedCorners)
                } else {
                    Modifier
                }
            )
            .padding(12.dp)
    ) {
        val width = 32.dp
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.width(width)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    color = GlobalStyling.getTextColor(),
                    strokeWidth = 3.dp,
                    modifier = Modifier.size(20.dp)
                )
            } else {
                if (loggedProcess.exitCode.value != null) {
                    if (loggedProcess.exitCode.value == 0) {
                        ProcessListUiGreenCheckmark()
                    } else {
                        Text(
                            loggedProcess.exitCode.value.toString(),
                            color = statusColor,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .background(statusBackground, CircleShape)
                                // set min width to be a circle, when exit code is 1
                                .widthIn(min = 22.dp)
                                .padding(3.dp)
                        )
                    }
                }
            }
        }

        SelectionContainer {
            Text(
                "${loggedProcess.command} ${loggedProcess.args.joinToString(" ")}",
                color = GlobalStyling.getTextColor(),
                modifier = Modifier
                    .padding(start = 8.dp)
                    .weight(1f)
            )
        }
    }
}
