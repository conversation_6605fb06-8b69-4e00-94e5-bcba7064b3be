package dima.process.app

import GlobalStyling
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import dima.apps.dired.addSelectedBorder
import dima.process.LoggedProcess
import dima.utils.scrollbarStyleThemed

@Composable
internal fun RowScope.ProcessListUiLoggedProcessOutput(
    loggedProcess: LoggedProcess,
    isRightSideSelected: Boolean,
    rightScrollState: ScrollState
) {
    val stdoutLines by loggedProcess.stdoutLines.collectAsState()
    val stderrLines by loggedProcess.stderrLines.collectAsState()
    Box(
        modifier = Modifier
            .weight(1f)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 12.dp)
                .addSelectedBorder(isRightSideSelected)
                .verticalScroll(state = rightScrollState)
                .padding(start = 12.dp, top = 12.dp, bottom = 12.dp, end = 20.dp)
        ) {
            val color = GlobalStyling.getTextColor()
            val stdoutJoined = stdoutLines.joinToString("\n")
            val hasStdout = stdoutJoined.isNotBlank()
            val stderrJoined = stderrLines.joinToString("\n")
            val hasStderr = stderrJoined.isNotBlank()
            if (hasStdout) {
                if (hasStderr) {
                    Text(
                        "Standard Output:",
                        color = color,
                        fontWeight = FontWeight.SemiBold
                    )
                }
                SelectionContainer {
                    Text(
                        stdoutJoined,
                        color = color,
                    )
                }
            }
            if (hasStderr) {
                if (hasStdout) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
                Text(
                    "Standard Error:",
                    color = color,
                    fontWeight = FontWeight.SemiBold
                )
                SelectionContainer {
                    Text(
                        stderrJoined,
                        color = GlobalStyling.getRedTextColor(),
                    )
                }
            }
        }
        VerticalScrollbar(
            style = scrollbarStyleThemed(),
            adapter = rememberScrollbarAdapter(scrollState = rightScrollState),
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(
                    top = GlobalStyling.ScrollBar.outerPadding,
                    bottom = GlobalStyling.ScrollBar.outerPadding,
                    end = if (isRightSideSelected) GlobalStyling.ScrollBar.outerPadding else 0.dp
                )
        )
    }
}
