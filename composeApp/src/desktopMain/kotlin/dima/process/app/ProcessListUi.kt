package dima.process.app

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

@Composable
internal fun ProcessListUiAny(
    processes: List<Any>,
    selected: Int?,
    isRightSideSelected: <PERSON>ole<PERSON>,
    rightScrollState: ScrollState,
    leftScrollState: LazyListState,
    onRowClick: (Int) -> Unit = {}
) {
    val selectedProcess = processes.getOrNull(selected ?: -1)
    Row(
        modifier = Modifier
            .fillMaxSize()
    ) {
        ProcessListUiLeftSideList(
            processes = processes,
            selected = selected,
            isRightSideSelected = isRightSideSelected,
            leftScrollState = leftScrollState,
            onRowClick = onRowClick
        )
        if (selectedProcess != null) {
            ProcessListUiRightSideOutput(
                process = selectedProcess,
                isRightSideSelected = isRightSideSelected,
                rightScrollState = rightScrollState
            )
        }
    }
}