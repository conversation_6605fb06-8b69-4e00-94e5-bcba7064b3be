package dima.process.app

import GlobalEvent
import GlobalStyling
import Globals.coroutineScope
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.dialogs.confirmation.openConfirmationDialog
import dialogs
import dima.dialogs.help.appKeys
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.process.*
import dima.utils.AppKey
import dima.utils.LaunchedEffectGlobalEventForApps
import dima.utils.ScrollVelocity
import dima.utils.handleAppMap
import globalEvent
import handleLostFocus
import kotlinx.coroutines.launch

// Helper to get a unique key for a ProcessUi item
private fun getItemKey(item: ProcessUi?): Pair<String, Int>? {
    return when (item) {
        is LoggedProcess -> "LoggedProcess" to item.id
        is GlobalThreadEntry -> "GlobalThreadEntry" to item.id
        else -> null
    }
}

@Composable
fun ProcessListApp() {
    val leftScrollState = rememberLazyListState()
    val rightScrollState = rememberScrollState()
    val rightScrollVelocity = remember { ScrollVelocity(rightScrollState) }
    var selectedIndex by remember { mutableStateOf<Int?>(null) }
    var selectedItemKey by remember { mutableStateOf<Pair<String, Int>?>(null) } // To store type and ID of the selected item
    var isRightSideSelected by remember { mutableStateOf(false) }
    val appFocusRequester = remember { FocusRequester() }
    val combinedProcesses: List<ProcessUi> by derivedStateOf {
        loggedProcesses + GlobalThreads.threads
    }

    remember {
        appKeys = listOf(
            AppKey(Key.Escape, "Deselect output text") {
                isRightSideSelected = false
            },
            AppKey(Key.Enter, "Select output text") {
                isRightSideSelected = true
            },
            AppKey(Key.J, "Copy from process") {
                if (selectedIndex == null) return@AppKey
                val process = combinedProcesses.getOrNull(selectedIndex!!)
                if (process == null) return@AppKey
                when (process) {
                    is LoggedProcess -> openCopyFromProcessDialogForLoggedProcess(process)
                    is GlobalThreadEntry -> copyToClipboard(process.name)
                }
            },
            AppKey(
                Key.C, "Go one process up or scroll up",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollUpKeyPressed()
                    } else {
                        if (selectedIndex != null && selectedIndex!! >= 1) {
                            selectedIndex = selectedIndex!! - 1
                        }
                    }
                }
            ),
            AppKey(
                Key.T, "Go one process down or scroll down",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollDownKeyPressed()
                    } else {
                        if (selectedIndex != null && selectedIndex!! + 1 < combinedProcesses.size) {
                            selectedIndex = selectedIndex!! + 1
                        }
                    }
                }
            ),
            AppKey(
                Key.M, "Go 6 processes down or scroll down more",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollDownMoreKeyPressed()
                    } else {
                        if (selectedIndex == null) {
                            return@AppKey
                        }
                        val newIndex = selectedIndex!! + 6
                        selectedIndex = newIndex.coerceAtMost(combinedProcesses.size - 1)
                    }
                }
            ),
            AppKey(
                Key.V, "Go 6 processes up or scroll up more",
                onKeyUp = {
                    rightScrollVelocity.onKeyReleased()
                },
                onKeyDown = {
                    if (isRightSideSelected) {
                        rightScrollVelocity.onScrollUpMoreKeyPressed()
                    } else {
                        if (selectedIndex == null) {
                            return@AppKey
                        }
                        val newIndex = selectedIndex!! - 6
                        selectedIndex = newIndex.coerceAtLeast(0)
                    }
                }
            ),
            AppKey(Key.D, "Kill selected process/thread") {
                if (selectedIndex == null) {
                    return@AppKey
                }
                val item = combinedProcesses.getOrNull(selectedIndex!!)
                when (item) {
                    is LoggedProcess -> {
                        if (item.isAlive()) {
                            openConfirmationDialog("Kill process?", item.command) {
                                coroutineScope.launch {
                                    item.kill()
                                    showNotification("Killed process", item.command, durationMillis = 1000)
                                }
                            }
                        } else {
                            showErrorNotification("Process is not running", durationMillis = 2000)
                        }
                    }

                    is GlobalThreadEntry -> {
                        openConfirmationDialog("Kill thread?", item.name) {
                            GlobalThreads.killThread(item.id)
                            showNotification("Killed thread", item.name, durationMillis = 1000)
                        }
                    }

                    null -> {}
                }
            }
        )
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
            .fillMaxSize()
            .padding(12.dp)
            .focusable()
            .focusRequester(appFocusRequester)
            .handleLostFocus()
            .onPreviewKeyEvent {
                return@onPreviewKeyEvent it.handleAppMap()
            }
    ) {
        Text(
            "Process List",
            fontSize = 20.sp,
            color = GlobalStyling.getTextColor(),
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier
                .focusable()
                .fillMaxWidth()
        )

        ProcessListUiAny(
            processes = combinedProcesses,
            selected = selectedIndex,
            isRightSideSelected = isRightSideSelected,
            rightScrollState = rightScrollState,
            leftScrollState = leftScrollState,
            onRowClick = { rowIndex ->
                if (!isRightSideSelected && selectedIndex != rowIndex) {
                    selectedIndex = rowIndex
                }
            }
        )

        // Compose best practice: All LaunchedEffect/side-effects at the bottom of the composable
        LaunchedEffect(Unit) {
            appFocusRequester.requestFocus()
            if (selectedIndex == null && combinedProcesses.isNotEmpty()) {
                selectedIndex = 0 // Initial selection
            }
            rightScrollVelocity.loopForeverAndTick()
        }

        // This effect updates selectedItemKey whenever selectedIndex changes.
        // This key is used to persist selection across combinedProcesses list changes.
        LaunchedEffect(
            selectedIndex,
            combinedProcesses
        ) { // combinedProcesses is needed to get the item at selectedIndex
            val currentItem = selectedIndex?.let { combinedProcesses.getOrNull(it) }
            val newKey = getItemKey(currentItem)
            if (selectedItemKey != newKey) {
                selectedItemKey = newKey
            }
        }

        // This effect tries to restore selection when combinedProcesses list changes
        LaunchedEffect(combinedProcesses) {
            if (combinedProcesses.isEmpty()) {
                if (selectedIndex != null) selectedIndex = null
                // selectedItemKey will be updated to null by the LaunchedEffect above
                return@LaunchedEffect
            }

            val newIdxToSelect = selectedItemKey?.let { key ->
                combinedProcesses.indexOfFirst { item -> getItemKey(item) == key }
            }

            if (newIdxToSelect != null && newIdxToSelect != -1) {
                // If the previously selected item (by key) is found, select it
                if (selectedIndex != newIdxToSelect) {
                    selectedIndex = newIdxToSelect
                }
            } else {
                // If previously selected item is not found (or no key was stored),
                // and the list is not empty, select the first item.
                // Avoid resetting if already null and list became non-empty (handled by initial selection)
                if (selectedIndex != 0) { // select 0 if not already 0, or if null
                    selectedIndex = 0
                }
            }
        }


        LaunchedEffect(selectedIndex) {
            if (selectedIndex == null) {
                return@LaunchedEffect
            }
            // Ensure selectedIndex is valid for the current combinedProcesses list before scrolling
            if (combinedProcesses.isNotEmpty() && selectedIndex!! >= 0 && selectedIndex!! < combinedProcesses.size) {
                val offset = (-GlobalState.mainWindow.heightInDp / 2).toInt()
                leftScrollState.animateScrollToItem(selectedIndex!!, scrollOffset = offset)
            }
        }

        LaunchedEffectGlobalEventForApps {
            when (globalEvent) {
                GlobalEvent.ScrollToTop -> {
                    if (isRightSideSelected) {
                        rightScrollVelocity.resetVelocity()
                        rightScrollState.animateScrollTo(0)
                    } else {
                        if (combinedProcesses.isNotEmpty()) selectedIndex = 0
                    }
                }

                GlobalEvent.ScrollToBottom -> {
                    if (isRightSideSelected) {
                        rightScrollVelocity.resetVelocity()
                        rightScrollState.animateScrollTo(rightScrollState.maxValue)
                    } else {
                        if (combinedProcesses.isNotEmpty()) selectedIndex = combinedProcesses.size - 1
                    }
                }

                else -> {}
            }
        }
        LaunchedEffect(dialogs) {
            if (dialogs.isEmpty()) {
                appFocusRequester.requestFocus()
            }
        }
    }
}