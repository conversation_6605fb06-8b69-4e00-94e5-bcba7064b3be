// LoggedProcess.kt
package dima.process

import Globals.coroutineScope
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import dima.apps.notifications.showErrorNotification
import dima.process.LoggedProcess.Companion.EXIT_CODE_ON_ERROR
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.io.InputStream
import java.io.IOException
import java.lang.ProcessBuilder.Redirect
import java.nio.charset.Charset
import java.util.*

sealed class ProcessUi

private var nextProcessId = 0
private val processIdMutex = Mutex()

internal fun getNextProcessId(): Int {
    return runBlocking {
        processIdMutex.withLock {
            nextProcessId++
        }
    }
}

/**
 * Index 0 has the most recent process.
 */
internal var loggedProcesses by mutableStateOf<List<LoggedProcess>>(emptyList())

private fun <T> InputStream.lineFlow(
    charset: Charset,
    block: (Flow<String>) -> T,
): T = bufferedReader(charset).use { reader ->
    try {
        reader.lineSequence().asFlow().let { flow ->
            block(flow)
        }
    } catch (e: IOException) {
        // throw on process kill
        throw e
    }
}

/**
 * @param shell if true, execute via /bin/sh -c "[command] [args]"
 * @param showErrorNotifications if true, shown after calling [onFinish]
 */
class LoggedProcess(
    val command: String,
    val args: List<String> = emptyList(),
    val workingDirectory: File? = null,
    val showErrorNotifications: Boolean = true,
    val ignoreStdout: Boolean = false,
    val ignoreStderr: Boolean = false,
    val onStdoutLine: ((String) -> Unit)? = null,
    val onStderrLine: ((String) -> Unit)? = null,
    val onOutputLine: ((String) -> Unit)? = null,
    val onFinish: ((LoggedProcess) -> Unit)? = null,
    val shell: Boolean = false,
    val charset: Charset = Charsets.UTF_8,
) : ProcessUi() {

    companion object {
        const val EXIT_CODE_ON_KILLED = -999
        const val EXIT_CODE_ON_ERROR = -99
    }

    val id: Int = getNextProcessId() // Made public
    val stdoutLines = MutableStateFlow<List<String>>(emptyList())
    val stderrLines = MutableStateFlow<List<String>>(emptyList())
    val outputLines = MutableStateFlow<List<String>>(emptyList())
    val exitCode = MutableStateFlow<Int?>(null)
    val isRunning = MutableStateFlow(false)
    private var processHandle: Process? = null
    private var wasKilled = false

    private fun handleProcessException(e: Exception) {
        if (showErrorNotifications) {
            showErrorNotification("Failed to execute process", e.message ?: "Unknown error")
        }
        this.exitCode.value = EXIT_CODE_ON_ERROR
        this.stderrLines.value = listOf(e.message ?: "Unknown error")
        this.isRunning.value = false
        loggedProcesses = if (loggedProcesses.none { it.id == this.id }) {
            listOf(this) + loggedProcesses
        } else {
            loggedProcesses.map {
                if (it.id == this.id) this else it
            }
        }
        onFinish?.invoke(this)
    }

    fun isAlive(): Boolean {
        return processHandle?.isAlive == true
    }

    /**
     * This does not block, the process is started in an IO coroutine.
     *
     * If the process fails to start, [EXIT_CODE_ON_ERROR] is returned and [outputLines] contains the error message like
     * "Cannot run program..."
     */
    fun startAsync(): LoggedProcess {
        return try {
            val processBuilder: ProcessBuilder

            if (shell) {
                // Use shell to interpret the command with all its arguments
                // This allows for shell operators like |, >, <, &&, etc.
                val fullCommand = if (args.isEmpty()) command else "$command ${args.joinToString(" ")}"
                processBuilder = ProcessBuilder("/bin/sh", "-c", fullCommand)
                    .redirectOutput(Redirect.PIPE)
                    .redirectError(Redirect.PIPE)
            } else {
                // Standard execution without shell interpretation
                processBuilder = ProcessBuilder(command, *args.toTypedArray())
                    .redirectOutput(Redirect.PIPE)
                    .redirectError(Redirect.PIPE)
            }

            if (workingDirectory != null) {
                processBuilder.directory(workingDirectory)
            }

            // Launch process creation in a coroutine
            coroutineScope.launch(Dispatchers.IO) {
                try {
                    processHandle = processBuilder.start()
                    isRunning.value = true
                    loggedProcesses = listOf(this@LoggedProcess) + loggedProcesses
                    val stdoutCollector = mutableListOf<String>()
                    val stderrCollector = mutableListOf<String>()
                    val outputCollector = Collections.synchronizedList(mutableListOf<String>())
                    if (!ignoreStdout) {
                        async {
                            try {
                                processHandle!!.inputStream.lineFlow(charset) {
                                    runBlocking {
                                        it.collect { line ->
                                            onStdoutLine?.invoke(line)
                                            stdoutCollector.add(line)
                                            stdoutLines.value = stdoutCollector.toList()
                                            synchronized(outputCollector) {
                                                onOutputLine?.invoke(line)
                                                outputCollector.add(line)
                                                outputLines.value = outputCollector.toList()
                                            }
                                        }
                                    }
                                }
                            } catch (_: IOException) {
                                // ignore on process kill
                            }
                        }
                    }
                    if (!ignoreStderr) {
                        async {
                            try {
                                processHandle!!.errorStream.lineFlow(charset) {
                                    runBlocking {
                                        it.collect { line ->
                                            onStderrLine?.invoke(line)
                                            stderrCollector.add(line)
                                            stderrLines.value = stderrCollector.toList()
                                            synchronized(outputCollector) {
                                                onOutputLine?.invoke(line)
                                                outputCollector.add(line)
                                                outputLines.value = outputCollector.toList()
                                            }
                                        }
                                    }
                                }
                            } catch (_: IOException) {
                                // ignore on process kill
                            }
                        }
                    }

                    // Wait for process to complete if not already killed
                    val exitCodeValue = if (wasKilled) {
                        EXIT_CODE_ON_KILLED
                    } else {
                        try {
                            processHandle!!.waitFor()
                        } catch (_: Exception) {
                            EXIT_CODE_ON_KILLED
                        }
                    }

                    exitCode.value = exitCodeValue
                    isRunning.value = false

                    // Update the instance in the global list (by replacing it with itself after updates)
                    // This ensures that any observers of loggedProcesses see the updated state.
                    loggedProcesses = loggedProcesses.map {
                        if (it.id == <EMAIL>) this@LoggedProcess else it
                    }

                    onFinish?.invoke(this@LoggedProcess)
                    if (showErrorNotifications && exitCodeValue != 0) {
                        val outputSubTitle = if (outputLines.value.isEmpty()) {
                            ""
                        } else {
                            val output = outputLines.value.joinToString("\n").trim()
                            if (output == "") {
                                ""
                            } else {
                                "\n" + output
                            }
                        }
                        showErrorNotification(
                            "Process failed: $command",
                            "Exit code: $exitCodeValue$outputSubTitle"
                        )
                    }
                } catch (e: Exception) {
                    outputLines.value = listOf(e.message ?: "Unknown error")
                    // Handle process creation error
                    handleProcessException(e)
                }
            }
            this
        } catch (e: Exception) {
            handleProcessException(e)
            this
        }
    }

    /**
     * Kills the process if it's running.
     */
    fun kill() {
        processHandle?.let {
            if (it.isAlive) {
                wasKilled = true
                isRunning.value = false
                it.destroy()
            }
        }
    }

    /**
     * Forcibly kills the process if it's running.
     */
    @Suppress("unused")
    fun forceKill() {
        processHandle?.let {
            if (it.isAlive) {
                wasKilled = true
                isRunning.value = false
                it.destroyForcibly()
            }
        }
    }

    @Suppress("unused")
    fun getStdout(): String = stdoutLines.value.joinToString("\n")

    @Suppress("unused")
    fun getStderr(): String = stderrLines.value.joinToString("\n")
    fun getOutput(): String = outputLines.value.joinToString("\n")
}