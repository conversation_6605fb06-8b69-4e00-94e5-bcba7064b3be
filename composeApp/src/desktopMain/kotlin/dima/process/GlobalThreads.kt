package dima.process

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import java.util.concurrent.atomic.AtomicInteger
import kotlin.concurrent.thread

enum class ThreadStatus { Running, Completed, Failed }

/**
 * Represents a tracked background thread for display/kill.
 */
data class GlobalThreadEntry(
    val id: Int,
    val name: String,
    val thread: Thread,
    var status: ThreadStatus = ThreadStatus.Running,
    var exception: Throwable? = null
) : ProcessUi()

object GlobalThreads {
    private val nextId = AtomicInteger(1)
    var threads by mutableStateOf<List<GlobalThreadEntry>>(emptyList())
        private set

    /**
     * Run [block] in a new thread and start it.
     */
    fun register(
        name: String,
        onException: ((Throwable) -> Unit)? = null,
        onFinish: (() -> Unit)? = null,
        block: () -> Unit
    ): GlobalThreadEntry {
        val id = nextId.getAndIncrement()
        val entry = GlobalThreadEntry(
            id = id,
            name = name,
            thread = thread {
                try {
                    block()
                    onFinish?.invoke()
                    updateStatus(id, ThreadStatus.Completed)
                } catch (e: Exception) {
                    onException?.invoke(e)
                    updateException(id, e)
                    updateStatus(id, ThreadStatus.Failed)
                }
            }
        )
        threads = threads + entry
        return entry
    }

    private fun updateException(id: Int, exception: Throwable) {
        threads = threads.map {
            if (it.id == id) {
                it.copy(exception = exception)
            } else {
                it
            }
        }
    }

    private fun updateStatus(id: Int, status: ThreadStatus) {
        threads = threads.map {
            if (it.id == id) {
                it.copy(status = status)
            } else {
                it
            }
        }
    }

    fun unregister(thread: Thread) {
        threads = threads.filterNot { it.thread == thread }
    }

    fun killThread(id: Int) {
        threads.find { it.id == id }?.thread?.interrupt()
    }
}
