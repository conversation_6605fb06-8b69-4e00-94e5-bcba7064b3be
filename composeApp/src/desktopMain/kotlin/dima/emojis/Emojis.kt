package dima.emojis

import androidx.compose.material.Text
import androidx.compose.ui.unit.sp
import dima.dialogs.completion.CompletionDialogCandidate
import dima.dialogs.completion.openCompletionDialog
import dima.os.copyToClipboard
import dima.utils.RecentHistory
import dima.utils.sortedByHistory

fun openDialogToCopyEmojis() {
    val emojis = listOf(
        // the 6 universal expressions
        "😄" to "smile",
        "😮" to "surprise",
        "😢" to "sad",
        "😠" to "anger",
        "😑" to "disgust",
        "😱" to "fear",

        "🙂" to "slight smile",
        "😇" to "holy",
        "😅" to "sweat smile",
        "😉" to "wink",
        "😛" to "tongue",
        "👍" to "thumbs up",
        "👎" to "thumbs down",
        "🍋" to "lemon/zitrone",
        "🎉" to "celebration",
        "🍌" to "banana",
        "😞" to "disappointed",
        "😕" to "bad",
        "❤️" to "heart",
        "😘" to "kiss",
        "💦" to "water",
        "🇩🇪" to "germany/deutschland",
        "🇺🇸" to "usa/amerika/english",
        "🤨" to "eyebrow",

        "…" to "ellipsis/dots",
        "✅" to "checkmark",
        "❌" to "cross / x",
        // note that this incorrectly rendered with the default font, it does not have the yellow background,
        // but is fine on pasting
        "⚠️" to "warning",
        "❓" to "question mark"
    )
    val recentTexts = RecentHistory.getRecentTexts()
    val emojiTexts = emojis.map {
        it.second
    }.sortedByHistory(recentTexts)
    val emojisSorted = emojiTexts.map { text ->
        emojis.find {
            it.second == text
        }!!
    }
    val candidates = emojisSorted.map { emoji ->
        CompletionDialogCandidate(
            emoji.second,
            prefixView = {
                Text(emoji.first, fontSize = 22.sp)
            })
    }
    openCompletionDialog(
        "Copy emoji",
        candidates,
    ) {
        val raw = emojisSorted[it.index!!]
        val emoji = raw.first
        val emojiText = raw.second
        RecentHistory.rememberText(emojiText)
        copyToClipboard(emoji)
    }
}