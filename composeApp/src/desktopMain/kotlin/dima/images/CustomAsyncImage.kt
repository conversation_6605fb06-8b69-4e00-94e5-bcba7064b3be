package dima.images

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.graphics.toComposeImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import dima.apps.notifications.showErrorNotification
import dima.utils.Ring
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.awt.Image
import java.awt.image.BufferedImage
import java.io.File
import java.io.IOException
import javax.imageio.ImageIO
import kotlin.math.max

private val imageCache = Ring<CacheEntry>(300)

fun clearCustomAsyncImageCache() {
    imageCache.clear()
}

data class CacheEntry(
    val filePath: String,
    val width: Int,
    val height: Int,
    val image: ImageBitmap
)

/**
 * Avoid out of memory exceptions which happen if the full size images are displayed.
 */
private fun loadResizedImage(filePath: String, targetWidth: Int, targetHeight: Int): ImageBitmap {
    // Check if the image is already cached
    val cachedImage = imageCache.find {
        it.filePath == filePath && it.width == targetWidth && it.height == targetHeight
    }
    if (cachedImage != null) {
        return cachedImage.image
    }

    // If not cached, proceed with loading and resizing
    val originalImage: BufferedImage? = ImageIO.read(File(filePath))
    if (originalImage == null) {
//        showErrorNotification("Failed to read image", filePath, durationMillis = 1000)
        return ImageBitmap(1, 1)
    }
    val aspectRatio = max(
        originalImage.width.toDouble() / targetWidth.toDouble(),
        originalImage.height.toDouble() / targetHeight.toDouble()
    )
    val scaledWidth = (originalImage.width / aspectRatio).toInt()
    val scaledHeight = (originalImage.height / aspectRatio).toInt()
    val resizedImage = BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_ARGB)
    val graphics = resizedImage.createGraphics()
    graphics.drawImage(
        originalImage.getScaledInstance(scaledWidth, scaledHeight, Image.SCALE_SMOOTH),
        0,
        0,
        null
    )
    graphics.dispose()
    val imageBitmap = resizedImage.toComposeImageBitmap()
    imageCache.add(CacheEntry(filePath, targetWidth, targetHeight, imageBitmap))
    return imageBitmap
}

data class ImageDisplaySize(val width: Int, val height: Int)

/**
 * Load the [fileSystemUrl] image from the file system in a coroutine.
 *
 * @param modifier do not set size here, but set [size]
 */
@Composable
fun CustomAsyncImage(
    fileSystemUrl: String,
    contentScale: ContentScale = ContentScale.Fit,
    displaySize: ImageDisplaySize? = null,
    size: Dp? = null,
    modifier: Modifier = Modifier,
) {
    var image: ImageBitmap? by remember { mutableStateOf(null) }

    if (image == null) {
        if (size != null) {
            Spacer(modifier = modifier.size(size))
        }
    } else {
        Image(
            painter = BitmapPainter(image!!),
            contentDescription = null,
            contentScale = contentScale,
            modifier = modifier.then(
                if (size == null) {
                    Modifier
                } else {
                    Modifier.size(size)
                }
            ),
        )
    }

    LaunchedEffect(Unit, fileSystemUrl) {
        image = withContext(Dispatchers.IO) {
            val file = File(fileSystemUrl)
            if (!file.exists()) {
                showErrorNotification("File does not exist for display as image", fileSystemUrl)
                return@withContext null
            }
            try {
                if (displaySize == null) {
                    ImageIO.read(file).toComposeImageBitmap()
                } else {
                    loadResizedImage(fileSystemUrl, displaySize.width, displaySize.height)
                }
            } catch (_: IOException) {
//                showErrorNotification("Failed to read image", fileSystemUrl, durationMillis = 1000)
                null
            }
        }
    }
}