package dima.images

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.networkActivity.NetworkActivity.updateFaked
import dima.apps.notifications.NotificationType
import dima.apps.notifications.showNotification
import dima.color.TailwindCssColors
import dima.globalState.GlobalState
import dima.utils.asSha256Hash
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.skia.Bitmap
import org.jetbrains.skia.ColorAlphaType
import org.jetbrains.skia.ImageInfo
import org.jetbrains.skiko.toBufferedImage
import java.awt.image.BufferedImage
import java.io.File
import java.io.IOException
import java.net.MalformedURLException
import java.net.URI
import java.net.URISyntaxException
import javax.imageio.IIOException
import javax.imageio.ImageIO
import javax.net.ssl.SSLException

/**
 * [closeToWhiteBackground] and [closeToDarkBackground] are for dark mode.
 */
private data class ConvertedImage(
    val closeToWhiteBackground: Boolean,
    val closeToDarkBackground: Boolean,
    val image: ImageBitmap
)

/**
 * Loads the image from cache if stored on the file system, otherwise loads it from the network.
 * The image is always stored as a PNG on the file system.
 */
@Composable
fun CachedInternetImage(
    imageUrl: String,
    cacheDir: File,
    cacheFilePrefix: String? = null,
    colorFilter: ColorFilter? = null,
    logCacheDir: String? = null,
    contentScale: ContentScale = ContentScale.Fit,
    displayCheckerboardWhenCloseToWhite: Boolean = false,
    loadImageOnMainThread: Boolean = false,
    modifier: Modifier = Modifier,
) {
    var image: ConvertedImage? by remember {
        val state = if (loadImageOnMainThread) {
            loadImageBitmap(
                imageUrl = imageUrl,
                cacheDir = cacheDir,
                displayCheckerboardWhenCloseToWhite = displayCheckerboardWhenCloseToWhite,
                logCacheDir = logCacheDir,
                cacheFilePrefix = cacheFilePrefix
            )
        } else {
            null
        }
        mutableStateOf(state)
    }

    if (image == null) {
        // This is a hack for ChatGptCard to allow the modifier to set a size. Otherwise, when the image is not yet loaded,
        // it does not take away any space which flickers the layout after the image is quickly loaded in
        Row(modifier = modifier) {}
    } else {
        Box {
            Image(
                painter = BitmapPainter(image!!.image),
                contentDescription = null,
                contentScale = contentScale,
                modifier = modifier.then(
                    if (GlobalState.isDarkMode) {
                        if (image!!.closeToDarkBackground) {
                            checkerboardModifier(
                                4.dp,
                                TailwindCssColors.gray200,
                                TailwindCssColors.gray300
                            )
                        } else {
                            Modifier
                        }
                    } else {
                        if (image!!.closeToWhiteBackground) {
                            checkerboardModifier(
                                4.dp,
                                TailwindCssColors.gray400,
                                TailwindCssColors.gray500
                            )
                        } else {
                            Modifier
                        }
                    }
                ),
                colorFilter = colorFilter
            )
        }
    }

    LaunchedEffect(Unit, imageUrl) {
        if (!loadImageOnMainThread) {
            image = withContext(Dispatchers.IO) {
                loadImageBitmap(
                    imageUrl = imageUrl,
                    cacheDir = cacheDir,
                    displayCheckerboardWhenCloseToWhite = displayCheckerboardWhenCloseToWhite,
                    logCacheDir = logCacheDir,
                    cacheFilePrefix = cacheFilePrefix
                )
            }
        }
    }
}

private fun loadImageBitmap(
    imageUrl: String,
    cacheDir: File,
    displayCheckerboardWhenCloseToWhite: Boolean,
    logCacheDir: String?,
    cacheFilePrefix: String?
): ConvertedImage {
    val hash = imageUrl.asSha256Hash()
    val cachedImageFile = File(
        cacheDir,
        if (cacheFilePrefix == null) {
            "$hash.png"
        } else {
            "$cacheFilePrefix$hash.png"
        }
    )
    if (cachedImageFile.exists()) {
        try {
            if (displayCheckerboardWhenCloseToWhite) {
                val converted = ImageIO.read(cachedImageFile).toComposeImageBitmapConverted()
                return converted
            }
            return ConvertedImage(
                closeToWhiteBackground = false,
                closeToDarkBackground = false,
                ImageIO.read(cachedImageFile).toComposeImageBitmap()
            )
        } catch (e: IOException) {
            NetworkActivity.addFakedEntry(
                "Failed to read existing downloaded cached image from disk",
                status = FakedNetworkActivityStatus.Error,
                fullUrl = cachedImageFile.absolutePath,
                responseBody = "IOException: ${e.message}"
            )
            return ConvertedImage(
                closeToWhiteBackground = false,
                closeToDarkBackground = false,
                ImageBitmap(1, 1)
            )
        }
    }
    val textCacheDir = logCacheDir ?: cacheDir.absolutePath
    val log = NetworkActivity.addFakedEntry(
        "Image cache: $textCacheDir",
        FakedNetworkActivityStatus.InTransit,
        fullUrl = imageUrl
    )
    try {
        val url = URI(imageUrl).toURL()
        // can be null on unknown image formats, see https://github.com/haraldk/TwelveMonkeys
        val bufferedImage = ImageIO.read(url)
        if (bufferedImage == null) {
            log.updateFaked(
                status = FakedNetworkActivityStatus.Error,
                responseBody = "ImageIO.read() returns null - Is this even an image? $imageUrl"
            )
            return ConvertedImage(
                closeToWhiteBackground = false,
                closeToDarkBackground = false,
                ImageBitmap(1, 1)
            )
        } else {
            log.updateFaked(status = FakedNetworkActivityStatus.Success)
        }
        val downloadedImage: ConvertedImage = if (displayCheckerboardWhenCloseToWhite) {
            bufferedImage.toComposeImageBitmapConverted()
        } else {
            ConvertedImage(
                closeToWhiteBackground = false,
                closeToDarkBackground = false,
                bufferedImage.toComposeImageBitmap()
            )
        }
        // use PNG, since other formatNames do not work
        val success = ImageIO.write(downloadedImage.image.asSkiaBitmap().toBufferedImage(), "PNG", cachedImageFile)
        if (!success) {
            showNotification(
                "Failed to write PNG image to cache directory!",
                imageUrl,
                type = NotificationType.Error
            )
        }
        return downloadedImage
    } catch (e: ArrayIndexOutOfBoundsException) {
        // java.lang.ArrayIndexOutOfBoundsException: Index -1 out of bounds for length 256
        // at java.desktop/com.sun.imageio.plugins.png.PNGImageWriter.encodePass(PNGImageWriter.java:1002)
        log.updateFaked(
            status = FakedNetworkActivityStatus.Error,
            responseBody = "Failed to write PNG image to cache directory: ${e.message}"
        )
        return ConvertedImage(
            closeToWhiteBackground = false,
            closeToDarkBackground = false,
            ImageBitmap(1, 1)
        )
    } catch (e: IIOException) {
        // for .read()
        log.updateFaked(
            status = FakedNetworkActivityStatus.Error,
            responseBody = "Image URL failed to parse as image: ${e.message}"
        )
        return ConvertedImage(
            closeToWhiteBackground = false,
            closeToDarkBackground = false,
            ImageBitmap(1, 1)
        )
    } catch (e: IllegalArgumentException) {
        log.updateFaked(
            status = FakedNetworkActivityStatus.Error,
            responseBody = "Image URL is not absolute: ${e.message}"
        )
        // some spam emails have URLs like "foo.png"
        return ConvertedImage(
            closeToWhiteBackground = false,
            closeToDarkBackground = false,
            ImageBitmap(1, 1)
        )
    } catch (e: MalformedURLException) {
        log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = "Malformed URL: ${e.message}")
        // can happen for email images which start with cid:
        return ConvertedImage(
            closeToWhiteBackground = false,
            closeToDarkBackground = false,
            ImageBitmap(1, 1)
        )
    } catch (e: URISyntaxException) {
        log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = "URISyntaxException: ${e.message}")
        // can happen for email images
        return ConvertedImage(
            closeToWhiteBackground = false,
            closeToDarkBackground = false,
            ImageBitmap(1, 1)
        )
    } catch (e: SSLException) {
        log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = "SSLException: ${e.message}")
        // can happen on very bad network connection, like on Wi-Fi
        return ConvertedImage(
            closeToWhiteBackground = false,
            closeToDarkBackground = false,
            ImageBitmap(1, 1)
        )
    } catch (e: IOException) {
        log.updateFaked(status = FakedNetworkActivityStatus.Error, responseBody = "IOException: ${e.message}")
        if (e.message != null && e.message!!.contains("Can't get input stream from URL!")) {
            return ConvertedImage(
                closeToWhiteBackground = false,
                closeToDarkBackground = false,
                ImageBitmap(1, 1)
            )
        }
        throw e
    }
}

/**
 * Copied from [BufferedImage.toComposeImageBitmap].
 */
private fun BufferedImage.toComposeImageBitmapConverted(): ConvertedImage {
    val bytesPerPixel = 4
    val pixels = ByteArray(width * height * bytesPerPixel)
    var k = 0
    var closeToWhiteBackground = true
    var closeToDarkBackground = false
    for (y in 0 until height) {
        for (x in 0 until width) {
            val argb = getRGB(x, y)
            val a = (argb shr 24) and 0xff
            val r = (argb shr 16) and 0xff
            val g = (argb shr 8) and 0xff
            val b = (argb shr 0) and 0xff
            if (a == 0xff) {
                if (r <= 200 || g <= 200 || b <= 200) {
                    closeToWhiteBackground = false
                }
                // this is not perfect, it fails on values like rgb(10,30,130) which is a very dark blue
                // but it costs too much to calculate the luminance for each pixel
                if (r < 70 && g < 70 && b < 70) {
//                if (r < 50 || g < 50 || b < 50) {
                    closeToDarkBackground = true
                }
            }
            pixels[k++] = b.toByte()
            pixels[k++] = g.toByte()
            pixels[k++] = r.toByte()
            pixels[k++] = a.toByte()
        }
    }
    val bitmap = Bitmap()
    bitmap.allocPixels(ImageInfo.makeS32(width, height, ColorAlphaType.UNPREMUL))
    bitmap.installPixels(pixels)
    return ConvertedImage(
        closeToWhiteBackground = closeToWhiteBackground,
        closeToDarkBackground = closeToDarkBackground,
        image = bitmap.asComposeImageBitmap()
    )
}