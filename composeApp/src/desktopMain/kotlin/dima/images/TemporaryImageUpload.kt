package dima.images

import dima.apps.networkActivity.submitFormWithBinaryDataLogged
import dima.utils.Result
import dima.utils.createHttpClientWithLocalhostProxy
import io.ktor.client.request.forms.*
import io.ktor.http.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.io.File

object TemporaryFileUploader {

    /**
     * Uploads a file to tmpfiles.org (https://tmpfiles.org/api/v1/upload) and returns the resulting download URL.
     *
     * API: POST https://tmpfiles.org/api/v1/upload
     * Form-data param: file=<file>
     * Returns JSON: { "data": { "url": "...", "url_short": "...", ... } }
     */
    suspend fun uploadFile(file: File): Result<String> {
        require(file.exists()) { "File does not exist: $file" }
        val client = createHttpClientWithLocalhostProxy()
        try {
            val response = client.submitFormWithBinaryDataLogged(
                url = "https://tmpfiles.org/api/v1/upload",
                formData = formData {
                    append("file", file.readBytes(), Headers.build {
                        append(HttpHeaders.ContentDisposition, "form-data; name=\"file\"; filename=\"${file.name}\"")
                    })
                }
            )
            if (response.statusCode != 200) {
                return Result.Error("Failed to upload file to tmpfiles.org: ${response.body}")
            }
            val json = runCatching { Json.parseToJsonElement(response.body).jsonObject }.getOrNull()
            val url: String? = json?.get("data")?.jsonObject?.get("url")?.jsonPrimitive?.content
            if (url == null) {
                return Result.Error("Failed to parse JSON response from tmpfiles.org: ${response.body}")
            }
            return Result.Success(url.replace("https://tmpfiles.org/", "https://tmpfiles.org/dl/"))
        } catch (e: Exception) {
            return Result.Error(e.message ?: "Unknown error")
        } finally {
            client.close()
        }
    }
}

