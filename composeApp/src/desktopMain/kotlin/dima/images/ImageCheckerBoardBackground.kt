package dima.images

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp

internal fun checkerboardModifier(size: Dp, color1: Color, color2: Color): Modifier {
    return Modifier.drawBehind {
        val w = this.size.width
        val h = this.size.height
        val cellSize = size.toPx()
        for (row in 0 until (h / cellSize).toInt()) {
            for (col in 0 until (w / cellSize).toInt()) {
                val color = if ((row + col) % 2 == 0) color1 else color2
                drawRect(
                    color = color,
                    topLeft = Offset(x = col * cellSize, y = row * cellSize),
                    size = Size(width = cellSize, height = cellSize)
                )
            }
        }
        // Draw partial cells at the edges
        val remainderWidth = w % cellSize
        if (remainderWidth > 0) {
            for (row in 0 until (h / cellSize).toInt()) {
                val color = if ((row + (w / cellSize).toInt()) % 2 == 0) color1 else color2
                drawRect(
                    color = color,
                    topLeft = Offset(x = w - remainderWidth, y = row * cellSize),
                    size = Size(width = remainderWidth, height = cellSize)
                )
            }
        }
        val remainderHeight = h % cellSize
        if (remainderHeight > 0) {
            for (col in 0 until (w / cellSize).toInt()) {
                val color = if (((h / cellSize).toInt() + col) % 2 == 0) color1 else color2
                drawRect(
                    color = color,
                    topLeft = Offset(x = col * cellSize, y = h - remainderHeight),
                    size = Size(width = cellSize, height = remainderHeight)
                )
            }
        }
        // Corner cell
        if (remainderWidth > 0 && remainderHeight > 0) {
            val color = if (((h / cellSize).toInt() + (w / cellSize).toInt()) % 2 == 0) color1 else color2
            drawRect(
                color = color,
                topLeft = Offset(x = w - remainderWidth, y = h - remainderHeight),
                size = Size(width = remainderWidth, height = remainderHeight)
            )
        }
    }
}
