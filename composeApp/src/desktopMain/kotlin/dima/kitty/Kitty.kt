package dima.kitty

import Globals
import com.github.pgreze.process.process
import dima.apps.notifications.showErrorNotification
import dima.os.Hammerspoon
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

/**
 * https://github.com/kovidgoyal/kitty
 */
object Kitty {

    fun openNewTabInWorkingDirectory(path: File) {
        Globals.coroutineScope.launch(Dispatchers.IO) {
            val p = process(
                "kitten",
                "@", "--to", "tcp:localhost:50005", "launch", "--type=tab", "--cwd", path.absolutePath
            )
            if (p.resultCode == 0) {
                Hammerspoon.focusApp("kitty")
            } else {
                showErrorNotification(
                    "Failed to launch kitty shell via kitten",
                    p.output.joinToString("\n")
                )
            }
        }
    }

}