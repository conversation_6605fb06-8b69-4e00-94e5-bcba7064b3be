package dima.markdownRendererLibraryPatches

import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onPlaced
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.toSize
import com.mikepenz.markdown.annotator.annotatorSettings
import com.mikepenz.markdown.compose.*
import com.mikepenz.markdown.compose.elements.createImageInlineTextContent
import com.mikepenz.markdown.compose.extendedspans.ExtendedSpans
import com.mikepenz.markdown.compose.extendedspans.drawBehind
import com.mikepenz.markdown.utils.MARKDOWN_TAG_IMAGE_URL
import org.intellij.markdown.IElementType
import org.intellij.markdown.ast.ASTNode
import org.intellij.markdown.ast.findChildOfType

/**
 * Copied from [com.mikepenz.markdown.compose.elements.MarkdownText].
 *
 * Patches:
 * - use [MyMarkdownText] instead
 */
@Composable
fun MyMarkdownText(
    content: String,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalMarkdownTypography.current.text,
) {
    MyMarkdownText(AnnotatedString(content), modifier, style)
}

/**
 * Copied from [com.mikepenz.markdown.compose.elements.MarkdownText].
 *
 * Patches:
 * - use [MyMarkdownText] instead
 * - use [buildMyMarkdownAnnotatedString] instead
 */
@Composable
fun MyMarkdownText(
    content: String,
    node: ASTNode,
    style: TextStyle,
    modifier: Modifier = Modifier,
    contentChildType: IElementType? = null,
) {
    val annotatorSettings = annotatorSettings()
    val childNode = contentChildType?.run(node::findChildOfType) ?: node

    val styledText = buildAnnotatedString {
        pushStyle(style.toSpanStyle())
        buildMyMarkdownAnnotatedString(
            content = content,
            node = childNode,
            annotatorSettings = annotatorSettings
        )
        pop()
    }

    MyMarkdownText(styledText, modifier = modifier, style = style)
}

/**
 * Copied from [com.mikepenz.markdown.compose.elements.MarkdownText].
 *
 * Patches:
 * - use [MyMarkdownText] instead
 */
@Composable
fun MyMarkdownText(
    content: AnnotatedString,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalMarkdownTypography.current.text,
    extendedSpans: ExtendedSpans? = LocalMarkdownExtendedSpans.current.extendedSpans?.invoke(),
) {
    // extend the annotated string with `extended-spans` styles if provided
    val extendedStyledText = if (extendedSpans != null) {
        remember(content) {
            extendedSpans.extend(content)
        }
    } else {
        content
    }

    // forward the `onTextLayout` to `extended-spans` if provided
    val onTextLayout: ((TextLayoutResult, Color?) -> Unit)? = if (extendedSpans != null) {
        { layoutResult, color ->
            extendedSpans.onTextLayout(layoutResult, color)
        }
    } else {
        null
    }

    // call drawBehind with the `extended-spans` if provided
    val extendedModifier = if (extendedSpans != null) {
        modifier.drawBehind(extendedSpans)
    } else modifier

    MyMarkdownText(extendedStyledText, extendedModifier, style, onTextLayout)
}

/**
 * Copied from [com.mikepenz.markdown.compose.elements.MarkdownText].
 *
 * Patches:
 * - use my copy-pasted rememberMarkdownImageState() since the library one is internal
 * - remove animateTextSize() usage since the animations are annoying on content change
 */
@Composable
fun MyMarkdownText(
    content: AnnotatedString,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalMarkdownTypography.current.text,
    onTextLayout: ((TextLayoutResult, Color?) -> Unit)?,
) {
    val baseColor = LocalMarkdownColors.current.text
    val transformer = LocalImageTransformer.current

    val layoutResult: MutableState<TextLayoutResult?> = remember { mutableStateOf(null) }
    val imageState = rememberMarkdownImageState()

    val placeholderState by remember(imageState) {
        derivedStateOf {
            transformer.placeholderConfig(
                imageState.density,
                imageState.containerSize,
                imageState.intrinsicImageSize
            )
        }
    }

    com.mikepenz.markdown.compose.elements.material.MarkdownBasicText(
        text = content,
        modifier = modifier
            .onPlaced {
                it.parentLayoutCoordinates?.also { coordinates ->
                    imageState.updateContainerSize(coordinates.size.toSize())
                }
            },
        style = style,
        inlineContent = mapOf(
            MARKDOWN_TAG_IMAGE_URL to createImageInlineTextContent(
                placeholderState,
                transformer,
                imageState
            )
        ),
        onTextLayout = {
            layoutResult.value = it
            onTextLayout?.invoke(it, baseColor)
        }
    )
}

