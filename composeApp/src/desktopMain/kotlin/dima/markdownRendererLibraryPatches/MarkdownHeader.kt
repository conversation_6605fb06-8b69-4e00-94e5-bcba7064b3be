package dima.markdownRendererLibraryPatches

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import com.mikepenz.markdown.annotator.annotatorSettings
import org.intellij.markdown.IElementType
import org.intellij.markdown.MarkdownTokenTypes
import org.intellij.markdown.ast.ASTNode
import org.intellij.markdown.ast.findChildOfType

/**
 * Copied from [com.mikepenz.markdown.compose.elements.MarkdownHeader].
 *
 * Patches:
 * - add modifier parameter
 * - change entire body, it is possible that an older library version used that code
 */
@Composable
fun MyMarkdownHeader(
    content: String,
    node: ASTNode,
    style: TextStyle,
    contentChildType: IElementType = MarkdownTokenTypes.ATX_CONTENT,
    modifier: Modifier = Modifier,
) {
    val annotatorSettings = annotatorSettings()

    node.findChildOfType(contentChildType)?.let {
        val styledText = buildAnnotatedString {
            pushStyle(style.toSpanStyle())
            buildMyMarkdownAnnotatedString(content, it, annotatorSettings)
            pop()
        }

        MyMarkdownText(styledText, style = style, modifier = modifier)
    }
}
