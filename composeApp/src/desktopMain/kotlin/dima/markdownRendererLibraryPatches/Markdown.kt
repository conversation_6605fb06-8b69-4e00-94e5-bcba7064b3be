package dima.markdownRendererLibraryPatches

import GlobalStyling
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mikepenz.markdown.compose.components.*
import com.mikepenz.markdown.compose.elements.MarkdownDivider
import com.mikepenz.markdown.m2.markdownColor
import com.mikepenz.markdown.m2.markdownTypography
import com.mikepenz.markdown.model.*
import com.mikepenz.markdown.utils.getUnescapedTextInNode
import org.intellij.markdown.flavours.MarkdownFlavourDescriptor
import org.intellij.markdown.flavours.gfm.GFMFlavourDescriptor

/**
 * Unset italics which are way too tilted
 */
@Composable
fun myMarkdownTypography(): MarkdownTypography {
    val initialSize = 14
    val evenStyle = TextStyle.Default.copy(lineHeight = 20.sp, fontSize = initialSize.sp)
    val color = GlobalStyling.getTextColor()
    return markdownTypography(
        h1 = TextStyle(fontSize = (initialSize + 6).sp, fontWeight = FontWeight.Bold, color = color),
        h2 = TextStyle(fontSize = (initialSize + 4).sp, fontWeight = FontWeight.Bold, color = color),
        h3 = TextStyle(fontSize = (initialSize + 2).sp, fontWeight = FontWeight.SemiBold, color = color),
        h4 = TextStyle(fontSize = initialSize.sp, fontWeight = FontWeight.SemiBold, color = color),
        h5 = TextStyle(fontSize = initialSize.sp, fontWeight = FontWeight.Medium, color = color),
        h6 = TextStyle(fontSize = initialSize.sp, fontWeight = FontWeight.Medium, color = color),
        quote = TextStyle(fontSize = initialSize.sp, color = GlobalStyling.getGrayColor()),
        // unset monospace font
        code = TextStyle(fontSize = (initialSize + 1).sp, color = color),
        paragraph = evenStyle.copy(color = color),
        ordered = evenStyle.copy(color = color),
        bullet = evenStyle.copy(color = color),
        list = evenStyle.copy(color = color),
        // set smaller font size like paragraph
        link = TextStyle(
            fontSize = initialSize.sp,
            fontWeight = FontWeight.Bold,
            textDecoration = TextDecoration.Underline,
            color = color
        ),
        table = evenStyle.copy(color = color)
    )
}

/**
 * Copied from [com.mikepenz.markdown.compose.components.getUnescapedTextInNode] since the library's
 * implementation is private.
 */
private fun MarkdownComponentModel.getUnescapedTextInNode() = node.getUnescapedTextInNode(content)

fun myMarkdownComponents(
    text: MarkdownComponent = {
        MyMarkdownText(it.getUnescapedTextInNode())
    },
    eol: MarkdownComponent = CurrentComponentsBridge.eol,
    codeFence: MarkdownComponent = CurrentComponentsBridge.codeFence,
    codeBlock: MarkdownComponent = CurrentComponentsBridge.codeBlock,
    heading1: MarkdownComponent = {
        MyMarkdownHeader(
            it.content,
            it.node,
            it.typography.h1,
            modifier = Modifier.padding(bottom = 20.dp)
        )
    },
    heading2: MarkdownComponent = {
        MyMarkdownHeader(
            it.content,
            it.node,
            it.typography.h2,
            modifier = Modifier.padding(bottom = 16.dp)
        )
    },
    heading3: MarkdownComponent = {
        MyMarkdownHeader(
            it.content,
            it.node,
            it.typography.h3,
            modifier = Modifier.padding(bottom = 12.dp)
        )
    },
    heading4: MarkdownComponent = {
        MyMarkdownHeader(
            it.content,
            it.node,
            it.typography.h4,
            modifier = Modifier.padding(bottom = 8.dp)
        )
    },
    heading5: MarkdownComponent = CurrentComponentsBridge.heading5,
    heading6: MarkdownComponent = CurrentComponentsBridge.heading6,
    setextHeading1: MarkdownComponent = CurrentComponentsBridge.setextHeading1,
    setextHeading2: MarkdownComponent = CurrentComponentsBridge.setextHeading2,
    blockQuote: MarkdownComponent = CurrentComponentsBridge.blockQuote,
    paragraph: MarkdownComponent = {
        MyMarkdownParagraph(
            it.content,
            it.node,
            style = it.typography.paragraph,
            modifier = Modifier.padding(bottom = 8.dp)
        )
    },
    orderedList: MarkdownComponent = {
        Column {
            MyMarkdownOrderedList(it.content, it.node, style = it.typography.ordered)
        }
    },
    unorderedList: MarkdownComponent = CurrentComponentsBridge.unorderedList,
    image: MarkdownComponent = CurrentComponentsBridge.image,
    linkDefinition: MarkdownComponent = CurrentComponentsBridge.linkDefinition,
    horizontalRule: MarkdownComponent = {
        MarkdownDivider(Modifier.fillMaxWidth(), color = GlobalStyling.getDividerColor())
    },
    table: MarkdownComponent = CurrentComponentsBridge.table,
    checkbox: MarkdownComponent = CurrentComponentsBridge.checkbox,
    custom: CustomMarkdownComponent? = CurrentComponentsBridge.custom,
): MarkdownComponents {
    return markdownComponents(
        text = text,
        eol = eol,
        codeFence = codeFence,
        codeBlock = codeBlock,
        heading1 = heading1,
        heading2 = heading2,
        heading3 = heading3,
        heading4 = heading4,
        heading5 = heading5,
        heading6 = heading6,
        setextHeading1 = setextHeading1,
        setextHeading2 = setextHeading2,
        blockQuote = blockQuote,
        paragraph = paragraph,
        orderedList = orderedList,
        unorderedList = unorderedList,
        image = image,
        linkDefinition = linkDefinition,
        horizontalRule = horizontalRule,
        table = table,
        checkbox = checkbox,
        custom = custom,
    )
}

/**
 * Copied from Markdown().
 * Always use this instead of the Markdown() composable since it provides better default typography and components.
 */
@Composable
fun MyMarkdown(
    content: String,
    colors: MarkdownColors = markdownColor(),
    typography: MarkdownTypography = myMarkdownTypography(),
    modifier: Modifier = Modifier.fillMaxSize(),
    padding: MarkdownPadding = markdownPadding(),
    dimens: MarkdownDimens = markdownDimens(),
    flavour: MarkdownFlavourDescriptor = GFMFlavourDescriptor(),
    imageTransformer: ImageTransformer = NoOpImageTransformerImpl(),
    annotator: MarkdownAnnotator = markdownAnnotator(),
    extendedSpans: MarkdownExtendedSpans = markdownExtendedSpans(),
    components: MarkdownComponents = markdownComponents(),
    animations: MarkdownAnimations = markdownAnimations(),
) = com.mikepenz.markdown.compose.Markdown(
    content = content,
    colors = colors,
    typography = typography,
    modifier = modifier,
    padding = padding,
    dimens = dimens,
    flavour = flavour,
    imageTransformer = imageTransformer,
    annotator = annotator,
    extendedSpans = extendedSpans,
    components = components,
    animations = animations,
)