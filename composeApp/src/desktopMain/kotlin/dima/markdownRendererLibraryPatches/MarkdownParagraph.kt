package dima.markdownRendererLibraryPatches

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import com.mikepenz.markdown.annotator.annotatorSettings
import com.mikepenz.markdown.compose.LocalMarkdownTypography
import kotlinx.serialization.json.JsonNull.content
import org.intellij.markdown.ast.ASTNode

/**
 * Copied from [com.mikepenz.markdown.compose.elements.MarkdownParagraph].
 *
 * Patches:
 * - use [buildMyMarkdownAnnotatedString] instead
 * - use [MyMarkdownText] instead
 */
@Composable
fun MyMarkdownParagraph(
    content: String,
    node: ASTNode,
    modifier: Modifier = Modifier,
    style: TextStyle = LocalMarkdownTypography.current.paragraph,
) {
    val annotatorSettings = annotatorSettings()
    val styledText = buildAnnotatedString {
        pushStyle(style.toSpanStyle())
        buildMyMarkdownAnnotatedString(content = content, node = node, annotatorSettings = annotatorSettings)
        pop()
    }

    MyMarkdownText(
        styledText,
        modifier = modifier,
        style = style,
    )
}