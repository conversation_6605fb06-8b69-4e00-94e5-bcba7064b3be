package dima.markdownRendererLibraryPatches

import androidx.compose.runtime.*
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.toSize
import com.mikepenz.markdown.model.MarkdownImageState

/**
 * Copied from [com.mikepenz.markdown.model.MarkdownImageStateImpl] since that one is private.
 */
internal class MarkdownImageStateImpl(override val density: Density) : MarkdownImageState {

    override var containerSize by mutableStateOf(Size.Unspecified)

    override var intrinsicImageSize by mutableStateOf(Size.Unspecified)

    @Deprecated("Use updateContainerSize instead", replaceWith = ReplaceWith("updateContainerSize(size)"))
    override fun setContainerSize(intSize: IntSize) = updateContainerSize(intSize.toSize())

    @Deprecated("Use updateImageSize instead", replaceWith = ReplaceWith("updateImageSize(size)"))
    override fun setImageSize(size: Size) = updateImageSize(size)

    override fun updateContainerSize(size: Size) {
        containerSize = size
    }

    override fun updateImageSize(size: Size) {
        intrinsicImageSize = size
    }
}

/**
 * Copied from [com.mikepenz.markdown.model.rememberMarkdownImageState] since that one is private.
 */
@Composable
internal fun rememberMarkdownImageState(): MarkdownImageState {
    val density = LocalDensity.current
    return remember(density) { MarkdownImageStateImpl(density) }
}
