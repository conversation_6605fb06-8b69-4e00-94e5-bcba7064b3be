package dima.kleinanzeigen

import GlobalEvent
import dima.apps.AppType
import dima.apps.dired.Dired
import dima.apps.dired.openDirectoryInDired
import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showLoadingNotification
import dima.dateTime.DateTimeFormat
import dima.dialogs.generic.GenericDialogRow
import dima.dialogs.generic.getTextInputSingleLineContent
import dima.dialogs.generic.isCheckboxTicked
import dima.dialogs.generic.openGenericDialog
import dima.git.Git
import dima.settings
import dima.utils.SimpleResult
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import setCurrentApp
import java.io.File
import java.net.URI
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import java.time.LocalDateTime

/** Mandatory, otherwise nothing is returned. */
private val headers = mapOf(
    "Host" to "www.kleinanzeigen.de",
    "User-Agent" to "Mozilla/5.0 (Windows NT 6.2; rv,20.0) Gecko/20121202 Firefox/20.0",
    "Accept" to "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language" to "en-US,en;q=0.5",
    "DNT" to "1",
    "Pragma" to "no-cache",
    "Cache-Control" to "no-cache"
)

fun openKleinanzeigenDownloader() {
    if (settings.kleinanzeigenBaseDirectory == null) {
        showErrorNotification("Kleinanzeigen base directory is not set in settings")
        return
    }
    openGenericDialog(
        "Kleinanzeigen Downloader",
        layout = listOf(
            GenericDialogRow.TextInputSingleLine(
                "URL", isRequired = true,
                validator = { url ->
                    if (url.trim().isNotEmpty() && url.lowercase().contains("kleinanzeigen.de")) {
                        val article = extractArticleNameFromUrl(url)
                        if (article == null) {
                            SimpleResult.Error("Needs Kleinanzeigen URL")
                        } else {
                            SimpleResult.Success
                        }
                    } else {
                        SimpleResult.Error("Needs Kleinanzeigen URL")
                    }
                }
            ),
            GenericDialogRow.Checkbox("Zum Verkaufen?", id = "verkaufen?", isChecked = true)
        )
    ) { dialogResult ->
        val url = dialogResult.getTextInputSingleLineContent("URL")
        val toVerkaufen = dialogResult.isCheckboxTicked("verkaufen?")
        val adName = extractArticleNameFromUrl(url)
        if (adName == null) {
            showErrorNotification("Invalid Kleinanzeigen URL")
        } else {
            val datePrefix = LocalDateTime.now().format(DateTimeFormat.dateTimeSpaceSeparated)
            val fullDirName = "$datePrefix $adName"
            val targetParentDir = if (toVerkaufen) {
                File(settings.kleinanzeigenBaseDirectory, "1-zum-verkaufen")
            } else {
                File(settings.kleinanzeigenBaseDirectory, "2-gekauft")
            }
            targetParentDir.mkdirs()
            val targetDir = File(targetParentDir, fullDirName)
            targetDir.mkdirs()
            downloadKleinanzeigenAd(url, targetDir)
        }
    }
}

private fun extractArticleNameFromUrl(url: String): String? {
    // Extract ad name from URL using regex similar to Emacs version
    val regex = Regex("s-anzeige/([^/]+)/")
    return regex.find(url)?.groupValues?.get(1)
}

private fun downloadKleinanzeigenAd(url: String, targetDir: File) {
    CoroutineScope(Dispatchers.IO).launch {
        val notification = showLoadingNotification("Downloading Kleinanzeigen...")
        try {
            // Download and parse the page
            val doc = downloadPage(url)

            // Extract content
            val category = extractCategory(doc)
            val title = extractTitle(doc)
            val price = extractPrice(doc)
            val details = extractDetails(doc)
            val description = extractDescription(doc)

            // Create full text description
            val fullText = """$category
                
$title
$price
                
$details
                
$description"""

            // Save description
            File(targetDir, "desc.md").writeText(fullText)

            // Download images
            val imageUrls = extractImageUrls(doc)
            val result = downloadImages(imageUrls, targetDir, notification)
            when (result) {
                is SimpleResult.Error -> notification.toError(message = result.error)
                SimpleResult.Success -> {
                    setCurrentApp(AppType.Dired)
                    targetDir.openDirectoryInDired()
                    Dired.hideNextReloadNotification = true
                    globalEvent = GlobalEvent.Reload
                    notification.dismiss()
                    gitCommitAndPush()
                }
            }
        } catch (e: Exception) {
            notification.toError("Failed to download Kleinanzeigen ad", e.message ?: "Unknown error")
        }
    }
}

private fun downloadPage(url: String): Document {
    return Jsoup.connect(url)
        .headers(headers)
        .get()
}

private fun extractCategory(doc: Document): String {
    return doc.getElementById("vap-brdcrmb")?.text()?.trim() ?: ""
}

private fun extractTitle(doc: Document): String {
    return doc.getElementById("viewad-title")?.text()?.trim() ?: ""
}

private fun extractPrice(doc: Document): String {
    return doc.getElementById("viewad-price")?.text()?.trim() ?: ""
}

private fun extractDetails(doc: Document): String {
    return doc.getElementsByClass("addetailslist--detail")
        .joinToString("\n") { element ->
            val parts = element.text().trim().split("\n")
            parts.joinToString(": ") { it.trim() }
        }
}

private fun extractDescription(doc: Document): String {
    return doc.getElementById("viewad-description-text")?.wholeText()?.trim() ?: ""
}

private fun extractImageUrls(doc: Document): List<String> {
    return doc.getElementById("viewad-product")
        ?.getElementsByTag("img")
        ?.mapNotNull { img ->
            if (img.hasAttr("data-inlinepreload")) img.attr("src") else null
        } ?: emptyList()
}

private fun downloadImages(imageUrls: List<String>, targetDir: File, notification: LoadingNotification): SimpleResult {
    notification.update(message = "Downloading ${imageUrls.size} images...")
    imageUrls.forEachIndexed { index, imageUrl ->
        try {
            val imageStream = URI(imageUrl).toURL().openStream()
            val imagePath = Paths.get(targetDir.absolutePath, "${index + 1}.jpg")
            Files.copy(imageStream, imagePath, StandardCopyOption.REPLACE_EXISTING)
            imageStream.close()
        } catch (e: Exception) {
            return SimpleResult.Error("Failed to download image: $imageUrl\n" + (e.message ?: "Unknown error"))
        }
    }
    return SimpleResult.Success
}

private fun gitCommitAndPush() {
    CoroutineScope(Dispatchers.IO).launch {
        Git.commitAndPushAllChanges("Update Kleinanzeigen archive", settings.kleinanzeigenBaseDirectory)
    }
}
