package dima.leader

import GlobalEvent
import androidx.compose.ui.input.key.Key
import dima.apps.AppType
import dima.apps.dired.DiredDatabase
import dima.apps.notifications.NotificationType
import dima.apps.notifications.showErrorNotification
import dima.apps.notifications.showNotification
import dima.apps.textEditor.TextEditor
import dima.dialogs.openFile.openFileDialog
import dima.git.openGitStatusDialog
import dima.globalState.GlobalState
import dima.kitty.Kitty
import dima.obsidianNotes.ObsidianNotes
import dima.os.homeWithoutSlash
import dima.utils.RecentHistory
import dima.utils.asLowerCase
import getCurrentApp
import globalEvent
import setCurrentApp
import java.io.File

/**
 * Both keys are null if the space bar is pressed.
 */
data class LeaderKeyState(val first: Key? = null, val second: Key? = null)

/**
 * Both parameters will be null when the key is part of a chain like Space-T-A and the current key is A.
 */
data class LeaderState(
    val title: String?,
    val isAppSwitcher: Boolean = false,
    val callback: (() -> Unit)?
)

internal fun showUnboundNotification(first: Key, second: Key?) {
    showNotification(
        Leader.formatKeys(LeaderKeyState(first = first, second = second), suffix = ""),
        "is not bound",
        type = NotificationType.Error
    )
}

/**
 * Only call this when first is set in the state.
 *
 * @return null if the leader key was not bound and the [LeaderKeyState] should be reset
 */
fun checkIfLeaderKeysHaveAction(state: LeaderKeyState): LeaderState? {
    val second = state.second
    if (second != null) {
        return when (state.first) {
            Key.N -> mapN(second)
            Key.T -> mapT(second)
            Key.W -> mapW(second)
            Key.H -> mapH(second)
            Key.Minus -> {
                // avoid that space - - is shown in the help dialog again
                if (second == Key.Minus) {
                    null
                } else {
                    mapMinusForVeroTransient(second)
                }
            }

            else -> null
        }
    }
    return when (state.first) {
        // handle key maps
        Key.N, Key.T, Key.W, Key.H -> {
            LeaderState(title = null, isAppSwitcher = false, callback = null)
        }

        Key.C -> {
            LeaderState("Open git status dialog") {

                val activePane = GlobalState.panes.find { it.id == GlobalState.activePaneId }
                if (activePane?.appType != AppType.Dired) {
                    showErrorNotification("Open git status is only available in Dired", durationMillis = 1000)
                    return@LeaderState
                }
                openGitStatusDialog(File(GlobalState.dired.directory))
            }
        }

        Key.Minus -> {
            // call mapMinus with the Minus key to open the transient dialog
            mapMinusForVeroTransient(Key.Minus)
        }

        Key.NumPadEquals -> {
            LeaderState("Open in kitty shell") {
                val app = getCurrentApp()
                if (app != AppType.Dired) {
                    showErrorNotification("Open in kitty shell is only available in Dired", durationMillis = 1000)
                    return@LeaderState
                }
                val path = GlobalState.dired.directory
                Kitty.openNewTabInWorkingDirectory(File(path))
            }
        }

        Key.M -> {
            LeaderState("Open home directory in Dired") {
                GlobalState.dired = GlobalState.dired.copy(
                    directory = homeWithoutSlash,
                    selectedFile = DiredDatabase.getLastSelectedFileInDirectory(File(homeWithoutSlash))
                )
                setCurrentApp(AppType.Dired)
            }
        }

        Key.G -> {
            LeaderState("Close current buffer in text editor without saving") {
                if (GlobalState.app == AppType.TextEditor) {
                    TextEditor.closeCurrentBufferWithoutSaving()
                } else {
                    showNotification("Not in text editor")
                }
            }
        }

        Key.R -> {
            LeaderState("Pick obsidian note to open in dired") {
                ObsidianNotes.browseTo()
            }
        }

        Key.U -> {
            LeaderState("Open recent path in Dired") {
                val recentFiles = RecentHistory.getRecentFiles()
                val files = recentFiles.map {
                    File(it)
                }.filter {
                    it.exists()
                }
                openFileDialog(files)
            }
        }

        Key.S -> {
            LeaderState("Save buffer") {
                globalEvent = GlobalEvent.SaveBuffer
            }
        }

        Key.D -> {
            LeaderState("Scroll to top") {
                globalEvent = GlobalEvent.ScrollToTop
            }
        }

        Key.V -> {
            LeaderState("Enter visual line mode in text editor") {
                globalEvent = GlobalEvent.EnterVisualLineMode
            }
        }

        Key.I -> {
            LeaderState("Kill to end of line in text editor") {
                globalEvent = GlobalEvent.KillToEndOfLine
            }
        }

        Key.Q -> {
            LeaderState("Kill all text in text editor") {
                globalEvent = GlobalEvent.KillAll
            }
        }

        Key.J -> {
            LeaderState("Copy all text in text editor or select all") {
                globalEvent = GlobalEvent.CopyAll
            }
        }

        Key.B -> {
            LeaderState("Scroll to bottom") {
                globalEvent = GlobalEvent.ScrollToBottom
            }
        }

        Key.E -> {
            LeaderState("Close current app and switch to previous") {
                // Switch to previous app using globalAppRing
                val globalRing = dima.globalState.GlobalState.globalAppRing
                if (globalRing.size >= 2) {
                    val previousApp = globalRing[1]
                    setCurrentApp(previousApp)
                } else {
                    showNotification("No previous app", durationMillis = 1000)
                }
            }
        }

        else -> {
            LeaderState(null) {
                showUnboundNotification(state.first!!, null)
            }
        }
    }
}

object Leader {

    const val SPACE_ABBREV = "SPC"

    fun formatKeys(state: LeaderKeyState, suffix: String = "-"): String {
        val parts = listOfNotNull(state.first, state.second).joinToString(" ") { it.asLowerCase() }
        if (parts.isEmpty()) {
            return "$SPACE_ABBREV$suffix"
        }
        return "$SPACE_ABBREV $parts$suffix".replace(Regex("--$"), "-")
    }

}