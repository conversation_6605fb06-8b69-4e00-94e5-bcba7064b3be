package dima.leader

import GlobalStyling
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import dima.color.TailwindCssColors

@Composable
fun GlobalLeaderOverlay(state: LeaderKeyState?) {
    if (state == null) {
        return
    }
    Popup(alignment = Alignment.BottomCenter) {
        Column(
            modifier = Modifier
                .padding(bottom = 42.dp)
                .shadow(
                    6.dp,
                    GlobalStyling.smallRoundedCorners,
                    ambientColor = GlobalStyling.leaderOverlayBackgroundColor,
                    spotColor = GlobalStyling.leaderOverlayBackgroundColor
                )
                .background(GlobalStyling.leaderOverlayBackgroundColor, shape = GlobalStyling.smallRoundedCorners)
                .padding(8.dp)
        ) {
            Text(
                Leader.formatKeys(state),
                color = TailwindCssColors.white,
                fontSize = 24.sp
            )
        }
    }
}
