package dima.leader

import GlobalEvent
import Globals
import MaximizeWindowForHotReload
import androidx.compose.ui.input.key.Key
import dima.apps.AppType
import dima.apps.dired.openDirectoryInDired
import dima.apps.notifications.*
import dima.apps.todoist.Todoist
import dima.dialogs.findText.openFindTextDialog
import dima.dialogs.readOnlyTextEditor.openReadOnlyTextEditorDialog
import dima.dialogs.textInput.TextInputDialogConfirmAction
import dima.dialogs.textInput.openTextInputDialog
import dima.events.emitEasy
import dima.git.Git
import dima.globalState.GlobalState
import dima.os.copyToClipboard
import dima.process.LoggedProcess
import dima.settings
import dima.systemProfiler.SystemProfiler
import dima.systemProfiler.openSystemProfileDialog
import dima.telegram.openTelegramChatSelectionDialog
import globalEvent
import kotlinx.coroutines.*
import setCurrentApp
import java.io.File

internal fun mapW(key: Key): LeaderState {
    return when (key) {
        Key.N -> {
            LeaderState("Toggle dark mode") {
                GlobalState.isDarkMode = !GlobalState.isDarkMode
            }
        }

        Key.E -> {
            LeaderState("Test read only text editor") {
                openReadOnlyTextEditorDialog(
                    "123\n\n\n" + "This is a test ooooooooooooooooooooooo ooooooo oeunoeuboeucoehunoe u123456 \n".repeat(
                        100
                    ).trim(),
                    "Test title",
                    showLineNumbers = true
                )
                /*
                                openReadOnlyTextEditorDialog(
                                    """       {
                                       "hi": 3
                                       }
                                   """.trimIndent(),
                                    "Test title",
                                    TreeSitter.Language.JSON
                                )
                */
            }
        }

        Key.S -> {
            LeaderState("Open scan directory in Dired") {
                if (settings.scannedDirectory == null) {
                    showErrorNotification("The scannedDirectory is not set in the settings")
                    return@LeaderState
                }
                settings.scannedDirectory.openDirectoryInDired()
            }

        }

        Key.F -> {
            LeaderState("Find text recursively") {
                val activePane = GlobalState.panes.find { it.id == GlobalState.activePaneId }
                when (activePane?.appType) {
                    AppType.Dired -> {
                        openFindTextDialog(GlobalState.dired.directory)
                    }
                    AppType.TextEditor -> {
                        showErrorNotification("TODO: Not implemented for text editor")
                    }
                    else -> {
                        showErrorNotification("Find text recursively is not implemented for this app")
                    }
                }
            }
        }

        Key.A -> {
            LeaderState("Show debug notifications") {
                showLoadingNotification("loader", "hello\n".repeat(20))
                showLoadingNotification("loader")
                showNotification("Debug notifications", "Debug notifications", type = NotificationType.Info)
                CoroutineScope(Dispatchers.Default).launch {
                    val nn =
                        showNotification("Debug notifications2", "Debug notifications", type = NotificationType.Error)
                    val n =
                        showNotification("Debug notifications3", "Debug notifications", type = NotificationType.Error)
                    delay(1000)
                    updateNotification(n, "Debug notifications4", "Debug notifications", type = NotificationType.Error)
                    val loader = showNotification("Loader", "Debug notifications", type = NotificationType.Loading)
                    dismissNotification(nn)
                    delay(1000)
                    updateNotification(
                        loader,
                        "Debug notifications5",
                        "Debug notifications",
                        type = NotificationType.Error,
                        durationMillis = 1000
                    )
                }
            }
        }

        Key.J -> {
            LeaderState("Join line to previous in text editor") {
                globalEvent = GlobalEvent.JoinLineToPrevious
            }
        }

        Key.D -> {
            LeaderState("Toggle window decoration") {
                GlobalState.mainWindow =
                    GlobalState.mainWindow.copy(windowDecorated = !GlobalState.mainWindow.windowDecorated)
            }
        }

        Key.U -> {
            LeaderState("Open Telegram") {
                openTelegramChatSelectionDialog("Open Telegram") {
                    GlobalState.telegram = GlobalState.telegram.copy(selectedContactId = it.id)
                    setCurrentApp(AppType.Telegram)
                }
            }
        }

        Key.H -> {
            LeaderState("Duplicate line in text editor") {
                globalEvent = GlobalEvent.DuplicateLine
            }
        }

        Key.T -> {
            LeaderState("Create new task in Todoist") {
                openTextInputDialog("Create new task in Todoist") {
                    CoroutineScope(Dispatchers.IO).launch {
                        Todoist.createNewTask(it)
                    }
                    return@openTextInputDialog TextInputDialogConfirmAction.Close
                }
            }
        }

        Key.G -> {
            LeaderState("Commit and push all changes via git") {
                CoroutineScope(Dispatchers.IO).launch {
                    Git.commitAndPushAllChanges(notificationSuffix = "Kotlin Emacs")
                }
            }
        }

        Key.B -> {
            LeaderState("Compile Kotlin Emacs") {
                if (settings.kotlinEmacsGradlewPath == null) {
                    showErrorNotification("Kotlin Emacs gradlew path is not set in settings")
                    return@LeaderState
                }
                CoroutineScope(Dispatchers.IO).launch {
                    val notification = showLoadingNotification("Compiling Kotlin Emacs...")
                    val lines = mutableListOf<String>()
                    LoggedProcess(
                        command = File(settings.kotlinEmacsGradlewPath, "gradlew").absolutePath,
                        args = listOf("compileKotlinDesktop"),
                        workingDirectory = settings.kotlinEmacsGradlewPath,
                        onOutputLine = { line ->
                            lines.add(line)
                            notification.update(message = lines.joinToString("\n").trim())
                        },
                        showErrorNotifications = false,
                        onFinish = { loggedProcess ->
                            if (loggedProcess.exitCode.value == 0) {
                                notification.toInfo(
                                    "Kotlin Emacs compilation successful",
                                    durationMillis = 500
                                )
                            } else {
                                val output = loggedProcess.getOutput()
                                copyToClipboard(output, showNotification = false)
                                notification.toError(
                                    "Kotlin Emacs compilation failed",
                                    "Error output copied to clipboard:\n${output.trim()}",
                                    durationMillis = 2000
                                )
                            }
                        }
                    ).startAsync()
                }
            }
        }

        Key.O -> {
            LeaderState("Maximize window, but keep space at left for hot reload") {
                runBlocking {
                    MaximizeWindowForHotReload().emitEasy()
                }
            }
        }

        Key.W -> {
            LeaderState("Dismiss all notifications") {
                dismissAllNotifications()
            }
        }

        Key.C -> {
            LeaderState("Start System Profiling") {
                val notification =
                    showLoadingNotification("Profiling System CPU/Memory...", "Collecting data for 5 seconds...")
                Globals.coroutineScope.launch(Dispatchers.IO) {
                    val results = SystemProfiler.startSystemProfiling()
                    notification.dismiss()
                    openSystemProfileDialog(results)
                }
            }
        }

        else -> {
            LeaderState(null) {
                showUnboundNotification(Key.W, key)
            }
        }
    }
}
