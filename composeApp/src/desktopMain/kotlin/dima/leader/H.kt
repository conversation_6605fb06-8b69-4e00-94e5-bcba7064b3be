package dima.leader

import androidx.compose.ui.input.key.Key
import dima.apps.AppType
import setCurrentApp

internal fun mapH(key: Key): LeaderState {
    return when (key) {
        Key.E -> {
            LeaderState("Open previous notifications", isAppSwitcher = true) {
                setCurrentApp(AppType.LastNotifications)
            }
        }
        else -> {
            LeaderState(null) {
                showUnboundNotification(Key.H, key)
            }
        }
    }
}
