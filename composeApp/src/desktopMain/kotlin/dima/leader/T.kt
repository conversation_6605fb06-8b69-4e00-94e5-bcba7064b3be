package dima.leader

import androidx.compose.ui.input.key.Key
import dima.apps.AppType
import dima.apps.notifications.showErrorNotification
import dima.apps.textEditor.TextEditor
import dima.dialogs.findFilesViaRipgrep.FindFilesViaRipgrepDialog
import dima.dialogs.findFilesViaRipgrep.openFindFilesViaRipgrepDialog
import dima.globalState.GlobalState
import dima.os.homeWithoutSlash
import dima.os.openUrl
import dima.settings
import dima.timers.Timers
import setCurrentApp
import java.io.File

internal fun mapT(key: Key): LeaderState {
    return when (key) {
        Key.D -> {
            LeaderState("Open calculator app", isAppSwitcher = true) {
                if (GlobalState.app != AppType.Calculator) {
                    GlobalState.calculator = GlobalState.calculator.copy(inputFocused = true)
                    setCurrentApp(AppType.Calculator)
                }
            }
        }

        Key.O -> {
            LeaderState("Switch to Process List App", isAppSwitcher = true) {
                setCurrentApp(AppType.ProcessList)
            }
        }

        Key.B -> {
            LeaderState("Switch to Ai Chatter App", isAppSwitcher = true) {
                setCurrentApp(AppType.AiChat)
            }
        }

        Key.A -> {
            LeaderState("Switch to Transcribe App", isAppSwitcher = true) {
                setCurrentApp(AppType.Transcribe)
            }
        }

        Key.K -> {
            LeaderState("Switch to Calendar App", isAppSwitcher = true) {
                if (settings.googleCalendar == null) {
                    showErrorNotification("Google Calendar is not setup in settings")
                    return@LeaderState
                }
                setCurrentApp(AppType.Calendar)
            }
        }

        Key.Minus -> {
            LeaderState("Switch to TreeSitter App", isAppSwitcher = true) {
                setCurrentApp(AppType.TreeSitter)
            }
        }

        Key.W -> {
            LeaderState("Open Lipperode weather in browser") {
                openUrl("https://duckduckgo.com/?t=ffab&q=weather+lipperode+g!")
            }
        }

        Key.G -> {
            LeaderState("Find file recursively, starting from current directory or entire home directory") {
                if (GlobalState.app != AppType.Dired || GlobalState.dired.directory == homeWithoutSlash) {
                    openFindFilesViaRipgrepDialog(
                        homeWithoutSlash,
                        additionalRipGrepArgs = FindFilesViaRipgrepDialog.getAdditionalRipGrepArgsForHomeDir()
                    )
                    return@LeaderState
                }
                openFindFilesViaRipgrepDialog(GlobalState.dired.directory)
            }
        }

        Key.N -> {
            LeaderState("Open new text editor buffer with clipboard contents", isAppSwitcher = true) {
                if (GlobalState.app != AppType.TextEditor) {
                    setCurrentApp(AppType.TextEditor)
                }
                TextEditor.openNewScratchBuffer()
            }
        }


        Key.S -> {
            LeaderState("Open Desktop (screenshots) in Dired") {
                GlobalState.dired.setDirectory(File(homeWithoutSlash, "Desktop").absolutePath)
                setCurrentApp(AppType.Dired)
            }
        }

        Key.Z -> {
            LeaderState("Open Downloads in Dired") {
                GlobalState.dired.setDirectory(File(homeWithoutSlash, "Downloads").absolutePath)
                setCurrentApp(AppType.Dired)
            }
        }

        Key.Q -> {
            LeaderState("Stop timer") {
                Timers.pickTimerToStop()
            }
        }

        Key.I -> {
            LeaderState("Switch to Todoist/task App", isAppSwitcher = true) {
                setCurrentApp(AppType.Todoist)
            }
        }

        Key.T -> {
            LeaderState("Add new timer") {
                Timers.addNew()
            }
        }

        Key.E -> {
            LeaderState("Switch to Email App", isAppSwitcher = true) {
                setCurrentApp(AppType.Email)
                // TODO use once split is stabler
                /*
                                if (GlobalState.app == AppType.Email) {
                                    globalEvent = GlobalEvent.ToNewEmails
                                } else {
                                    setCurrentApp(AppType.Email)
                                }
                */
            }
        }


        Key.C -> {
            LeaderState("Switch to ChatGPT App", isAppSwitcher = true) {
                setCurrentApp(AppType.AiSinglePrompt)
            }
        }

        Key.P -> {
            LeaderState("Open working project") {
                if (settings.dired.workingProjectPath == null) {
                    showErrorNotification("The diredWorkingProjectPath is not set in settings")
                    return@LeaderState
                }
                GlobalState.dired.setDirectory(settings.dired.workingProjectPath.absolutePath)
                setCurrentApp(AppType.Dired)
            }
        }

        Key.M -> {
            LeaderState("Switch to Network Activity App", isAppSwitcher = true) {
                setCurrentApp(AppType.NetworkActivity)
            }
        }

        Key.V -> {
            LeaderState("Switch to Vine App", isAppSwitcher = true) {
                setCurrentApp(AppType.AmazonVine)
            }
        }

        Key.F -> {
            LeaderState("Switch to Feed App", isAppSwitcher = true) {
                setCurrentApp(AppType.Feeds)
            }
        }

        Key.H -> {
            LeaderState("Switch to Tailwind CSS Contraster", isAppSwitcher = true) {
                setCurrentApp(AppType.TailwindContraster)
            }
        }

        Key.L -> {
            LeaderState("Switch to Dummy App", isAppSwitcher = true) {
                setCurrentApp(AppType.Dummy)
            }
        }

        Key.Y -> {
            LeaderState("Switch to Vero Log App", isAppSwitcher = true) {
                setCurrentApp(AppType.VeroLog)
            }
        }

        else -> {
            LeaderState(null) {
                showUnboundNotification(Key.T, key)
            }
        }
    }
}
