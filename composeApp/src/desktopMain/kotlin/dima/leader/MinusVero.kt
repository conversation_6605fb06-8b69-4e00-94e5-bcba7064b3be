package dima.leader

import androidx.compose.ui.input.key.Key
import dima.vero.mapKeyToVeroLeaderState
import dima.vero.openVeroTransientDialog

internal fun mapMinusForVeroTransient(key: Key): LeaderState {
    if (key == Key.Minus) {
        return LeaderState("Open Vero transient") {
            openVeroTransientDialog()
        }
    }
    val leaderState = mapKeyToVeroLeaderState(key)
    return if (leaderState.title == null) {
        LeaderState(title = null) {
            showUnboundNotification(Key.Minus, key)
        }
    } else {
        leaderState
    }
}
