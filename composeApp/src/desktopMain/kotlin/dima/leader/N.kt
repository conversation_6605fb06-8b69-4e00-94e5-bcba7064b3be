package dima.leader

import GlobalEvent
import androidx.compose.ui.input.key.Key
import dima.ai.image.ImageGeneration
import dima.ai.standaloneChatDialog.openAiStandaloneDialog
import dima.apps.AppType
import dima.apps.notifications.showErrorNotification
import dima.color.getTailwindCssColorKotlinString
import dima.dialogs.completion.openCompletionDialog
import dima.emojis.openDialogToCopyEmojis
import dima.git.GitRepoSearcher
import dima.git.openGitHubRepoCreatorDialog
import dima.images.TemporaryFileUploader
import dima.instagram.Instagram
import dima.kleinanzeigen.openKleinanzeigenDownloader
import dima.network.Network
import dima.os.copyToClipboard
import dima.process.GlobalThreads
import dima.settings
import dima.utils.Result
import dima.vero.loginAndCopyVeroTokenToClipboard
import dima.weather.checkColdWeather
import dima.youtube.YouTube
import globalEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import setCurrentApp
import java.io.File

internal fun mapN(key: Key): LeaderState {
    return when (key) {
        Key.K -> {
            LeaderState("Test temporary file uploader") {
                CoroutineScope(Dispatchers.IO).launch {
                    val s =
                        TemporaryFileUploader.uploadFile(File("/Users/<USER>/Downloads/ChatGPT Image May 24, 2025, 05_07_38 PM.png"))
                    when (s) {
                        is Result.Error -> showErrorNotification(s.error)
                        is Result.Success -> copyToClipboard(s.value)
                    }
                }
            }
        }

        Key.F -> {
            LeaderState("Test GlobalThreads for process list") {
                GlobalThreads.register("Test Thread") {
                    while (true) {
                        Thread.sleep(1000)
                    }
                }
                GlobalThreads.register("Test Thread #2") {
                    Thread.sleep(3000)
                }
                GlobalThreads.register("Test Thread #3") {
                    Thread.sleep(1000)
                    throw RuntimeException("Test exception")
                }
            }
        }

        Key.T -> {
            LeaderState("Generate PollinationsAI image") {
                ImageGeneration.openDialogToEnterParameters()
            }
        }


        Key.I -> {
            LeaderState("Download Instagram video/image from clipboard URL") {
                Instagram.downloadVideoOrImageFromUrlInClipboard()
            }
        }

        Key.D -> {
            LeaderState("Open ebay kleinanzeigen downloader") {
                openKleinanzeigenDownloader()
            }
        }

        Key.Z -> {
            // for testing
            LeaderState("Switch to ScrollVelocityTest app") {
                setCurrentApp(AppType.ScrollVelocityTest)
            }
        }

        Key.Y -> {
            LeaderState("Open dialog to download from YouTube") {
                YouTube.openDownloadDialog()
            }
        }

        Key.E -> {
            LeaderState("Copy emojis") {
                openDialogToCopyEmojis()
            }
        }

        Key.A -> {
            LeaderState("Copy address") {
                if (settings.addressesToCopy.isEmpty()) {
                    showErrorNotification("Address array is not setup in settings.json")
                    return@LeaderState
                }
                openCompletionDialog("Copy address", settings.addressesToCopy) {
                    copyToClipboard(it.text)
                }
            }
        }

        Key.W -> {
            LeaderState("Check if weather is cold today or tomorrow") {
                if (settings.weather.latitude == null || settings.weather.longitude == null) {
                    showErrorNotification("Weather latitude or longitude is not set in Settings")
                    return@LeaderState
                }
                checkColdWeather()
            }
        }


        Key.N -> {
            LeaderState("Wait until internet is available") {
                Network.waitUntilInternetIsAvailable()
            }
        }

        Key.G -> {
            LeaderState("Search GitHub and GitLab repositories") {
                GitRepoSearcher.searchAndDisplayRepositories()
            }
        }

        Key.H -> {
            LeaderState("Insert from history ring") {
                globalEvent = GlobalEvent.InsertTextFromHistory
            }
        }

        Key.O -> {
            LeaderState("Open Process Tester app") {
                setCurrentApp(AppType.ProcessTester)
            }
        }

        Key.B -> {
            LeaderState("Open loader") {
                setCurrentApp(AppType.Loader)
            }
        }

        Key.C -> {
            LeaderState("Create GitHub repository") {
                openGitHubRepoCreatorDialog()
            }
        }

        Key.J -> {
            LeaderState("Copy Tailwind CSS colors") {
                copyToClipboard(getTailwindCssColorKotlinString())
            }
        }

        Key.U -> {
            LeaderState("Open AI chat dialog") {
                openAiStandaloneDialog()
            }
        }

        Key.V -> {
            LeaderState("Vero: Login and copy token to clipboard") {
                CoroutineScope(Dispatchers.IO).launch {
                    loginAndCopyVeroTokenToClipboard()
                }
            }
        }

        else -> {
            LeaderState(null) {
                showUnboundNotification(Key.N, key)
            }
        }
    }
}
