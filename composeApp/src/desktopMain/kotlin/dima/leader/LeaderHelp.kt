package dima.leader

import androidx.compose.ui.input.key.Key

data class LeaderHelpKey(
    val firstKey: Key,
    val secondKey: Key?,
    val title: String,
    val isAppSwitcher: Boolean = false,
    val callback: (() -> Unit)?
)

fun getLeaderHelpKeys(): List<LeaderHelpKey> {
    // the order here is important and will be used on the left side of the help dialog
    val keysToCheck = listOf(
        Key.Minus,
        Key.Equals,
        Key.A,
        Key.B,
        Key.C,
        Key.D,
        Key.E,
        Key.F,
        Key.G,
        Key.H,
        Key.I,
        Key.J,
        Key.K,
        Key.L,
        Key.M,
        Key.N,
        Key.O,
        Key.P,
        Key.Q,
        Key.R,
        Key.S,
        Key.T,
        Key.U,
        Key.V,
        Key.W,
        Key.X,
        Key.Y,
        Key.Z,
    )

    val mappedKeys = mutableListOf<LeaderHelpKey>()

    fun addToMappedKeys(firstKey: Key, secondKey: Key?) {
        val hasAction = checkIfLeaderKeysHaveAction(LeaderKeyState(first = firstKey, second = secondKey))
        if (hasAction?.title != null) {
            mappedKeys.add(
                LeaderHelpKey(
                    firstKey = firstKey,
                    secondKey = secondKey,
                    title = hasAction.title,
                    isAppSwitcher = hasAction.isAppSwitcher,
                    callback = hasAction.callback
                )
            )
        }
    }

    keysToCheck.forEach { first ->
        addToMappedKeys(firstKey = first, secondKey = null)
        keysToCheck.forEach { second: Key ->
            addToMappedKeys(firstKey = first, secondKey = second)
            checkIfLeaderKeysHaveAction(LeaderKeyState(first = first, second = second))
        }
    }
    return mappedKeys
}
