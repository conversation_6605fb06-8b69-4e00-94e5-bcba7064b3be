package dima.systemProfiler

import com.github.pgreze.process.Redirect
import com.github.pgreze.process.process
import kotlinx.coroutines.delay

// Data class to store the final aggregated result for a process
data class ProcessFinalResult(
    val pid: Int,
    val name: String,
    val avgCpu: Double,
    val avgMem: Double
)

// Internal data class to hold raw samples before aggregation
private data class ProcessAggregatedData(
    val pid: Int,
    val name: String, // Use the first name encountered for the PID
    val cpuSamples: MutableList<Double> = mutableListOf(),
    val memSamples: MutableList<Double> = mutableListOf()
)

object SystemProfiler {
    private const val PROFILING_DURATION_MS = 5000L
    private const val SAMPLING_INTERVAL_MS = 200L
    private const val NUM_SAMPLES = (PROFILING_DURATION_MS / SAMPLING_INTERVAL_MS).toInt()

    suspend fun startSystemProfiling(): List<ProcessFinalResult> {
        val aggregatedData = mutableMapOf<Int, ProcessAggregatedData>()

        repeat(NUM_SAMPLES) {
            try {
                val result = process(
                    "ps", "-Ao", "pid,pcpu,pmem,comm", // No --no-headers, will skip first line
                    stdout = Redirect.CAPTURE,
                    stderr = Redirect.SILENT
                )

                if (result.resultCode == 0) {
                    result.output.drop(1) // Skip header line
                        .forEach { line ->
                            val trimmedLine = line.trim()
                            if (trimmedLine.isNotEmpty()) {
                                try {
                                    val parts = trimmedLine.split(Regex("\\s+"), limit = 4)
                                    if (parts.size == 4) {
                                        val pid = parts[0].toInt()
                                        val cpu = parts[1].toDouble()
                                        val mem = parts[2].toDouble()
                                        val name = parts[3]

                                        val processData = aggregatedData.getOrPut(pid) {
                                            ProcessAggregatedData(pid, name)
                                        }
                                        processData.cpuSamples.add(cpu)
                                        processData.memSamples.add(mem)
                                    }
                                } catch (e: Exception) {
                                    println("SysProfiler: Error parsing line '$trimmedLine': ${e.message}")
                                }
                            }
                        }
                } else {
                    println("SysProfiler: ps command failed with exit code ${result.resultCode}")
                }
            } catch (e: Exception) {
                println("SysProfiler: Error executing ps command: ${e.message}")
                // Optionally rethrow or return an error indicator
            }
            if (it < NUM_SAMPLES - 1) { // Don't delay after the last sample
                delay(SAMPLING_INTERVAL_MS)
            }
        }

        return aggregatedData.values.map { data ->
            ProcessFinalResult(
                pid = data.pid,
                name = data.name,
                avgCpu = if (data.cpuSamples.isNotEmpty()) data.cpuSamples.average() else 0.0,
                avgMem = if (data.memSamples.isNotEmpty()) data.memSamples.average() else 0.0
            )
        }
    }
}