package dima.systemProfiler

import androidx.compose.foundation.VerticalScrollbar
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollbarAdapter
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.type
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dima.dialogs.DialogIdentifier
import dima.dialogs.DialogOverlay
import dima.dialogs.closeDialogWithId
import dima.dialogs.openDialog

// Renamed to avoid conflict if SystemProfiler object has same name
data class SystemProfileDialogParams(val results: List<ProcessFinalResult>)

fun openSystemProfileDialog(results: List<ProcessFinalResult>) {
    openDialog(
        identifier = DialogIdentifier.SystemProfile,
        data = SystemProfileDialogParams(results)
    ) { id, data ->
        SystemProfileDialog(id, data)
    }
}

@Composable
fun SystemProfileDialog(id: Long, data: Any?) {
    val params = data as? SystemProfileDialogParams
    if (params == null) {
        closeDialogWithId(id)
        return
    }

    val results = params.results

    // Filter out processes with 0 CPU and 0 Memory, then sort
    val cpuResults = remember(results) {
        results.filter { it.avgCpu > 0.01 }.sortedByDescending { it.avgCpu }
    }
    val memResults = remember(results) {
        results.filter { it.avgMem > 0.01 }.sortedByDescending { it.avgMem }
    }

    DialogOverlay(
        dialogId = id,
        widthFraction = 0.8f,
        showHelpIcon = true,
        onPreviewKeyEvent = {
            if (it.key == Key.Escape && it.type == KeyEventType.KeyUp) {
                closeDialogWithId(id)
                true
            } else {
                false
            }
        }
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier
                .fillMaxSize(),
        ) {
            Text(
                "System CPU & Memory Profile (5s, 0.2s interval)",
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )

            Text("CPU Usage (%)", fontWeight = FontWeight.Bold, fontSize = 18.sp)
            Box(modifier = Modifier.weight(1f).fillMaxWidth()) {
                val listState = rememberLazyListState()
                LazyColumn(state = listState, modifier = Modifier.fillMaxSize()) {
                    items(cpuResults) { process ->
                        Row {
                            Text(
                                "${"%.1f".format(process.avgCpu)}%",
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Right,
                                modifier = Modifier
                                    .width(70.dp)
                                    .padding(end = 8.dp)
                            )
                            Text(process.name)
                        }
                    }
                }
                VerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd).fillMaxHeight(),
                    adapter = rememberScrollbarAdapter(scrollState = listState)
                )
            }

            Text("Memory Usage (%)", fontWeight = FontWeight.Bold, fontSize = 18.sp)
            Box(modifier = Modifier.weight(1f).fillMaxWidth()) {
                val listState = rememberLazyListState()
                LazyColumn(state = listState, modifier = Modifier.fillMaxSize()) {
                    items(memResults) { process ->
                        Row {
                            Text(
                                "${"%.1f".format(process.avgMem)}%",
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Right,
                                modifier = Modifier
                                    .width(70.dp)
                                    .padding(end = 8.dp)
                            )
                            Text(process.name)
                        }
                    }
                }
                VerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd).fillMaxHeight(),
                    adapter = rememberScrollbarAdapter(scrollState = listState)
                )
            }
        }
    }
}