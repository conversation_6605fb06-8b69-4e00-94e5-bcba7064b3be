package dima.cache

import dima.apps.amazonVine.AmazonVine
import dima.apps.dired.preview.DiredPdfPreview
import dima.apps.networkActivity.FakedNetworkActivityStatus
import dima.apps.networkActivity.NetworkActivity
import dima.apps.notifications.showErrorNotification
import dima.feeds.FeedsDatabase
import java.io.File
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.minutes

class CacheWiperThread : Thread() {

    override fun run() {
        val sleepDuration = 5.minutes.inWholeMilliseconds
        while (true) {
            sleep(sleepDuration)
            // Ensure directories are created if they don't exist
            // This prevents errors if they are accessed before creation
            listOf(FeedsDatabase.cacheDirectory, DiredPdfPreview.cacheDirectory, AmazonVine.cacheDirectory)
                .forEach { dir -> if (!dir.exists()) dir.mkdirs() }

            clearOldFiles(FeedsDatabase.cacheDirectory, ageInDays = 30, exceptionFileName = "feeds.db")
            clearOldFiles(DiredPdfPreview.cacheDirectory, ageInDays = 15)
            clearOldFiles(AmazonVine.cacheDirectory, ageInDays = 15)
        }
    }

    private fun clearOldFiles(directory: File, ageInDays: Int, exceptionFileName: String? = null) {
        val ageInMillis = ageInDays.days.inWholeMilliseconds
        val now = System.currentTimeMillis()
        if (directory.exists() && directory.isDirectory) {
            directory.listFiles()?.forEach { file ->
                if (file.name == exceptionFileName) {
                    return@forEach
                }
                if (now - file.lastModified() >= ageInMillis) {
                    if (file.delete()) {
                        NetworkActivity.addFakedEntry(
                            "Deleted file from cache because it is older than $ageInDays days: ${file.absolutePath}",
                            FakedNetworkActivityStatus.Info
                        )
                    } else {
                        showErrorNotification("The cache wiper thread failed to delete", file.absolutePath)
                    }
                }
            }
        }
    }
}
