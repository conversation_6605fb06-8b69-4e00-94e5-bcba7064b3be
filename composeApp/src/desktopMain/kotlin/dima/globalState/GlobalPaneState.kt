package dima.globalState

import dima.apps.AppType
import dima.globalState.GlobalState.panes
import kotlinx.serialization.Serializable

@Serializable
data class AppStateEntry(
    val appType: AppType,
    val appState: AppState
)

@Serializable
data class PaneState(
    /** Used to indicate what pane is active via [GlobalState.activePaneId] **/
    val id: String = getNextPaneId(),
    var appType: AppType,
    /** Ring buffer of app states with max size 20, most recent first **/
    var appStateRing: List<AppStateEntry> = emptyList(),
) {
    fun isActive(): Boolean {
        return GlobalState.activePaneId == id
    }

    /** Get the current app state for the active app type, fallback to global state */
    fun getCurrentAppState(): AppState? {
        return appStateRing.firstOrNull { it.appType == appType }?.appState
            ?: GlobalState.globalAppStates[appType]
            ?: run {
                // Auto-populate globalAppStates with default state if missing
                val defaultState = createDefaultStateForAppType(appType)
                GlobalState.globalAppStates[appType] = defaultState
                defaultState
            }
    }

    /** Update or add app state for the current app type and update global state */
    fun updateAppState(newState: AppState): PaneState {
        // Update global state first (never forgotten)
        GlobalState.globalAppStates[appType] = newState

        val updatedRing = appStateRing.toMutableList()

        // Remove existing entry for this app type if it exists
        updatedRing.removeAll { it.appType == appType }

        // Add new entry at the beginning
        updatedRing.add(0, AppStateEntry(appType, newState))

        // Keep only the most recent 20 entries
        val trimmedRing = updatedRing.take(20)

        return copy(appStateRing = trimmedRing)
    }

    /** Initialize app state from global state when switching to a new app type */
    fun initializeAppStateFromGlobal(): PaneState {
        val globalState = GlobalState.globalAppStates[appType]
        return if (globalState != null && appStateRing.none { it.appType == appType }) {
            updateAppState(globalState)
        } else {
            this
        }
    }
}

/**
 * Generic helper function to update the app state for a specific pane
 * @param paneId The ID of the pane to update
 * @param newState The new app state to set for that pane
 */
fun updatePaneState(paneId: String, newState: AppState) {
    panes = panes.map { pane ->
        if (pane.id == paneId) {
            pane.updateAppState(newState)
        } else {
            pane
        }
    }
}

private fun getNextPaneId(): String {
    // Handle case where GlobalState.panes might not be initialized yet (during object initialization)
    val existingIds = try {
        panes.map { pane ->
            pane.id.toIntOrNull() ?: 0
        }.toSet()
    } catch (_: Exception) {
        // If GlobalState.panes is not initialized yet, start with empty set
        emptySet()
    }
    var nextId = 1
    while (existingIds.contains(nextId)) {
        nextId++
    }
    return nextId.toString()
}

/**
 * Creates a default state for the given app type
 */
private fun createDefaultStateForAppType(appType: AppType): AppState {
    return when (appType) {
        AppType.TailwindContraster -> TailwindContrasterAppState()
        AppType.Email -> EmailAppState()
        AppType.Telegram -> TelegramAppState()

        // For all other app types, use TailwindContrasterAppState as fallback
        // since most apps don't have AppState implementations yet
        else -> TailwindContrasterAppState()
    }
}