package dima.globalState

import dima.apps.AppType
import dima.apps.notifications.showErrorNotification
import dima.dialogs.help.openHelpDialog
import dima.globalState.GlobalState.All
import dima.globalState.GlobalState.app
import dima.globalState.GlobalState.audioPlayer
import dima.globalState.GlobalState.calculator
import dima.globalState.GlobalState.calendar
import dima.globalState.GlobalState.llmSinglePromptApp
import dima.globalState.GlobalState.coldWeather
import dima.globalState.GlobalState.dired
import dima.globalState.GlobalState.email
import dima.globalState.GlobalState.feed
import dima.globalState.GlobalState.helpDialog
import dima.globalState.GlobalState.imageGeneration
import dima.globalState.GlobalState.isDarkMode
import dima.globalState.GlobalState.lastState
import dima.globalState.GlobalState.mainWindow
import dima.globalState.GlobalState.moon
import dima.globalState.GlobalState.networkActivity
import dima.globalState.GlobalState.telegram
import dima.globalState.GlobalState.textEditor
import dima.globalState.GlobalState.timers
import dima.globalState.GlobalState.todoist
import dima.globalState.GlobalState.transcribe
import dima.globalState.GlobalState.treeSitter
import dima.globalState.GlobalState.vero
import dima.globalState.GlobalState.vine
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import setCurrentApp
import java.io.File

private val file = File("state.json")
private val json = Json {
    encodeDefaults = true
    prettyPrint = true
}

object GlobalStateStorage {

    /**
     * Writes the state to the file system only if they have changed.
     */
    fun write() {
        val new = All(
            // Pane management
            panes = GlobalState.panes,
            activePaneId = GlobalState.activePaneId,
            globalAppStates = GlobalState.globalAppStates.toMap(),
            globalAppRing = GlobalState.globalAppRing,

            // Global states
            audioPlayer = audioPlayer,
            mainWindow = mainWindow,
            email = email,
            help = helpDialog,
            todoist = todoist,
            networkActivity = networkActivity,
            treeSitter = treeSitter,
            calculator = calculator,
            vero = vero,
            dired = dired,
            vine = vine,
            textEditor = textEditor,
            llmSinglePromptApp = llmSinglePromptApp,
            rss = feed,
            moon = moon,
            coldWeather = coldWeather,
            // copy for correct equality check below
            timers = timers.toList(),
            calendar = calendar,
            telegram = telegram, // Ensure this is `telegram` not `GlobalState.telegram`
            isDarkMode = isDarkMode,
            transcribe = transcribe,
            imageGeneration = imageGeneration,
        )
        if (lastState == new) {
            return
        }
        file.writeText(json.encodeToString(new))
        lastState = new
    }

    fun initFromFileSystem() {
        if (file.exists()) {
            val stateText = file.readText()
            try {
                val state = Json.decodeFromString<All>(stateText)
                
                // Restore panes
                val wasEmpty = state.panes.isEmpty()
                GlobalState.panes = state.panes.ifEmpty { 
                    listOf(PaneState(id = "1", appType = AppType.Dired))
                }
                GlobalState.activePaneId = state.activePaneId ?: GlobalState.panes.firstOrNull()?.id
                GlobalState.globalAppStates.clear()
                GlobalState.globalAppStates.putAll(state.globalAppStates)
                GlobalState.globalAppRing = if (wasEmpty) {
                    listOf(AppType.Dired)
                } else {
                    state.globalAppRing
                }

                // Global states
                audioPlayer = state.audioPlayer
                mainWindow = state.mainWindow
                moon = state.moon
                coldWeather = state.coldWeather
                email = state.email
                dired = state.dired
                calculator = state.calculator
                networkActivity = state.networkActivity
                textEditor = state.textEditor
                treeSitter = state.treeSitter
                helpDialog = state.help
                todoist = state.todoist
                calendar = state.calendar
                telegram = state.telegram // Ensure this is `telegram` not `GlobalState.telegram`
                vero = state.vero
                vine = state.vine
                llmSinglePromptApp = state.llmSinglePromptApp
                feed = state.rss
                timers = state.timers.toMutableList()
                isDarkMode = state.isDarkMode
                imageGeneration = state.imageGeneration
                transcribe = state.transcribe

                if (state.help.visible) {
                    openHelpDialog()
                }
            } catch (_: SerializationException) {
                showErrorNotification("Failed to load state from file system, resetting to default")
                write()
            }
        } else {
            // File doesn't exist - initialize with default pane
            GlobalState.panes = listOf(PaneState(id = "1", appType = AppType.Dired))
            GlobalState.activePaneId = GlobalState.panes.firstOrNull()?.id
            GlobalState.globalAppRing = listOf(AppType.Dired)
            showErrorNotification("File does not exist")
            write()
        }
    }

}
