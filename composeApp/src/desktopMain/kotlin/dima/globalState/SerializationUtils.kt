package dima.globalState

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer
import kotlin.reflect.typeOf

val defaultJsonForDeepCopy = Json {
    encodeDefaults = true
    ignoreUnknownKeys = true
    prettyPrint = false
}

/**
 * Creates a deep copy of a serializable object using JSON serialization and deserialization.
 *
 * @param T The type of the object to be copied. Must be serializable.
 * @param jsonInstance An optional [Json] instance to use for serialization. Defaults to a lenient configuration.
 * @return A deep copy of the original object.
 * @throws kotlinx.serialization.SerializationException if the object cannot be serialized or deserialized.
 */
@Suppress("UNCHECKED_CAST")
inline fun <reified T> T.deepCopy(jsonInstance: Json = defaultJsonForDeepCopy): T where T : @Serializable Any {
    val serializer = jsonInstance.serializersModule.serializer(typeOf<T>())
    val string = jsonInstance.encodeToString(serializer, this)
    return jsonInstance.decodeFromString(serializer, string) as T
}
