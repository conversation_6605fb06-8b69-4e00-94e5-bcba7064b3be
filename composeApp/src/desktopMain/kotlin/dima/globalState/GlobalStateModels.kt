package dima.globalState

import dima.ai.singlePromptApp.LlmModel
import dima.apps.email.models.EmailFolderType
import dima.apps.networkActivity.NetworkActivityAppTab
import dima.treeSitter.TreeSitter
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import java.time.LocalDate

@Serializable
sealed interface AppState

@Serializable
data class TranscribeState(
    val transcribedTexts: List<String> = emptyList()
)

@Serializable
data class CalculatorState(
    val input: String = "",
    val inputFocused: Boolean = true,
    val decimalPoints: Int = 6,
    val selectedHistory: Int? = null,
)

@Serializable
data class AmazonVineAppState(
    val query: String = "",
    /**
     * null means all categories.
     */
    val category: String? = null,
    val freeOnly: Boolean = true,
    val selectedArticleLink: String? = null,
)

@Serializable
data class TimerState(
    /**
     * Stored as [dima.dateTime.DateTimeFormat.isoDateTimeWithTandZ].
     */
    val start: String,
    val durationSeconds: Int,
    val name: String? = null,
    @Transient val displayText: String = ""
)

@Serializable
data class HelpDialogState(val visible: Boolean = false, val query: String = "")

@Serializable
data class TodoistState(
    val selectedId: String? = null,
)

@Serializable
data class FeedState(
    /**
     * Stored as DateTime in ISO.
     */
    val lastUpdate: String? = null,
    val newOnly: Boolean = false,
    val selectedLink: String? = null,
)

@Serializable
data class TreeSitterAppState(
    val language: String = TreeSitter.defaultAppLanguageName,
    val query: String = "",
    val textToHighlight: String = "",
)

@Serializable
data class TextEditorState(
    val activeBufferName: String? = null,
)

@Serializable
data class VeroGlobalState(
    /**
     * Stored as DateTime in ISO.
     */
    val lastUpdate: String? = null,
    val isWorking: Boolean = false,
    /**
     * 4.5 means 4 hours and 30 minutes.
     */
    val todayDuration: Double = 0.0,
    /**
     * The login token from POST login on the vero hour booking principal which is required on all API calls.
     * It was not required prior to 2025
     */
    val token: String? = null
)

/**
 * The position and the total duration are stored here, since VLC needs some time to load metadata.
 */
@Serializable
data class AudioPlayerState(
    var randomMode: Boolean = true,
    /**
     * The full path to the currently playing track.
     */
    val currentTrack: String? = null,
    val positionMs: Long = 0,
    /**
     * The total duration of the current track.
     */
    var totalDurationMs: Long = 0,
    /**
     * If false, the regular 100% volume is used.
     */
    val quietVolume: Boolean = true,
    val loopTrack: Boolean = false,
    val isPlaying: Boolean = false,
)

@Serializable
data class EmailAppState(
    val selectedUid: String? = null,
    /**
     * Stored as [dima.dateTime.DateTimeFormat.isoDateTimeWithTandZ].
     */
    val lastUpdate: String? = null,
    val isPreviewSelected: Boolean = false,
    val folderType: EmailFolderType? = null,
    /**
     * If empty, do not filter.
     */
    val query: String = "",
    val markedEmails: List<String> = listOf()
) : AppState

@Serializable
data class LlmSinglePromptAppState(
    val selectedModel: LlmModel? = null,
    /**
     * If an empty string, wrap the response with ```. If null, do not patch the response. Otherwise, append ```kotlin, depending on value.
     */
    val codeModeLanguage: String? = null
)

@Serializable
data class TailwindContrasterAppState(
    /** Default to white */
    val selectedBgHex: String = "#ffffff"
) : AppState

@Serializable
data class TelegramAppState(
    /** Currently selected contact ID */
    val selectedContactId: String? = null,
    /** Currently selected message ID */
    val selectedMessageId: Long? = null
) : AppState

@Serializable
data class NetworkActivityState(
    val tab: NetworkActivityAppTab = NetworkActivityAppTab.All,
    /**
     * If empty, do not filter.
     */
    val query: String = "",
    val followMode: Boolean = true
)

@Serializable
data class WindowState(
    val x: Float = 0f,
    val y: Float = 0f,
    val widthInDp: Float = 800f,
    val heightInDp: Float = 600f,
    /**
     * Note that false breaks Hammerspoon sizing.
     */
    val windowDecorated: Boolean = true
)

@Serializable
data class ImageGenerationState(
    val prompt: String = "",
    val numImages: Int = 10,
    val useSlowHighQualityGptImageModel: Boolean = false
)

@Serializable
data class CalendarState(
    @Serializable(with = dima.utils.LocalDateSerializer::class)
    val selectedDate: LocalDate = LocalDate.now()
) {
    // Setter that ensures GlobalState is updated correctly for Compose reactivity
    fun setSelectedDate(date: LocalDate) {
        GlobalState.calendar = GlobalState.calendar.copy(selectedDate = date)
    }
}
