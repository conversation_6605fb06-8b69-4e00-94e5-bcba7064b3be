package dima.globalState

import dima.apps.dired.DiredSort
import dima.os.homeWithoutSlash
import dima.utils.FileAsStringSerializer
import kotlinx.serialization.Serializable
import java.io.File

@Serializable
data class DiredImageState(
    val zoom: Float = 1f,
    val offsetX: Float = 0f,
    val offsetY: Float = 0f
)

enum class DiredFileRowThumbnailMode {
    NoThumbnail,
    ShowThumbnail
}

@Serializable
data class GlobalDiredState(
    /**
     * The absolute directory.
     */
    val directory: String = homeWithoutSlash,
    /**
     * The absolute file path. The entire path is used for easier string comparison.
     */
    val selectedFile: String? = null,
    val sort: DiredSort = DiredSort.Name,
    val imageState: DiredImageState = DiredImageState(),
    val pdfImageState: DiredImageState = DiredImageState(),
    val fileRowThumbnailMode: DiredFileRowThumbnailMode = DiredFileRowThumbnailMode.ShowThumbnail,
    val isPreviewActive: Boolean = false,
    val isPreviewFullWidth: Boolean = false,
    /**
     * If empty, do not filter.
     */
    val search: String = "",
    val showHiddenFiles: Boolean = false,
    val markedFiles: List<@Serializable(with = FileAsStringSerializer::class) File> = emptyList(),
    /**
     * Tracks the last renamed or copied files.
     */
    val lastModifiedFiles: List<String> = emptyList()
) {

    fun setSelectedFile(selectedFile: String?) {
        GlobalState.dired = this.copy(selectedFile = selectedFile)
    }

    fun setDirectory(directory: String) {
        GlobalState.dired = this.copy(directory = directory)
    }

    fun setImageState(state: DiredImageState) {
        GlobalState.dired = this.copy(imageState = state)
    }

    fun setPdfImageState(state: DiredImageState) {
        GlobalState.dired = this.copy(pdfImageState = state)
    }
}