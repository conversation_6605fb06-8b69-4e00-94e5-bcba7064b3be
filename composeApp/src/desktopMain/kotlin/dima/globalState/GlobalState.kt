package dima.globalState

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateMap
import dima.apps.AppType
import dima.telegram.app.TelegramAppState
import kotlinx.serialization.Serializable

object GlobalState {
    @Deprecated("Use panes/activePaneId + PaneState.appType; GlobalState.app is deprecated.")
    var app by mutableStateOf(AppType.Email)
        private set

    var panes: List<PaneState> by mutableStateOf(emptyList())
    var activePaneId: String? by mutableStateOf(panes.firstOrNull()?.id)

    /** Global "last known state" for each app type - never forgotten */
    var globalAppStates: SnapshotStateMap<AppType, AppState> by mutableStateOf(mutableStateMapOf())

    /** Global app ring for tracking recent app types across all panes */
    var globalAppRing: List<AppType> by mutableStateOf(emptyList())

    /**
     * Add an app type to the global app ring (called when pane app type changes)
     */
    fun addToGlobalAppRing(appType: AppType) {
        val currentRing = globalAppRing.toMutableList()
        currentRing.remove(appType)
        currentRing.add(0, appType)
        globalAppRing = currentRing.take(30)
    }

    // Truly global states
    var audioPlayer by mutableStateOf(AudioPlayerState())
    var mainWindow by mutableStateOf(WindowState())
    var moon by mutableStateOf("")

    // Questionable global states, not everything is global
    var email by mutableStateOf(EmailAppState())
    var dired by mutableStateOf(GlobalDiredState())
    var calculator by mutableStateOf(CalculatorState())
    var textEditor by mutableStateOf(TextEditorState())
    var treeSitter by mutableStateOf(TreeSitterAppState())

    /**
     * Contains the lowest cold temperature for today and tomorrow.
     */
    var coldWeather by mutableStateOf<Double?>(null)
    var networkActivity by mutableStateOf(NetworkActivityState())
    var vero by mutableStateOf(VeroGlobalState())
    var timers by mutableStateOf(listOf<TimerState>())
    var llmSinglePromptApp by mutableStateOf(LlmSinglePromptAppState())
    var helpDialog = HelpDialogState()
    var vine = AmazonVineAppState()
    var feed = FeedState()
    var todoist = TodoistState()
    var calendar by mutableStateOf(CalendarState())
    var telegram by mutableStateOf(TelegramAppState())
    var transcribe = TranscribeState()

    var isDarkMode by mutableStateOf(false)
    var imageGeneration = ImageGenerationState()
    internal var lastState: All? = null

    @Serializable
    data class All(
        // Pane management
        val panes: List<PaneState>,
        val activePaneId: String?,
        val globalAppStates: Map<AppType, AppState>,
        val globalAppRing: List<AppType> = emptyList(),

        // Global states
        val audioPlayer: AudioPlayerState,
        val mainWindow: WindowState,
        val email: EmailAppState,
        val help: HelpDialogState,
        val textEditor: TextEditorState,
        val vine: AmazonVineAppState,
        val calculator: CalculatorState,
        val treeSitter: TreeSitterAppState,
        val dired: GlobalDiredState,
        val llmSinglePromptApp: LlmSinglePromptAppState,
        val moon: String,
        val coldWeather: Double?,
        val networkActivity: NetworkActivityState,
        val rss: FeedState,
        val vero: VeroGlobalState,
        val todoist: TodoistState,
        val timers: List<TimerState>,
        val calendar: CalendarState,
        val telegram: TelegramAppState,
        val transcribe: TranscribeState,
        val isDarkMode: Boolean,
        val imageGeneration: ImageGenerationState,
    )
}