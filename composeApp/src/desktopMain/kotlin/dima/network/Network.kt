package dima.network

import dima.apps.notifications.LoadingNotification
import dima.apps.notifications.showLoadingNotification
import dima.os.Hammerspoon
import kotlinx.coroutines.*

private var internetAvailableJob: Job? = null
private var internetAvailableNotification: LoadingNotification? = null

object Network {

    /**
     * Waits until the internet is available by continuously pinging a well-known IP address.
     * A loading notification is displayed during the waiting process, and an information
     * notification is shown once the internet becomes available.
     *
     * The function operates asynchronously using a coroutine and runs in the IO dispatcher
     * to prevent blocking the main thread. The ping command is executed with the help of an
     * external process to overcome DNS resolution limitations in Java.
     *
     * Note: This function uses Google's public DNS server IP (*******) for the ping test.
     */
    fun waitUntilInternetIsAvailable() {
        if (internetAvailableJob != null && !internetAvailableJob!!.isCompleted) {
            internetAvailableJob!!.cancel()
            internetAvailableJob = null
            if (internetAvailableNotification != null) {
                internetAvailableNotification!!.dismiss()
            }
            internetAvailableNotification = null
            return
        }
        internetAvailableNotification = showLoadingNotification("Waiting for internet access...")
        internetAvailableJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                while (true) {
                    val processBuilder = ProcessBuilder("ping", "-c", "1", "*******")
                    val proc = processBuilder.start()
                    val returnVal = proc.waitFor()
                    if (returnVal == 0) {
                        Hammerspoon.showAlert("Internet is available!")
                        break
                    }
                    delay(1000)
                }
            } finally {
                if (internetAvailableNotification != null) {
                    internetAvailableNotification!!.dismiss()
                    internetAvailableNotification = null
                }
            }
        }
    }

}
