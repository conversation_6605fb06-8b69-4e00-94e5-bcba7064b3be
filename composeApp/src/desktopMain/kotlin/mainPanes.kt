import dima.apps.AppType
import dima.apps.notifications.showErrorNotification
import dima.globalState.*

internal fun makeSinglePane() {
    if (GlobalState.panes.size == 1) {
        showErrorNotification("There is only one pane")
        return
    }
    if (GlobalState.panes.isEmpty()) {
        val newPane = PaneState(
            appType = AppType.TailwindContraster,
            appStateRing = listOf(AppStateEntry(AppType.TailwindContraster, TailwindContrasterAppState()))
        )
        GlobalState.panes = listOf(newPane)
        GlobalState.activePaneId = newPane.id
    } else if (GlobalState.panes.size >= 2) {
        val activePane =
            GlobalState.panes.firstOrNull { it.id == GlobalState.activePaneId } ?: GlobalState.panes.first()
        GlobalState.panes = listOf(activePane)
        GlobalState.activePaneId = activePane.id
    }
    openFocusRestorerDialog()
}

internal fun switchToNextPane() {
    if (GlobalState.panes.size == 1) {
        showErrorNotification("There is only one pane")
        return
    }
    val currentIndex = GlobalState.panes.indexOfFirst { it.id == GlobalState.activePaneId }
    val nextIndex = if (currentIndex == -1) {
        0
    } else {
        (currentIndex + 1) % GlobalState.panes.size
    }
    GlobalState.activePaneId = GlobalState.panes[nextIndex].id
    openFocusRestorerDialog()
}

fun getCurrentApp(): AppType {
    val activePane = GlobalState.panes.find { it.id == GlobalState.activePaneId }!!
    return activePane.appType
}

fun setCurrentApp(appType: AppType) {
    println("in setCurrentApp")
    val currentIndex = GlobalState.panes.indexOfFirst { it.id == GlobalState.activePaneId }
    if (currentIndex == -1) {
        // During startup, activePaneId might not match yet - just use the first pane
        if (GlobalState.panes.isNotEmpty()) {
            GlobalState.activePaneId = GlobalState.panes.first().id
        } else {
            return // No panes available, nothing to do
        }
    }
    println(GlobalState.panes)
    GlobalState.panes = GlobalState.panes.map { pane ->
        if (pane.id == GlobalState.activePaneId) {
            // First update the app type, then initialize from global state if needed
            val updatedPane = pane.copy(appType = appType)
            updatedPane.initializeAppStateFromGlobal()
        } else {
            pane
        }
    }

    // Add to global app ring when app type changes
    GlobalState.addToGlobalAppRing(appType)
}

internal fun openNewPane() {
    val activePaneId = GlobalState.activePaneId ?: GlobalState.panes.firstOrNull()?.id
    val activePane = GlobalState.panes.find { it.id == activePaneId }
    if (activePane == null) {
        showErrorNotification("No active pane to split from.")
        return
    }
    val newPaneAppType = activePane.appType

    // Add current app type to global ring when creating new pane
    GlobalState.addToGlobalAppRing(newPaneAppType)
    val newAppStateRing = when (newPaneAppType) {
        AppType.TailwindContraster -> {
            val sourceState = activePane.getCurrentAppState() as? TailwindContrasterAppState
            val copiedState = sourceState?.deepCopy() ?: TailwindContrasterAppState()
            listOf(AppStateEntry(AppType.TailwindContraster, copiedState))
        }

        else -> {
            activePane.appStateRing.map {
                AppStateEntry(it.appType, it.appState.deepCopy())
            }
        }
    }

    val newPane = PaneState(
        appType = newPaneAppType,
        appStateRing = newAppStateRing
    )
    GlobalState.panes = GlobalState.panes + newPane
    GlobalState.activePaneId = newPane.id
    openFocusRestorerDialog()
}

internal fun closeCurrentPane() {
    if (GlobalState.panes.size == 1) {
        showErrorNotification("Can not close the last pane")
        return
    }
    val activePane = GlobalState.panes.find { p -> p.id == GlobalState.activePaneId }!!
    val newPaneIndex = (GlobalState.panes.indexOfFirst { p -> p.id == activePane.id } - 1).coerceAtLeast(0)
    GlobalState.panes = GlobalState.panes.filter { p -> p.id != activePane.id }
    GlobalState.activePaneId = GlobalState.panes[newPaneIndex].id
    openFocusRestorerDialog()
}

