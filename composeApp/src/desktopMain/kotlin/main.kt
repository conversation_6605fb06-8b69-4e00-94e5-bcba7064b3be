
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFontFamilyResolver
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.FrameWindowScope
import androidx.compose.ui.window.WindowPosition
import androidx.compose.ui.window.rememberWindowState
import dev.tclement.fonticons.fa.brands.rememberBrandsFontAwesomeFont
import dima.CustomSettings
import dima.apps.AppPane
import dima.apps.dired.DiredDatabase
import dima.apps.notifications.NotificationOverlay
import dima.apps.textEditor.TextEditorMode
import dima.audio.Audio
import dima.dialogs.DialogIdentifier
import dima.dialogs.audio.openAudioDialog
import dima.dialogs.help.openHelpDialog
import dima.dialogs.help.openMiniHelpDialogForLeaderState
import dima.events.emitEasy
import dima.feeds.Feeds
import dima.feeds.FeedsDatabase
import dima.globalState.GlobalState
import dima.globalState.GlobalStateStorage
import dima.leader.GlobalLeaderOverlay
import dima.leader.LeaderKeyState
import dima.leader.checkIfLeaderKeysHaveAction
import dima.modeline.Modeline
import dima.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.seconds

fun main() {
    val title = "Kotlin Emacs"
    System.setProperty("apple.awt.application.name", title)
    System.setProperty("com.apple.mrj.application.apple.menu.about.name", "Kotlin Emacs")
    setMacDockIcon()
    GlobalStateStorage.initFromFileSystem()

    // Safety check: ensure we always have at least one pane
    if (GlobalState.panes.isEmpty()) {
        GlobalState.panes = listOf(dima.globalState.PaneState(id = "1", appType = dima.apps.AppType.Dired))
        GlobalState.activePaneId = GlobalState.panes.firstOrNull()?.id
        GlobalState.globalAppRing = listOf(dima.apps.AppType.Dired)
    }

    CustomSettings.init()
    launchGlobalThreads()
    Audio.setup()
    // setup early because global functions in Leader open Dired, and would otherwise crash
    DiredDatabase.setup()
    applicationWithQuitListener(onQuit = {
        shutdownApplication()
    }) {
        var leaderKeyState by remember { mutableStateOf<LeaderKeyState?>((null)) }
        val windowState = rememberWindowState(
            position = WindowPosition(x = GlobalState.mainWindow.x.dp, y = GlobalState.mainWindow.y.dp),
            size = DpSize(width = GlobalState.mainWindow.widthInDp.dp, height = GlobalState.mainWindow.heightInDp.dp)
        )

        fun onPreviewKeyEvent(it: KeyEvent): Boolean {
            if (it.isMetaPressed && it.key == Key.T && it.type == KeyEventType.KeyDown) {
                openNewPane()
                return true
            }
            if (it.isMetaPressed && it.key == Key.W && it.type == KeyEventType.KeyDown) {
                closeCurrentPane()
                return true
            }
            if (it.key.isPlus() && it.isMetaPressed && it.type == KeyEventType.KeyDown) {
                openCopyRecentTextHistoryDialog()
                return true
            }
            if (leaderKeyState != null && (it.key == Key.ShiftLeft || it.key == Key.MetaLeft)) {
                // ignore shift for question mark and cmd to not have false positives
                return true
            }
            if (it.type == KeyEventType.KeyDown && it.isQuestionMark() && leaderKeyState != null) {
                openMiniHelpDialogForLeaderState(leaderKeyState!!)
                return true
            }
            if (it.type != KeyEventType.KeyDown ||
                isTextFieldFocused ||
                textEditorMode == TextEditorMode.Insert ||
                textEditorJumpCharMode != TextEditorCharJumpMode.NotActive
            ) {
                return false
            }
            if (dialogs.isNotEmpty() && dialogs.last().identifier != DialogIdentifier.ReadOnlyTextEditor) {
                if (isShowingFocusListOverlay && it.isQuestionMark()) {
                    openFocusRestorerDialog()
                    return true
                }
                return false
            }
            return when {
                leaderKeyState != null -> {
                    val state = leaderKeyState!!
                    if (it.key == Key.Escape) {
                        leaderKeyState = null
                    } else {
                        if (state.first == null) {
                            leaderKeyState = LeaderKeyState(first = it.key)
                        } else if (state.second == null) {
                            leaderKeyState = leaderKeyState!!.copy(second = it.key)
                        }
                        val hasAction = checkIfLeaderKeysHaveAction(leaderKeyState!!)
                        if (hasAction == null) {
                            leaderKeyState = null
                        } else if (hasAction.callback != null) {
                            hasAction.callback.invoke()
                            leaderKeyState = null
                        }
                    }
                    true
                }

                it.key == Key.One -> {
                    makeSinglePane()
                    true
                }

                it.key == Key.W -> {
                    switchToNextPane()
                    true
                }

                it.key == Key.Four -> {
                    GlobalState.mainWindow =
                        GlobalState.mainWindow.copy(windowDecorated = !GlobalState.mainWindow.windowDecorated)
                    true
                }

                it.key == Key.Six -> {
                    Globals.coroutineScope.launch(Dispatchers.IO) {
                        MaximizeWindowForHotReload().emitEasy()
                    }
                    true
                }

                it.key == Key.Minus -> {
                    openAudioDialog()
                    true
                }

                it.isQuestionMark() -> {
                    openHelpDialog()
                    true
                }

                it.key == Key.Spacebar -> {
                    leaderKeyState = LeaderKeyState()
                    true
                }

                it.key.isPlus() -> {
                    globalEvent = GlobalEvent.Reload
                    true
                }

                else -> false
            }
        }

        @Composable
        fun FrameWindowScope.windowBody() {
            remember {
                window.addWindowFocusListener(MyWindowFocusListener())
            }
            Globals.window = window
            Globals.coroutineScope = rememberCoroutineScope()
            Globals.fontFamilyResolver = LocalFontFamilyResolver.current
            Globals.density = LocalDensity.current
            Globals.faBrandsFont = rememberBrandsFontAwesomeFont()
            Globals.textMeasurer = rememberTextMeasurer()

            // Track when panes are ready for startup focus restoration
            var panesInitialized by remember { mutableStateOf(false) }
            LaunchedEffect(GlobalState.panes.size) {
                if (GlobalState.panes.isNotEmpty() && !panesInitialized) {
                    // Wait for panes to be rendered and initialized
                    delay(200)
                    panesInitialized = true
                    if (dialogs.isEmpty()) {
                        RestoreAppFocus().emitEasy()
                    }
                }
            }
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(GlobalStyling.getWindowBackgroundColor())
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f - Modeline.HEIGHT_WEIGHT)
                ) {
                    Row(Modifier.fillMaxSize()) {
                        GlobalState.panes.forEachIndexed { index, paneState ->
                            val paneModifier = if (GlobalState.panes.size >= 2 && paneState.id != GlobalState.activePaneId) {
                                Modifier
                                    .weight(1f)
                                    .clickableWithoutBackgroundRipple { GlobalState.activePaneId = paneState.id }
                            } else {
                                Modifier.weight(1f)
                            }
                            Box(
                                modifier = paneModifier
                            ) {
                                AppPane(paneState)
                                if (GlobalState.panes.size >= 2 && paneState.id == GlobalState.activePaneId) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(8.dp)
                                            .background(GlobalStyling.getSelectedBorderColor())
                                            .align(Alignment.BottomCenter)
                                    )
                                }
                            }
                        }
                    }
                }
                Modeline()
            }
            dialogs.forEach { it.composable(it.id, it.data) }
            GlobalLeaderOverlay(leaderKeyState)
            GlobalFocusLostOverlay()
            NotificationOverlay()
            LaunchedEffect(dialogs) {
                if (dialogs.isEmpty()) {
                    RestoreAppFocus().emitEasy()
                }
            }
        }

        mainUi(
            windowState = windowState,
            onPreviewKeyEvent = { onPreviewKeyEvent(it) },
            windowBody = { windowBody() }
        )
        LaunchedEffect(windowState.position) {
            GlobalState.mainWindow = GlobalState.mainWindow.copy(
                x = windowState.position.x.value,
                y = windowState.position.y.value
            )
        }
        LaunchedEffect(windowState.size) {
            GlobalState.mainWindow = GlobalState.mainWindow.copy(
                widthInDp = windowState.size.width.value,
                heightInDp = windowState.size.height.value
            )
        }
        LaunchedEffect(Unit) {
            launch {
                while (true) {
                    delay(30.seconds)
                    if (Feeds.needsUpdate()) {
                        FeedsDatabase.setup()
                        Feeds.downloadRssYouTubeAndInstagramToDatabase(showNotification = false)
                    }
                }
            }
            launch {
                while (true) {
                    delay(1.seconds)
                    GlobalStateStorage.write()
                }
            }
            handleMaximizeWindowForHotReload(windowState)
        }
        DisposableEffect(Unit) {
            onDispose {
                shutdownApplication()
            }
        }
    }
}
