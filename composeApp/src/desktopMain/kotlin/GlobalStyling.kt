import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.dp
import dima.color.TailwindCssColors
import dima.globalState.GlobalState

object GlobalStyling {
    val leaderOverlayBackgroundColor = TailwindCssColors.orange600
    val smallRoundedCornersDp = 6.dp
    val smallRoundedCorners = RoundedCornerShape(smallRoundedCornersDp)
    val selectedBorderWidth = 4.dp
    val paddingToOuterWindow = 12.dp
    const val DISABLED_ALPHA = 0.6f

    object ScrollBar {
        val outerPadding = 8.dp
    }

    object Light {
        /** Note that the default Compose background color is not white, but slight grayish: rgb(238,238,238) */
        val windowBackgroundColor = TailwindCssColors.white
        val borderColor = TailwindCssColors.gray200
        val redTextColor = TailwindCssColors.red600
        val redBackgroundColor = TailwindCssColors.red600
        val textColor = TailwindCssColors.black
        val grayColor = TailwindCssColors.gray600
        val blueColor = TailwindCssColors.blue600
        val greenColor = TailwindCssColors.green600
        val orangeColor = TailwindCssColors.orange600
        val fuchsiaColor = TailwindCssColors.fuchsia600
        val dividerColor = TailwindCssColors.gray300
        val markedBackgroundColor = TailwindCssColors.orange200
        val selectedInactiveBackgroundColor = TailwindCssColors.gray300
        val markedBackgroundColorForWhiteText = TailwindCssColors.orange600
        val checkboxCheckedColor = TailwindCssColors.violet600
        val checkboxUncheckedColor = TailwindCssColors.gray700
        val selectedBackgroundColor = TailwindCssColors.blue200
        val selectedBorderColor = TailwindCssColors.blue600
    }

    object Dark {
        val windowBackgroundColor = TailwindCssColors.neutral800
        val borderColor = TailwindCssColors.gray600
        val textColor = TailwindCssColors.gray300
        val boldTextColor = TailwindCssColors.white
        val grayColor = TailwindCssColors.gray300
        val redTextColor = TailwindCssColors.red400
        val redBackgroundColor = TailwindCssColors.red600
        val greenColor = TailwindCssColors.green400
        val blueColor = TailwindCssColors.amber500
        val orangeColor = TailwindCssColors.amber500
        val fuchsiaColor = TailwindCssColors.fuchsia400
        val dividerColor = TailwindCssColors.gray400
        val markedBackgroundColor = TailwindCssColors.orange800
        val selectedInactiveBackgroundColor = TailwindCssColors.gray600
        val checkboxCheckedColor = TailwindCssColors.violet600
        val checkboxUncheckedColor = TailwindCssColors.violet200
        val selectedBackgroundColor = TailwindCssColors.blue900
        val selectedBorderColor = TailwindCssColors.blue600
    }

    object Tray {
        val backgroundColor = TailwindCssColors.blue600
        val foregroundColor = TailwindCssColors.white
        val cornerRadius = CornerRadius(10f)
    }

    object Dialog {
        val darkThemeBorderColor = TailwindCssColors.gray500
        val smallRoundedCorners = RoundedCornerShape(5.dp)
        val largeRoundedCorners = RoundedCornerShape(12.dp)
        val backdropColor = TailwindCssColors.black.copy(alpha = 0.5f)
    }

    object TextField {
        val cursorBrush: Brush
            get() = if (GlobalState.isDarkMode) SolidColor(Color.White) else SolidColor(Color.Black)
        val selectedColor: Color
            get() = if (GlobalState.isDarkMode) TailwindCssColors.sky500 else TailwindCssColors.blue600
        val unfocusedColor: Color
            get() {
                return if (GlobalState.isDarkMode) {
                    TailwindCssColors.gray200.copy(alpha = DISABLED_ALPHA)
                } else {
                    TailwindCssColors.gray500
                }
            }
    }

    fun getWindowBackgroundColor() =
        if (GlobalState.isDarkMode) Dark.windowBackgroundColor else Light.windowBackgroundColor

    fun getTextColor() = if (GlobalState.isDarkMode) Dark.textColor else Light.textColor
    fun getBlueColor() = if (GlobalState.isDarkMode) Dark.blueColor else Light.blueColor
    fun getRedTextColor() = if (GlobalState.isDarkMode) Dark.redTextColor else Light.redTextColor
    fun getRedBackgroundColor() = if (GlobalState.isDarkMode) Dark.redBackgroundColor else Light.redBackgroundColor
    fun getGrayColor() = if (GlobalState.isDarkMode) Dark.grayColor else Light.grayColor
    fun getGreenColor() = if (GlobalState.isDarkMode) Dark.greenColor else Light.greenColor
    fun getOrangeColor() = if (GlobalState.isDarkMode) Dark.orangeColor else Light.orangeColor
    fun getFuchsiaColor() = if (GlobalState.isDarkMode) Dark.fuchsiaColor else Light.fuchsiaColor
    fun getCheckboxCheckedColor() =
        if (GlobalState.isDarkMode) Dark.checkboxCheckedColor else Light.checkboxCheckedColor

    fun getCheckboxUncheckedColor() =
        if (GlobalState.isDarkMode) Dark.checkboxUncheckedColor else Light.checkboxUncheckedColor

    fun getSelectedBackgroundColor() =
        if (GlobalState.isDarkMode) Dark.selectedBackgroundColor else Light.selectedBackgroundColor

    fun getSelectedBorderColor() =
        if (GlobalState.isDarkMode) Dark.selectedBorderColor else Light.selectedBorderColor

    fun getDividerColor() = if (GlobalState.isDarkMode) Dark.dividerColor else Light.dividerColor

    fun getMarkedBackgroundColor() =
        if (GlobalState.isDarkMode) Dark.markedBackgroundColor else Light.markedBackgroundColor

    fun getSelectedInactiveBackgroundColor() =
        if (GlobalState.isDarkMode) Dark.selectedInactiveBackgroundColor else Light.selectedInactiveBackgroundColor

    fun getMarkedBackgroundColorForWhiteText() = if (GlobalState.isDarkMode) {
        Dark.markedBackgroundColor
    } else {
        Light.markedBackgroundColorForWhiteText
    }

    fun getBorderColor() = if (GlobalState.isDarkMode) Dark.borderColor else Light.borderColor
    fun getBoldTextColor() = if (GlobalState.isDarkMode) Dark.boldTextColor else Light.textColor
}