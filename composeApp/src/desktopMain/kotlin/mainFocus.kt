import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import dima.dialogs.DialogIdentifier
import dima.dialogs.closeDialogWithId
import dima.dialogs.openDialog
import dima.events.emitEasy
import dima.utils.clickableWithoutBackgroundRipple
import dima.utils.dashedBorder
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

var hasGlobalFocus by mutableStateOf(true)

// TODO at some point in time, using a fake dialog should be removed
fun openFocusRestorerDialog() {
    openDialog(DialogIdentifier.FocusRestorer, data = null) { id, _ ->
        FocusRestorerDialog(id)
    }
    runBlocking {
        RestoreAppFocus().emitEasy()
    }
}

@Composable
fun FocusRestorerDialog(id: Long) {
    LaunchedEffect(Unit) {
        closeDialogWithId(id)
    }
}

/**
 * If this does not work, flip the order of .focused() and .focusRequester() - it works in this order:
 * .focusable()
 * .focusRequester()
 * .onFocusChanged()
 */
@Composable
fun Modifier.handleLostFocus(): Modifier {
    return composed {
        Modifier.onFocusChanged { focus ->
            val s = focus.toString()
            hasGlobalFocus = s == "Active" || s == "ActiveParent"
        }
    }
}

internal var isShowingFocusListOverlay by mutableStateOf(false)

@Composable
internal fun GlobalFocusLostOverlay() {
    LaunchedEffect(hasGlobalFocus) {
        if (hasGlobalFocus) {
            isShowingFocusListOverlay = false
        } else {
            delay(500L)
            if (!hasGlobalFocus) {
                isShowingFocusListOverlay = true
            }
        }
    }

    if (isShowingFocusListOverlay) {
        Popup(alignment = Alignment.Center) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .background(
                        color = GlobalStyling.getWindowBackgroundColor().copy(alpha = 0.8f),
                        shape = GlobalStyling.smallRoundedCorners
                    )
                    .clip(GlobalStyling.smallRoundedCorners)
                    .dashedBorder(
                        strokeWidth = 4.dp,
                        color = GlobalStyling.getRedTextColor(),
                        shape = GlobalStyling.smallRoundedCorners,
                        dashLength = 5.dp,
                        gapLength = 10.dp
                    )
                    .fillMaxSize()
                    .clickableWithoutBackgroundRipple {
                        openFocusRestorerDialog()
                    }
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                ) {
                    Text(
                        text = "Lost Focus",
                        color = GlobalStyling.getRedTextColor(),
                        fontSize = 28.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(bottom = 12.dp)
                    )
                    Text(
                        text = "Restore with ?",
                        color = GlobalStyling.getRedTextColor(),
                        fontSize = 20.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                    )
                }
            }
        }
    }
}