import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.WindowPlacement
import androidx.compose.ui.window.WindowPosition
import androidx.compose.ui.window.WindowState
import dima.apps.dired.DiredSaveCurrentPathToHistory
import dima.apps.dired.DiredSaveToDatabaseThread
import dima.apps.dired.DiredWipeMarkedThread
import dima.apps.email.DownloadAllEmailFoldersAndDeleteUnreadEmailsLocallyThread
import dima.apps.email.UnreadEmailsThread
import dima.apps.textEditor.TextEditorSaveTextToDatabaseThread
import dima.apps.todoist.TasksModelineThread
import dima.ai.singlePromptApp.AiSinglePromptAppService
import dima.audio.Audio
import dima.cache.CacheWiperThread
import dima.events.collectEvent
import dima.globalState.GlobalStateStorage
import dima.moon.MoonThread
import dima.server.JavalinServerThread
import dima.settings
import dima.timers.TimerThread
import dima.utils.startWithLowPriority
import dima.vero.VeroUpdateBookedHoursThread
import dima.weather.ColdTemperatureWeatherThread
import dima.webdriver.WebDriverCleanupThread
import io.sellmair.evas.Events
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.awt.Taskbar
import java.awt.event.WindowEvent
import java.awt.event.WindowFocusListener
import java.awt.image.BufferedImage
import java.io.File
import javax.imageio.ImageIO

val events = Events()

/**
 * Works not always, but kinda works.
 */
internal fun CoroutineScope.handleMaximizeWindowForHotReload(
    windowState: WindowState
) {
    collectEvent<MaximizeWindowForHotReload> {
        launch {
            val screenSize = java.awt.GraphicsEnvironment
                .getLocalGraphicsEnvironment()
                .defaultScreenDevice
                .defaultConfiguration
                .bounds
            val density = androidx.compose.ui.unit.Density(density = 1f)
            launch {
                delay(10)
                // required to set to allow setting position and size
                windowState.placement = WindowPlacement.Floating
                windowState.position = WindowPosition(10.dp, 0.dp)
                windowState.size = DpSize(
                    width = with(density) { screenSize.width.toDp() - 10.dp },
                    height = with(density) { screenSize.height.toDp() }
                )
            }
        }
    }
}

internal fun launchGlobalThreads() {
    buildList {
        add(TimerThread())
        add(JavalinServerThread())
        add(UnreadEmailsThread())
        add(DownloadAllEmailFoldersAndDeleteUnreadEmailsLocallyThread())
        add(TasksModelineThread())
        add(WebDriverCleanupThread())
        add(VeroUpdateBookedHoursThread())
        add(CacheWiperThread())
        add(DiredSaveToDatabaseThread())
        add(DiredWipeMarkedThread())
        add(MoonThread())
        add(DiredSaveCurrentPathToHistory())
        add(TextEditorSaveTextToDatabaseThread())
        if (settings.weather.latitude != null && settings.weather.longitude != null) {
            add(ColdTemperatureWeatherThread())
        }
    }.forEach { it.startWithLowPriority() }
}

/**
 * Note that this is not called when the process is terminated forcibly, such as when using the red 'Stop' button in IntelliJ.
 */
internal fun shutdownApplication() {
    println("Shutting down with proper cleanup")
    AiSinglePromptAppService.shutdown()
    Audio.shutdown()
    GlobalStateStorage.write()
}

internal class MyWindowFocusListener : WindowFocusListener {
    override fun windowGainedFocus(e: WindowEvent?) {
    }

    override fun windowLostFocus(e: WindowEvent?) {
        GlobalStateStorage.write()
    }
}

/**
 * This is only required for Hot Reload. Without hot reload, build.gradle.kts specifies the icon.
 */
internal fun setMacDockIcon() {
    val isMac = System.getProperty("os.name").contains("Mac", ignoreCase = true)
    if (isMac && Taskbar.isTaskbarSupported()) {
        val image: BufferedImage = ImageIO.read(File("icon.png"))
        Taskbar.getTaskbar().iconImage = image
    }
}
