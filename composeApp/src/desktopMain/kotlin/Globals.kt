import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.awt.ComposeWindow
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.Density
import dev.tclement.fonticons.StaticIconFont
import dima.apps.textEditor.TextEditorMode
import kotlinx.coroutines.CoroutineScope

/**
 * The values can also be used in dialogs, and actually do not need to be named differently.
 *
 * Use [dima.utils.LaunchedEffectGlobalEventForApps] to read it in apps.
 */
enum class GlobalEvent {
    ScrollToTop,
    ScrollToBottom,

    /**
     * This is bound to +.
     */
    Reload,

    /**
     * This is like [Reload], but should not download anything.
     */
    ReloadData,

    KillAll,
    KillToEndOfLine,

    CopyAll,
    EnterVisualLineMode,

    DuplicateLine,
    InsertTextFrom<PERSON>ist<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    ToNewEmails
}

enum class TextEditorCharJumpMode {
    NotActive,
    WaitingForFirstCharToJumpTo,
    HintsVisibleAndWaitingForSelection,
}

/**
 * Read it via [dima.utils.LaunchedEffectGlobalEventForApps] or [dima.utils.LaunchedEffectGlobalEventForDialogs].
 * Note that both apps and dialogs can read and set it, but only the active app or the topmost dialog with dialogs
 * taking precedence.
 */
var globalEvent by mutableStateOf<GlobalEvent?>(null)

/**
 * Only modify this inside apps, not for text fields in dialogs since it can cause issues when this is stuck on true,
 * although the dialog was closed.
 */
var isTextFieldFocused by mutableStateOf(false)
var textEditorMode by mutableStateOf(TextEditorMode.Command)
var textEditorJumpCharMode by mutableStateOf(TextEditorCharJumpMode.NotActive)

object Globals {
    lateinit var window: ComposeWindow
    lateinit var coroutineScope: CoroutineScope
    lateinit var fontFamilyResolver: FontFamily.Resolver
    lateinit var density: Density
    lateinit var textMeasurer: TextMeasurer
    lateinit var faBrandsFont: StaticIconFont
}
