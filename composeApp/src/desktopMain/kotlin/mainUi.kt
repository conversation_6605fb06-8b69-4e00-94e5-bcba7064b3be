import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.input.key.KeyEvent
import androidx.compose.ui.window.*
import dima.ai.transcribe.TranscribeTray
import dima.apps.email.NewEmailTray
import dima.audio.AudioTray
import dima.dialogs.DialogData
import dima.dialogs.openDialog
import dima.globalState.GlobalState
import dima.timers.TimerTray
import dima.vero.VeroTray
import kotlinemacs.composeapp.generated.resources.Res
import kotlinemacs.composeapp.generated.resources.icon
import org.jetbrains.compose.resources.painterResource

/**
 * Has to be public, so LaunchedEffect(dialogs) works.
 * Use [openDialog] to open a new dialog. Do not append to [dialogs] directly!
 */
var dialogs by mutableStateOf(listOf<DialogData>())

/**
 * Using 2 windows is a hack since modifying decoration when using a single [Window] crashes.
 */
@Composable
internal fun ApplicationScope.mainUi(
    windowState: WindowState,
    onPreviewKeyEvent: (KeyEvent) -> Boolean,
    windowBody: @Composable FrameWindowScope.() -> Unit,
) {
    val title = "Kotlin Emacs"
    if (GlobalState.mainWindow.windowDecorated) {
        Window(
            icon = painterResource(Res.drawable.icon),
            state = windowState,
            onCloseRequest = ::exitApplication,
            onPreviewKeyEvent = { onPreviewKeyEvent(it) },
            title = title,
        ) {
            windowBody()
        }
    } else {
        @OptIn(ExperimentalComposeUiApi::class) // for undecorated
        Window(
            icon = painterResource(Res.drawable.icon),
            state = windowState,
            decoration = WindowDecoration.Undecorated(),
            onCloseRequest = ::exitApplication,
            onPreviewKeyEvent = { onPreviewKeyEvent(it) },
            title = title,
        ) {
            windowBody()
        }
    }
    TimerTray()
    AudioTray()
    NewEmailTray()
    VeroTray()
    TranscribeTray()
}
