#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x000000010676a6c0, pid=96330, tid=99843
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.6+9-895.109-jcef (21.0.6+9) (build 21.0.6+9-b895.109)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.6+9-895.109-jcef (21.0.6+9-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, bsd-aarch64)
# Problematic frame:
# V  [libjvm.dylib+0x62a6c0]  VM_EnhancedRedefineClasses::do_topological_class_sorting(JavaThread*)+0x308
#
# No core dump will be written. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://youtrack.jetbrains.com/issues/JBR
#

---------------  S U M M A R Y ------------

Command Line: -Dcompose.reload.argfile=/Users/<USER>/Developer/kotlin-emacs/composeApp/build/run/desktopMain/desktopMain.argfile -Dcompose.reload.build.continuous=true -Dcompose.reload.buildSystem=Gradle -Dcompose.reload.devToolsClasspath=/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-devtools/1.0.0-alpha11/7271425ca174e47a07c9c9e548f740368134ca61/hot-reload-devtools-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-runtime-api-jvm/1.0.0-alpha11/e784fec47791cba1a21f056aed2408a90bb72576/hot-reload-runtime-api-jvm-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-runtime-jvm/1.0.0-alpha11/f62217e3b4ce58047ec1412be4ef66c2ae91edb4/hot-reload-runtime-jvm-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.desktop/desktop-jvm/1.8.1/7a5516c08efe9fef2e6aa38a10e5fc17fb556148/desktop-jvm-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material3/material3-desktop/1.8.1/763803ef38c846a60dadabe187bd402c5a52222e/material3-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.components/components-resources-desktop/1.8.1/10110d785f486bc13235f40e1d0b91a48b5f3ee6/library-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.sellmair/evas-compose-jvm/1.3.0/1214dcce9040d1b9f856b02b443cf8c2ebe1573a/evas-compose-jvm-1.3.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-desktop/1.8.1/ef0aa2ceffba36f64a1ba916c1d0116fd3c383c6/material-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-ripple-desktop/1.8.1/5364a917218879742c9f08f55791adb3a7c26c8a/material-ripple-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.foundation/foundation-desktop/1.8.1/43f4d514df27ef16c4fbe77807c2a7e12e18b6fd/foundation-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-icons-core-desktop/1.7.3/414b3096050fe647b0638832eace80f22c06b85c/material-icons-core-des-Dcompose.reload.devToolsEnabled=true -Dcompose.reload.devToolsHeadless=false -Dcompose.reload.devToolsTransparencyEnabled=true -Dcompose.reload.dirtyResolveDepthLimit=5 -Dcompose.reload.launchMode=GradleBlocking -Dcompose.reload.mainClass=MainKt -Dcompose.reload.pidFile=/Users/<USER>/Developer/kotlin-emacs/composeApp/build/run/desktopMain/desktopMain.pid -Dcompose.reload.virtualMethodResolveEnabled=true -Dgradle.build.project=:composeApp -Dgradle.build.root=/Users/<USER>/Developer/kotlin-emacs -Dgradle.build.task=hotReloadDesktopMain -Dorg.gradle.java.home=/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home -XX:+IgnoreUnrecognizedVMOptions -XX:+AllowEnhancedClassRedefinition -javaagent:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-agent/1.0.0-alpha11/c4ee8ce7bac236244150134658fff9a6f21443ad/hot-reload-agent-1.0.0-alpha11.jar -Dfile.encoding=UTF-8 -Duser.country=DE -Duser.language=en -Duser.variant MainKt

Host: "MacBookPro17,1" arm64, 8 cores, 16G, Darwin 23.0.0, macOS 14.0 (23A344)
Time: Tue Jun 17 23:42:10 2025 EEST elapsed time: 518.822713 seconds (0d 0h 8m 38s)

---------------  T H R E A D  ---------------

Current thread (0x000000011e26be00):  JavaThread "AWT-EventQueue-0"        [_thread_in_vm, id=99843, stack(0x000000028c184000,0x000000028c387000) (2060K)]

Stack: [0x000000028c184000,0x000000028c387000],  sp=0x000000028c385be0,  free space=2054k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [libjvm.dylib+0x62a6c0]  VM_EnhancedRedefineClasses::do_topological_class_sorting(JavaThread*)+0x308
V  [libjvm.dylib+0x627b28]  VM_EnhancedRedefineClasses::find_sorted_affected_classes(bool, GrowableArray<Klass*>*, JavaThread*)+0x18c
V  [libjvm.dylib+0x6265d0]  VM_EnhancedRedefineClasses::load_new_class_versions(JavaThread*)+0xe0
V  [libjvm.dylib+0x626380]  VM_EnhancedRedefineClasses::doit_prologue()+0x170
V  [libjvm.dylib+0xa9e798]  VMThread::execute(VM_Operation*)+0x74
V  [libjvm.dylib+0x6753f0]  JvmtiEnv::RedefineClasses(int, jvmtiClassDefinition const*)+0x74
V  [libjvm.dylib+0x63a7d0]  jvmti_RedefineClasses(_jvmtiEnv*, int, jvmtiClassDefinition const*)+0x1d4
C  [libinstrument.dylib+0x463c]  redefineClasses+0x414
j  sun.instrument.InstrumentationImpl.redefineClasses0(J[Ljava/lang/instrument/ClassDefinition;)V+0 java.instrument@21.0.6
j  sun.instrument.InstrumentationImpl.redefineClasses([Ljava/lang/instrument/ClassDefinition;)V+79 java.instrument@21.0.6
j  org.jetbrains.compose.reload.agent.ReloadKt.reload(Ljava/lang/instrument/Instrumentation;Ljava/util/UUID;Ljava/util/Map;)Lorg/jetbrains/compose/reload/core/Either;+1674
j  org.jetbrains.compose.reload.agent.ReloadRequestHandlerKt$launchReloadRequestHandler$1$1.invoke()V+58
j  org.jetbrains.compose.reload.agent.ReloadRequestHandlerKt$launchReloadRequestHandler$1$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.compose.reload.agent.MainThreadKt.runOnMainThread$lambda$0(Ljava/util/concurrent/CompletableFuture;Lkotlin/jvm/functions/Function0;)V+3
j  org.jetbrains.compose.reload.agent.MainThreadKt$$Lambda+0x000000b801313db0.run()V+8
J 33264 c2 java.awt.EventDispatchThread.pumpOneEventForFilters(I)V java.desktop (106 bytes) @ 0x000000013ff2ac8c [0x000000013ff29f80+0x0000000000000d0c]
j  java.awt.EventDispatchThread.pumpEventsForFilter(ILjava/awt/Conditional;Ljava/awt/EventFilter;)V+35 java.desktop
j  java.awt.EventDispatchThread.pumpEventsForHierarchy(ILjava/awt/Conditional;Ljava/awt/Component;)V+11 java.desktop
j  java.awt.EventDispatchThread.pumpEvents(ILjava/awt/Conditional;)V+4 java.desktop
j  java.awt.EventDispatchThread.pumpEvents(Ljava/awt/Conditional;)V+3 java.desktop
j  java.awt.EventDispatchThread.run()V+18 java.desktop
v  ~StubRoutines::call_stub 0x000000013f534154
V  [libjvm.dylib+0x4b761c]  JavaCalls::call_helper(JavaValue*, methodHandle const&, JavaCallArguments*, JavaThread*)+0x3d8
V  [libjvm.dylib+0x4b6554]  JavaCalls::call_virtual(JavaValue*, Klass*, Symbol*, Symbol*, JavaCallArguments*, JavaThread*)+0x140
V  [libjvm.dylib+0x4b6620]  JavaCalls::call_virtual(JavaValue*, Handle, Klass*, Symbol*, Symbol*, JavaThread*)+0x64
V  [libjvm.dylib+0x588ad8]  thread_entry(JavaThread*, JavaThread*)+0x9c
V  [libjvm.dylib+0x4cbcdc]  JavaThread::thread_main_inner()+0x98
V  [libjvm.dylib+0xa14038]  Thread::call_run()+0xc8
V  [libjvm.dylib+0x83379c]  thread_native_entry(Thread*)+0x118
C  [libsystem_pthread.dylib+0x7034]  _pthread_start+0x88
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  sun.instrument.InstrumentationImpl.redefineClasses0(J[Ljava/lang/instrument/ClassDefinition;)V+0 java.instrument@21.0.6
j  sun.instrument.InstrumentationImpl.redefineClasses([Ljava/lang/instrument/ClassDefinition;)V+79 java.instrument@21.0.6
j  org.jetbrains.compose.reload.agent.ReloadKt.reload(Ljava/lang/instrument/Instrumentation;Ljava/util/UUID;Ljava/util/Map;)Lorg/jetbrains/compose/reload/core/Either;+1674
j  org.jetbrains.compose.reload.agent.ReloadRequestHandlerKt$launchReloadRequestHandler$1$1.invoke()V+58
j  org.jetbrains.compose.reload.agent.ReloadRequestHandlerKt$launchReloadRequestHandler$1$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.compose.reload.agent.MainThreadKt.runOnMainThread$lambda$0(Ljava/util/concurrent/CompletableFuture;Lkotlin/jvm/functions/Function0;)V+3
j  org.jetbrains.compose.reload.agent.MainThreadKt$$Lambda+0x000000b801313db0.run()V+8
J 33264 c2 java.awt.EventDispatchThread.pumpOneEventForFilters(I)V java.desktop (106 bytes) @ 0x000000013ff2ac8c [0x000000013ff29f80+0x0000000000000d0c]
j  java.awt.EventDispatchThread.pumpEventsForFilter(ILjava/awt/Conditional;Ljava/awt/EventFilter;)V+35 java.desktop
j  java.awt.EventDispatchThread.pumpEventsForHierarchy(ILjava/awt/Conditional;Ljava/awt/Component;)V+11 java.desktop
j  java.awt.EventDispatchThread.pumpEvents(ILjava/awt/Conditional;)V+4 java.desktop
j  java.awt.EventDispatchThread.pumpEvents(Ljava/awt/Conditional;)V+3 java.desktop
j  java.awt.EventDispatchThread.run()V+18 java.desktop
v  ~StubRoutines::call_stub 0x000000013f534154

siginfo: si_signo: 11 (SIGSEGV), si_code: 2 (SEGV_ACCERR), si_addr: 0x0000000000000000

Registers:
 x0=0x000000028c385c80  x1=0x000000028c385c38  x2=0x000000011e26be00  x3=0x00006000010e0140
 x4=0x000000028c385c18  x5=0x0000000000000000  x6=0x0000000000000001  x7=0x000000011e26be00
 x8=0x000000071e1cb7c8  x9=0x534c8339625f00d2 x10=0x000000b800000000 x11=0x00000000d1750009
x12=0x0000000000034e00 x13=0x00034e0000034e00 x14=0x0000000000000040 x15=0x0000000000000006
x16=0x0000000189bc1b4c x17=0x0000600001382130 x18=0x0000000000000000 x19=0x0000000000000018
x20=0x0000000000000000 x21=0x0000000106f0f9d8 x22=0x000000028c386010 x23=0x000000011e26be00
x24=0x0000000000000011 x25=0x000000b801155e18 x26=0x0000000700e1d570 x27=0x000000028c385c20
x28=0x0000000000000002  fp=0x000000028c385ea0  lr=0x000000010676a58c  sp=0x000000028c385be0
pc=0x000000010676a6c0 cpsr=0x0000000060001000

Register to memory mapping:

x0 =0x000000028c385c80 is pointing into the stack for thread: 0x000000011e26be00
x1 =0x000000028c385c38 is pointing into the stack for thread: 0x000000011e26be00
x2 =0x000000011e26be00 is a thread
x3 =0x00006000010e0140 points into unknown readable memory: 0x000000012d705310 | 10 53 70 2d 01 00 00 00
x4 =0x000000028c385c18 is pointing into the stack for thread: 0x000000011e26be00
x5 =0x0 is null
x6 =0x0000000000000001 is an unknown value
x7 =0x000000011e26be00 is a thread
x8 =0x000000071e1cb7c8 is an oop: java.lang.ClassFormatError 
{0x000000071e1cb7c8} - klass: 'java/lang/ClassFormatError'
 - ---- fields (total size 5 words):
 - private transient 'depth' 'I' @12  19 (0x00000013)
 - private transient 'backtrace' 'Ljava/lang/Object;' @16  a 'java/lang/Object'[7] {0x000000071e1cb7f0} (0xe3c396fe)
 - private 'detailMessage' 'Ljava/lang/String;' @20  "Illegal field modifiers in class dima/globalState/AppState: 0x9"{0x000000071e1cb488} (0xe3c39691)
 - private 'cause' 'Ljava/lang/Throwable;' @24  a 'java/lang/ClassFormatError'{0x000000071e1cb7c8} (0xe3c396f9)
 - private 'stackTrace' '[Ljava/lang/StackTraceElement;' @28  a 'java/lang/StackTraceElement'[0] {0x00000007008c0348} (0xe0118069)
 - private 'suppressedExceptions' 'Ljava/util/List;' @32  a 'java/util/Collections$EmptyList'{0x00000007008c0358} (0xe011806b)
x9 =0x534c8339625f00d2 is an unknown value
x10=0x000000b800000000 is pointing into metadata
x11=0x00000000d1750009 is an unknown value
x12=0x0000000000034e00 is an unknown value
x13=0x00034e0000034e00 is an unknown value
x14=0x0000000000000040 is an unknown value
x15=0x0000000000000006 is an unknown value
x16=0x0000000189bc1b4c: pthread_mutex_unlock+0 in /usr/lib/system/libsystem_pthread.dylib at 0x0000000189bc0000
x17=0x0000600001382130 points into unknown readable memory: 0x00000000ffffffff | ff ff ff ff 00 00 00 00
x18=0x0 is null
x19=0x0000000000000018 is an unknown value
x20=0x0 is null
x21=0x0000000106f0f9d8: _MergedGlobals.108+0x600 in /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/server/libjvm.dylib at 0x0000000106140000
x22=0x000000028c386010 is pointing into the stack for thread: 0x000000011e26be00
x23=0x000000011e26be00 is a thread
x24=0x0000000000000011 is an unknown value
x25=0x000000b801155e18 is a pointer to class: 
dima.globalState.AppStateEntry {0x000000b801155e18}
 - instance size:     3
 - klass size:        69
 - access:            public final synchronized 
 - flags:             rewritten has_nonstatic_fields should_verify_class has_localvariable_table has_final_method has_resolved_methods 
 - state:             fully_initialized
 - name:              'dima/globalState/AppStateEntry'
 - super:             'java/lang/Object'
 - sub:               
 - arrays:            null
 - methods:           Array<T>(0x0000000128a612a8)
 - method ordering:   Array<T>(0x0000000128a62278)
 - local interfaces:  Array<T>(0x000000b8006ab048)
 - trans. interfaces: Array<T>(0x000000b8006ab048)
 - constants:         constant pool [252]/operands[19] {0x0000000128a609a8} for 'dima/globalState/AppStateEntry' cache=0x0000000128a622f8
 - class loader data:  loader data: 0x00006000010e0140 for instance a 'jdk/internal/loader/ClassLoaders$AppClassLoader'{0x00000007008afae0}
 - source file:       'GlobalPaneState.kt'
 - class annotations:       Array<T>(0x0000000128a62148)
 - inner classes:     Array<T>(0x0000000128a621f0)
 - nest members:     Array<T>(0x000000b8006aafd0)
 - permitted subclasses:     Array<T>(0x000000b8006aafd0)
 - java mirror:       a 'java/lang/Class'{0x0000000700f53750} = 'dima/globalState/AppStateEntry'
 - vtable length      5  (start addr: 0x000000b801156000)
 - itable length      2 (start addr: 0x000000b801156028)
 - ---- static fields (2 words):
 - public static final 'Companion' 'Ldima/globalState/AppStateEntry$Companion;' @112 
 - public static final '$stable' 'I' @120 
 - private static final '$childSerializers' '[Lkotlin/Lazy;' @116 
 - ---- non-static fields (2 words):
 - private final 'appType' 'Ldima/apps/AppType;' @12 
 - private final 'appState' 'Ldima/globalState/AppState;' @16 
 - non-static oop maps: 12-16 
x26=0x0000000700e1d570 is an oop: java.security.ProtectionDomain 
{0x0000000700e1d570} - klass: 'java/security/ProtectionDomain'
 - ---- fields (total size 5 words):
 - private 'hasAllPerm' 'Z' @12  false (0x00)
 - private final 'staticPermissions' 'Z' @13  false (0x00)
 - private final 'codesource' 'Ljava/security/CodeSource;' @16  a 'java/security/CodeSource'{0x0000000700e1d598} (0xe01c3ab3)
 - private final 'classloader' 'Ljava/lang/ClassLoader;' @20  a 'jdk/internal/loader/ClassLoaders$AppClassLoader'{0x00000007008afae0} (0xe0115f5c)
 - private final 'principals' '[Ljava/security/Principal;' @24  a 'java/security/Principal'[0] {0x0000000700e1d648} (0xe01c3ac9)
 - private 'permissions' 'Ljava/security/PermissionCollection;' @28  a 'sun/security/util/LazyCodeSourcePermissionCollection'{0x0000000700e1d658} (0xe01c3acb)
 - final 'key' 'Ljava/security/ProtectionDomain$Key;' @32  a 'java/security/ProtectionDomain$Key'{0x0000000700e1d818} (0xe01c3b03)
x27=0x000000028c385c20 is pointing into the stack for thread: 0x000000011e26be00
x28=0x0000000000000002 is an unknown value
 fp=0x000000028c385ea0 is pointing into the stack for thread: 0x000000011e26be00
 lr=0x000000010676a58c: _ZN26VM_EnhancedRedefineClasses28do_topological_class_sortingEP10JavaThread+0x1d4 in /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/server/libjvm.dylib at 0x0000000106140000
 sp=0x000000028c385be0 is pointing into the stack for thread: 0x000000011e26be00

Top of Stack: (sp=0x000000028c385be0)
0x000000028c385be0:   000000028c385e50 00000000000003d8
0x000000028c385bf0:   000000011c124c28 000000011c124850
0x000000028c385c00:   000000011c124840 0000600002f38ba0
0x000000028c385c10:   0000000102fe9860 0000600001eace58
0x000000028c385c20:   0000000000000000 0000000000000000
0x000000028c385c30:   2a0f00a704000000 0000000106e11100
0x000000028c385c40:   000000011e984600 000000011e98640a
0x000000028c385c50:   000000011e984600 0000000106d18920
0x000000028c385c60:   000000028c380001 0000000200000000
0x000000028c385c70:   000000011c124850 0000000000000000
0x000000028c385c80:   000000028c385c38 0000600000292a38
0x000000028c385c90:   00006000010e0140 00000000063e0000
0x000000028c385ca0:   0000000000000000 0000000000000000
0x000000028c385cb0:   0000000000000000 0000000000000000
0x000000028c385cc0:   0000000000000000 0000000000000000
0x000000028c385cd0:   0000000000000000 0001100000450000
0x000000028c385ce0:   0000000000000000 0000000000000000
0x000000028c385cf0:   0000000000000000 0000000000000000
0x000000028c385d00:   0000000000000000 0000000000000000
0x000000028c385d10:   0000000000000000 0000000000000000
0x000000028c385d20:   0000000000000000 0000000000000000
0x000000028c385d30:   0000000000000000 0000000000000000
0x000000028c385d40:   0000000000000000 0000000000000000
0x000000028c385d50:   0000000000000000 0000000000000000
0x000000028c385d60:   0000000000000000 a3b10029005501ff
0x000000028c385d70:   4dff11ff15006200 11f703ff927111fc
0x000000028c385d80:   07007a11f409ff21 12ed03ff605012f0
0x000000028c385d90:   230918500607ff33 12e203ff1a0bff09
0x000000028c385da0:   00001d47ffe3f915 000000011e26be00
0x000000028c385db0:   00000007009116b0 0000000000000000
0x000000028c385dc0:   000000028c385e30 0000600001eace10
0x000000028c385dd0:   000000028c385df0 0000000106262c24 

Instructions: (pc=0x000000010676a6c0)
0x000000010676a5c0:   a0 00 00 54 2c 79 6a f8 4b 05 00 91 9f 01 14 eb
0x000000010676a5d0:   41 ff ff 54 5f 01 08 eb 22 07 00 54 f3 23 51 29
0x000000010676a5e0:   7f 02 08 6b 81 02 00 54 68 06 00 11 1f 01 13 6a
0x000000010676a5f0:   09 00 80 12 64 02 49 7a 08 11 c0 5a e8 03 08 4b
0x000000010676a600:   29 00 80 52 28 21 c8 1a 00 d5 93 1a e0 8f 00 b9
0x000000010676a610:   e2 4f 40 f9 42 01 00 b4 e2 01 00 37 01 02 80 52
0x000000010676a620:   49 b5 f8 97 fa 03 00 aa 7f 06 00 71 0a 02 00 54
0x000000010676a630:   19 00 00 14 fa 4b 40 f9 1d 00 00 14 01 02 80 52
0x000000010676a640:   3b b5 f8 97 fa 03 00 aa 7f 06 00 71 0a 01 00 54
0x000000010676a650:   11 00 00 14 42 20 01 53 01 02 80 52 4b b5 f8 97
0x000000010676a660:   fa 03 00 aa 7f 06 00 71 6b 01 00 54 08 00 80 d2
0x000000010676a670:   09 00 80 d2 ea 4b 40 f9 40 69 e8 3c 40 6b a8 3c
0x000000010676a680:   29 05 00 91 f3 8b 80 b9 08 41 00 91 3f 01 13 eb
0x000000010676a690:   2b ff ff 54 e0 4b 40 f9 80 00 00 b4 e8 63 42 39
0x000000010676a6a0:   48 00 00 36 40 b5 f8 97 fa 4b 00 f9 68 06 00 11
0x000000010676a6b0:   e8 8b 00 b9 48 d3 33 8b 14 65 00 a9 f4 8b 40 f9
0x000000010676a6c0:   88 02 40 b9 1f 05 00 71 6b ec ff 54 15 00 80 d2
0x000000010676a6d0:   0a 00 00 14 fa 4b 00 f9 08 07 00 11 e8 8b 00 b9
0x000000010676a6e0:   48 d3 38 8b 13 65 00 a9 88 02 40 b9 b5 06 00 91
0x000000010676a6f0:   bf c2 28 eb 0a eb ff 54 89 0e 15 8b 33 05 40 f9
0x000000010676a700:   73 ff ff b4 ca 26 40 f9 49 01 40 b9 3f 05 00 71
0x000000010676a710:   eb fe ff 54 4a 05 40 f9 4b 01 40 f9 7f 01 13 eb
0x000000010676a720:   60 01 00 54 2c 00 80 52 eb 03 0c aa 3f 01 0c eb
0x000000010676a730:   a0 00 00 54 4d 79 6b f8 6c 05 00 91 bf 01 13 eb
0x000000010676a740:   41 ff ff 54 7f 01 09 eb 22 fd ff 54 f8 23 51 29
0x000000010676a750:   1f 03 08 6b 81 02 00 54 08 07 00 11 1f 01 18 6a
0x000000010676a760:   09 00 80 12 04 03 49 7a 08 11 c0 5a e8 03 08 4b
0x000000010676a770:   29 00 80 52 28 21 c8 1a 00 d5 98 1a e0 8f 00 b9
0x000000010676a780:   e2 4f 40 f9 42 01 00 b4 e2 01 00 37 01 02 80 52
0x000000010676a790:   ed b4 f8 97 fa 03 00 aa 1f 07 00 71 0a 02 00 54
0x000000010676a7a0:   19 00 00 14 fa 4b 40 f9 cc ff ff 17 01 02 80 52
0x000000010676a7b0:   df b4 f8 97 fa 03 00 aa 1f 07 00 71 0a 01 00 54 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x000000028c385e50 is pointing into the stack for thread: 0x000000011e26be00
stack at sp + 1 slots: 0x00000000000003d8 is an unknown value
stack at sp + 2 slots: 0x000000011c124c28 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
stack at sp + 3 slots: 0x000000011c124850 points into unknown readable memory: 0x0000000106e47f48 | 48 7f e4 06 01 00 00 00
stack at sp + 4 slots: 0x000000011c124840 points into unknown readable memory: 0x00000002a2ad7600 | 00 76 ad a2 02 00 00 00
stack at sp + 5 slots: 0x0000600002f38ba0 points into unknown readable memory: 0x0000000000000002 | 02 00 00 00 00 00 00 00
stack at sp + 6 slots: 0x0000000102fe9860 is an unknown value
stack at sp + 7 slots: 0x0000600001eace58 points into unknown readable memory: 0x0000000700e1d570 | 70 d5 e1 00 07 00 00 00


Compiled method (c2) 518832 33264   !   4       java.awt.EventDispatchThread::pumpOneEventForFilters (106 bytes)
 total in heap  [0x000000013ff29710,0x000000013ff31c40] = 34096
 relocation     [0x000000013ff29860,0x000000013ff29f78] = 1816
 main code      [0x000000013ff29f80,0x000000013ff2ef30] = 20400
 stub code      [0x000000013ff2ef30,0x000000013ff2f550] = 1568
 oops           [0x000000013ff2f550,0x000000013ff2f5b0] = 96
 metadata       [0x000000013ff2f5b0,0x000000013ff2faf0] = 1344
 scopes data    [0x000000013ff2faf0,0x000000013ff30b30] = 4160
 scopes pcs     [0x000000013ff30b30,0x000000013ff31530] = 2560
 dependencies   [0x000000013ff31530,0x000000013ff31668] = 312
 handler table  [0x000000013ff31668,0x000000013ff31b30] = 1224
 nul chk table  [0x000000013ff31b30,0x000000013ff31c40] = 272

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000001293e2108} 'pumpOneEventForFilters' '(I)V' in 'java/awt/EventDispatchThread'
  # this:     c_rarg1:c_rarg1 
                        = 'java/awt/EventDispatchThread'
  # parm0:    c_rarg2   = int
  #           [sp+0x80]  (sp of caller)
  0x000000013ff29f80: 2808 40b9 | 3f01 086b | 4000 0054 

  0x000000013ff29f8c: ;   {runtime_call ic_miss_stub}
  0x000000013ff29f8c: bd17 d917 
[Verified Entry Point]
  0x000000013ff29f90: 1f20 03d5 | e953 40d1 | 3f01 00f9 | ff03 02d1 | fd7b 07a9 | 487c 0218 | 8923 40b9 | 1f01 09eb 
  0x000000013ff29fb0: 417b 0254 | 8ac3 41f9 | ed83 0191 | 5a01 40f9 | 2c00 40f9 | ac01 0837 | 8b01 40b2 | ab01 00f9 
  0x000000013ff29fd0: ec03 0baa | 2dfc ecc8 | 9f01 0beb | 6002 0054 | e803 0091 | 8c01 08cb | ebcf 72b2 | 8b01 0bea 
  0x000000013ff29ff0: ab01 00f9 | 0d00 0014 | 8bf9 0091 | ea03 1faa | 7cfd eac8 | 5f01 1feb | eb07 40b2 | ab01 00f9 
  0x000000013ff2a010: c000 0054 | 5f01 1ceb | 8100 0054 | 8861 48f8 | 0805 0091 | 8861 08f8 | 8100 0054 | 88a7 42f9 
  0x000000013ff2a030: 0805 0091 | 88a7 02f9 | fd03 022a | f803 01aa | 6176 0154 | 177b 40b9 | ec83 0191 | 8b01 40f9 
  0x000000013ff2a050: 7f01 1feb | 8002 0054 | 0a03 40f9 | aa00 0837 | ea03 0caa | 0bff aac8 | 5f01 0ceb | 0e00 0014 
  0x000000013ff2a070: 4a09 00d1 | 4b45 40f9 | ab00 00b4 | 6b05 00d1 | 4b45 00f9 | 7f01 0beb | 0700 0014 | 482d 49a9 
  0x000000013ff2a090: 0801 0baa | 1f01 1feb | 6800 00b5 | 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 
  0x000000013ff2a0b0: 88a7 02f9 | 0171 0154 | ebf2 7dd3 | ec03 1d2a | 9f05 0031 | c18c 0154 

  0x000000013ff2a0c8: ; implicit exception: dispatches to 0x000000013ff2e3e8
  0x000000013ff2a0c8: 6c45 40b9 | 8cf1 7dd3 

  0x000000013ff2a0d0: ; implicit exception: dispatches to 0x000000013ff2e400
  0x000000013ff2a0d0: 8a0d 40b9 | 4ef1 7dd3 | 6e68 01b4 | f083 0191 | ca01 40f9 | aa01 0837 | 4d01 40b2 | 0d02 00f9 
  0x000000013ff2a0f0: ea03 0daa | d0fd eac8 | 5f01 0deb | 6002 0054 | e803 0091 | 4a01 08cb | edcf 72b2 | 4d01 0dea 
  0x000000013ff2a110: 0d02 00f9 | 0d00 0014 | 4df9 0091 | ef03 1faa | bcfd efc8 | ff01 1feb | ed07 40b2 | 0d02 00f9 
  0x000000013ff2a130: c000 0054 | ff01 1ceb | 8100 0054 | 4861 48f8 | 0805 0091 | 4861 08f8 | 8100 0054 | 88a7 42f9 
  0x000000013ff2a150: 0805 0091 | 88a7 02f9 | c16e 0154 | 8a31 40b9 | 4af1 7dd3 

  0x000000013ff2a164: ; implicit exception: dispatches to 0x000000013ff2e588
  0x000000013ff2a164: 500d 40b9 

  0x000000013ff2a168: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2a168: ad03 bcd2 | adb0 93f2 

  0x000000013ff2a170: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2a170: b103 bcd2 | b1b0 93f2 | 1f02 0d6b | ef0f 0d8b | f10f 118b | 4104 0054 | 5611 40b9 | ed83 0191 
  0x000000013ff2a190: ac01 40f9 | 9f01 1feb | 8002 0054 | ca01 40f9 | aa00 0837 | ea03 0daa | ccfd aac8 | 5f01 0deb 
  0x000000013ff2a1b0: 0e00 0014 | 4a09 00d1 | 4c45 40f9 | ac00 00b4 | 8c05 00d1 | 4c45 00f9 | 9f01 0ceb | 0700 0014 
  0x000000013ff2a1d0: 4831 49a9 | 0801 0caa | 1f01 1feb | 6800 00b5 | 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 
  0x000000013ff2a1f0: 0805 00d1 | 88a7 02f9 | 01aa 0154 | 567e 0134 | ed03 1f2a | f003 1faa | a000 0014 | 8a0d 40b9 
  0x000000013ff2a210: 4af1 7dd3 

  0x000000013ff2a214: ; implicit exception: dispatches to 0x000000013ff2e568
  0x000000013ff2a214: 5009 40b9 

  0x000000013ff2a218: ;   {metadata('java/util/HashMap')}
  0x000000013ff2a218: 4d01 a0d2 | 0d0f 82f2 | 1f02 0d6b | 017e 0154 | 4a25 40b9 | 41f1 7dd3 | 61b8 00b4 | 2d0c 40b9 
  0x000000013ff2a238: ea0d 40b9 | bd05 0051 | bf01 0071 | a9b6 0154 | 4a9d 0034 | 4041 4a4a | aa03 000a | 2ac8 2a8b 
  0x000000013ff2a258: 4d11 40b9 | a1f1 7dd3 | 218d 00b5 | ed03 1f2a | f003 1faa | f303 1faa | 1f00 0014 | 2d10 40b9 
  0x000000013ff2a278: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2a278: aa03 bcd2 | aab0 93f2 | bf01 0a6b | c002 0054 | f703 0029 | eb07 00f9 | fd03 1aaa | f833 01a9 
  0x000000013ff2a298: ef3b 02a9 | f107 03a9 | a2f1 7dd3 

  0x000000013ff2a2a4: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2a2a4: 0185 9dd2 | 811d a0f2 | e100 c0f2 

  0x000000013ff2a2b0: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.HashMap::getNode@63 (line 578)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2a2b0: b417 d997 

  0x000000013ff2a2b4: ;   {other}
  0x000000013ff2a2b4: 1f20 03d5 | 9f74 81f2 | 1f00 80f2 | 2029 0134 | fa03 1daa | f703 40b9 | ebe3 40a9 | ecbf 41a9 
  0x000000013ff2a2d4: eec7 42a9 | e11f 40f9 | ed03 1f2a | f003 1faa | 2a14 40b9 | 53f1 7dd3 | 9529 4629 | b9f2 7dd3 
  0x000000013ff2a2f4: 54f1 7dd3 | 39b2 01b4 | f474 01b4 | 8003 c139 | 00f7 0035 

  0x000000013ff2a308: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2a308: 0a85 9dd2 | 8a1d a0f2 | ea00 c0f2 | e003 14aa | 4a01 00ca | 41fd 55d3 

  0x000000013ff2a320: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2a320: aa03 bcd2 | aab0 93f2 | 76fe 43d3 | 8a0e 00b9 | 0101 00b4 | 0afc 49d3 | a03f a0d2 | 2000 c0f2 
  0x000000013ff2a340: 0000 0a8b | 0a00 c039 | 5f09 0071 | 61fa 0054 | 8a03 c139 | 2af3 0035 | ea03 14aa | e003 13aa 
  0x000000013ff2a360: 0000 0aca | 00fc 55d3 | 9612 00b9 | 2001 00b4 | 1301 00b4 | 4afd 49d3 | a03f a0d2 | 2000 c0f2 
  0x000000013ff2a380: 0000 0a8b | 0a00 c039 | 5f09 0071 | a101 0154 | 8a03 c139 | 9435 40b9 | aaef 0035 | 8af2 7dd3 
  0x000000013ff2a3a0: e003 0caa | e103 0aaa | 2100 00ca | 21fc 55d3 | 9431 00b9 | 2101 00b4 | 0a01 00b4 | 0afc 49d3 
  0x000000013ff2a3c0: a03f a0d2 | 2000 c0f2 | 0000 0a8b | 0a00 c039 | 5f09 0071 | c1fa 0054 | 8003 c139 | 20ec 0035 
  0x000000013ff2a3e0: ea03 0caa | e003 19aa | 0000 0aca | 9535 00b9 | 0cfc 55d3 | 0c01 00b4 | 4afd 49d3 | ac3f a0d2 
  0x000000013ff2a400: 2c00 c0f2 | 8001 0a8b | 0c00 c039 | 9f09 0071 | 01f0 0054 | e083 0191 | 0c00 40f9 | 9f01 1feb 
  0x000000013ff2a420: 8002 0054 | ca01 40f9 | aa00 0837 | ea03 00aa | ccfd aac8 | 5f01 00eb | 0e00 0014 | 4a09 00d1 
  0x000000013ff2a440: 4c45 40f9 | ac00 00b4 | 8c05 00d1 | 4c45 00f9 | 9f01 0ceb | 0700 0014 | 4831 49a9 | 0801 0caa 
  0x000000013ff2a460: 1f01 1feb | 6800 00b5 | 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 
  0x000000013ff2a480: 216c 0154 | d369 01b4 | c1f2 7dd3 | 2a08 40b9 

  0x000000013ff2a490: ;   {metadata('sun/awt/PostEventQueue')}
  0x000000013ff2a490: ae21 a0d2 | 0e0b 8bf2 | 5f01 0e6b | 81f5 0154 | f737 0029 | eb07 00f9 | fd03 1aaa | f83f 01a9 
  0x000000013ff2a4b0: f143 02a9 

  0x000000013ff2a4b4: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop }
                      ;*invokevirtual flush {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@15 (line 557)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2a4b4: 2765 1494 

  0x000000013ff2a4b8: ;   {other}
  0x000000013ff2a4b8: 1f20 03d5 | 1fb5 81f2 | 1f20 80f2 | eb07 40f9 | 6c2d 40b9 | 8af1 7dd3 

  0x000000013ff2a4d0: ; implicit exception: dispatches to 0x000000013ff2e41c
  0x000000013ff2a4d0: 4c09 40b9 

  0x000000013ff2a4d4: ;   {metadata('java/util/concurrent/locks/ReentrantLock')}
  0x000000013ff2a4d4: 6e03 a0d2 | 0e18 83f2 | 9f01 0e6b | a154 0154 | 4c0d 40b9 | 8ef1 7dd3 | ae48 01b4 | ec03 0032 
  0x000000013ff2a4f4: e107 40b9 | ca41 0091 | e803 01aa | 4cfd e888 | 1f01 016b | ea17 9f9a | f103 1daa | 22fe 43d3 
  0x000000013ff2a514: 6a2c 0134 | 8a03 c139 | eac1 0035 | ea03 0eaa | ec03 11aa | 8c01 0aca | 8cfd 55d3 | c20d 00b9 
  0x000000013ff2a534: 0c01 00b4 | 4afd 49d3 | ac3f a0d2 | 2c00 c0f2 | 8001 0a8b | 0a00 c039 | 5f09 0071 | a1c1 0054 
  0x000000013ff2a554: 7421 40b9 | 8af2 7dd3 

  0x000000013ff2a55c: ; implicit exception: dispatches to 0x000000013ff2cda8
  0x000000013ff2a55c: 4c0d 40b9 | 9f05 0071 | 2942 0154 | 9f0d 0071 | e941 0154 | 4e1d 40b9 | d8f1 7dd3 

  0x000000013ff2a578: ; implicit exception: dispatches to 0x000000013ff2e3cc
  0x000000013ff2a578: 0d0f 40b9 | 6d3d 0034 | f307 0032 | 0200 0014 | f303 0032 | b6f1 7dd3 | 8c03 c139 | d512 40b9 
  0x000000013ff2a598: acbc 0035 | aaf2 7dd3 | ec03 18aa | ed03 0aaa | ad01 0cca | adfd 55d3 | 150f 00b9 | 2dba 00b4 
  0x000000013ff2a5b8: 2aba 00b4 | 8afd 49d3 | ac3f a0d2 | 2c00 c0f2 | 8001 0a8b | 0c00 c039 | 9f09 0071 | 81c1 0054 
  0x000000013ff2a5d8: c30e 40b9 | 6af0 7dd3 

  0x000000013ff2a5e0: ; implicit exception: dispatches to 0x000000013ff2e434
  0x000000013ff2a5e0: 4c11 40b9 | 8fdd 0751 | 4d09 40b9 | ffad 0471 | 4341 0154 

  0x000000013ff2a5f4: ;   {metadata('sun/awt/PeerEvent')}
  0x000000013ff2a5f4: 6e22 a0d2 | 0e0e 8cf2 | bf01 0e6b | e175 0054 | ef03 1e32 | ff05 0031 | 619f 0054 | 6d2d 40b9 
  0x000000013ff2a614: acf1 7dd3 

  0x000000013ff2a618: ; implicit exception: dispatches to 0x000000013ff2e450
  0x000000013ff2a618: 8d09 40b9 

  0x000000013ff2a61c: ;   {metadata('java/util/concurrent/locks/ReentrantLock')}
  0x000000013ff2a61c: 6f03 a0d2 | 0f18 83f2 | bf01 0f6b | 414b 0154 | 8d0d 40b9 | b3f1 7dd3 | d340 01b4 | 6c42 0091 
  0x000000013ff2a63c: 94fd df88 | 6c0e 40b9 | 8cf1 7dd3 | 9f01 11eb | a16a 0154 | 9f06 0071 | 01a2 0054 | 8d03 c139 
  0x000000013ff2a65c: adc2 0035 | 7f0e 00b9 | ed03 0032 | 8e06 0051 | 6c42 0091 | 8efd 9f88 | ad00 0034 | 6c52 0091 
  0x000000013ff2a67c: 8dfd df88 | acf1 7dd3 | 6c82 00b5 | ec0b 40f9 | 8d7d 40b9 | aef1 7dd3 | ae3e 01b4 | e083 0191 
  0x000000013ff2a69c: d001 40f9 | b001 0837 | 0d02 40b2 | 0d00 00f9 | f003 0daa | c0fd f0c8 | 1f02 0deb | 6002 0054 
  0x000000013ff2a6bc: e803 0091 | 1002 08cb | edcf 72b2 | 0d02 0dea | 0d00 00f9 | 0d00 0014 | 0dfa 0091 | ef03 1faa 
  0x000000013ff2a6dc: bcfd efc8 | ff01 1feb | ed07 40b2 | 0d00 00f9 | c000 0054 | ff01 1ceb | 8100 0054 | 0862 48f8 
  0x000000013ff2a6fc: 0805 0091 | 0862 08f8 | 8100 0054 | 88a7 42f9 | 0805 0091 | 88a7 02f9 | e144 0154 | 8d7d 40b9 
  0x000000013ff2a71c: a0f1 7dd3 

  0x000000013ff2a720: ; implicit exception: dispatches to 0x000000013ff2e46c
  0x000000013ff2a720: 0d10 40b9 | b005 0051 | 9003 f837 | 0414 40b9 | 84f0 7dd3 | 4d5a f937 

  0x000000013ff2a738: ; implicit exception: dispatches to 0x000000013ff2d27c
  0x000000013ff2a738: 850c 40b9 

  0x000000013ff2a73c: ;   {metadata('java/awt/EventDispatchThread$HierarchyEventFilter')}
  0x000000013ff2a73c: 7322 a0d2 | 13bc 8df2 | 1f20 03d5 | 1f20 03d5 | 1f20 03d5 | 1f02 0d6b | a239 0154 | ef03 102a 
  0x000000013ff2a75c: 86c8 2f8b | ff01 056b | 623a 0154 | d010 40b9 | 10f2 7dd3 

  0x000000013ff2a770: ; implicit exception: dispatches to 0x000000013ff2e4c4
  0x000000013ff2a770: 070a 40b9 | ff00 136b | 81df 0154 | 070e 40b9 | e6f0 7dd3 | 0663 01b5 | 942b 42f9 

  0x000000013ff2a78c: ; ImmutableOopMap {r10=Oop r11=Oop r12=Oop r14=Oop r17=Oop c_rarg0=Oop c_rarg2=NarrowOop c_rarg3=NarrowOop c_rarg4=Oop [0]=NarrowOop }
                      ;*goto {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventDispatchThread::filterAndCheckEvent@76 (line 171)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
  0x000000013ff2a78c: f005 0051 

  0x000000013ff2a790: ;   {poll}
  0x000000013ff2a790: 9f02 40b9 | f0fd ff36 | f083 0191 | 0f02 40f9 | ff01 1feb | 8002 0054 | cd01 40f9 | ad00 0837 
  0x000000013ff2a7b0: ed03 10aa | cffd adc8 | bf01 10eb | 0e00 0014 | ad09 00d1 | af45 40f9 | af00 00b4 | ef05 00d1 
  0x000000013ff2a7d0: af45 00f9 | ff01 0feb | 0700 0014 | a83d 49a9 | 0801 0faa | 1f01 1feb | 6800 00b5 | ad01 0191 
  0x000000013ff2a7f0: bffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 | 0140 0154 

  0x000000013ff2a808: ;   {oop(a 'java/lang/Class'{0x0000000701cc43a8} = 'sun/awt/dnd/SunDragSourceContextPeer')}
  0x000000013ff2a808: 0d75 88d2 | 8d39 a0f2 | ed00 c0f2 | ad21 0291 | adfd df08 

  0x000000013ff2a81c: ;   {oop(a 'sun/util/logging/PlatformLogger'{0x0000000701c98f90})}
  0x000000013ff2a81c: 0ef2 91d2 | 2e39 a0f2 | ee00 c0f2 | cf0d 40b9 | f0f1 7dd3 | ad5c 0135 

  0x000000013ff2a834: ; implicit exception: dispatches to 0x000000013ff2e48c
  0x000000013ff2a834: 0d0a 40b9 

  0x000000013ff2a838: ;   {metadata('jdk/internal/logger/LazyLoggers$JdkLazyLogger')}
  0x000000013ff2a838: ef20 a0d2 | 0f64 9af2 | bf01 0f6b | 8141 0154 | 0e0e 40b9 | c4f1 7dd3 

  0x000000013ff2a850: ; implicit exception: dispatches to 0x000000013ff2e4a8
  0x000000013ff2a850: 8d08 40b9 

  0x000000013ff2a854: ;   {metadata('jdk/internal/logger/LazyLoggers$LazyLoggerAccessor')}
  0x000000013ff2a854: ef20 a0d2 | 0f2c 9bf2 | bf01 0f6b | a141 0154 | 8d80 0091 | aefd df88 | cdf1 7dd3 | 8d99 00b4 
  0x000000013ff2a874: af09 40b9 

  0x000000013ff2a878: ;   {metadata('jdk/internal/logger/SurrogateLogger')}
  0x000000013ff2a878: ee20 a0d2 | 0ebb 9cf2 | ff01 0e6b | c16e 0054 | ae51 0091 | cefd df88 | cef1 7dd3 | 4e68 01b5 
  0x000000013ff2a898: 8fdf 40f9 | 5d0d 40b9 | 8de7 40f9 | ee61 0091 | b0f3 7dd3 | df01 0deb | 4213 0154 | ed03 40b2 
  0x000000013ff2a8b8: 8edf 00f9 | ed01 00f9 

  0x000000013ff2a8c0: ;   {metadata('java/awt/EventQueue$4')}
  0x000000013ff2a8c0: 6d22 a0d2 | 0d25 91f2 | ed7d 0129 | d0c1 80f9 | ff11 00b9 | e107 00b9 | f1b3 00a9 | eb0f 00f9 
  0x000000013ff2a8e0: e20f 0429 | ea43 03a9 | eb03 40b9 | fd2d 0229 | ea27 40b9 | ea0d 00b9 | ef23 00f9 | bf3b 03d5 
  0x000000013ff2a900: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [32]=NarrowOop [48]=Oop [56]=Oop [64]=Oop }
                      ;*invokestatic getStackAccessControlContext {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.AccessController::getContext@0 (line 1006)
                      ; - java.awt.EventQueue::dispatchEvent@16 (line 744)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2a900: f45b f397 

  0x000000013ff2a904: ;   {other}
  0x000000013ff2a904: 1f20 03d5 | 9f3e 82f2 | 1f60 80f2 | e0dc 00b4 | e103 00aa 

  0x000000013ff2a918: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [32]=NarrowOop [48]=Oop [56]=Oop [64]=Oop }
                      ;*invokevirtual optimize {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.AccessController::getContext@19 (line 1012)
                      ; - java.awt.EventQueue::dispatchEvent@16 (line 744)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2a918: 2e9b 1494 

  0x000000013ff2a91c: ;   {other}
  0x000000013ff2a91c: 1f20 03d5 | 9f41 82f2 | 1f80 80f2 | e203 00aa | ed1f 40f9 

  0x000000013ff2a930: ; implicit exception: dispatches to 0x000000013ff2e4e8
  0x000000013ff2a930: ab09 40b9 | ea03 0b2a | 0a17 c0f2 | 4a1d 40f9 

  0x000000013ff2a940: ;   {metadata('java/awt/Component')}
  0x000000013ff2a940: 0c05 8ed2 | 6c22 a0f2 | 0c17 c0f2 | 5f01 0ceb | a15b 0054 | aa01 0291 | 4bfd df88 | 8b3f 0134 
  0x000000013ff2a960: aa01 0291 | 4afd df88 | 4df1 7dd3 | eb1b 40f9 | 6a71 0091 | 4cfd df88 | 6b71 0091 | 8af1 7dd3 
  0x000000013ff2a980: 7dfd df88 | 4a54 01b4 | a1f3 7dd3 | 4d78 00b5 

  0x000000013ff2a990: ; ImmutableOopMap {[8]=Oop [16]=Oop [32]=NarrowOop [64]=Oop }
                      ;*invokestatic getCombinedACC {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@15 (line 89)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2a990: 1c5b 0394 

  0x000000013ff2a994: ;   {other}
  0x000000013ff2a994: 1f20 03d5 | 9f50 82f2 | 1fa0 80f2 | e000 00b4 | e017 00f9 | e103 00aa 

  0x000000013ff2a9ac: ; ImmutableOopMap {[8]=Oop [16]=Oop [32]=NarrowOop [40]=Oop [64]=Oop }
                      ;*invokestatic ensureMaterializedForStackWalk {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@5 (line 774)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2a9ac: e9b4 f497 

  0x000000013ff2a9b0: ;   {other}
  0x000000013ff2a9b0: 1f20 03d5 | 1f54 82f2 | 1fc0 80f2 | ee23 40f9 | cb15 40b9 | cd0d 40b9 | 6ff1 7dd3 

  0x000000013ff2a9cc: ; implicit exception: dispatches to 0x000000013ff2e50c
  0x000000013ff2a9cc: ea4d 40b9 | 4af1 7dd3 | b5f1 7dd3 | ea36 01b5 

  0x000000013ff2a9dc: ; implicit exception: dispatches to 0x000000013ff2e520
  0x000000013ff2a9dc: ac0a 40b9 | e403 0c2a | 0417 c0f2 | f003 0032 | 8a10 40f9 | b05a 0039 

  0x000000013ff2a9f4: ;   {metadata('java/awt/ActiveEvent')}
  0x000000013ff2a9f4: 0000 89d2 | 6022 a0f2 | 0017 c0f2 | 9128 40f9 | 5f01 00eb | a156 0054 | ea2d 40b9 | 4af1 7dd3 
  0x000000013ff2aa14: ; implicit exception: dispatches to 0x000000013ff2e534
  0x000000013ff2aa14: 4b09 40b9 

  0x000000013ff2aa18: ;   {metadata('java/util/concurrent/locks/ReentrantLock')}
  0x000000013ff2aa18: 7003 a0d2 | 1018 83f2 | 7f01 106b | 4138 0154 | 4b0d 40b9 | 62f1 7dd3 | 0235 01b4 | eb03 0032 
  0x000000013ff2aa38: ee07 40b9 | 4a40 0091 | e803 0eaa | 4bfd e888 | 1f01 0e6b | ea17 9f9a | ea03 0134 | 8a03 c139 
  0x000000013ff2aa58: eaa6 0035 | f007 40f9 | ea03 02aa | eb03 10aa | 6b01 0aca | 6bfd 55d3 | e123 40b9 | 410c 00b9 
  0x000000013ff2aa78: 0b01 00b4 | 4afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | 21b0 0054 
  0x000000013ff2aa98: ea35 40b9 | 4bf1 7dd3 

  0x000000013ff2aaa0: ;   {oop(a 'java/lang/Class'{0x00000007010e5f50} = 'java/awt/EventQueue')}
  0x000000013ff2aaa0: 0aea 8bd2 | ca21 a0f2 | ea00 c0f2 | 4ab1 4239 | 0a4c 0135 | 1f02 0beb | a159 0154 | 81df 40f9 
  0x000000013ff2aac0: 8ae7 40f9 | 2b80 0091 | 7f01 0aeb | 2204 0154 | ea03 40b2 | 8bdf 00f9 | 2a00 00f9 

  0x000000013ff2aadc: ;   {metadata('java/lang/ref/WeakReference')}
  0x000000013ff2aadc: 8a00 a0d2 | 0a82 8ef2 | 2a08 00b9 | 70c1 80f9 | 3f0c 00b9 | 2a40 0091 | 5f7d 00a9 | bf3a 03d5 
  0x000000013ff2aafc: 2d0c 00b9 | 8a03 c139 | 2aa0 0035 

  0x000000013ff2ab08: ;   {oop(a 'java/lang/ref/ReferenceQueue$Null'{0x00000007008f9920})}
  0x000000013ff2ab08: 2b02 bcd2 | 8b64 9ef2 | 2a40 0091 | 4bfd 9f88 

  0x000000013ff2ab18: ;   {oop(a 'java/lang/ref/ReferenceQueue$Null'{0x00000007008f9920})}
  0x000000013ff2ab18: 0b24 93d2 | eb11 a0f2 | eb00 c0f2 | ea03 01aa | 6b01 0aca | 6bfd 55d3 | 34fc 43d3 | 0b01 00b4 
  0x000000013ff2ab38: 4afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0b00 c039 | 7f09 0071 | 41a5 0054 | 8a03 c139 
  0x000000013ff2ab58: 2a9c 0035 | ea03 0faa | eb03 01aa | 6b01 0aca | 6bfd 55d3 | f441 00b9 | 0b01 00b4 | 4afd 49d3 
  0x000000013ff2ab78: ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | a19e 0054 | eb2d 40b9 | 61f1 7dd3 
  0x000000013ff2ab98: ;   {metadata('java/awt/event/InputEvent')}
  0x000000013ff2ab98: 0b56 90d2 | 8b21 a0f2 | 0b17 c0f2 | ea09 40f9 | 3f02 0beb | 0150 0054 

  0x000000013ff2abb0: ;   {metadata('java/awt/event/KeyEvent')}
  0x000000013ff2abb0: 1129 a0d2 | 1119 9af2 | 9f01 116b | eb03 15aa | 6b11 40f9 | 4100 0054 | eb0d 00f9 | 5f01 0beb 
  0x000000013ff2abd0: ab62 0054 | ea09 00f9 

  0x000000013ff2abd8: ; implicit exception: dispatches to 0x000000013ff2e550
  0x000000013ff2abd8: 2b08 40b9 

  0x000000013ff2abdc: ;   {metadata('java/util/concurrent/locks/ReentrantLock')}
  0x000000013ff2abdc: 6a03 a0d2 | 0a18 83f2 | 7f01 0a6b | 2128 0154 | 2a0c 40b9 | 54f1 7dd3 | 1429 01b4 | 8a42 0091 
  0x000000013ff2abfc: 53fd df88 | 8b0e 40b9 | 6af1 7dd3 | 5f01 10eb | 0150 0154 | 7f06 0071 | a100 0054 | 8b03 c139 
  0x000000013ff2ac1c: cba8 0035 | 9f0e 00b9 | ee03 0032 | 6c06 0051 | 8a42 0091 | 4cfd 9f88 | ae00 0034 | 8a52 0091 
  0x000000013ff2ac3c: 4afd df88 | 4af1 7dd3 | 6a58 00b5 | e103 15aa | 2a08 40b9 

  0x000000013ff2ac50: ;   {metadata('java/awt/event/InvocationEvent')}
  0x000000013ff2ac50: 6c22 a0d2 | 0c43 89f2 | 5f01 0c6b | 6167 0054 | ec03 01aa | 8b2d 40b9 | 8aa5 4039 | 61f1 7dd3 
  0x000000013ff2ac70: 6a77 0035 | ec03 00f9 | fd03 15aa | 09b4 85d2 | a941 a0f2 | 0900 ccf2 

  0x000000013ff2ac88: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::dispatch@47 (line 318)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2ac88: e0a0 dc97 

  0x000000013ff2ac8c: ;   {other}
  0x000000013ff2ac8c: 1f20 03d5 | 9faf 82f2 | 1fe0 80f2 | ea03 1daa | ed03 0032 | 4ba1 0091 | 6dfd 9f08 | 4bc1 0091 
  0x000000013ff2acac: 6bfd df88 | 4d35 40b9 | 6bf1 7dd3 | a1f1 7dd3 | ab4e 01b5 | a173 00b5 | fd7b 47a9 | ff03 0291 
  0x000000013ff2accc: 886b 42f9 | ff63 28eb | 2302 0054 | fd7b bfa9 | fd03 0091 

  0x000000013ff2ace0: ;   {external_word}
  0x000000013ff2ace0: 888b 85d2 | 28d4 a0f2 | 2800 c0f2 | e003 1caa | 0001 3fd6 | bf03 0091 | fd7b c1a8 

  0x000000013ff2acfc: ;   {runtime_call delayed StackOverflowError throw_exception}
  0x000000013ff2acfc: 0840 8ed2 | 68ea a7f2 | 2800 c0f2 | 0001 1fd6 | c1d5 bbd4 | 6792 cd06 | 0100 0000 

  0x000000013ff2ad18: ;   {poll_return}
  0x000000013ff2ad18: 8827 42f9 | ff63 28eb | 680f 0254 | c003 5fd6 | 9f09 0071 | c904 0154 | 4c19 40b9 | 98f1 7dd3 
  0x000000013ff2ad38: ; implicit exception: dispatches to 0x000000013ff2e3cc
  0x000000013ff2ad38: 0d0f 40b9 | 6d00 0034 | f303 1f32 | 12fe ff17 | 4d15 40b9 | b8f1 7dd3 

  0x000000013ff2ad50: ; implicit exception: dispatches to 0x000000013ff2e3cc
  0x000000013ff2ad50: 0d0f 40b9 | adc1 ff35 | 4a11 40b9 | 58f1 7dd3 

  0x000000013ff2ad60: ; implicit exception: dispatches to 0x000000013ff2e3cc
  0x000000013ff2ad60: 0d0f 40b9 | 6d00 0034 | f303 1f2a | 08fe ff17 | 6c35 40b9 | e107 00b9 | fd03 11aa | eb07 00f9 
  0x000000013ff2ad80: e233 00b9 | 82f1 7dd3 

  0x000000013ff2ad88: ;   {oop(a 'sun/awt/AWTAutoShutdown'{0x0000000700e01860})}
  0x000000013ff2ad88: 010c 83d2 | 011c a0f2 | e100 c0f2 

  0x000000013ff2ad94: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokevirtual notifyThreadFree {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@45 (line 571)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2ad94: 8fd2 0194 

  0x000000013ff2ad98: ;   {other}
  0x000000013ff2ad98: 1f20 03d5 | 1fd1 82f2 | 1f00 81f2 | ea07 40f9 | 4a35 40b9 | 41f1 7dd3 

  0x000000013ff2adb0: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokestatic getInstance {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.awt.EventQueue::getNextEvent@52 (line 572)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {static_call}
  0x000000013ff2adb0: 84e6 1494 

  0x000000013ff2adb4: ;   {other}
  0x000000013ff2adb4: 1f20 03d5 | 9fd4 82f2 | 1f20 81f2 | 6043 01b4 | e103 00aa 

  0x000000013ff2adc8: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokevirtual notifyEventDispatchThreadFree {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@55 (line 572)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2adc8: d203 1694 

  0x000000013ff2adcc: ;   {other}
  0x000000013ff2adcc: 1f20 03d5 | 9fd7 82f2 | 1f40 81f2 | ed07 40f9 | ab31 40b9 | 61f1 7dd3 

  0x000000013ff2ade4: ; implicit exception: dispatches to 0x000000013ff2e5a8
  0x000000013ff2ade4: 2a08 40b9 

  0x000000013ff2ade8: ;   {metadata('java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject')}
  0x000000013ff2ade8: cc03 a0d2 | 0cdf 98f2 | 5f01 0c6b | c145 0154 

  0x000000013ff2adf8: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokeinterface await {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@62 (line 573)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2adf8: c694 1d94 

  0x000000013ff2adfc: ;   {other}
  0x000000013ff2adfc: 1f20 03d5 | 9fdd 82f2 | 1f60 81f2 | ec07 40f9 | 8b2d 40b9 | 6af1 7dd3 

  0x000000013ff2ae14: ; implicit exception: dispatches to 0x000000013ff2e5c0
  0x000000013ff2ae14: 4d09 40b9 

  0x000000013ff2ae18: ;   {metadata('java/util/concurrent/locks/ReentrantLock')}
  0x000000013ff2ae18: 6b03 a0d2 | 0b18 83f2 | bf01 0b6b | 2145 0154 | 4a0d 40b9 | 56f1 7dd3 | b640 01b4 | ca42 0091 
  0x000000013ff2ae38: 53fd df88 | cb0e 40b9 | 6af1 7dd3 | ed03 1daa | 5f01 0deb | 8167 0154 | 7f06 0071 | 216a 0054 
  0x000000013ff2ae58: 8b03 c139 | 2bb1 0035 | df0e 00b9 | eb03 0032 | 6e06 0051 | ca42 0091 | 4efd 9f88 | ab00 0034 
  0x000000013ff2ae78: ca52 0091 | 4bfd df88 | 6af1 7dd3 | 4a4d 00b5 | 8a45 40b9 | 4ff1 7dd3 

  0x000000013ff2ae90: ; implicit exception: dispatches to 0x000000013ff2e5d8
  0x000000013ff2ae90: eb0d 40b9 | 71f1 7dd3 | 313e 01b4 | f083 0191 | 2e02 40f9 | ae01 0837 | cb01 40b2 | 0b02 00f9 
  0x000000013ff2aeb0: ee03 0baa | 30fe eec8 | df01 0beb | 6002 0054 | e803 0091 | ce01 08cb | ebcf 72b2 | cb01 0bea 
  0x000000013ff2aed0: 0b02 00f9 | 0d00 0014 | cbf9 0091 | ea03 1faa | 7cfd eac8 | 5f01 1feb | eb07 40b2 | 0b02 00f9 
  0x000000013ff2aef0: c000 0054 | 5f01 1ceb | 8100 0054 | c861 48f8 | 0805 0091 | c861 08f8 | 8100 0054 | 88a7 42f9 
  0x000000013ff2af10: 0805 0091 | 88a7 02f9 | 613e 0154 | eb31 40b9 | 6af1 7dd3 

  0x000000013ff2af24: ; implicit exception: dispatches to 0x000000013ff2e62c
  0x000000013ff2af24: 4b0d 40b9 

  0x000000013ff2af28: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2af28: b003 bcd2 | b0b0 93f2 | 7f01 106b | 2104 0054 | 5811 40b9 | ee83 0191 | cb01 40f9 | 7f01 1feb 
  0x000000013ff2af48: 8002 0054 | 2a02 40f9 | aa00 0837 | ea03 0eaa | 2bfe aac8 | 5f01 0eeb | 0e00 0014 | 4a09 00d1 
  0x000000013ff2af68: 4b45 40f9 | ab00 00b4 | 6b05 00d1 | 4b45 00f9 | 7f01 0beb | 0700 0014 | 482d 49a9 | 0801 0baa 
  0x000000013ff2af88: 1f01 1feb | 6800 00b5 | 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 
  0x000000013ff2afa8: c15e 0154 | 184f 0134 | e2bb 41a9 | c400 0014 | eb0d 40b9 | 6af1 7dd3 

  0x000000013ff2afc0: ; implicit exception: dispatches to 0x000000013ff2e60c
  0x000000013ff2afc0: 4b09 40b9 

  0x000000013ff2afc4: ;   {metadata('java/util/HashMap')}
  0x000000013ff2afc4: 5001 a0d2 | 100f 82f2 | 7f01 106b | e14e 0154 | 4b25 40b9 | 60f1 7dd3 | a050 00b4 | 0a0c 40b9 
  0x000000013ff2afe4: e20f 40f9 | 4e0c 40b9 | 5d05 0051 | 5f01 0071 | 696c 0154 | ce3d 0034 | d041 4e4a | ab03 100a 
  0x000000013ff2b004: ee13 40f9 | 0ac8 2b8b | 4b11 40b9 | 61f1 7dd3 | 0105 00b4 | 2a0c 40b9 | 5f01 106b | e004 0054 
  0x000000013ff2b024: 2b18 40b9 | 7df1 7dd3 | 5d04 00b4 | 2a08 40b9 

  0x000000013ff2b034: ;   {metadata('java/util/HashMap$TreeNode')}
  0x000000013ff2b034: a003 a0d2 | 00db 9af2 | 5f01 006b | 2154 0054 | e20f 00f9 | ec07 00f9 | ee13 00f9 | ef1f 00f9 
  0x000000013ff2b054: f127 00f9 | fd03 0daa | e203 102a 

  0x000000013ff2b060: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b060: 0385 9dd2 | 831d a0f2 | e300 c0f2 

  0x000000013ff2b06c: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop [56]=Oop [72]=Oop }
                      ;*invokevirtual getTreeNode {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.HashMap::getNode@95 (line 582)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b06c: 4514 d997 

  0x000000013ff2b070: ;   {other}
  0x000000013ff2b070: 1f20 03d5 | 1f2c 83f2 | 1f80 81f2 | e000 00b5 | ed03 1daa | ec07 40f9 | e2bb 41a9 | ef1f 40f9 
  0x000000013ff2b090: f127 40f9 | 0800 0014 | ed03 1daa | ec07 40f9 | e2bb 41a9 | ef1f 40f9 | f127 40f9 | e103 00aa 
  0x000000013ff2b0b0: 1c00 0014 | f403 1faa | 1c00 0014 | 2b10 40b9 

  0x000000013ff2b0c0: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b0c0: aa03 bcd2 | aab0 93f2 | 7f01 0a6b | a002 0054 | ec07 00f9 | e2bb 01a9 | f037 00b9 | ef87 03a9 
  0x000000013ff2b0e0: f127 00f9 | fd03 0daa | 62f1 7dd3 

  0x000000013ff2b0ec: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b0ec: 0185 9dd2 | 811d a0f2 | e100 c0f2 

  0x000000013ff2b0f8: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.HashMap::getNode@63 (line 578)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b0f8: 2214 d997 

  0x000000013ff2b0fc: ;   {other}
  0x000000013ff2b0fc: 1f20 03d5 | 9f3d 83f2 | 1fa0 81f2 | a0cf 0034 | ed03 1daa | ec07 40f9 | e2bb 41a9 | ef87 43a9 
  0x000000013ff2b11c: f127 40f9 | 2a14 40b9 | 54f1 7dd3 | f629 4629 | d5f2 7dd3 | 53f1 7dd3 | 9563 01b4 | 5341 01b4 
  0x000000013ff2b13c: 8b03 c139 | 4b9f 0035 

  0x000000013ff2b144: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b144: 0b85 9dd2 | 8b1d a0f2 | eb00 c0f2 | ea03 13aa | 6b01 0aca 

  0x000000013ff2b158: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b158: a003 bcd2 | a0b0 93f2 | 6bfd 55d3 | 98fe 43d3 | 600e 00b9 | 0b01 00b4 | 4afd 49d3 | ab3f a0d2 
  0x000000013ff2b178: 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | 21a1 0054 | 8a03 c139 | 8a9f 0035 | ea03 13aa 
  0x000000013ff2b198: eb03 14aa | 6b01 0aca | 6bfd 55d3 | 7812 00b9 | 2b01 00b4 | 1401 00b4 | 4afd 49d3 | ab3f a0d2 
  0x000000013ff2b1b8: 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | 61ac 0054 | 8b03 c139 | f335 40b9 | 4b99 0035 
  0x000000013ff2b1d8: 6af2 7dd3 | eb03 0faa | f003 0aaa | 1002 0bca | 10fe 55d3 | f331 00b9 | 3001 00b4 | 0a01 00b4 
  0x000000013ff2b1f8: 6afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0b00 c039 | 7f09 0071 | a1a5 0054 | 8a03 c139 
  0x000000013ff2b218: ea99 0035 | ea03 0faa | eb03 15aa | 6b01 0aca | 6bfd 55d3 | f635 00b9 | 0b01 00b4 | 4afd 49d3 
  0x000000013ff2b238: ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | 819f 0054 | ef83 0191 | eb01 40f9 
  0x000000013ff2b258: 7f01 1feb | 8002 0054 | 2a02 40f9 | aa00 0837 | ea03 0faa | 2bfe aac8 | 5f01 0feb | 0e00 0014 
  0x000000013ff2b278: 4a09 00d1 | 4b45 40f9 | ab00 00b4 | 6b05 00d1 | 4b45 00f9 | 7f01 0beb | 0700 0014 | 482d 49a9 
  0x000000013ff2b298: 0801 0baa | 1f01 1feb | 6800 00b5 | 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 
  0x000000013ff2b2b8: 88a7 02f9 | 8138 0154 | 3436 01b4 | 01f3 7dd3 | 2b08 40b9 

  0x000000013ff2b2cc: ;   {metadata('sun/awt/PostEventQueue')}
  0x000000013ff2b2cc: aa21 a0d2 | 0a0b 8bf2 | 7f01 0a6b | a185 0154 | ec07 00f9 | e2bb 01a9 | fd03 0daa 

  0x000000013ff2b2e8: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokevirtual flush {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@15 (line 557)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b2e8: 9a61 1494 

  0x000000013ff2b2ec: ;   {other}
  0x000000013ff2b2ec: 1f20 03d5 | 9f7b 83f2 | 1fc0 81f2 | eb07 40f9 | 6a2d 40b9 | 4af1 7dd3 

  0x000000013ff2b304: ; implicit exception: dispatches to 0x000000013ff2e5f4
  0x000000013ff2b304: 4d09 40b9 

  0x000000013ff2b308: ;   {metadata('java/util/concurrent/locks/ReentrantLock')}
  0x000000013ff2b308: 6c03 a0d2 | 0c18 83f2 | bf01 0c6b | 4120 0154 | 4a0d 40b9 | 4ef1 7dd3 | ce1a 01b4 | ec03 0032 
  0x000000013ff2b328: e107 40b9 | ca41 0091 | e803 01aa | 4cfd e888 | 1f01 016b | ec17 9f9a | 2cc3 0034 | 8c03 c139 
  0x000000013ff2b348: 6c88 0035 | f103 1daa | ea03 0eaa | ec03 11aa | 8c01 0aca | 8cfd 55d3 | e233 40b9 | c20d 00b9 
  0x000000013ff2b368: 6c8f ffb4 | 4afd 49d3 | ac3f a0d2 | 2c00 c0f2 | 8001 0a8b | 0c00 c039 | 9f09 0071 | 808e ff54 
  0x000000013ff2b388: 8a27 40f9 | 8c2f 40f9 | bf3b 03d5 | 0e00 c039 | ee8d ff34 | 1f00 0039 | 8a02 00b5 | f503 022a 
  0x000000013ff2b3a8: f403 0baa | f603 012a | e103 1caa | c900 0010 

  0x000000013ff2b3b8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2b3b8: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2b3cc: ;   {other}
  0x000000013ff2b3cc: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 1daa | eb03 14aa | e103 162a | e203 152a 
  0x000000013ff2b3ec: 5afc ff17 | 4d21 00d1 | 8a01 0a8b | 4081 1ff8 | 8d27 00f9 | 55fc ff17 | 2a0c 40b9 | 5f01 006b 
  0x000000013ff2b40c: 4073 ff54 | 2d18 40b9 | bdf1 7dd3 | dd04 00b4 | 2a08 40b9 

  0x000000013ff2b420: ;   {metadata('java/util/HashMap$TreeNode')}
  0x000000013ff2b420: b003 a0d2 | 10db 9af2 | 5f01 106b | e129 0054 | f703 00b9 | eb07 00f9 | fd03 1aaa | f833 01a9 
  0x000000013ff2b440: ef3b 02a9 | f11b 00f9 | e203 002a 

  0x000000013ff2b44c: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b44c: 0385 9dd2 | 831d a0f2 | e300 c0f2 

  0x000000013ff2b458: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [40]=Oop [48]=Oop }
                      ;*invokevirtual getTreeNode {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.HashMap::getNode@95 (line 582)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b458: 4a13 d997 

  0x000000013ff2b45c: ;   {other}
  0x000000013ff2b45c: 1f20 03d5 | 9fa9 83f2 | 1fe0 81f2 | 2001 00b5 | ed03 1f2a | f003 1faa | fa03 1daa | f703 40b9 
  0x000000013ff2b47c: ebe3 40a9 | ecbf 41a9 | eec7 42a9 | 79fb ff17 | ed03 1f2a | f003 1faa | fa03 1daa | f703 40b9 
  0x000000013ff2b49c: ebe3 40a9 | ecbf 41a9 | eec7 42a9 | e103 00aa | 8efb ff17 | ed03 1f2a | f003 1faa | 6dfb ff17 
  0x000000013ff2b4bc: 0f00 a012 | 52fc ff17 

  0x000000013ff2b4c4: ;   {metadata('java/awt/TrayIcon')}
  0x000000013ff2b4c4: 6c22 a0d2 | 0cc4 91f2 | 7f01 0c6b | 6034 0154 | ed03 1faa | 25fd ff17 | 8514 40f9 | a200 40b9 
  0x000000013ff2b4e4: a520 0091 | ff63 3feb | c200 00b4 | a884 40f8 | 1f00 08eb | 6000 0054 | 4204 00d1 | 82ff ffb5 
  0x000000013ff2b504: 6100 0054 | 8010 00f9 | e503 1faa | e5a7 ffb4 | cb11 40b9 | 61f1 7dd3 

  0x000000013ff2b51c: ; implicit exception: dispatches to 0x000000013ff2e668
  0x000000013ff2b51c: 2a08 40b9 | 0a17 c0f2 | 4a1d 40f9 

  0x000000013ff2b528: ;   {metadata('java/awt/Component')}
  0x000000013ff2b528: 0b05 8ed2 | 6b22 a0f2 | 0b17 c0f2 | 5f01 0beb | 6147 0154 | f103 00f9 | fd03 15aa | e203 15aa 
  0x000000013ff2b548: e9ff 9fd2 | e9ff bff2 | e9ff dff2 

  0x000000013ff2b554: ; ImmutableOopMap {rfp=Oop [16]=Oop [40]=Oop }
                      ;*invokevirtual dispatchEventImpl {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.Component::dispatchEvent@2 (line 4871)
                      ; - java.awt.EventQueue::dispatchEventImpl@41 (line 783)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2b554: cb13 d997 

  0x000000013ff2b558: ;   {other}
  0x000000013ff2b558: 1f20 03d5 | 1fc9 83f2 | 1f00 82f2 

  0x000000013ff2b564: ;   {metadata('java/awt/event/InputEvent')}
  0x000000013ff2b564: 0b56 90d2 | 8b21 a0f2 | 0b17 c0f2 | ea03 40f9 | 5f01 0beb | 61ba ff54 

  0x000000013ff2b57c: ; ImmutableOopMap {rfp=Oop [16]=Oop [40]=Oop }
                      ;*invokestatic getInputEventAccessor {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.awt.AWTEvent::dispatched@7 (line 596)
                      ; - java.awt.EventQueue::dispatchEventImpl@45 (line 784)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2b57c: 8114 d997 

  0x000000013ff2b580: ;   {other}
  0x000000013ff2b580: 1f20 03d5 | 1fce 83f2 | 1f20 82f2 | ea03 1daa 

  0x000000013ff2b590: ; implicit exception: dispatches to 0x000000013ff2e684
  0x000000013ff2b590: 0c08 40b9 

  0x000000013ff2b594: ;   {metadata('java/awt/event/InputEvent$1')}
  0x000000013ff2b594: 8b21 a0d2 | 0b13 92f2 | 9f01 0b6b | 2145 0154 | 5fb1 0039 | c7fd ff17 

  0x000000013ff2b5ac: ;   {metadata('java/awt/event/InputMethodEvent')}
  0x000000013ff2b5ac: 7122 a0d2 | 1127 92f2 | 9f01 116b | 8b24 40f9 | 6030 0154 

  0x000000013ff2b5c0: ;   {metadata('java/awt/event/ActionEvent')}
  0x000000013ff2b5c0: 7122 a0d2 | 117c 92f2 | 9f01 116b | c030 0154 

  0x000000013ff2b5d0: ;   {metadata('java/awt/event/InvocationEvent')}
  0x000000013ff2b5d0: 0c43 89d2 | 6c22 a0f2 | 0c17 c0f2 | 7f01 0ceb | a11a 0054 | eb03 15aa | 6b11 40f9 | 78fd ff17 
  0x000000013ff2b5f0: 2a46 4039 | ca2c 0035 | fa03 00f9 | f70b 00b9 | f82f 01a9 | ec3b 02a9 | ef47 03a9 | e123 00f9 
  0x000000013ff2b610: ;   {oop([B{0x0000000700ecec40})}
  0x000000013ff2b610: 0188 9dd2 | 811d a0f2 | e100 c0f2 

  0x000000013ff2b61c: ; ImmutableOopMap {[0]=Oop [8]=NarrowOop [16]=Oop [24]=Oop [32]=Oop [40]=Oop [48]=Oop [56]=Oop [64]=Oop }
                      ;*invokestatic hashCode {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::hashCode@27 (line 2369)
                      ; - java.util.HashMap::hash@9 (line 338)
                      ; - java.util.HashMap::getNode@23 (line 576)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {static_call}
  0x000000013ff2b61c: 5914 d997 

  0x000000013ff2b620: ;   {other}
  0x000000013ff2b620: 1f20 03d5 | 1fe2 83f2 | 1f40 82f2 | 1040 404a | e0b8 0034 | ef1b 40f9 | e00d 00b9 | aa03 100a 
  0x000000013ff2b640: fa03 40f9 | f70b 40b9 | f82f 41a9 | ec3b 42a9 | f187 43a9 | e003 102a | fffa ff17 

  0x000000013ff2b65c: ;   {metadata('sun/util/logging/internal/LoggingProviderImpl$JULWrapper')}
  0x000000013ff2b65c: d02c a0d2 | 1047 80f2 | ff01 106b | e13c 0154 | ae0d 40b9 | cdf1 7dd3 

  0x000000013ff2b674: ;   {oop(a 'java/util/logging/Level'[9] {0x0000000702583a40})}
  0x000000013ff2b674: 0e48 87d2 | 0e4b a0f2 | ee00 c0f2 | ce15 40b9 | cef1 7dd3 | 0d3a 01b4 | ad41 0091 | affd df88 
  0x000000013ff2b694: edf1 7dd3 | 8d3a 01b4 | ad31 0091 | adfd df88 

  0x000000013ff2b6a4: ; implicit exception: dispatches to 0x000000013ff2e64c
  0x000000013ff2b6a4: cf0d 40b9 | ff01 0d6b | 6b8f ff54 | 4117 8012 | fd03 0caa | ea2f 00a9 | efb7 0229 

  0x000000013ff2b6c0: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*if_icmplt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.logging.Logger::isLoggable@13 (line 2037)
                      ; - sun.util.logging.internal.LoggingProviderImpl$JULWrapper::isLoggable@8 (line 215)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@20 (line 124)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2b6c0: 302e d997 

  0x000000013ff2b6c4: ;   {other}
  0x000000013ff2b6c4: 1f20 03d5 | 9ff6 83f2 | 1f60 82f2 | 8c51 0091 | 8cfd df88 | 8ef1 7dd3 | 6e7d ffb4 | cc31 0091 
  0x000000013ff2b6e4: 8dfd df88 | 0d7d ff34 | e107 00b9 | eb07 00f9 | e20f 0329 | ea13 00f9 | ee1b 00f9 | fd03 11aa 
  0x000000013ff2b704: e103 0eaa | e203 0032 

  0x000000013ff2b70c: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=NarrowOop [28]=NarrowOop [32]=Oop [48]=Oop }
                      ;*invokevirtual getAndUnsetStatus {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext@22 (line 644)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@12 (line 1060)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@31 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b70c: e15b 1494 

  0x000000013ff2b710: ;   {other}
  0x000000013ff2b710: 1f20 03d5 | 1f00 84f2 | 1f80 82f2 | ea1b 40f9 | 4a19 40b9 | 41f1 7dd3 

  0x000000013ff2b728: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=NarrowOop [28]=NarrowOop [32]=Oop }
                      ;*invokestatic unpark {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext@30 (line 645)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@12 (line 1060)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@31 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {static_call}
  0x000000013ff2b728: 66fb 1494 

  0x000000013ff2b72c: ;   {other}
  0x000000013ff2b72c: 1f20 03d5 | 9f03 84f2 | 1fa0 82f2 | f103 1daa | e107 40b9 | eb07 40f9 | e20f 4329 | ea13 40f9 
  0x000000013ff2b74c: cffb ff17 | 4a51 0091 | 4bfd df88 | 6bf1 7dd3 | 6ba7 ffb4 | 6a31 0091 | 4afd df88 | 0aa7 ff34 
  0x000000013ff2b76c: eb03 00f9 | fd03 15aa | e103 0baa | e203 0032 

  0x000000013ff2b77c: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [40]=Oop }
                      ;*invokevirtual getAndUnsetStatus {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext@22 (line 644)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@12 (line 1060)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@168 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2b77c: c55b 1494 

  0x000000013ff2b780: ;   {other}
  0x000000013ff2b780: 1f20 03d5 | 1f0e 84f2 | 1fc0 82f2 | ea03 40f9 | 4b19 40b9 | 61f1 7dd3 

  0x000000013ff2b798: ; ImmutableOopMap {rfp=Oop [16]=Oop [40]=Oop }
                      ;*invokestatic unpark {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext@30 (line 645)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@12 (line 1060)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@168 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2b798: 4afb 1494 

  0x000000013ff2b79c: ;   {other}
  0x000000013ff2b79c: 1f20 03d5 | 9f11 84f2 | 1fe0 82f2 | f503 1daa | 27fd ff17 | ee13 40f9 | cb45 4039 | ab29 0035 
  0x000000013ff2b7bc: e813 40f9 | e81f 00f9 | e80f 40f9 | e813 00f9 | ed07 00f9 | ec0f 00f9 | ef47 04a9 | e02b 00f9 
  0x000000013ff2b7dc: ;   {oop([B{0x0000000700ecec40})}
  0x000000013ff2b7dc: 0188 9dd2 | 811d a0f2 | e100 c0f2 

  0x000000013ff2b7e8: ; ImmutableOopMap {[0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop [80]=Oop }
                      ;*invokestatic hashCode {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::hashCode@27 (line 2369)
                      ; - java.util.HashMap::hash@9 (line 338)
                      ; - java.util.HashMap::getNode@23 (line 576)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {static_call}
  0x000000013ff2b7e8: e613 d997 

  0x000000013ff2b7ec: ;   {other}
  0x000000013ff2b7ec: 1f20 03d5 | 9f1b 84f2 | 1f00 83f2 | 0a40 404a | a0cb 0034 | e213 40f9 | 400c 00b9 | ab03 0a0a 
  0x000000013ff2b80c: ed07 40f9 | ec0f 40f9 | eebf 43a9 | f183 44a9 | f003 0a2a | fafd ff17 | ea03 0baa | ebfc ff17 
  0x000000013ff2b82c: 4a51 0091 | 4afd df88 | 4ef1 7dd3 | 8eb2 ffb4 | ca31 0091 | 4bfd df88 | 2bb2 ff34 | ec07 00f9 
  0x000000013ff2b84c: ee1f 00f9 | fd03 0daa | e103 0eaa | e203 0032 

  0x000000013ff2b85c: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop [56]=Oop }
                      ;*invokevirtual getAndUnsetStatus {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext@22 (line 644)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@12 (line 1060)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@71 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b85c: 8d5b 1494 

  0x000000013ff2b860: ;   {other}
  0x000000013ff2b860: 1f20 03d5 | 1f2a 84f2 | 1f20 83f2 | ea1f 40f9 | 4a19 40b9 | 41f1 7dd3 

  0x000000013ff2b878: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokestatic unpark {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::signalNext@30 (line 645)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@12 (line 1060)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@71 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {static_call}
  0x000000013ff2b878: 12fb 1494 

  0x000000013ff2b87c: ;   {other}
  0x000000013ff2b87c: 1f20 03d5 | 9f2d 84f2 | 1f40 83f2 | ed03 1daa | ec07 40f9 | 7efd ff17 | 80df 40f9 | 8ae7 40f9 
  0x000000013ff2b89c: 0b60 0091 | 7f01 0aeb | 82a2 0054 | ea03 40b2 | 8bdf 00f9 

  0x000000013ff2b8b0: ;   {metadata('java/awt/EventQueue$5')}
  0x000000013ff2b8b0: 6c29 a0d2 | 0c03 99f2 | 0a00 00f9 | 0c7c 0129 | 70c1 80f9 | 1f08 00f9 | e213 00f9 | ed1b 00f9 
  0x000000013ff2b8d0: ea23 40f9 | 4bfd 43d3 | ea03 00aa | 4bf5 0129 | ea03 00f9 | e213 40f9 | e11b 40f9 

  0x000000013ff2b8ec: ; ImmutableOopMap {[0]=Oop [16]=Oop }
                      ;*invokestatic getCombinedACC {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@15 (line 89)
                      ; - java.awt.EventQueue::dispatchEvent@73 (line 752)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2b8ec: 4557 0394 

  0x000000013ff2b8f0: ;   {other}
  0x000000013ff2b8f0: 1f20 03d5 | 1f3c 84f2 | 1f60 83f2 | 0001 00b4 | fd03 00aa | e103 00aa 

  0x000000013ff2b908: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop }
                      ;*invokestatic ensureMaterializedForStackWalk {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@5 (line 774)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@73 (line 752)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {static_call}
  0x000000013ff2b908: 12b1 f497 

  0x000000013ff2b90c: ;   {other}
  0x000000013ff2b90c: 1f20 03d5 | 9f3f 84f2 | 1f80 83f2 | fd17 00f9 | e103 40f9 

  0x000000013ff2b920: ; ImmutableOopMap {[16]=Oop [40]=Oop }
                      ;*invokevirtual run {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.awt.EventQueue$5::run@1 (line 753)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@73 (line 752)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2b920: 7879 7c96 

  0x000000013ff2b924: ;   {other}
  0x000000013ff2b924: 1f20 03d5 | 9f42 84f2 | 1fa0 83f2 | e5fc ff17 | eb03 41b2 | a5fc ff17 | ed03 1f2a | f003 1faa 
  0x000000013ff2b944: 4afa ff17 | 09d8 94d2 | 8941 a0f2 | 0900 ccf2 

  0x000000013ff2b954: ; ImmutableOopMap {[16]=Oop [40]=Oop }
                      ;*invokeinterface dispatch {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2b954: ad9d dc97 

  0x000000013ff2b958: ;   {other}
  0x000000013ff2b958: 1f20 03d5 | 1f49 84f2 | 1fc0 83f2 | d8fc ff17 | ed03 1f2a | f003 1faa | a10f 40b9 | 3f00 006b 
  0x000000013ff2b978: 2108 0054 | a113 40b9 

  0x000000013ff2b980: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b980: a203 bcd2 | a2b0 93f2 | 3f00 026b | e002 0054 | 22f0 7dd3 | fa03 00f9 | f737 0129 | f82f 01a9 
  0x000000013ff2b9a0: ec3b 02a9 | ef47 03a9 | f023 00f9 | e04b 00b9 

  0x000000013ff2b9b0: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2b9b0: 0185 9dd2 | 811d a0f2 | e100 c0f2 

  0x000000013ff2b9bc: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=NarrowOop [16]=Oop [24]=Oop [32]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.HashMap::getNode@128 (line 585)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2b9bc: f111 d997 

  0x000000013ff2b9c0: ;   {other}
  0x000000013ff2b9c0: 1f20 03d5 | 1f56 84f2 | 1fe0 83f2 | a004 0034 | fa03 40f9 | f737 4129 | f82f 41a9 | ec3b 42a9 
  0x000000013ff2b9e0: ef47 43a9 | f023 40f9 | e103 1daa | 3efa ff17 | e2bb 41a9 | b0fd ff17 | 4c0d 40b9 | 9df1 7dd3 
  0x000000013ff2ba00: ; implicit exception: dispatches to 0x000000013ff2e69c
  0x000000013ff2ba00: ad0b 40b9 | ec03 0d2a | 0c17 c0f2 | 8c1d 40f9 

  0x000000013ff2ba10: ;   {metadata('java/awt/Component')}
  0x000000013ff2ba10: 0e05 8ed2 | 6e22 a0f2 | 0e17 c0f2 | 9f01 0eeb | 815f ff54 

  0x000000013ff2ba24: ;   {metadata('androidx/compose/ui/awt/ComposeWindow')}
  0x000000013ff2ba24: 6e28 a0d2 | 0e5a 8cf2 | bf01 0e6b | c14b 0154 | adcb 40b9 | bdf1 7dd3 | bd5e ffb4 | ad0f 40b9 
  0x000000013ff2ba44: b4cb 2f8b | ff01 0d6b | e227 0154 | 8d03 c139 | cd8f 0035 | 9f12 00b9 | edfa ff17 | fa03 40f9 
  0x000000013ff2ba64: f737 4129 | f82f 41a9 | ec3b 42a9 | ef47 43a9 | f023 40f9 | e04b 40b9 | a11b 40b9 | 842b 42f9 
  0x000000013ff2ba84: ; ImmutableOopMap {r11=Oop r12=Oop r14=Oop r15=Oop r17=Oop r23=NarrowOop rlocals=Oop rcpool=Oop rfp=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.HashMap::getNode@145 (line 587)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
  0x000000013ff2ba84: 3df0 7dd3 

  0x000000013ff2ba88: ;   {poll}
  0x000000013ff2ba88: 9f00 40b9 | 3df7 ffb5 | f7f9 ff17 | ed03 1f2a | f4fa ff17 | ed07 40f9 | ec8b 41a9 | f037 40b9 
  0x000000013ff2baa8: eebf 43a9 | f127 40f9 | ab1b 40b9 | 832b 42f9 

  0x000000013ff2bab8: ; ImmutableOopMap {r12=Oop r13=Oop r14=Oop r15=Oop r17=Oop c_rarg2=Oop rfp=Oop [0]=NarrowOop [16]=Oop [48]=NarrowOop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.HashMap::getNode@145 (line 587)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
  0x000000013ff2bab8: 7df1 7dd3 

  0x000000013ff2babc: ;   {poll}
  0x000000013ff2babc: 7f00 40b9 | bdaf ffb4 | ab0f 40b9 | 7f01 106b | 21ff ff54 | aa13 40b9 

  0x000000013ff2bad4: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2bad4: a003 bcd2 | a0b0 93f2 | 5f01 006b | 6002 0054 | ed07 00f9 | ec8b 01a9 | f037 00b9 | eebf 03a9 
  0x000000013ff2baf4: f127 00f9 | 42f1 7dd3 

  0x000000013ff2bafc: ;   {oop("PostEventQueue"{0x0000000700ecec28})}
  0x000000013ff2bafc: 0185 9dd2 | 811d a0f2 | e100 c0f2 

  0x000000013ff2bb08: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop [56]=Oop [64]=Oop [72]=Oop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.HashMap::getNode@128 (line 585)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2bb08: 9e11 d997 

  0x000000013ff2bb0c: ;   {other}
  0x000000013ff2bb0c: 1f20 03d5 | 9f7f 84f2 | 1f40 84f2 | 20fc ff34 | ed07 40f9 | ec8b 41a9 | eebf 43a9 | f127 40f9 
  0x000000013ff2bb2c: e103 1daa | 7cfd ff17 | 2b08 40b9 

  0x000000013ff2bb38: ;   {metadata('sun/awt/AWTThreading$TrackedInvocationEvent$$Lambda+0x000000b8016554d0')}
  0x000000013ff2bb38: aa2c a0d2 | 0a9a 8af2 | 7f01 0a6b | 4121 0154 

  0x000000013ff2bb48: ; ImmutableOopMap {[16]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::finishedDispatching@47 (line 409)
                      ; - java.awt.event.InvocationEvent::dispatch@54 (line 321)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2bb48: 8e11 d997 

  0x000000013ff2bb4c: ;   {other}
  0x000000013ff2bb4c: 1f20 03d5 | 9f87 84f2 | 1f60 84f2 | 5bfc ff17 

  0x000000013ff2bb5c: ; implicit exception: dispatches to 0x000000013ff2e6c0
  0x000000013ff2bb5c: 2b08 40b9 

  0x000000013ff2bb60: ;   {metadata('sun/lwawt/macosx/LWCToolkit$CallableWrapper')}
  0x000000013ff2bb60: aa2c a0d2 | 0aac 89f2 | 7f01 0a6b | 0121 0154 | ec03 00f9 | fd03 15aa 

  0x000000013ff2bb78: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::dispatch@11 (line 308)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2bb78: 8211 d997 

  0x000000013ff2bb7c: ;   {other}
  0x000000013ff2bb7c: 1f20 03d5 | 9f8d 84f2 | 1f80 84f2 | 44fc ff17 | ea03 1f2a | e003 1f2a | b0f9 ff17 | eb03 1f2a 
  0x000000013ff2bb9c: b3fc ff17 | e083 0191 | 8d00 40f9 | ad01 0837 | ae01 40b2 | 0e00 00f9 | ed03 0eaa | 80fc edc8 
  0x000000013ff2bbbc: bf01 0eeb | 6002 0054 | e803 0091 | ad01 08cb | eecf 72b2 | ae01 0eea | 0e00 00f9 | 0d00 0014 
  0x000000013ff2bbdc: aef9 0091 | ef03 1faa | dcfd efc8 | ff01 1feb | ee07 40b2 | 0e00 00f9 | c000 0054 | ff01 1ceb 
  0x000000013ff2bbfc: 8100 0054 | a861 48f8 | 0805 0091 | a861 08f8 | 8100 0054 | 88a7 42f9 | 0805 0091 | 88a7 02f9 
  0x000000013ff2bc1c: c11f 0154 | 8d70 0091 | aefd df88 | ce5e 0034 | 8d80 0091 | adfd df88 | 2d6e 0034 | 8d80 0091 
  0x000000013ff2bc3c: b3fd df88 | ef83 0191 | ed01 40f9 | bf01 1feb | 8002 0054 | 8e00 40f9 | ae00 0837 | ee03 0faa 
  0x000000013ff2bc5c: 8dfc aec8 | df01 0feb | 0e00 0014 | ce09 00d1 | cd45 40f9 | ad00 00b4 | ad05 00d1 | cd45 00f9 
  0x000000013ff2bc7c: bf01 0deb | 0700 0014 | c835 49a9 | 0801 0daa | 1f01 1feb | 6800 00b5 | ce01 0191 | dffd 9fc8 
  0x000000013ff2bc9c: 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 | 011e 0154 | 6df2 7dd3 | 0d5e ffb5 | ec2f 01a9 
  0x000000013ff2bcbc: ea13 00f9 | f01b 00f9 | ea0b 40f9 | ea03 00f9 | ea0f 40f9 | 7df2 7dd3 | 4117 8012 | ea0b 00f9 
  0x000000013ff2bcdc: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [32]=Oop [48]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.logger.AbstractLoggerWrapper::isLoggable@6 (line 123)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2bcdc: a92c d997 

  0x000000013ff2bce0: ;   {other}
  0x000000013ff2bce0: 1f20 03d5 | 1fba 84f2 | 1fa0 84f2 | eb03 1f2a | f003 1f2a | c5fc ff17 | 0a47 ffb5 | 9df2 7dd3 
  0x000000013ff2bd00: ad0f 40b9 | aacb 338b | 7f02 0d6b | c21e 0154 | 4c11 40b9 | 97f1 7dd3 | b71f 01b4 | 8a03 c139 
  0x000000013ff2bd20: 0a80 0035 | ff12 00b9 | 2cfa ff17 | 0a0f 40b9 | 40f1 7dd3 | 4043 ffb4 | 8a17 40f9 | 2aab 00b4 
  0x000000013ff2bd40: 8c1f 40f9 | 4d21 00d1 | 8a01 0a8b | 4081 1ff8 | 8d17 00f9 | 12fa ff17 | cc0d 40b9 | 80f1 7dd3 
  0x000000013ff2bd60: 003e ffb4 | 8a17 40f9 | 4aac 00b4 | 8c1f 40f9 | 4d21 00d1 | 8a01 0a8b | 4081 1ff8 | 8d17 00f9 
  0x000000013ff2bd80: e8f9 ff17 | 8a27 40f9 | 8c2f 40f9 | bf3b 03d5 | 0d00 c039 | 0d3e ff34 | 1f00 0039 | aa02 00b5 
  0x000000013ff2bda0: f703 022a | f303 11aa | f403 0baa | f503 012a | e103 1caa | c900 0010 

  0x000000013ff2bdb8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2bdb8: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2bdcc: ;   {other}
  0x000000013ff2bdcc: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 13aa | eb03 14aa | e103 152a | e203 172a 
  0x000000013ff2bdec: daf9 ff17 | 4d21 00d1 | 8a01 0a8b | 4081 1ff8 | 8d27 00f9 | d5f9 ff17 | 8a27 40f9 | 8c2f 40f9 
  0x000000013ff2be0c: bf3b 03d5 | 0e00 c039 | 6e03 0034 | 1f00 0039 | ca00 00b4 | 4d21 00d1 | 8a01 0a8b | 4081 1ff8 
  0x000000013ff2be2c: 8d27 00f9 | 1400 0014 | f903 022a | f403 11aa | f503 0baa | f703 012a | e103 1caa | c900 0010 
  0x000000013ff2be4c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2be4c: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2be60: ;   {other}
  0x000000013ff2be60: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 14aa | eb03 15aa | e103 172a | e203 192a 
  0x000000013ff2be80: ca12 40b9 | aa3a ff35 | 7421 40b9 | 94f3 ff35 | fd0b 40f9 | 2101 8012 | eb07 00f9 | f313 00b9 
  0x000000013ff2bea0: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEventPrivate@53 (line 589)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2bea0: 382c d997 

  0x000000013ff2bea4: ;   {other}
  0x000000013ff2bea4: 1f20 03d5 | 9ff2 84f2 | 1fc0 84f2 | 6c0e 40b9 | 80f1 7dd3 | 403d ffb4 | 8c17 40f9 | 0ca4 00b4 
  0x000000013ff2bec4: 8d1f 40f9 | 8e21 00d1 | ac01 0c8b | 8081 1ff8 | 8e17 00f9 | e2f9 ff17 | eb41 40b9 | 60f1 7dd3 
  0x000000013ff2bee4: c063 ffb4 | 8a17 40f9 | 2aab 00b4 | 8b1f 40f9 | 4d21 00d1 | 6a01 0a8b | 4081 1ff8 | 8d17 00f9 
  0x000000013ff2bf04: 16fb ff17 | 2b10 40b9 | 60f1 7dd3 | c05f ffb4 | 8a17 40f9 | 4aad 00b4 | 8b1f 40f9 | 4d21 00d1 
  0x000000013ff2bf24: 6a01 0a8b | 4081 1ff8 | 8d17 00f9 | f6fa ff17 | 4b0c 40b9 | 60f1 7dd3 | 0059 ffb4 | 8a17 40f9 
  0x000000013ff2bf44: 6aaf 00b4 | 8b1f 40f9 | 5021 00d1 | 6a01 0a8b | 4081 1ff8 | 9017 00f9 | c0fa ff17 | 8a27 40f9 
  0x000000013ff2bf64: 8b2f 40f9 | bf3b 03d5 | 0d00 c039 | 0d61 ff34 | 1f00 0039 | 6a03 00b5 | f703 04aa | f903 11aa 
  0x000000013ff2bf84: f303 10aa | f603 15aa | f503 0faa | f403 0e2a | f803 0c2a | e103 1caa | c900 0010 

  0x000000013ff2bfa0: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2bfa0: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2bfb4: ;   {other}
  0x000000013ff2bfb4: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ef03 15aa | f003 13aa | ee03 142a | f503 16aa 
  0x000000013ff2bfd4: ec03 182a | e403 17aa | f103 19aa | ecfa ff17 | 4d21 00d1 | 6a01 0a8b | 4081 1ff8 | 8d27 00f9 
  0x000000013ff2bff4: e7fa ff17 | 8a27 40f9 | 8b2f 40f9 | bf3b 03d5 | 0200 c039 | 625a ff34 | 1f00 0039 | aa03 00b5 
  0x000000013ff2c014: f803 04aa | fa03 11aa | f303 10aa | f603 0faa | f703 15aa | f503 0e2a | f903 0c2a | fb03 01aa 
  0x000000013ff2c034: e103 1caa | c900 0010 

  0x000000013ff2c03c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c03c: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c050: ;   {other}
  0x000000013ff2c050: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ee03 152a | f003 13aa | ef03 16aa | f503 17aa 
  0x000000013ff2c070: ec03 192a | e403 18aa | f103 1aaa | e103 1baa | b5fa ff17 | 4d21 00d1 | 6a01 0a8b | 4081 1ff8 
  0x000000013ff2c090: 8d27 00f9 | b0fa ff17 | 8a27 40f9 | 8b2f 40f9 | bf3b 03d5 | 0100 c039 | 814f ff34 | 1f00 0039 
  0x000000013ff2c0b0: 8a03 00b5 | fa03 11aa | f803 04aa | f603 0faa | f703 15aa | f503 0e2a | f403 0d2a | f903 0c2a 
  0x000000013ff2c0d0: e103 1caa | c900 0010 

  0x000000013ff2c0d8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c0d8: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c0ec: ;   {other}
  0x000000013ff2c0ec: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ee03 152a | f007 40f9 | ed03 142a | ef03 16aa 
  0x000000013ff2c10c: f503 17aa | ec03 192a | e403 18aa | f103 1aaa | 5ffa ff17 | 4121 00d1 | 6a01 0a8b | 4081 1ff8 
  0x000000013ff2c12c: 8127 00f9 | 5afa ff17 | 8a0e 40b9 | 40f1 7dd3 | 2057 ffb4 | 8a17 40f9 | caa3 00b4 | 8b1f 40f9 
  0x000000013ff2c14c: 4c21 00d1 | 6a01 0a8b | 4081 1ff8 | 8c17 00f9 | b1fa ff17 | 8a35 40b9 | 40f1 7dd3 | c013 ffb4 
  0x000000013ff2c16c: 8a17 40f9 | 0ab7 00b4 | 811f 40f9 | 4221 00d1 | 2a00 0a8b | 4081 1ff8 | 8217 00f9 | 96f8 ff17 
  0x000000013ff2c18c: 8031 40b9 | 00f0 7dd3 | 4010 ffb4 | 8a17 40f9 | 6aaf 00b4 | 811f 40f9 | 4221 00d1 | 2a00 0a8b 
  0x000000013ff2c1ac: 4081 1ff8 | 8217 00f9 | 7af8 ff17 | 8a12 40b9 | 40f1 7dd3 | c00c ffb4 | 8a17 40f9 | 2ab1 00b4 
  0x000000013ff2c1cc: 811f 40f9 | 4221 00d1 | 2a00 0a8b | 4081 1ff8 | 8217 00f9 | 5ef8 ff17 | 8a0e 40b9 | 40f1 7dd3 
  0x000000013ff2c1ec: e008 ffb4 | 8a17 40f9 | 0ab6 00b4 | 811f 40f9 | 4221 00d1 | 2a00 0a8b | 4081 1ff8 | 8217 00f9 
  0x000000013ff2c20c: 3ff8 ff17 | 8a27 40f9 | 8c2f 40f9 | bf3b 03d5 | 0200 c039 | a20f ff34 | 1f00 0039 | ea02 00b5 
  0x000000013ff2c22c: f1c3 00a9 | ef03 00f9 | fb03 0eaa | fd03 0d2a | f903 0baa | e103 1caa | c900 0010 

  0x000000013ff2c248: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c248: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c25c: ;   {other}
  0x000000013ff2c25c: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | eb03 19aa | ee03 1baa | ed03 1d2a | ef47 40a9 
  0x000000013ff2c27c: f00b 40f9 | 65f8 ff17 | 4121 00d1 | 8a01 0a8b | 4081 1ff8 | 8127 00f9 | 60f8 ff17 | 8127 40f9 
  0x000000013ff2c29c: 822f 40f9 | bf3b 03d5 | 0a00 c039 | 4a05 ff34 | 1f00 0039 | 4103 00b5 | f143 02a9 | ef0f 00f9 
  0x000000013ff2c2bc: ee07 00f9 | ed13 00b9 | ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 

  0x000000013ff2c2d4: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c2d4: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c2e8: ;   {other}
  0x000000013ff2c2e8: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2c308: ed13 40b9 | efc7 41a9 | f017 40f9 | 0ff8 ff17 | 2a20 00d1 | 4100 018b | 2080 1ff8 | 8a27 00f9 
  0x000000013ff2c328: 0af8 ff17 | 8127 40f9 | 822f 40f9 | bf3b 03d5 | 0a00 c039 | ea04 ff34 | 1f00 0039 | 4103 00b5 
  0x000000013ff2c348: f143 02a9 | ef0f 00f9 | ee07 00f9 | ed13 00b9 | ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 
  0x000000013ff2c368: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c368: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c37c: ;   {other}
  0x000000013ff2c37c: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2c39c: ed13 40b9 | efc7 41a9 | f017 40f9 | 0cf8 ff17 | 2a20 00d1 | 4100 018b | 2080 1ff8 | 8a27 00f9 
  0x000000013ff2c3bc: 07f8 ff17 | 8127 40f9 | 822f 40f9 | bf3b 03d5 | 0a00 c039 | 0afe fe34 | 1f00 0039 | 4103 00b5 
  0x000000013ff2c3dc: f143 02a9 | ef0f 00f9 | ee07 00f9 | ed13 00b9 | ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 
  0x000000013ff2c3fc: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c3fc: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c410: ;   {other}
  0x000000013ff2c410: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2c430: ed13 40b9 | efc7 41a9 | f017 40f9 | d5f7 ff17 | 2a20 00d1 | 4100 018b | 2080 1ff8 | 8a27 00f9 
  0x000000013ff2c450: d0f7 ff17 | ca0d 40b9 | 40f1 7dd3 | 8077 ffb4 | 8a17 40f9 | cab2 00b4 | 8c1f 40f9 | 4d21 00d1 
  0x000000013ff2c470: 8a01 0a8b | 4081 1ff8 | 8d17 00f9 | b4fb ff17 | ca0e 40b9 | 40f1 7dd3 | c04e ffb4 | 8a17 40f9 
  0x000000013ff2c490: 8ab3 00b4 | 8b1f 40f9 | 4e21 00d1 | 6a01 0a8b | 4081 1ff8 | 8e17 00f9 | 6efa ff17 | 80df 40f9 
  0x000000013ff2c4b0: 8ae7 40f9 | 0ba0 0091 | 7f01 0aeb | 22cd 0054 | ea03 40b2 | 8bdf 00f9 | 0a00 00f9 

  0x000000013ff2c4cc: ;   {metadata('java/security/AccessControlContext')}
  0x000000013ff2c4cc: 4a00 a0d2 | 0a96 9ef2 | 70c1 80f9 | 0a08 00b9 | 0a40 0091 | 5f01 00f9 | 5ffd 00a9 | 2a20 8052 
  0x000000013ff2c4ec: 0a0c 00b9 | bf3a 03d5 | e203 00aa | 0df9 ff17 | ea31 40b9 | 40f1 7dd3 | a066 ffb4 | 8a17 40f9 
  0x000000013ff2c50c: aaba 00b4 | 8b1f 40f9 | 5021 00d1 | 6a01 0a8b | 4081 1ff8 | 9017 00f9 | 2dfb ff17 | 6a0e 40b9 
  0x000000013ff2c52c: 40f1 7dd3 | a060 ffb4 | 8a17 40f9 | 4abf 00b4 | 8b1f 40f9 | 5021 00d1 | 6a01 0a8b | 4081 1ff8 
  0x000000013ff2c54c: 9017 00f9 | fdfa ff17 | eb35 40b9 | 60f1 7dd3 | 0066 ffb4 | 8a17 40f9 | eaba 00b4 | 8b1f 40f9 
  0x000000013ff2c56c: 5021 00d1 | 6a01 0a8b | 4081 1ff8 | 9017 00f9 | 28fb ff17 | 6b12 40b9 | 60f1 7dd3 | 6060 ffb4 
  0x000000013ff2c58c: 8a17 40f9 | 8ab3 00b4 | 8b1f 40f9 | 5021 00d1 | 6a01 0a8b | 4081 1ff8 | 9017 00f9 | fbfa ff17 
  0x000000013ff2c5ac: 8a27 40f9 | 902f 40f9 | bf3b 03d5 | 0b00 c039 | 8b5e ff34 | 1f00 0039 | 2a03 00b5 | fa03 02aa 
  0x000000013ff2c5cc: f107 00f9 | fd03 0faa | fb03 0eaa | f703 0daa | f903 0caa | e103 1caa | c900 0010 

  0x000000013ff2c5e8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c5e8: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c5fc: ;   {other}
  0x000000013ff2c5fc: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 17aa | ec03 19aa | e203 1aaa | ee03 1baa 
  0x000000013ff2c61c: ef03 1daa | f107 40f9 | dafa ff17 | 4b21 00d1 | 0a02 0a8b | 4081 1ff8 | 8b27 00f9 | d5fa ff17 
  0x000000013ff2c63c: 8a27 40f9 | 8b2f 40f9 | bf3b 03d5 | 0f00 c039 | 2f60 ff34 | 1f00 0039 | ea02 00b5 | f603 02aa 
  0x000000013ff2c65c: f903 11aa | f703 0eaa | f303 0daa | f503 0caa | e103 1caa | c900 0010 

  0x000000013ff2c674: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c674: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c688: ;   {other}
  0x000000013ff2c688: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | ec03 15aa | e203 16aa | ee03 17aa 
  0x000000013ff2c6a8: f103 19aa | e9fa ff17 | 4f21 00d1 | 6a01 0a8b | 4081 1ff8 | 8f27 00f9 | e4fa ff17 | 8a27 40f9 
  0x000000013ff2c6c8: 8b2f 40f9 | bf3b 03d5 | 0100 c039 | 015a ff34 | 1f00 0039 | 2a03 00b5 | f903 02aa | fd03 11aa 
  0x000000013ff2c6e8: fb03 0faa | fa03 0eaa | f303 0daa | f703 0caa | e103 1caa | c900 0010 

  0x000000013ff2c700: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c700: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c714: ;   {other}
  0x000000013ff2c714: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | ec03 17aa | e203 19aa | ee03 1aaa 
  0x000000013ff2c734: ef03 1baa | f103 1daa | b6fa ff17 | 5021 00d1 | 6a01 0a8b | 4081 1ff8 | 9027 00f9 | b1fa ff17 
  0x000000013ff2c754: 8a27 40f9 | 8b2f 40f9 | bf3b 03d5 | 1000 c039 | 5053 ff34 | 1f00 0039 | 2a03 00b5 | f903 02aa 
  0x000000013ff2c774: fd03 11aa | fb03 0faa | fa03 0eaa | f303 0daa | f703 0caa | e103 1caa | c900 0010 

  0x000000013ff2c790: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2c790: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2c7a4: ;   {other}
  0x000000013ff2c7a4: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | ec03 17aa | e203 19aa | ee03 1aaa 
  0x000000013ff2c7c4: ef03 1baa | f103 1daa | 80fa ff17 | 5021 00d1 | 6a01 0a8b | 4081 1ff8 | 9027 00f9 | 7bfa ff17 
  0x000000013ff2c7e4: fa03 1daa | f703 4029 | ebe3 40a9 | ecbf 41a9 | eec7 42a9 | e11f 40f9 | 05fb ff17 | ef83 0191 
  0x000000013ff2c804: ee01 40f9 | df01 1feb | 8002 0054 | 8d00 40f9 | ad00 0837 | ed03 0faa | 8efc adc8 | bf01 0feb 
  0x000000013ff2c824: 0e00 0014 | ad09 00d1 | ae45 40f9 | ae00 00b4 | ce05 00d1 | ae45 00f9 | df01 0eeb | 0700 0014 
  0x000000013ff2c844: a839 49a9 | 0801 0eaa | 1f01 1feb | 6800 00b5 | ad01 0191 | bffd 9fc8 | 8100 0054 | 88a7 42f9 
  0x000000013ff2c864: 0805 00d1 | 88a7 02f9 | 61c9 0054 | 8e14 40b9 | ddf1 7dd3 | 1dc8 00b5 | e18b 0029 | e30f 00b9 
  0x000000013ff2c884: fd03 11aa | ec2f 01a9 | ea13 00f9 | f013 03a9 | e103 04aa | e217 40f9 

  0x000000013ff2c89c: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=NarrowOop [12]=NarrowOop [16]=Oop [24]=Oop [32]=Oop [48]=Oop [56]=Oop }
                      ;*invokestatic getLogger {reexecute=0 rethrow=0 return_oop=1}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@62 (line 211)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {static_call}
  0x000000013ff2c89c: b90f d997 

  0x000000013ff2c8a0: ;   {other}
  0x000000013ff2c8a0: 1f20 03d5 | 1f32 86f2 | 1fe0 84f2 | ee83 0191 | ed1f 40f9 | ac01 40f9 | ac01 0837 | 8a01 40b2 
  0x000000013ff2c8c0: ca01 00f9 | ec03 0aaa | aefd ecc8 | 9f01 0aeb | 6002 0054 | e803 0091 | 8c01 08cb | eacf 72b2 
  0x000000013ff2c8e0: 8a01 0aea | ca01 00f9 | 0d00 0014 | 8af9 0091 | eb03 1faa | 5cfd ebc8 | 7f01 1feb | ea07 40b2 
  0x000000013ff2c900: ca01 00f9 | c000 0054 | 7f01 1ceb | 8100 0054 | 8861 48f8 | 0805 0091 | 8861 08f8 | 8100 0054 
  0x000000013ff2c920: 88a7 42f9 | 0805 0091 | 88a7 02f9 | e023 00f9 | 41c7 0054 | ed1f 00f9 | e103 0daa | e223 40f9 
  0x000000013ff2c940: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=NarrowOop [12]=NarrowOop [16]=Oop [24]=Oop [32]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual setWrappedIfNotSet {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@72 (line 214)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {optimized virtual_call}
  0x000000013ff2c940: 100e d997 

  0x000000013ff2c944: ;   {other}
  0x000000013ff2c944: 1f20 03d5 | 9f46 86f2 | 1f00 85f2 | ed1f 40f9 | aa81 0091 | 4bfd df88 | eb13 0034 | aa81 0091 
  0x000000013ff2c964: 53fd df88 | ec83 0191 | 8b01 40f9 | 7f01 1feb | 8002 0054 | aa01 40f9 | aa00 0837 | ea03 0caa 
  0x000000013ff2c984: abfd aac8 | 5f01 0ceb | 0e00 0014 | 4a09 00d1 | 4b45 40f9 | ab00 00b4 | 6b05 00d1 | 4b45 00f9 
  0x000000013ff2c9a4: 7f01 0beb | 0700 0014 | 482d 49a9 | 0801 0baa | 1f01 1feb | 6800 00b5 | 4a01 0191 | 5ffd 9fc8 
  0x000000013ff2c9c4: 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 | 21c3 0054 | 6df2 7dd3 | 4d97 ffb4 | f103 1daa 
  0x000000013ff2c9e4: e18b 4029 | e30f 40b9 | ec2f 41a9 | ea13 40f9 | a0f7 ff17 | eb07 00f9 | ea13 00f9 | 8a70 0091 
  0x000000013ff2ca04: 4bfd df88 | e107 00b9 | fd03 11aa | ec0b 00f9 | e20f 0329 | f013 03a9 | 61f1 7dd3 

  0x000000013ff2ca20: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=NarrowOop [28]=NarrowOop [32]=Oop [48]=Oop [56]=Oop }
                      ;*invokestatic convert {reexecute=0 rethrow=0 return_oop=1}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@34 (line 202)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {static_call}
  0x000000013ff2ca20: 580f d997 

  0x000000013ff2ca24: ;   {other}
  0x000000013ff2ca24: 1f20 03d5 | 9f62 86f2 | 1f20 85f2 | 8a03 c139 | f303 00aa | 6a7f 0035 | e41f 40f9 | ec03 13aa 
  0x000000013ff2ca44: 8bfd 43d3 | 8a80 0091 | 4bfd 9f88 | ea03 04aa | eb03 0caa | 6b01 0aca | 6bfd 55d3 | 2b01 00b4 
  0x000000013ff2ca64: 0c01 00b4 | 4afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | a17e 0054 
  0x000000013ff2ca84: f103 1daa | e107 40b9 | ebb3 40a9 | e20f 4329 | ea13 40f9 | f01b 40f9 | 67fc ff17 | cc0d 40b9 
  0x000000013ff2caa4: 8af1 7dd3 | 5f01 11eb | 610e 0054 | ca41 0091 | 4cfd df88 | 9d05 0011 | 7dbf f837 | ca41 0091 
  0x000000013ff2cac4: 5dfd 9f88 | a3f6 ff17 | 4b0c 40b9 | 6af1 7dd3 | f007 40f9 | 5f01 10eb | 610e 0054 | 4a40 0091 
  0x000000013ff2cae4: 4bfd df88 | 7d05 0011 | fdbe f837 | 4a40 0091 | 5dfd 9f88 | e8f7 ff17 | ed03 1daa | ec07 40f9 
  0x000000013ff2cb04: e2bb 41a9 | f037 40b9 | ef87 43a9 | f127 40f9 | 44f9 ff17 | e107 00b9 | f1b3 00a9 | eb0f 00f9 
  0x000000013ff2cb24: e20f 0429 | ea43 03a9 

  0x000000013ff2cb2c: ;   {metadata('java/awt/EventQueue$4')}
  0x000000013ff2cb2c: 0125 91d2 | 6122 a0f2 | 0117 c0f2 

  0x000000013ff2cb38: ; ImmutableOopMap {rfp=NarrowOop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=NarrowOop [36]=NarrowOop [48]=Oop [56]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.awt.EventQueue::dispatchEvent@5 (line 722)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call _new_instance_Java}
  0x000000013ff2cb38: 7278 dc97 

  0x000000013ff2cb3c: ;   {other}
  0x000000013ff2cb3c: 1f20 03d5 | 9f85 86f2 | 1f40 85f2 | ef03 00aa | 67f7 ff17 | ee37 0029 | ef07 00f9 | f50f 00f9 
  0x000000013ff2cb5c: ec23 00b9 | e447 03a9 | fd03 10aa 

  0x000000013ff2cb68: ;   {metadata('java/lang/ref/WeakReference')}
  0x000000013ff2cb68: 0182 8ed2 | 8100 a0f2 | 0117 c0f2 

  0x000000013ff2cb74: ; ImmutableOopMap {rfp=Oop [4]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@36 (line 1276)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call _new_instance_Java}
  0x000000013ff2cb74: 6378 dc97 

  0x000000013ff2cb78: ;   {other}
  0x000000013ff2cb78: 1f20 03d5 | 1f8d 86f2 | 1f60 85f2 | f003 1daa | ee37 4029 | ef07 40f9 | f50f 40f9 | ec23 40b9 
  0x000000013ff2cb98: e447 43a9 | e103 00aa | d6f7 ff17 | ca0d 40b9 | 4af1 7dd3 | f103 1daa | 5f01 11eb | 410e 0054 
  0x000000013ff2cbb8: ca41 0091 | 4afd df88 | 5d05 0011 | 9db9 f837 | ca41 0091 | 5dfd 9f88 | e233 40b9 | 60f6 ff17 
  0x000000013ff2cbd8: aa71 0091 | 4afd df88 | 41f1 7dd3 

  0x000000013ff2cbe4: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=NarrowOop [12]=NarrowOop [16]=Oop [24]=Oop [32]=Oop [48]=Oop [56]=Oop }
                      ;*invokestatic convert {reexecute=0 rethrow=0 return_oop=1}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@87 (line 215)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {static_call}
  0x000000013ff2cbe4: e70e d997 

  0x000000013ff2cbe8: ;   {other}
  0x000000013ff2cbe8: 1f20 03d5 | 1f9b 86f2 | 1f80 85f2 | 8b03 c139 | f303 00aa | 2b94 0035 | ed1f 40f9 | ee03 13aa 
  0x000000013ff2cc08: ccfd 43d3 | aa81 0091 | 4cfd 9f88 | ea03 0daa | eb03 0eaa | 6b01 0aca | 6bfd 55d3 | 2b01 00b4 
  0x000000013ff2cc28: 0e01 00b4 | 4afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0b00 c039 | 7f09 0071 | 6193 0054 
  0x000000013ff2cc48: 46ff ff17 | 8c12 40b9 | 80f1 7dd3 | 2070 ffb4 | 8c17 40f9 | cc9a 00b4 | 8d1f 40f9 | 8e21 00d1 
  0x000000013ff2cc68: ac01 0c8b | 8081 1ff8 | 8e17 00f9 | 79fb ff17 | e233 00b9 | e103 0eaa | e203 0032 

  0x000000013ff2cc84: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokevirtual acquire {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock$Sync::lock@9 (line 153)
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2cc84: dff8 7396 

  0x000000013ff2cc88: ;   {other}
  0x000000013ff2cc88: 1f20 03d5 | 1faf 86f2 | 1fa0 85f2 | f103 1daa | e107 40b9 | eb07 40f9 | e233 40b9 | 2cf6 ff17 
  0x000000013ff2cca8: e103 02aa | ee37 0029 | ef07 00f9 | f50f 00f9 | ec23 00b9 | e447 03a9 | fd03 10aa | e203 0032 
  0x000000013ff2ccc8: ; ImmutableOopMap {rfp=Oop [4]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*invokevirtual acquire {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock$Sync::lock@9 (line 153)
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@4 (line 1270)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2ccc8: cef8 7396 

  0x000000013ff2cccc: ;   {other}
  0x000000013ff2cccc: 1f20 03d5 | 9fb7 86f2 | 1fc0 85f2 | f003 1daa | ee37 4029 | ef07 40f9 | f50f 40f9 | ec23 40b9 
  0x000000013ff2ccec: e447 43a9 | 6af7 ff17 | e107 00f9 | e213 00f9 | ed1b 00f9 

  0x000000013ff2cd00: ;   {metadata('java/awt/EventQueue$5')}
  0x000000013ff2cd00: 0103 99d2 | 6129 a0f2 | 0117 c0f2 

  0x000000013ff2cd0c: ; ImmutableOopMap {rfp=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=Oop [64]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.awt.EventQueue::dispatchEvent@58 (line 752)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call _new_instance_Java}
  0x000000013ff2cd0c: fd77 dc97 

  0x000000013ff2cd10: ;   {other}
  0x000000013ff2cd10: 1f20 03d5 | 1fc0 86f2 | 1fe0 85f2 | edfa ff17 | ec12 40b9 | 80f1 7dd3 | e07f ffb4 | 8a17 40f9 
  0x000000013ff2cd30: ca9f 00b4 | 8c1f 40f9 | 4d21 00d1 | 8a01 0a8b | 4081 1ff8 | 8d17 00f9 | f7fb ff17 | ea03 0032 
  0x000000013ff2cd50: f11f 40f9 | 2a46 0039 | ea03 1f2a | e003 1f2a | fa03 40f9 | f70b 40b9 | f82f 41a9 | ec3b 42a9 
  0x000000013ff2cd70: ef1b 40f9 | e123 40f9 | 37f5 ff17 | e103 0eaa | e203 0032 

  0x000000013ff2cd84: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop }
                      ;*invokevirtual acquire {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock$Sync::lock@9 (line 153)
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {optimized virtual_call}
  0x000000013ff2cd84: 9ff8 7396 

  0x000000013ff2cd88: ;   {other}
  0x000000013ff2cd88: 1f20 03d5 | 1fcf 86f2 | 1f00 86f2 | f103 1daa | e107 40b9 | eb07 40f9 | e233 40b9 | ecf5 ff17 
  0x000000013ff2cda8: fd0b 40f9 | 2111 8012 | ebaf 00a9 

  0x000000013ff2cdb4: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::getNextEventPrivate@3 (line 584)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cdb4: 7328 d997 

  0x000000013ff2cdb8: ;   {other}
  0x000000013ff2cdb8: 1f20 03d5 | 1fd5 86f2 | 1f20 86f2 | fd0b 40f9 | 6103 8012 | ebaf 00a9 | ea0f 00f9 

  0x000000013ff2cdd4: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [24]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEventPrivate@11 (line 585)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cdd4: 6b28 d997 

  0x000000013ff2cdd8: ;   {other}
  0x000000013ff2cdd8: 1f20 03d5 | 1fd9 86f2 | 1f40 86f2 | 2101 8012 | fd03 18aa | ebb3 00a9 

  0x000000013ff2cdf0: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@6 (line 607)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cdf0: 6428 d997 

  0x000000013ff2cdf4: ;   {other}
  0x000000013ff2cdf4: 1f20 03d5 | 9fdc 86f2 | 1f60 86f2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2ce08: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ce08: 5e28 d997 

  0x000000013ff2ce0c: ;   {other}
  0x000000013ff2ce0c: 1f20 03d5 | 9fdf 86f2 | 1f80 86f2 | 9fed 0771 | eb87 0054 | 9f7d 0c71 | 8dbe fe54 | 4117 8012 
  0x000000013ff2ce2c: fd0b 40f9 | eb07 00f9 | f6ab 01a9 | ec2b 00b9 

  0x000000013ff2ce3c: ; ImmutableOopMap {rfp=Oop [8]=Oop [24]=Oop [32]=Oop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::eventToCacheIndex@4 (line 518)
                      ; - java.awt.EventQueue::uncacheEQItem@4 (line 500)
                      ; - java.awt.EventQueue::getNextEventPrivate@60 (line 591)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ce3c: 5128 d997 

  0x000000013ff2ce40: ;   {other}
  0x000000013ff2ce40: 1f20 03d5 | 1fe6 86f2 | 1fa0 86f2 | 2101 8012 | fd0b 40f9 | eb2b 00a9 

  0x000000013ff2ce58: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*invokevirtual release {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@31 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ce58: 4a28 d997 

  0x000000013ff2ce5c: ;   {other}
  0x000000013ff2ce5c: 1f20 03d5 | 9fe9 86f2 | 1fc0 86f2 | 2101 8012 | fd03 0caa | eb07 00f9 | ea0f 00f9 

  0x000000013ff2ce78: ; ImmutableOopMap {rfp=Oop [8]=Oop [24]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@8 (line 170)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ce78: 4228 d997 

  0x000000013ff2ce7c: ;   {other}
  0x000000013ff2ce7c: 1f20 03d5 | 9fed 86f2 | 1fe0 86f2 | 6103 8012 | ebb3 00a9 | ea0f 00f9 | f02f 00b9 | ee03 03a9 
  0x000000013ff2ce9c: f037 0829 

  0x000000013ff2cea0: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [48]=Oop [56]=Oop }
                      ;*invokestatic checkIndex {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.Objects::checkIndex@3 (line 385)
                      ; - java.util.ArrayList::get@5 (line 427)
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@31 (line 172)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cea0: 3828 d997 

  0x000000013ff2cea4: ;   {other}
  0x000000013ff2cea4: 1f20 03d5 | 9ff2 86f2 | 1f00 87f2 | 6103 8012 | ebb3 00a9 | ea0f 00f9 | f03f 0529 | ee13 03a9 
  0x000000013ff2cec4: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [48]=Oop [56]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.ArrayList::elementData@5 (line 411)
                      ; - java.util.ArrayList::get@11 (line 428)
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@31 (line 172)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cec4: 2f28 d997 

  0x000000013ff2cec8: ;   {other}
  0x000000013ff2cec8: 1f20 03d5 | 1ff7 86f2 | 1f20 87f2 | e183 0191 | e203 1caa | e003 18aa | c900 0010 

  0x000000013ff2cee4: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2cee4: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2cef8: ;   {other}
  0x000000013ff2cef8: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 6cf4 ff17 | fa07 00f9 | e283 0191 | e103 00f9 
  0x000000013ff2cf18: ; ImmutableOopMap {[0]=Oop [8]=Oop }
                      ;*synchronization entry
                      ; - java.awt.EventDispatchThread::getEventQueue@-1 (line 226)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@8 (line 192)
                      ;   {runtime_call _complete_monitor_locking_Java}
  0x000000013ff2cf18: 3a9e dc97 

  0x000000013ff2cf1c: ;   {other}
  0x000000013ff2cf1c: 1f20 03d5 | 9f01 87f2 | 1f40 87f2 | f86b 40a9 | 46f4 ff17 | fa03 00f9 | fd03 18aa | f70b 00b9 
  0x000000013ff2cf3c: ee13 00f9 | e103 0eaa | eb33 01a9 | e283 0191 

  0x000000013ff2cf4c: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=NarrowOop [16]=Oop [24]=Oop [32]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@6 (line 607)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call _complete_monitor_locking_Java}
  0x000000013ff2cf4c: 2d9e dc97 

  0x000000013ff2cf50: ;   {other}
  0x000000013ff2cf50: 1f20 03d5 | 1f08 87f2 | 1f60 87f2 | f803 1daa | fa03 40f9 | f70b 40b9 | eb33 41a9 | ee13 40f9 
  0x000000013ff2cf70: 7bf4 ff17 | fd0b 40f9 | 2104 8012 | ea0b 00f9 

  0x000000013ff2cf80: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cf80: 0028 d997 

  0x000000013ff2cf84: ;   {other}
  0x000000013ff2cf84: 1f20 03d5 | 9f0e 87f2 | 1f80 87f2 | fd0b 40f9 | 2104 8012 | eb2b 00a9 | ec0b 00f9 

  0x000000013ff2cfa0: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@31 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2cfa0: f827 d997 

  0x000000013ff2cfa4: ;   {other}
  0x000000013ff2cfa4: 1f20 03d5 | 9f12 87f2 | 1fa0 87f2 | e31f 00b9 | f107 00f9 | e107 00b9 | ee1b 00f9 | e103 0eaa 
  0x000000013ff2cfc4: fd03 0caa | eb0b 00f9 | ea13 00f9 | e21b 00b9 | e283 0191 

  0x000000013ff2cfd8: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=NarrowOop [28]=NarrowOop [32]=Oop [48]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@8 (line 170)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call _complete_monitor_locking_Java}
  0x000000013ff2cfd8: 0a9e dc97 

  0x000000013ff2cfdc: ;   {other}
  0x000000013ff2cfdc: 1f20 03d5 | 9f19 87f2 | 1fc0 87f2 | ec03 1daa | e107 40b9 | f1af 40a9 | e20f 4329 | ea13 40f9 
  0x000000013ff2cffc: ee1b 40f9 | c6f5 ff17 | f703 032a | f403 11aa | f303 0caa | f503 0baa | f903 0aaa | f603 012a 
  0x000000013ff2d01c: e183 0191 | f803 022a | e203 1caa | e003 0eaa | c900 0010 

  0x000000013ff2d030: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2d030: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d044: ;   {other}
  0x000000013ff2d044: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ec03 13aa | f103 14aa | eb03 15aa | e103 162a 
  0x000000013ff2d064: e203 182a | e303 172a | ea03 19aa | e6f5 ff17 | 2104 8012 | fd03 0caa | ea2f 00a9 | f00b 00f9 
  0x000000013ff2d084: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface isLoggable {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d084: bf27 d997 

  0x000000013ff2d088: ;   {other}
  0x000000013ff2d088: 1f20 03d5 | 1f2f 87f2 | 1fe0 87f2 | 2104 8012 | fd03 0caa | ea2f 00a9 | f013 01a9 

  0x000000013ff2d0a4: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=Oop }
                      ;*invokeinterface platform {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d0a4: b727 d997 

  0x000000013ff2d0a8: ;   {other}
  0x000000013ff2d0a8: 1f20 03d5 | 1f33 87f2 | 1f00 88f2 | fd0b 40f9 | 6102 8012 | ea0b 00f9 

  0x000000013ff2d0c0: ; ImmutableOopMap {rfp=Oop [16]=Oop [40]=Oop [64]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue$4::run@7 (line 727)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d0c0: b027 d997 

  0x000000013ff2d0c4: ;   {other}
  0x000000013ff2d0c4: 1f20 03d5 | 9f36 87f2 | 1f20 88f2 | fd0b 40f9 | 2101 8012 | ef57 01a9 

  0x000000013ff2d0dc: ; ImmutableOopMap {rfp=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*invokevirtual lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@4 (line 1270)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d0dc: a927 d997 

  0x000000013ff2d0e0: ;   {other}
  0x000000013ff2d0e0: 1f20 03d5 | 1f3a 87f2 | 1f40 88f2 | fd03 15aa | e9ff 9fd2 | e9ff bff2 | e9ff dff2 

  0x000000013ff2d0fc: ; ImmutableOopMap {rfp=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@168 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2d0fc: e10c d997 

  0x000000013ff2d100: ;   {other}
  0x000000013ff2d100: 1f20 03d5 | 1f3e 87f2 | 1f60 88f2 | f503 1daa | cef6 ff17 | 2101 8012 | f507 00f9 

  0x000000013ff2d11c: ; ImmutableOopMap {[8]=Oop [16]=Oop [40]=Oop }
                      ;*invokevirtual release {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@168 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d11c: 9927 d997 

  0x000000013ff2d120: ;   {other}
  0x000000013ff2d120: 1f20 03d5 | 1f42 87f2 | 1f80 88f2 | fd0b 40f9 | 2104 8012 | ef57 01a9 | ea13 00f9 

  0x000000013ff2d13c: ; ImmutableOopMap {rfp=Oop [16]=Oop [24]=Oop [32]=Oop [40]=Oop }
                      ;*invokeinterface lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@4 (line 1270)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d13c: 9127 d997 

  0x000000013ff2d140: ;   {other}
  0x000000013ff2d140: 1f20 03d5 | 1f46 87f2 | 1fa0 88f2 | ea0f 40f9 | 6101 8052 | fd0b 40f9 | ea03 00f9 | e20f 00f9 
  0x000000013ff2d160: ; ImmutableOopMap {rfp=Oop [0]=Oop [24]=Oop [48]=Oop [64]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.Component::getAccessControlContext@7 (line 739)
                      ; - java.awt.EventQueue::getAccessControlContextFrom@11 (line 765)
                      ; - java.awt.EventQueue::dispatchEvent@22 (line 746)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d160: 8827 d997 

  0x000000013ff2d164: ;   {other}
  0x000000013ff2d164: 1f20 03d5 | 9f4a 87f2 | 1fc0 88f2 | eb03 0032 | ee1f 40f9 | cb45 0039 | eb03 1f2a | f003 1f2a 
  0x000000013ff2d184: ed07 40f9 | ec8b 41a9 | ef47 44a9 | e02b 40f9 | 9df7 ff17 | 2101 8012 | fd03 18aa | ebb3 00a9 
  0x000000013ff2d1a4: f9cf 02a9 | ee1f 00f9 

  0x000000013ff2d1ac: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual setPair {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@94 (line 625)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d1ac: 7527 d997 

  0x000000013ff2d1b0: ;   {other}
  0x000000013ff2d1b0: 1f20 03d5 | 1f54 87f2 | 1fe0 88f2 | ea03 18aa | 0200 0014 | ea03 18aa | 6101 8012 | fd03 0aaa 
  0x000000013ff2d1d0: eb07 00f9 

  0x000000013ff2d1d4: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@6 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d1d4: 6b27 d997 

  0x000000013ff2d1d8: ;   {other}
  0x000000013ff2d1d8: 1f20 03d5 | 1f59 87f2 | 1f00 89f2 | 2104 8012 | fd03 18aa | ebb3 00a9 | ea3b 02a9 

  0x000000013ff2d1f4: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop [40]=Oop }
                      ;*invokeinterface get {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d1f4: 6327 d997 

  0x000000013ff2d1f8: ;   {other}
  0x000000013ff2d1f8: 1f20 03d5 | 1f5d 87f2 | 1f20 89f2 | f143 00a9 | fb03 0faa | fd03 0d2a | f903 0baa | e183 0191 
  0x000000013ff2d218: e203 1caa | e003 0eaa | c900 0010 

  0x000000013ff2d224: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2d224: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d238: ;   {other}
  0x000000013ff2d238: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | eb03 19aa | ed03 1d2a | ef03 1baa | f143 40a9 
  0x000000013ff2d258: 8bf4 ff17 | 4117 8012 | fd03 18aa | ec07 00b9 | eb07 00f9 

  0x000000013ff2d26c: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventDispatchThread::pumpOneEventForFilters@15 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d26c: 4527 d997 

  0x000000013ff2d270: ;   {other}
  0x000000013ff2d270: 1f20 03d5 | 1f6c 87f2 | 1f40 89f2 | 2111 8012 | ebb3 00a9 | ea0f 00f9 | f02f 00b9 | ee1b 00f9 
  0x000000013ff2d290: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [48]=Oop }
                      ;*iflt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventDispatchThread::filterAndCheckEvent@22 (line 171)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d290: 3c27 d997 

  0x000000013ff2d294: ;   {other}
  0x000000013ff2d294: 1f20 03d5 | 9f70 87f2 | 1f60 89f2 | fd03 022a | f703 11aa | f903 0baa | fa03 012a | e103 1caa 
  0x000000013ff2d2b4: c900 0010 

  0x000000013ff2d2b8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d2b8: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d2cc: ;   {other}
  0x000000013ff2d2cc: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 17aa | eb03 19aa | e103 1a2a | e203 1d2a 
  0x000000013ff2d2ec: acf4 ff17 | f803 022a | f603 0eaa | f403 0baa | e103 1caa | c900 0010 

  0x000000013ff2d304: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d304: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d318: ;   {other}
  0x000000013ff2d318: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 1daa | eb03 14aa | e107 40b9 | ee03 16aa 
  0x000000013ff2d338: e203 182a | 79f4 ff17 | fa03 032a | f703 022a | f503 11aa | f603 0baa | f903 0aaa | f803 012a 
  0x000000013ff2d358: e103 1caa | c900 0010 

  0x000000013ff2d360: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d360: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d374: ;   {other}
  0x000000013ff2d374: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 15aa | eb03 16aa | e103 182a | e203 172a 
  0x000000013ff2d394: e303 1a2a | ea03 19aa | b1f4 ff17 | fd0b 40f9 | 4117 8012 | eb2b 00a9 | f333 01a9 | f113 00f9 
  0x000000013ff2d3b4: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=Oop [32]=Oop }
                      ;*if_acmpeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.ReentrantLock$Sync::tryRelease@14 (line 174)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@2 (line 1059)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@31 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d3b4: f326 d997 

  0x000000013ff2d3b8: ;   {other}
  0x000000013ff2d3b8: 1f20 03d5 | 1f95 87f2 | 1f80 89f2 | 4117 8012 | fd03 0caa | ebab 00a9 | ed1b 00b9 

  0x000000013ff2d3d4: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) sun.awt.dnd.SunDragSourceContextPeer::checkEvent@3 (line 368)
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@96 (line 182)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d3d4: eb26 d997 

  0x000000013ff2d3d8: ;   {other}
  0x000000013ff2d3d8: 1f20 03d5 | 1f99 87f2 | 1fa0 89f2 | 4117 8012 | ebb3 00a9 | ea0f 00f9 | ef2b 00b9 | ee43 03a9 
  0x000000013ff2d3f8: ea1b 04a9 

  0x000000013ff2d3fc: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [48]=Oop [56]=Oop [64]=Oop [72]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventDispatchThread$HierarchyEventFilter::acceptEvent@4 (line 238)
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@42 (line 173)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d3fc: e126 d997 

  0x000000013ff2d400: ;   {other}
  0x000000013ff2d400: 1f20 03d5 | 1f9e 87f2 | 1fc0 89f2 | fd2f 41a9 | 4117 8012 | eb03 00f9 | e237 01a9 | ea13 00f9 
  0x000000013ff2d420: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [24]=Oop [32]=Oop [64]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.AWTEvent::getAccessControlContext@4 (line 127)
                      ; - java.awt.EventQueue::dispatchEvent@28 (line 748)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d420: d826 d997 

  0x000000013ff2d424: ;   {other}
  0x000000013ff2d424: 1f20 03d5 | 9fa2 87f2 | 1fe0 89f2 | fd0b 40f9 | 0117 8012 | ef57 01a9 | ea23 00b9 

  0x000000013ff2d440: ; ImmutableOopMap {rfp=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*ifne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@12 (line 1272)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d440: d026 d997 

  0x000000013ff2d444: ;   {other}
  0x000000013ff2d444: 1f20 03d5 | 9fa6 87f2 | 1f00 8af2 | f803 04aa | fa03 11aa | f303 10aa | f603 0faa | f703 15aa 
  0x000000013ff2d464: f503 0e2a | f903 0c2a | fb03 01aa | e103 1caa | c900 0010 

  0x000000013ff2d478: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d478: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d48c: ;   {other}
  0x000000013ff2d48c: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ee03 152a | f003 13aa | ef03 16aa | f503 17aa 
  0x000000013ff2d4ac: ec03 192a | e403 18aa | f103 1aaa | e103 1baa | a8f5 ff17 | f703 04aa | f903 11aa | f303 10aa 
  0x000000013ff2d4cc: f603 15aa | f503 0faa | f403 0e2a | f803 0c2a | fa03 01aa | e103 1caa | c900 0010 

  0x000000013ff2d4e8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d4e8: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d4fc: ;   {other}
  0x000000013ff2d4fc: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ef03 15aa | f003 13aa | ee03 142a | f503 16aa 
  0x000000013ff2d51c: ec03 182a | e403 17aa | f103 19aa | e103 1aaa | 77f5 ff17 | f803 04aa | fa03 02aa | f903 11aa 
  0x000000013ff2d53c: f403 0faa | f603 15aa | f503 0d2a | f703 0c2a | e103 1caa | c900 0010 

  0x000000013ff2d554: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d554: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d568: ;   {other}
  0x000000013ff2d568: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 152a | ee07 40b9 | ef03 14aa | f503 16aa 
  0x000000013ff2d588: ec03 172a | e403 18aa | f103 19aa | e203 1aaa | 31f5 ff17 | 4117 8012 | fd03 0caa | ea2f 00a9 
  0x000000013ff2d5a8: ed3b 01a9 

  0x000000013ff2d5ac: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.logger.SimpleConsoleLogger::effectiveLevel@4 (line 164)
                      ; - jdk.internal.logger.SimpleConsoleLogger::isLoggable@1 (line 132)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@20 (line 124)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d5ac: 7526 d997 

  0x000000013ff2d5b0: ;   {other}
  0x000000013ff2d5b0: 1f20 03d5 | 1fd4 87f2 | 1f20 8af2 | e103 1caa | c900 0010 

  0x000000013ff2d5c4: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d5c4: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d5d8: ;   {other}
  0x000000013ff2d5d8: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 8ef5 ff17 | fd0b 40f9 | 0117 8012 | f5bf 00a9 
  0x000000013ff2d5f8: f0af 01a9 

  0x000000013ff2d5fc: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [40]=Oop }
                      ;*if_acmpeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@22 (line 1272)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d5fc: 6126 d997 

  0x000000013ff2d600: ;   {other}
  0x000000013ff2d600: 1f20 03d5 | 1fde 87f2 | 1f40 8af2 | fd0b 40f9 | 0117 8012 | f5d3 00a9 | eac3 01a9 

  0x000000013ff2d61c: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [40]=Oop }
                      ;*if_acmpeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.ReentrantLock$Sync::tryRelease@14 (line 174)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@2 (line 1059)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@168 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d61c: 5926 d997 

  0x000000013ff2d620: ;   {other}
  0x000000013ff2d620: 1f20 03d5 | 1fe2 87f2 | 1f60 8af2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2d634: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual notifyEventDispatchThreadFree {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@55 (line 572)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d634: 5326 d997 

  0x000000013ff2d638: ;   {other}
  0x000000013ff2d638: 1f20 03d5 | 1fe5 87f2 | 1f80 8af2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2d64c: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual release {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@71 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d64c: 4d26 d997 

  0x000000013ff2d650: ;   {other}
  0x000000013ff2d650: 1f20 03d5 | 1fe8 87f2 | 1fa0 8af2 | fd0b 40f9 | 2101 8012 | ecbf 00a9 

  0x000000013ff2d668: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@6 (line 607)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d668: 4626 d997 

  0x000000013ff2d66c: ;   {other}
  0x000000013ff2d66c: 1f20 03d5 | 9feb 87f2 | 1fc0 8af2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2d680: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d680: 4026 d997 

  0x000000013ff2d684: ;   {other}
  0x000000013ff2d684: 1f20 03d5 | 9fee 87f2 | 1fe0 8af2 | fd0b 40f9 | 0117 8012 | eaaf 00a9 

  0x000000013ff2d69c: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.event.InvocationEvent::finishedDispatching@9 (line 402)
                      ; - java.awt.event.InvocationEvent::dispatch@54 (line 321)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d69c: 3926 d997 

  0x000000013ff2d6a0: ;   {other}
  0x000000013ff2d6a0: 1f20 03d5 | 1ff2 87f2 | 1f00 8bf2 | fd0b 40f9 | e10b 00f9 | 2104 8012 

  0x000000013ff2d6b8: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface await {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@62 (line 573)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d6b8: 3226 d997 

  0x000000013ff2d6bc: ;   {other}
  0x000000013ff2d6bc: 1f20 03d5 | 9ff5 87f2 | 1f20 8bf2 | fd0b 40f9 | 2104 8012 | ea0b 00f9 

  0x000000013ff2d6d4: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@71 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d6d4: 2b26 d997 

  0x000000013ff2d6d8: ;   {other}
  0x000000013ff2d6d8: 1f20 03d5 | 1ff9 87f2 | 1f40 8bf2 | f123 00f9 | e103 11aa | ef1f 00f9 | fd03 0daa | ec07 00f9 
  0x000000013ff2d6f8: e283 0191 

  0x000000013ff2d6fc: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=Oop [32]=Oop [48]=NarrowOop [56]=Oop [64]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@6 (line 607)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call _complete_monitor_locking_Java}
  0x000000013ff2d6fc: 419c dc97 

  0x000000013ff2d700: ;   {other}
  0x000000013ff2d700: 1f20 03d5 | 1ffe 87f2 | 1f60 8bf2 | ed03 1daa | ec07 40f9 | efc7 43a9 | 01f6 ff17 | fd0b 40f9 
  0x000000013ff2d720: 2104 8012 | ea0b 00f9 

  0x000000013ff2d728: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d728: 1626 d997 

  0x000000013ff2d72c: ;   {other}
  0x000000013ff2d72c: 1f20 03d5 | 9f03 88f2 | 1f80 8bf2 | fd03 11aa | fb03 0faa | f903 0baa | e183 0191 | e203 1caa 
  0x000000013ff2d74c: e003 0eaa | c900 0010 

  0x000000013ff2d754: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2d754: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d768: ;   {other}
  0x000000013ff2d768: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | eb03 19aa | ef03 1baa | f103 1daa | 9ef2 ff17 
  0x000000013ff2d788: f143 02a9 | ef0f 00f9 | ee07 00f9 | ed13 00b9 | ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 
  0x000000013ff2d7a8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d7a8: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d7bc: ;   {other}
  0x000000013ff2d7bc: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2d7dc: ed13 40b9 | efc7 41a9 | f017 40f9 | edf2 ff17 | f143 02a9 | ef0f 00f9 | ee07 00f9 | ed13 00b9 
  0x000000013ff2d7fc: ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 

  0x000000013ff2d80c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d80c: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d820: ;   {other}
  0x000000013ff2d820: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2d840: ed13 40b9 | efc7 41a9 | f017 40f9 | c3f2 ff17 | f143 02a9 | ef0f 00f9 | ee07 00f9 | ed13 00b9 
  0x000000013ff2d860: ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 

  0x000000013ff2d870: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d870: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d884: ;   {other}
  0x000000013ff2d884: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2d8a4: ed13 40b9 | efc7 41a9 | f017 40f9 | ccf2 ff17 | f143 02a9 | ef0f 00f9 | ee07 00f9 | ed13 00b9 
  0x000000013ff2d8c4: ec03 00f9 | fd03 0baa | e103 1caa | c900 0010 

  0x000000013ff2d8d4: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2d8d4: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2d8e8: ;   {other}
  0x000000013ff2d8e8: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | fb03 172a | f703 1b2a | eb03 1daa | ec3b 40a9 
  0x000000013ff2d908: ed13 40b9 | efc7 41a9 | f017 40f9 | 7df2 ff17 | 4117 8012 | fd03 18aa | ebb3 00a9 | ee13 00f9 
  0x000000013ff2d928: ed2b 00b9 

  0x000000013ff2d92c: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.HashMap::getNode@14 (line 575)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d92c: 9525 d997 

  0x000000013ff2d930: ;   {other}
  0x000000013ff2d930: 1f20 03d5 | 1f44 88f2 | 1fa0 8bf2 | 4117 8012 | fd03 18aa | ebb3 00a9 | f367 02a9 | ee1b 00f9 
  0x000000013ff2d950: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop [40]=Oop [48]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) sun.awt.AppContext::get@47 (line 620)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d950: 8c25 d997 

  0x000000013ff2d954: ;   {other}
  0x000000013ff2d954: 1f20 03d5 | 9f48 88f2 | 1fc0 8bf2 | fd0b 40f9 | 2101 8012 | ecbf 00a9 | f5d3 02a9 | f11f 00f9 
  0x000000013ff2d974: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop [48]=Oop [56]=Oop }
                      ;*invokevirtual setPair {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@94 (line 625)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d974: 8325 d997 

  0x000000013ff2d978: ;   {other}
  0x000000013ff2d978: 1f20 03d5 | 1f4d 88f2 | 1fe0 8bf2 | ea03 0caa | 0200 0014 | ea03 0caa | 6101 8012 | fd0b 40f9 
  0x000000013ff2d998: ea07 00f9 

  0x000000013ff2d99c: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@6 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d99c: 7925 d997 

  0x000000013ff2d9a0: ;   {other}
  0x000000013ff2d9a0: 1f20 03d5 | 1f52 88f2 | 1f00 8cf2 | fd0b 40f9 | 2104 8012 | ecbf 00a9 | ea47 02a9 

  0x000000013ff2d9bc: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop [40]=Oop }
                      ;*invokeinterface get {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2d9bc: 7125 d997 

  0x000000013ff2d9c0: ;   {other}
  0x000000013ff2d9c0: 1f20 03d5 | 1f56 88f2 | 1f20 8cf2 | f703 0eaa | f303 0daa | f503 0caa | e183 0191 | f603 02aa 
  0x000000013ff2d9e0: e203 1caa | e003 11aa | c900 0010 

  0x000000013ff2d9ec: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2d9ec: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2da00: ;   {other}
  0x000000013ff2da00: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | ec03 15aa | e203 16aa | ee03 17aa 
  0x000000013ff2da20: 28f6 ff17 | e41f 40f9 | 8b20 40b9 | 60f1 7dd3 | 8080 ffb4 | 8a17 40f9 | ca41 00b4 | 8b1f 40f9 
  0x000000013ff2da40: 4c21 00d1 | 6a01 0a8b | 4081 1ff8 | 8c17 00f9 | fcfb ff17 | 8a2f 40f9 | 8b27 40f9 | bf3b 03d5 
  0x000000013ff2da60: 0c00 c039 | 0c81 ff34 | 1f00 0039 | eb01 00b5 | f303 04aa | e103 1caa | c900 0010 

  0x000000013ff2da7c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2da7c: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2da90: ;   {other}
  0x000000013ff2da90: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | e403 13aa | f8fb ff17 | 6c21 00d1 | 4a01 0b8b 
  0x000000013ff2dab0: 4081 1ff8 | 8c27 00f9 | f3fb ff17 | f503 0eaa | f303 0baa | e103 1caa | c900 0010 

  0x000000013ff2dacc: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2dacc: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dae0: ;   {other}
  0x000000013ff2dae0: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | eb03 13aa | e107 40b9 | ee03 15aa | 14f6 ff17 
  0x000000013ff2db00: f503 0caa | e103 1caa | c900 0010 

  0x000000013ff2db0c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2db0c: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2db20: ;   {other}
  0x000000013ff2db20: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 1daa | ec03 15aa | caf4 ff17 | fd0b 40f9 
  0x000000013ff2db40: 4117 8012 | f62b 01a9 | ed13 00f9 

  0x000000013ff2db4c: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [24]=Oop [32]=Oop }
                      ;*if_acmpeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.ReentrantLock$Sync::tryRelease@14 (line 174)
                      ; - java.util.concurrent.locks.AbstractQueuedSynchronizer::release@2 (line 1059)
                      ; - java.util.concurrent.locks.ReentrantLock::unlock@5 (line 494)
                      ; - java.awt.EventQueue::getNextEvent@71 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2db4c: 0d25 d997 

  0x000000013ff2db50: ;   {other}
  0x000000013ff2db50: 1f20 03d5 | 1f88 88f2 | 1f40 8cf2 | ea0f 40f9 | 4117 8012 | fd0b 40f9 | ea03 00f9 | e20f 00f9 
  0x000000013ff2db70: ; ImmutableOopMap {rfp=Oop [0]=Oop [24]=Oop [48]=Oop [56]=Oop [64]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::getAccessControlContextFrom@38 (line 768)
                      ; - java.awt.EventQueue::dispatchEvent@22 (line 746)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2db70: 0425 d997 

  0x000000013ff2db74: ;   {other}
  0x000000013ff2db74: 1f20 03d5 | 9f8c 88f2 | 1f60 8cf2 | f303 0daa | f403 0caa | e183 0191 | e203 1caa | e003 11aa 
  0x000000013ff2db94: c900 0010 

  0x000000013ff2db98: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2db98: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dbac: ;   {other}
  0x000000013ff2dbac: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | ec03 14aa | faf4 ff17 | fd0b 40f9 
  0x000000013ff2dbcc: 0117 8012 | ef57 01a9 

  0x000000013ff2dbd4: ; ImmutableOopMap {rfp=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@93 (line 1293)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2dbd4: eb24 d997 

  0x000000013ff2dbd8: ;   {other}
  0x000000013ff2dbd8: 1f20 03d5 | 1f99 88f2 | 1f80 8cf2 | fd0b 40f9 | 0117 8012 | ef57 01a9 

  0x000000013ff2dbf0: ; ImmutableOopMap {rfp=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@115 (line 1296)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2dbf0: e424 d997 

  0x000000013ff2dbf4: ;   {other}
  0x000000013ff2dbf4: 1f20 03d5 | 9f9c 88f2 | 1fa0 8cf2 | fa03 02aa | f107 00f9 | fd03 0faa | fb03 0eaa | f703 0daa 
  0x000000013ff2dc14: f903 0caa | e103 1caa | c900 0010 

  0x000000013ff2dc20: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2dc20: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dc34: ;   {other}
  0x000000013ff2dc34: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 17aa | ec03 19aa | e203 1aaa | ee03 1baa 
  0x000000013ff2dc54: ef03 1daa | f107 40f9 | 4ef5 ff17 | fa03 02aa | f107 00f9 | fd03 0faa | fb03 0eaa | f703 0daa 
  0x000000013ff2dc74: f903 0caa | e103 1caa | c900 0010 

  0x000000013ff2dc80: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2dc80: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dc94: ;   {other}
  0x000000013ff2dc94: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 17aa | ec03 19aa | e203 1aaa | ee03 1baa 
  0x000000013ff2dcb4: ef03 1daa | f107 40f9 | 47f5 ff17 | f903 02aa | fd03 11aa | fb03 0faa | fa03 0eaa | f303 0daa 
  0x000000013ff2dcd4: f703 0caa | e103 1caa | c900 0010 

  0x000000013ff2dce0: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2dce0: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dcf4: ;   {other}
  0x000000013ff2dcf4: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | ec03 17aa | e203 19aa | ee03 1aaa 
  0x000000013ff2dd14: ef03 1baa | f103 1daa | 40f5 ff17 | f903 02aa | fd03 11aa | fb03 0faa | fa03 0eaa | f703 0daa 
  0x000000013ff2dd34: f803 0caa | e103 1caa | c900 0010 

  0x000000013ff2dd40: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2dd40: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dd54: ;   {other}
  0x000000013ff2dd54: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 17aa | ec03 18aa | e203 19aa | ee03 1aaa 
  0x000000013ff2dd74: ef03 1baa | f103 1daa | f2f4 ff17 | fd0b 40f9 | 4117 8012 | ecbf 00a9 | f113 00f9 | ea2b 00b9 
  0x000000013ff2dd94: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop }
                      ;*ifle {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.HashMap::getNode@14 (line 575)
                      ; - java.util.HashMap::get@2 (line 564)
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2dd94: 7b24 d997 

  0x000000013ff2dd98: ;   {other}
  0x000000013ff2dd98: 1f20 03d5 | 1fd1 88f2 | 1fc0 8cf2 | fd0b 40f9 | 4117 8012 | ecbf 00a9 | f457 02a9 | f11b 00f9 
  0x000000013ff2ddb8: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop [40]=Oop [48]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) sun.awt.AppContext::get@47 (line 620)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ddb8: 7224 d997 

  0x000000013ff2ddbc: ;   {other}
  0x000000013ff2ddbc: 1f20 03d5 | 9fd5 88f2 | 1fe0 8cf2 | 2101 8012 | fd03 0caa | ea2f 00a9 | ee0b 00f9 

  0x000000013ff2ddd8: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop }
                      ;*invokevirtual isLoggable {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.util.logging.internal.LoggingProviderImpl$JULWrapper::isLoggable@8 (line 215)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@20 (line 124)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ddd8: 6a24 d997 

  0x000000013ff2dddc: ;   {other}
  0x000000013ff2dddc: 1f20 03d5 | 9fd9 88f2 | 1f00 8df2 | 2101 8012 | fd03 0caa | ea2f 00a9 

  0x000000013ff2ddf4: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*getfield levelValue {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.logging.Logger::isLoggable@4 (line 2036)
                      ; - sun.util.logging.internal.LoggingProviderImpl$JULWrapper::isLoggable@8 (line 215)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@20 (line 124)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2ddf4: 6324 d997 

  0x000000013ff2ddf8: ;   {other}
  0x000000013ff2ddf8: 1f20 03d5 | 1fdd 88f2 | 1f20 8df2 | 2107 8012 | fd03 0caa | ea2f 00a9 | ed0b 00f9 

  0x000000013ff2de14: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop }
                      ;*invokeinterface isLoggable {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@20 (line 124)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2de14: 5b24 d997 

  0x000000013ff2de18: ;   {other}
  0x000000013ff2de18: 1f20 03d5 | 1fe1 88f2 | 1f40 8df2 | fd0b 40f9 | efd7 00a9 | e10f 00f9 | 0117 8012 

  0x000000013ff2de34: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::dispatchEventImpl@33 (line 782)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2de34: 5324 d997 

  0x000000013ff2de38: ;   {other}
  0x000000013ff2de38: 1f20 03d5 | 1fe5 88f2 | 1f60 8df2 | fd0b 40f9 | e17b 1a32 | e0ab 00a9 

  0x000000013ff2de50: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface setCanAccessSystemClipboard {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.AWTEvent::dispatched@15 (line 597)
                      ; - java.awt.EventQueue::dispatchEventImpl@45 (line 784)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2de50: 4c24 d997 

  0x000000013ff2de54: ;   {other}
  0x000000013ff2de54: 1f20 03d5 | 9fe8 88f2 | 1f80 8df2 

  0x000000013ff2de60: ;   {metadata('java/security/AccessControlContext')}
  0x000000013ff2de60: 0196 9ed2 | 4100 a0f2 | 0117 c0f2 

  0x000000013ff2de6c: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [32]=NarrowOop [48]=Oop [56]=Oop [64]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.AccessController::getContext@8 (line 1010)
                      ; - java.awt.EventQueue::dispatchEvent@16 (line 744)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call _new_instance_Java}
  0x000000013ff2de6c: a573 dc97 

  0x000000013ff2de70: ;   {other}
  0x000000013ff2de70: 1f20 03d5 | 1fec 88f2 | 1fa0 8df2 | 9bf9 ff17 | ed1f 40f9 | aa21 40b9 | 40f1 7dd3 | c06b ffb4 
  0x000000013ff2de90: 8a17 40f9 | 0a24 00b4 | 8b1f 40f9 | 4c21 00d1 | 6a01 0a8b | 4081 1ff8 | 8c17 00f9 | 56fb ff17 
  0x000000013ff2deb0: 8a2f 40f9 | 8b27 40f9 | bf3b 03d5 | 0e00 c039 | 4e6c ff34 | 1f00 0039 | eb01 00b5 | f303 0daa 
  0x000000013ff2ded0: e103 1caa | c900 0010 

  0x000000013ff2ded8: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2ded8: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2deec: ;   {other}
  0x000000013ff2deec: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 13aa | 52fb ff17 | 6c21 00d1 | 4a01 0b8b 
  0x000000013ff2df0c: 4081 1ff8 | 8c27 00f9 | 4dfb ff17 | 8fe1 0751 | ff09 0071 | a336 fe54 | 4118 8012 | fd0b 40f9 
  0x000000013ff2df2c: eb07 00f9 | f6ab 01a9 | ec2b 00b9 

  0x000000013ff2df38: ; ImmutableOopMap {rfp=Oop [8]=Oop [24]=Oop [32]=Oop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::eventToCacheIndex@4 (line 518)
                      ; - java.awt.EventQueue::uncacheEQItem@4 (line 500)
                      ; - java.awt.EventQueue::getNextEventPrivate@60 (line 591)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2df38: 1224 d997 

  0x000000013ff2df3c: ;   {other}
  0x000000013ff2df3c: 1f20 03d5 | 9f05 89f2 | 1fc0 8df2 | ea0b 40f9 | 6103 8012 | ea03 00f9 | eb5b 01a9 | ef23 00b9 
  0x000000013ff2df5c: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [24]=Oop }
                      ;*aastore {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::uncacheEQItem@51 (line 506)
                      ; - java.awt.EventQueue::getNextEventPrivate@60 (line 591)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2df5c: 0924 d997 

  0x000000013ff2df60: ;   {other}
  0x000000013ff2df60: 1f20 03d5 | 1f0a 89f2 | 1fe0 8df2 | e9ff 9fd2 | e9ff bff2 | e9ff dff2 

  0x000000013ff2df78: ; ImmutableOopMap {[16]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::finishedDispatching@47 (line 409)
                      ; - java.awt.event.InvocationEvent::dispatch@54 (line 321)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2df78: 4209 d997 

  0x000000013ff2df7c: ;   {other}
  0x000000013ff2df7c: 1f20 03d5 | 9f0d 89f2 | 1f00 8ef2 | 4ff3 ff17 | ec03 00f9 | fd03 15aa | e9ff 9fd2 | e9ff bff2 
  0x000000013ff2df9c: e9ff dff2 

  0x000000013ff2dfa0: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::dispatch@11 (line 308)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2dfa0: 3809 d997 

  0x000000013ff2dfa4: ;   {other}
  0x000000013ff2dfa4: 1f20 03d5 | 9f12 89f2 | 1f20 8ef2 | 3af3 ff17 | f703 032a | f803 022a | f303 11aa | f503 0baa 
  0x000000013ff2dfc4: f903 0aaa | f603 012a | e103 1caa | c900 0010 

  0x000000013ff2dfd4: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2dfd4: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2dfe8: ;   {other}
  0x000000013ff2dfe8: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 13aa | eb03 15aa | e103 162a | e203 182a 
  0x000000013ff2e008: e303 172a | ea03 19aa | 92f6 ff17 | e107 00b9 | e41f 00f9 | e103 04aa | e31f 00b9 | f107 00f9 
  0x000000013ff2e028: f01b 00f9 | fd03 0caa | eb0b 00f9 | ea13 00f9 | e21b 00b9 | e283 0191 

  0x000000013ff2e040: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=Oop [16]=Oop [24]=NarrowOop [28]=NarrowOop [32]=Oop [48]=Oop [56]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@14 (line 200)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call _complete_monitor_locking_Java}
  0x000000013ff2e040: f099 dc97 

  0x000000013ff2e044: ;   {other}
  0x000000013ff2e044: 1f20 03d5 | 9f26 89f2 | 1f40 8ef2 | ec03 1daa | e107 40b9 | f1af 40a9 | e20f 4329 | ea13 40f9 
  0x000000013ff2e064: f013 43a9 | eef6 ff17 | fa03 032a | f503 11aa | fb03 10aa | f403 0caa | f603 0baa | f903 0aaa 
  0x000000013ff2e084: f803 012a | e183 0191 | f703 022a | e203 1caa | e003 04aa | c900 0010 

  0x000000013ff2e09c: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2e09c: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e0b0: ;   {other}
  0x000000013ff2e0b0: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ec03 14aa | f103 15aa | eb03 16aa | e103 182a 
  0x000000013ff2e0d0: e203 172a | e303 1a2a | ea03 19aa | f003 1baa | f4f6 ff17 | ea0b 40f9 | 6103 8012 | ea03 00f9 
  0x000000013ff2e0f0: eb2f 01a9 | f613 00f9 | f32b 00b9 

  0x000000013ff2e0fc: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [24]=Oop [32]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEventPrivate@53 (line 589)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e0fc: a123 d997 

  0x000000013ff2e100: ;   {other}
  0x000000013ff2e100: 1f20 03d5 | 1f3e 89f2 | 1f60 8ef2 | 2101 8012 | fd0b 40f9 | eb07 00f9 

  0x000000013ff2e118: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*putfield tail {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::getNextEventPrivate@55 (line 589)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e118: 9a23 d997 

  0x000000013ff2e11c: ;   {other}
  0x000000013ff2e11c: 1f20 03d5 | 9f41 89f2 | 1f80 8ef2 | f803 022a | f303 11aa | f403 0baa | f503 012a | e103 1caa 
  0x000000013ff2e13c: c900 0010 

  0x000000013ff2e140: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2e140: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e154: ;   {other}
  0x000000013ff2e154: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f103 13aa | eb03 14aa | e103 152a | e203 182a 
  0x000000013ff2e174: ecf6 ff17 | 6102 8012 | ec2b 00a9 | eb43 01a9 | e417 00f9 

  0x000000013ff2e188: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*invokestatic getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@62 (line 211)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e188: 7e23 d997 

  0x000000013ff2e18c: ;   {other}
  0x000000013ff2e18c: 1f20 03d5 | 9f4f 89f2 | 1fa0 8ef2 | fb03 04aa | e003 04aa | f703 032a | f403 11aa | fa03 10aa 
  0x000000013ff2e1ac: f303 0caa | f503 0baa | f903 0aaa | f603 012a | e183 0191 | f803 022a | e203 1caa | c900 0010 
  0x000000013ff2e1cc: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2e1cc: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e1e0: ;   {other}
  0x000000013ff2e1e0: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ec03 13aa | f103 14aa | eb03 15aa | e103 162a 
  0x000000013ff2e200: e203 182a | e303 172a | ea03 19aa | f003 1aaa | e403 1baa | 97f9 ff17 | e103 0daa | e283 0191 
  0x000000013ff2e220: ; ImmutableOopMap {rfp=Oop [0]=NarrowOop [8]=NarrowOop [12]=NarrowOop [16]=Oop [24]=Oop [32]=Oop [48]=Oop [56]=Oop [64]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerAccessor::platform@69 (line 212)
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call _complete_monitor_locking_Java}
  0x000000013ff2e220: 7899 dc97 

  0x000000013ff2e224: ;   {other}
  0x000000013ff2e224: 1f20 03d5 | 9f62 89f2 | 1fc0 8ef2 | ed1f 40f9 | c0f9 ff17 | e183 0191 | e203 1caa | e003 0daa 
  0x000000013ff2e244: c900 0010 

  0x000000013ff2e248: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2e248: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e25c: ;   {other}
  0x000000013ff2e25c: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | dbf9 ff17 | f403 04aa | e103 1caa | c900 0010 
  0x000000013ff2e27c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2e27c: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e290: ;   {other}
  0x000000013ff2e290: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | e403 14aa | e7f9 ff17 | ea0b 40f9 | 4117 8012 
  0x000000013ff2e2b0: ea03 00f9 | ee0f 00f9 

  0x000000013ff2e2b8: ; ImmutableOopMap {[0]=Oop [8]=Oop [24]=Oop }
                      ;*ifge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.ReentrantLock$NonfairSync::initialTryLock@36 (line 230)
                      ; - java.util.concurrent.locks.ReentrantLock$Sync::lock@1 (line 152)
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e2b8: 3223 d997 

  0x000000013ff2e2bc: ;   {other}
  0x000000013ff2e2bc: 1f20 03d5 | 9f75 89f2 | 1fe0 8ef2 | ea0b 40f9 | ea03 00f9 | ea17 40f9 | 0117 8012 | ea07 00f9 
  0x000000013ff2e2dc: efd7 01a9 | e217 00f9 

  0x000000013ff2e2e4: ; ImmutableOopMap {[0]=Oop [8]=Oop [24]=Oop [32]=Oop [40]=Oop }
                      ;*ifge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.ReentrantLock$NonfairSync::initialTryLock@36 (line 230)
                      ; - java.util.concurrent.locks.ReentrantLock$Sync::lock@1 (line 152)
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@4 (line 1270)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e2e4: 2723 d997 

  0x000000013ff2e2e8: ;   {other}
  0x000000013ff2e2e8: 1f20 03d5 | 1f7b 89f2 | 1f00 8ff2 | ea0b 40f9 | 4117 8012 | ea03 00f9 | ee0f 00f9 

  0x000000013ff2e304: ; ImmutableOopMap {[0]=Oop [8]=Oop [24]=Oop }
                      ;*ifge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.concurrent.locks.ReentrantLock$NonfairSync::initialTryLock@36 (line 230)
                      ; - java.util.concurrent.locks.ReentrantLock$Sync::lock@1 (line 152)
                      ; - java.util.concurrent.locks.ReentrantLock::lock@4 (line 322)
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e304: 1f23 d997 

  0x000000013ff2e308: ;   {other}
  0x000000013ff2e308: 1f20 03d5 | 1f7f 89f2 | 1f20 8ff2 | f403 0daa | e103 1caa | c900 0010 

  0x000000013ff2e320: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2e320: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e334: ;   {other}
  0x000000013ff2e334: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | ed03 14aa | 2ffa ff17 | fd03 18aa | eb87 00a9 
  0x000000013ff2e354: 2104 8012 

  0x000000013ff2e358: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@6 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e358: 0a23 d997 

  0x000000013ff2e35c: ;   {other}
  0x000000013ff2e35c: 1f20 03d5 | 9f89 89f2 | 1f40 8ff2 | 2104 8012 | ebb3 00a9 | ea0f 00f9 | ef2b 00b9 | f03b 03a9 
  0x000000013ff2e37c: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [48]=Oop [56]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@34 (line 172)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e37c: 0123 d997 

  0x000000013ff2e380: ;   {other}
  0x000000013ff2e380: 1f20 03d5 | 1f8e 89f2 | 1f60 8ff2 | fd0b 40f9 | ec87 00a9 | 2104 8012 

  0x000000013ff2e398: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@6 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e398: fa22 d997 

  0x000000013ff2e39c: ;   {other}
  0x000000013ff2e39c: 1f20 03d5 | 9f91 89f2 | 1f80 8ff2 | ea0b 40f9 | 2104 8012 | ea03 00f9 | eb5b 01a9 | ef23 00b9 
  0x000000013ff2e3bc: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop [24]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::uncacheEQItem@33 (line 502)
                      ; - java.awt.EventQueue::getNextEventPrivate@60 (line 591)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e3bc: f122 d997 

  0x000000013ff2e3c0: ;   {other}
  0x000000013ff2e3c0: 1f20 03d5 | 1f96 89f2 | 1fa0 8ff2 | 2101 8012 | fd0b 40f9 | eb07 00f9 

  0x000000013ff2e3d8: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*getfield head {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::getNextEventPrivate@12 (line 585)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e3d8: ea22 d997 

  0x000000013ff2e3dc: ;   {other}
  0x000000013ff2e3dc: 1f20 03d5 | 9f99 89f2 | 1fc0 8ff2 | 2101 8012 | fd03 18aa 

  0x000000013ff2e3f0: ; ImmutableOopMap {rfp=Oop }
                      ;*invokevirtual getNextEvent {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e3f0: e422 d997 

  0x000000013ff2e3f4: ;   {other}
  0x000000013ff2e3f4: 1f20 03d5 | 9f9c 89f2 | 1fe0 8ff2 | 2101 8012 | fd03 18aa | eb07 00f9 

  0x000000013ff2e40c: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual get {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e40c: dd22 d997 

  0x000000013ff2e410: ;   {other}
  0x000000013ff2e410: 1f20 03d5 | 1fa0 89f2 | 1f00 90f2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2e424: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokeinterface lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e424: d722 d997 

  0x000000013ff2e428: ;   {other}
  0x000000013ff2e428: 1f20 03d5 | 1fa3 89f2 | 1f20 90f2 | 2101 8012 | fd0b 40f9 | eb07 00f9 

  0x000000013ff2e440: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual getID {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::eventToCacheIndex@1 (line 518)
                      ; - java.awt.EventQueue::uncacheEQItem@4 (line 500)
                      ; - java.awt.EventQueue::getNextEventPrivate@60 (line 591)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e440: d022 d997 

  0x000000013ff2e444: ;   {other}
  0x000000013ff2e444: 1f20 03d5 | 9fa6 89f2 | 1f40 90f2 | 2101 8012 | fd0b 40f9 | eb2b 00a9 

  0x000000013ff2e45c: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@31 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e45c: c922 d997 

  0x000000013ff2e460: ;   {other}
  0x000000013ff2e460: 1f20 03d5 | 1faa 89f2 | 1f60 90f2 | 2101 8012 | ebb3 00a9 | ea0f 00f9 | ee17 00f9 

  0x000000013ff2e47c: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*invokevirtual size {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@13 (line 171)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e47c: c122 d997 

  0x000000013ff2e480: ;   {other}
  0x000000013ff2e480: 1f20 03d5 | 1fae 89f2 | 1f80 90f2 | 2101 8012 | fd03 0caa | ea2f 00a9 

  0x000000013ff2e498: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*invokeinterface isLoggable {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e498: ba22 d997 

  0x000000013ff2e49c: ;   {other}
  0x000000013ff2e49c: 1f20 03d5 | 9fb1 89f2 | 1fa0 90f2 | 2101 8012 | fd03 0caa | ea2f 00a9 

  0x000000013ff2e4b4: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*invokeinterface platform {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.logger.LazyLoggers$LazyLoggerWrapper::platformProxy@4 (line 337)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@1 (line 122)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e4b4: b322 d997 

  0x000000013ff2e4b8: ;   {other}
  0x000000013ff2e4b8: 1f20 03d5 | 1fb5 89f2 | 1fc0 90f2 | 6101 8012 | ebb3 00a9 | ea0f 00f9 | ef2b 00b9 | ee1b 00f9 
  0x000000013ff2e4d8: ; ImmutableOopMap {[8]=Oop [16]=Oop [24]=Oop [48]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::filterAndCheckEvent@34 (line 172)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@35 (line 196)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e4d8: aa22 d997 

  0x000000013ff2e4dc: ;   {other}
  0x000000013ff2e4dc: 1f20 03d5 | 9fb9 89f2 | 1fe0 90f2 | ea0f 40f9 | 6101 8012 | fd0b 40f9 | ea03 00f9 | e20f 00f9 
  0x000000013ff2e4fc: ; ImmutableOopMap {rfp=Oop [0]=Oop [24]=Oop [48]=Oop [64]=Oop }
                      ;*instanceof {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getAccessControlContextFrom@1 (line 764)
                      ; - java.awt.EventQueue::dispatchEvent@22 (line 746)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e4fc: a122 d997 

  0x000000013ff2e500: ;   {other}
  0x000000013ff2e500: 1f20 03d5 | 1fbe 89f2 | 1f00 91f2 | 2101 8012 

  0x000000013ff2e510: ; ImmutableOopMap {[16]=Oop }
                      ;*getfield fwDispatcher {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue$4::run@4 (line 727)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e510: 9c22 d997 

  0x000000013ff2e514: ;   {other}
  0x000000013ff2e514: 1f20 03d5 | 9fc0 89f2 | 1f20 91f2 | 2101 8012 

  0x000000013ff2e524: ; ImmutableOopMap {[16]=Oop }
                      ;*putfield isPosted {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.awt.EventQueue::dispatchEventImpl@2 (line 777)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e524: 9722 d997 

  0x000000013ff2e528: ;   {other}
  0x000000013ff2e528: 1f20 03d5 | 1fc3 89f2 | 1f40 91f2 | fd0b 40f9 | 2101 8012 | ef57 01a9 

  0x000000013ff2e540: ; ImmutableOopMap {rfp=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*invokeinterface lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@4 (line 1270)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e540: 9022 d997 

  0x000000013ff2e544: ;   {other}
  0x000000013ff2e544: 1f20 03d5 | 9fc6 89f2 | 1f60 91f2 | 2101 8012 | f507 00f9 

  0x000000013ff2e558: ; ImmutableOopMap {[8]=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@168 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e558: 8a22 d997 

  0x000000013ff2e55c: ;   {other}
  0x000000013ff2e55c: 1f20 03d5 | 9fc9 89f2 | 1f80 91f2 | 2101 8012 | fd03 18aa | ebb3 00a9 | ee13 00f9 

  0x000000013ff2e578: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop }
                      ;*invokeinterface get {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e578: 8222 d997 

  0x000000013ff2e57c: ;   {other}
  0x000000013ff2e57c: 1f20 03d5 | 9fcd 89f2 | 1fa0 91f2 | 4117 8012 | fd03 18aa | ebb3 00a9 | eabb 02a9 

  0x000000013ff2e598: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop [48]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) sun.awt.AppContext::get@13 (line 615)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e598: 7a22 d997 

  0x000000013ff2e59c: ;   {other}
  0x000000013ff2e59c: 1f20 03d5 | 9fd1 89f2 | 1fc0 91f2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2e5b0: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokeinterface await {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@62 (line 573)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e5b0: 7422 d997 

  0x000000013ff2e5b4: ;   {other}
  0x000000013ff2e5b4: 1f20 03d5 | 9fd4 89f2 | 1fe0 91f2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2e5c8: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@71 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e5c8: 6e22 d997 

  0x000000013ff2e5cc: ;   {other}
  0x000000013ff2e5cc: 1f20 03d5 | 9fd7 89f2 | 1f00 92f2 | 2101 8012 | fd0b 40f9 | ec07 00f9 

  0x000000013ff2e5e4: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokevirtual get {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e5e4: 6722 d997 

  0x000000013ff2e5e8: ;   {other}
  0x000000013ff2e5e8: 1f20 03d5 | 1fdb 89f2 | 1f20 92f2 | 2101 8012 | fd0b 40f9 

  0x000000013ff2e5fc: ; ImmutableOopMap {rfp=Oop [8]=Oop }
                      ;*invokeinterface lock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@11 (line 565)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e5fc: 6122 d997 

  0x000000013ff2e600: ;   {other}
  0x000000013ff2e600: 1f20 03d5 | 1fde 89f2 | 1f40 92f2 | fd0b 40f9 | 2101 8012 | ecbf 00a9 | f113 00f9 

  0x000000013ff2e61c: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [32]=Oop }
                      ;*invokeinterface get {reexecute=0 rethrow=0 return_oop=0}
                      ; - sun.awt.AppContext::get@36 (line 619)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e61c: 5922 d997 

  0x000000013ff2e620: ;   {other}
  0x000000013ff2e620: 1f20 03d5 | 1fe2 89f2 | 1f60 92f2 | fd0b 40f9 | 4117 8012 | ecbf 00a9 | eac7 02a9 

  0x000000013ff2e63c: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop [48]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) sun.awt.AppContext::get@13 (line 615)
                      ; - sun.awt.SunToolkit::flushPendingEvents@3 (line 555)
                      ; - java.awt.EventQueue::getNextEvent@4 (line 564)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e63c: 5122 d997 

  0x000000013ff2e640: ;   {other}
  0x000000013ff2e640: 1f20 03d5 | 1fe6 89f2 | 1f80 92f2 | 2101 8012 | fd03 0caa | ea2f 00a9 

  0x000000013ff2e658: ; ImmutableOopMap {rfp=Oop [0]=Oop [8]=Oop }
                      ;*invokevirtual intValue {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.logging.Logger::isLoggable@9 (line 2037)
                      ; - sun.util.logging.internal.LoggingProviderImpl$JULWrapper::isLoggable@8 (line 215)
                      ; - jdk.internal.logger.AbstractLoggerWrapper::isLoggable@20 (line 124)
                      ; - sun.util.logging.PlatformLogger::isLoggable@17 (line 340)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@57 (line 203)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e658: 4a22 d997 

  0x000000013ff2e65c: ;   {other}
  0x000000013ff2e65c: 1f20 03d5 | 9fe9 89f2 | 1fa0 92f2 | fd0b 40f9 | 6101 8012 | efd7 00a9 

  0x000000013ff2e674: ; ImmutableOopMap {rfp=Oop [8]=Oop [16]=Oop [40]=Oop }
                      ;*instanceof {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::dispatchEventImpl@30 (line 782)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e674: 4322 d997 

  0x000000013ff2e678: ;   {other}
  0x000000013ff2e678: 1f20 03d5 | 1fed 89f2 | 1fc0 92f2 | 2101 8012 | ea03 00f9 

  0x000000013ff2e68c: ; ImmutableOopMap {[0]=Oop [16]=Oop }
                      ;*invokeinterface setCanAccessSystemClipboard {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.AWTEvent::dispatched@15 (line 597)
                      ; - java.awt.EventQueue::dispatchEventImpl@45 (line 784)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e68c: 3d22 d997 

  0x000000013ff2e690: ;   {other}
  0x000000013ff2e690: 1f20 03d5 | 1ff0 89f2 | 1fe0 92f2 | 6101 8012 | fd0b 40f9 | eb07 00f9 | f60f 00f9 | ef23 00b9 
  0x000000013ff2e6b0: ; ImmutableOopMap {rfp=Oop [8]=Oop [24]=Oop }
                      ;*instanceof {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::uncacheEQItem@20 (line 501)
                      ; - java.awt.EventQueue::getNextEventPrivate@60 (line 591)
                      ; - java.awt.EventQueue::getNextEvent@17 (line 567)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e6b0: 3422 d997 

  0x000000013ff2e6b4: ;   {other}
  0x000000013ff2e6b4: 1f20 03d5 | 9ff4 89f2 | 1f00 93f2 | 2101 8012 | ec07 00f9 

  0x000000013ff2e6c8: ; ImmutableOopMap {[8]=Oop [16]=Oop [40]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::dispatch@11 (line 308)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {runtime_call UncommonTrapBlob}
  0x000000013ff2e6c8: 2e22 d997 

  0x000000013ff2e6cc: ;   {other}
  0x000000013ff2e6cc: 1f20 03d5 | 9ff7 89f2 | 1f20 93f2 | e103 00aa | fd7b 47a9 | ff03 0291 | 886b 42f9 | ff63 28eb 
  0x000000013ff2e6ec: 2302 0054 | fd7b bfa9 | fd03 0091 

  0x000000013ff2e6f8: ;   {external_word}
  0x000000013ff2e6f8: 888b 85d2 | 28d4 a0f2 | 2800 c0f2 | e003 1caa | 0001 3fd6 | bf03 0091 | fd7b c1a8 

  0x000000013ff2e714: ;   {runtime_call delayed StackOverflowError throw_exception}
  0x000000013ff2e714: 0840 8ed2 | 68ea a7f2 | 2800 c0f2 | 0001 1fd6 | c1d5 bbd4 | 6792 cd06 | 0100 0000 

  0x000000013ff2e730: ;   {runtime_call _rethrow_Java}
  0x000000013ff2e730: 348a dc17 | e103 1caa | c900 0010 

  0x000000013ff2e73c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2e73c: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e750: ;   {other}
  0x000000013ff2e750: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 1d00 0014 | e103 1caa | c900 0010 

  0x000000013ff2e76c: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_pre_entry(oopDesc*, JavaThread*)}
  0x000000013ff2e76c: 8812 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e780: ;   {other}
  0x000000013ff2e780: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 2200 0014 | ea03 00aa | 0200 0014 | ea03 00aa 
  0x000000013ff2e7a0: f303 0aaa | 4a09 40b9 | 0a17 c0f2 | 4a21 40f9 

  0x000000013ff2e7b0: ;   {metadata('java/lang/Exception')}
  0x000000013ff2e7b0: 0bd5 9ad2 | 0b00 a0f2 | 0b17 c0f2 | 74fe 43d3 | 5f01 0beb | 4107 0054 | 8a03 c139 | aa08 0035 
  0x000000013ff2e7d0: f503 40f9 | ea03 13aa | eb03 15aa | ec03 0aaa | 8c01 0bca | 4dfd 43d3 | 8afd 55d3 | ad3a 00b9 
  0x000000013ff2e7f0: 0a01 00b4 | 6afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0a00 c039 | 5f09 0071 | 2108 0054 
  0x000000013ff2e810: 8b03 c139 | 0b05 0035 | ea03 15aa | eb03 13aa | 6b01 0aca | 6bfd 55d3 | b43e 00b9 | 6b23 feb4 
  0x000000013ff2e830: 4afd 49d3 | ab3f a0d2 | 2b00 c0f2 | 6001 0a8b | 0b00 c039 | 7f09 0071 | 8022 fe54 | 8a27 40f9 
  0x000000013ff2e850: 8b2f 40f9 | bf3b 03d5 | 0d00 c039 | ed21 fe34 | 1f00 0039 | aa01 00b5 | e103 1caa | c900 0010 
  0x000000013ff2e870: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2e870: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e884: ;   {other}
  0x000000013ff2e884: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 01f1 ff17 | 4c21 00d1 | 6a01 0a8b | 4081 1ff8 
  0x000000013ff2e8a4: 8c27 00f9 | fcf0 ff17 | f503 40f9 | d8ff ff17 | aa3e 40b9 | 40f1 7dd3 | e0fa ffb4 | 8a17 40f9 
  0x000000013ff2e8c4: 0af5 ffb4 | 8b1f 40f9 | 4c21 00d1 | 6a01 0a8b | 4081 1ff8 | 8c17 00f9 | cfff ff17 | f503 40f9 
  0x000000013ff2e8e4: aa3a 40b9 | 40f1 7dd3 | 40f7 ffb4 | 8a17 40f9 | 0af2 ffb4 | 8b1f 40f9 | 4c21 00d1 | 6a01 0a8b 
  0x000000013ff2e904: 4081 1ff8 | 8c17 00f9 | b2ff ff17 | 8a27 40f9 | 8b2f 40f9 | bf3b 03d5 | 0c00 c039 | 8cf7 ff34 
  0x000000013ff2e924: 1f00 0039 | aa01 00b5 | e103 1caa | c900 0010 

  0x000000013ff2e934: ;   {runtime_call G1BarrierSetRuntime::write_ref_field_post_entry(unsigned char volatile*, JavaThread*)}
  0x000000013ff2e934: 8817 9dd2 | a8c9 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2e948: ;   {other}
  0x000000013ff2e948: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | aeff ff17 | 4c21 00d1 | 6a01 0a8b | 4081 1ff8 
  0x000000013ff2e968: 8c27 00f9 | a9ff ff17 | 3f01 0014 | 3e01 0014 | 3101 0014 | 3c01 0014 | 0b08 40b9 | ec03 00aa 
  0x000000013ff2e988: 0b17 c0f2 | 1e01 0014 | 0b08 40b9 | ea03 0b2a | 0a17 c0f2 | 3601 0014 | 3301 0014 | 0b08 40b9 
  0x000000013ff2e9a8: ec03 00aa | 0b17 c0f2 | 1501 0014 | 2e01 0014 | 0a08 40b9 | ec03 00aa | eb03 0a2a | 0b17 c0f2 
  0x000000013ff2e9c8: 0f01 0014 | f303 00aa | 1300 0014 | ea07 40f9 | 4a2d 40b9 | e013 00f9 | 41f1 7dd3 | e9ff 9fd2 
  0x000000013ff2e9e8: e9ff bff2 | e9ff dff2 

  0x000000013ff2e9f0: ; ImmutableOopMap {[16]=Oop [24]=Oop [32]=Oop [40]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::setCurrentEventAndMostRecentTimeImpl@182 (line 1305)
                      ; - java.awt.EventQueue::dispatchEventImpl@14 (line 780)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {virtual_call}
  0x000000013ff2e9f0: a406 d997 

  0x000000013ff2e9f4: ;   {other}
  0x000000013ff2e9f4: 1f20 03d5 | 9f5c 8af2 | 1f40 93f2 | e013 40f9 | 1a01 0014 | 0b08 40b9 | ea03 0b2a | 0a17 c0f2 
  0x000000013ff2ea14: 1801 0014 | f303 00aa | ed83 0191 | ec1f 40f9 | ab01 40f9 | 7f01 1feb | 8002 0054 | 8a01 40f9 
  0x000000013ff2ea34: aa00 0837 | ea03 0daa | 8bfd aac8 | 5f01 0deb | 0e00 0014 | 4a09 00d1 | 4b45 40f9 | ab00 00b4 
  0x000000013ff2ea54: 6b05 00d1 | 4b45 00f9 | 7f01 0beb | 0700 0014 | 482d 49a9 | 0801 0baa | 1f01 1feb | 6800 00b5 
  0x000000013ff2ea74: 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 | 8120 0054 | 6a0a 40b9 
  0x000000013ff2ea94: ec03 13aa | 0a17 c0f2 | 2800 0014 | 0b08 40b9 | ec03 00aa | ea03 0b2a | 0a17 c0f2 | 2300 0014 
  0x000000013ff2eab4: ed83 0191 | ec1f 40f9 | aa01 40f9 | 5f01 1feb | 8002 0054 | 8b01 40f9 | ab00 0837 | eb03 0daa 
  0x000000013ff2ead4: 8afd abc8 | 7f01 0deb | 0e00 0014 | 6b09 00d1 | 6a45 40f9 | aa00 00b4 | 4a05 00d1 | 6a45 00f9 
  0x000000013ff2eaf4: 5f01 0aeb | 0700 0014 | 6829 49a9 | 0801 0aaa | 1f01 1feb | 6800 00b5 | 6b01 0191 | 7ffd 9fc8 
  0x000000013ff2eb14: 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 | f303 00aa | 611d 0054 | ec03 13aa | 8b09 40b9 
  0x000000013ff2eb34: ea03 0b2a | 0a17 c0f2 | 4a25 40f9 

  0x000000013ff2eb40: ;   {metadata('java/lang/InterruptedException')}
  0x000000013ff2eb40: 0b66 87d2 | eb21 a0f2 | 0b17 c0f2 | 5f01 0beb | e019 0054 | e203 0caa | b200 0014 | f303 00aa 
  0x000000013ff2eb60: 2400 0014 | f303 00aa | 5200 0014 | ea03 00aa | 0200 0014 | ea03 00aa | 4b09 40b9 | ec03 0aaa 
  0x000000013ff2eb80: 0b17 c0f2 | a000 0014 | b900 0014 | b800 0014 | b700 0014 | ea03 00aa | 0200 0014 | ea03 00aa 
  0x000000013ff2eba0: 4b09 40b9 | ec03 0aaa | 0b17 c0f2 | 9600 0014 | af00 0014 | 0a08 40b9 | 0a17 c0f2 | ae00 0014 
  0x000000013ff2ebc0: 0100 0014 | 0b08 40b9 | ea03 0b2a | 0a17 c0f2 | a900 0014 | a600 0014 | a500 0014 | f303 00aa 
  0x000000013ff2ebe0: 0400 0014 | f303 00aa | 0200 0014 | f303 00aa | ed83 0191 | ec27 40f9 | aa01 40f9 | 5f01 1feb 
  0x000000013ff2ec00: 8002 0054 | 8b01 40f9 | ab00 0837 | eb03 0daa | 8afd abc8 | 7f01 0deb | 0e00 0014 | 6b09 00d1 
  0x000000013ff2ec20: 6a45 40f9 | aa00 00b4 | 4a05 00d1 | 6a45 00f9 | 5f01 0aeb | 0700 0014 | 6829 49a9 | 0801 0aaa 
  0x000000013ff2ec40: 1f01 1feb | 6800 00b5 | 6b01 0191 | 7ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 
  0x000000013ff2ec60: 0008 0054 | e183 0191 | e203 1caa | e027 40f9 | c900 0010 

  0x000000013ff2ec74: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2ec74: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2ec88: ;   {other}
  0x000000013ff2ec88: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 3200 0014 | f303 00aa | 0400 0014 | f303 00aa 
  0x000000013ff2eca8: 0200 0014 | f303 00aa | ed83 0191 | ec17 40f9 | ab01 40f9 | 7f01 1feb | 8002 0054 | 8a01 40f9 
  0x000000013ff2ecc8: aa00 0837 | ea03 0daa | 8bfd aac8 | 5f01 0deb | 0e00 0014 | 4a09 00d1 | 4b45 40f9 | ab00 00b4 
  0x000000013ff2ece8: 6b05 00d1 | 4b45 00f9 | 7f01 0beb | 0700 0014 | 482d 49a9 | 0801 0baa | 1f01 1feb | 6800 00b5 
  0x000000013ff2ed08: 4a01 0191 | 5ffd 9fc8 | 8100 0054 | 88a7 42f9 | 0805 00d1 | 88a7 02f9 | a006 0054 | e183 0191 
  0x000000013ff2ed28: e203 1caa | e017 40f9 | c900 0010 

  0x000000013ff2ed34: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2ed34: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2ed48: ;   {other}
  0x000000013ff2ed48: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 2700 0014 | f303 00aa | 6a0a 40b9 | ec03 13aa 
  0x000000013ff2ed68: eb03 0a2a | 0b17 c0f2 | 2500 0014 | 0b08 40b9 | ea03 0b2a | 0a17 c0f2 | 3d00 0014 | e203 00aa 
  0x000000013ff2ed88: 2600 0014 | fd03 00aa | 0600 0014 | fd03 00aa | 0400 0014 | fd03 00aa | 0200 0014 | fd03 00aa 
  0x000000013ff2eda8: ea07 40f9 | 4b2d 40b9 | ac0b 40b9 | 61f1 7dd3 | ea03 00f9 | ec0b 00b9 | e9ff 9fd2 | e9ff bff2 
  0x000000013ff2edc8: e9ff dff2 

  0x000000013ff2edcc: ; ImmutableOopMap {rfp=Oop [0]=Oop [16]=Oop }
                      ;*invokeinterface unlock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventQueue::getNextEvent@84 (line 575)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@20 (line 194)
                      ;   {virtual_call}
  0x000000013ff2edcc: ad05 d997 

  0x000000013ff2edd0: ;   {other}
  0x000000013ff2edd0: 1f20 03d5 | 1fd8 8af2 | 1f60 93f2 | ea0b 40b9 | eb03 0a2a | 0b17 c0f2 | ec03 1daa | 0600 0014 
  0x000000013ff2edf0: f303 00aa | 6a0a 40b9 | ec03 13aa | eb03 0a2a | 0b17 c0f2 | 6a25 40f9 

  0x000000013ff2ee08: ;   {metadata('java/lang/InterruptedException')}
  0x000000013ff2ee08: 0b66 87d2 | eb21 a0f2 | 0b17 c0f2 | 5f01 0beb | a003 0054 | e203 0caa | e10b 40f9 

  0x000000013ff2ee24: ; ImmutableOopMap {}
                      ;*invokevirtual processException {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@102 (line 214)
                      ;   {optimized virtual_call}
  0x000000013ff2ee24: d704 d997 

  0x000000013ff2ee28: ;   {other}
  0x000000013ff2ee28: 1f20 03d5 | 1fe3 8af2 | 1f80 93f2 | a4ef ff17 | 0100 0014 | 0a08 40b9 | 0a17 c0f2 | 0c00 0014 
  0x000000013ff2ee48: 0900 0014 | e007 00f9 | e103 40f9 | e203 0032 

  0x000000013ff2ee58: ; ImmutableOopMap {[8]=Oop [16]=Oop [40]=Oop }
                      ;*invokevirtual finishedDispatching {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.awt.event.InvocationEvent::dispatch@63 (line 321)
                      ; - java.awt.EventQueue::dispatchEventImpl@21 (line 781)
                      ; - java.awt.EventQueue$4::run@32 (line 728)
                      ; - java.awt.EventQueue$4::run@1 (line 722)
                      ; - java.security.AccessController::executePrivileged@29 (line 778)
                      ; - java.security.AccessController::doPrivileged@13 (line 400)
                      ; - java.security.ProtectionDomain$JavaSecurityAccessImpl::doIntersectionPrivilege@18 (line 87)
                      ; - java.awt.EventQueue::dispatchEvent@46 (line 750)
                      ; - java.awt.EventDispatchThread::pumpOneEventForFilters@81 (line 207)
                      ;   {optimized virtual_call}
  0x000000013ff2ee58: ca04 d997 

  0x000000013ff2ee5c: ;   {other}
  0x000000013ff2ee5c: 1f20 03d5 | 9fe9 8af2 | 1fa0 93f2 | e007 40f9 | 0a08 40b9 | 0a17 c0f2 | 4a25 40f9 

  0x000000013ff2ee78: ;   {metadata('java/lang/InterruptedException')}
  0x000000013ff2ee78: 0b66 87d2 | eb21 a0f2 | 0b17 c0f2 | 5f01 0beb | e1f7 ff54 | ea0b 40f9 | 4ad1 0191 | 5ffd 9f08 
  0x000000013ff2ee98: 8bef ff17 | e183 0191 | e203 1caa | e003 0caa | c900 0010 

  0x000000013ff2eeac: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2eeac: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2eec0: ;   {other}
  0x000000013ff2eec0: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | f0fe ff17 | e183 0191 | e203 1caa | e003 0caa 
  0x000000013ff2eee0: c900 0010 

  0x000000013ff2eee4: ;   {runtime_call SharedRuntime::complete_monitor_unlocking_C(oopDesc*, BasicLock*, JavaThread*)}
  0x000000013ff2eee4: 88d1 81d2 | 28d4 a0f2 | 2800 c0f2 | ff27 bfa9 | 0001 3fd6 

  0x000000013ff2eef8: ;   {other}
  0x000000013ff2eef8: 1f20 03d5 | 1f00 80f2 | 1f00 80f2 | ff43 0091 | 09ff ff17 

  0x000000013ff2ef0c: ;   {internal_word}
  0x000000013ff2ef0c: 68f0 fd10 | 8833 02f9 

  0x000000013ff2ef14: ;   {runtime_call SafepointBlob}
  0x000000013ff2ef14: 7b1e d917 | 0830 93d2 | e8ea a7f2 | 2800 c0f2 | 0001 3fd6 | 23ec ff17 

  0x000000013ff2ef2c: ;   {other}
  0x000000013ff2ef2c: 2100 0000 
[Stub Code]
  0x000000013ff2ef30: ;   {no_reloc}
  0x000000013ff2ef30: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2ef40: ;   {trampoline_stub}
  0x000000013ff2ef40: 4800 0058 | 0001 1fd6 | 5039 4440 | 0100 0000 

  0x000000013ff2ef50: ;   {static_stub}
  0x000000013ff2ef50: df3f 03d5 

  0x000000013ff2ef54: ;   {metadata(nullptr)}
  0x000000013ff2ef54: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2ef70: ;   {trampoline_stub}
  0x000000013ff2ef70: 4800 0058 | 0001 1fd6 | d018 c03f | 0100 0000 

  0x000000013ff2ef80: ;   {trampoline_stub}
  0x000000013ff2ef80: 4800 0058 | 0001 1fd6 | d015 4540 | 0100 0000 

  0x000000013ff2ef90: ;   {trampoline_stub}
  0x000000013ff2ef90: 4800 0058 | 0001 1fd6 | 0016 0040 | 0100 0000 

  0x000000013ff2efa0: ;   {trampoline_stub}
  0x000000013ff2efa0: 4800 0058 | 0001 1fd6 | 507d c53f | 0100 0000 

  0x000000013ff2efb0: ;   {trampoline_stub}
  0x000000013ff2efb0: 4800 0058 | 0001 1fd6 | 0830 653f | 0100 0000 

  0x000000013ff2efc0: ;   {trampoline_stub}
  0x000000013ff2efc0: 4800 0058 | 0001 1fd6 | d0f7 f93f | 0100 0000 

  0x000000013ff2efd0: ;   {trampoline_stub}
  0x000000013ff2efd0: 4800 0058 | 0001 1fd6 | c047 4640 | 0100 0000 

  0x000000013ff2efe0: ;   {trampoline_stub}
  0x000000013ff2efe0: 4800 0058 | 0001 1fd6 | 10bd 4a40 | 0100 0000 

  0x000000013ff2eff0: ;   {static_stub}
  0x000000013ff2eff0: df3f 03d5 

  0x000000013ff2eff4: ;   {metadata(nullptr)}
  0x000000013ff2eff4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f010: ;   {trampoline_stub}
  0x000000013ff2f010: 4800 0058 | 0001 1fd6 | 1001 6940 | 0100 0000 

  0x000000013ff2f020: ;   {trampoline_stub}
  0x000000013ff2f020: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f030: ;   {trampoline_stub}
  0x000000013ff2f030: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f040: ;   {trampoline_stub}
  0x000000013ff2f040: 4800 0058 | 0001 1fd6 | 5039 4440 | 0100 0000 

  0x000000013ff2f050: ;   {static_stub}
  0x000000013ff2f050: df3f 03d5 

  0x000000013ff2f054: ;   {metadata(nullptr)}
  0x000000013ff2f054: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f070: ;   {trampoline_stub}
  0x000000013ff2f070: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f080: ;   {trampoline_stub}
  0x000000013ff2f080: 4800 0058 | 0001 1fd6 | 8004 573f | 0100 0000 

  0x000000013ff2f090: ;   {trampoline_stub}
  0x000000013ff2f090: 4800 0058 | 0001 1fd6 | 8007 573f | 0100 0000 

  0x000000013ff2f0a0: ;   {trampoline_stub}
  0x000000013ff2f0a0: 4800 0058 | 0001 1fd6 | 8007 573f | 0100 0000 

  0x000000013ff2f0b0: ;   {trampoline_stub}
  0x000000013ff2f0b0: 4800 0058 | 0001 1fd6 | 9026 4440 | 0100 0000 

  0x000000013ff2f0c0: ;   {trampoline_stub}
  0x000000013ff2f0c0: 4800 0058 | 0001 1fd6 | c0a4 4640 | 0100 0000 

  0x000000013ff2f0d0: ;   {trampoline_stub}
  0x000000013ff2f0d0: 4800 0058 | 0001 1fd6 | 9026 4440 | 0100 0000 

  0x000000013ff2f0e0: ;   {trampoline_stub}
  0x000000013ff2f0e0: 4800 0058 | 0001 1fd6 | c0a4 4640 | 0100 0000 

  0x000000013ff2f0f0: ;   {trampoline_stub}
  0x000000013ff2f0f0: 4800 0058 | 0001 1fd6 | 8007 573f | 0100 0000 

  0x000000013ff2f100: ;   {trampoline_stub}
  0x000000013ff2f100: 4800 0058 | 0001 1fd6 | 9026 4440 | 0100 0000 

  0x000000013ff2f110: ;   {trampoline_stub}
  0x000000013ff2f110: 4800 0058 | 0001 1fd6 | c0a4 4640 | 0100 0000 

  0x000000013ff2f120: ;   {trampoline_stub}
  0x000000013ff2f120: 4800 0058 | 0001 1fd6 | 0016 0040 | 0100 0000 

  0x000000013ff2f130: ;   {trampoline_stub}
  0x000000013ff2f130: 4800 0058 | 0001 1fd6 | 507d c53f | 0100 0000 

  0x000000013ff2f140: ;   {trampoline_stub}
  0x000000013ff2f140: 4800 0058 | 0001 1fd6 | 009f e439 | 0100 0000 

  0x000000013ff2f150: ;   {static_stub}
  0x000000013ff2f150: df3f 03d5 

  0x000000013ff2f154: ;   {metadata({method} {0x000000012a969ad8} 'run' '()Ljava/lang/Void;' in 'java/awt/EventQueue$5')}
  0x000000013ff2f154: 0c5b 93d2 | cc52 a5f2 | 2c00 c0f2 | 886a 89d2 | e8ea a7f2 | 2800 c0f2 | 0001 1fd6 

  0x000000013ff2f170: ;   {trampoline_stub}
  0x000000013ff2f170: 4800 0058 | 0001 1fd6 | 0830 653f | 0100 0000 

  0x000000013ff2f180: ;   {trampoline_stub}
  0x000000013ff2f180: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f190: ;   {trampoline_stub}
  0x000000013ff2f190: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f1a0: ;   {trampoline_stub}
  0x000000013ff2f1a0: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f1b0: ;   {trampoline_stub}
  0x000000013ff2f1b0: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f1c0: ;   {trampoline_stub}
  0x000000013ff2f1c0: 4800 0058 | 0001 1fd6 | 8007 573f | 0100 0000 

  0x000000013ff2f1d0: ;   {trampoline_stub}
  0x000000013ff2f1d0: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f1e0: ;   {trampoline_stub}
  0x000000013ff2f1e0: 4800 0058 | 0001 1fd6 | 8007 573f | 0100 0000 

  0x000000013ff2f1f0: ;   {trampoline_stub}
  0x000000013ff2f1f0: 4800 0058 | 0001 1fd6 | 8007 573f | 0100 0000 

  0x000000013ff2f200: ;   {trampoline_stub}
  0x000000013ff2f200: 4800 0058 | 0001 1fd6 | 00b0 c239 | 0100 0000 

  0x000000013ff2f210: ;   {trampoline_stub}
  0x000000013ff2f210: 4800 0058 | 0001 1fd6 | 00b0 c239 | 0100 0000 

  0x000000013ff2f220: ;   {trampoline_stub}
  0x000000013ff2f220: 4800 0058 | 0001 1fd6 | 00b0 c239 | 0100 0000 

  0x000000013ff2f230: ;   {trampoline_stub}
  0x000000013ff2f230: 4800 0058 | 0001 1fd6 | 8004 573f | 0100 0000 

  0x000000013ff2f240: ;   {trampoline_stub}
  0x000000013ff2f240: 4800 0058 | 0001 1fd6 | 8004 573f | 0100 0000 

  0x000000013ff2f250: ;   {trampoline_stub}
  0x000000013ff2f250: 4800 0058 | 0001 1fd6 | 8004 573f | 0100 0000 

  0x000000013ff2f260: ;   {trampoline_stub}
  0x000000013ff2f260: 4800 0058 | 0001 1fd6 | 8004 573f | 0100 0000 

  0x000000013ff2f270: ;   {trampoline_stub}
  0x000000013ff2f270: 4800 0058 | 0001 1fd6 | 8004 573f | 0100 0000 

  0x000000013ff2f280: ;   {trampoline_stub}
  0x000000013ff2f280: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f290: ;   {trampoline_stub}
  0x000000013ff2f290: 4800 0058 | 0001 1fd6 | 8001 573f | 0100 0000 

  0x000000013ff2f2a0: ;   {static_stub}
  0x000000013ff2f2a0: df3f 03d5 

  0x000000013ff2f2a4: ;   {metadata(nullptr)}
  0x000000013ff2f2a4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f2c0: ;   {static_stub}
  0x000000013ff2f2c0: df3f 03d5 

  0x000000013ff2f2c4: ;   {metadata(nullptr)}
  0x000000013ff2f2c4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f2e0: ;   {static_stub}
  0x000000013ff2f2e0: df3f 03d5 

  0x000000013ff2f2e4: ;   {metadata(nullptr)}
  0x000000013ff2f2e4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f300: ;   {static_stub}
  0x000000013ff2f300: df3f 03d5 

  0x000000013ff2f304: ;   {metadata(nullptr)}
  0x000000013ff2f304: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f320: ;   {static_stub}
  0x000000013ff2f320: df3f 03d5 

  0x000000013ff2f324: ;   {metadata(nullptr)}
  0x000000013ff2f324: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f340: ;   {static_stub}
  0x000000013ff2f340: df3f 03d5 

  0x000000013ff2f344: ;   {metadata(nullptr)}
  0x000000013ff2f344: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f360: ;   {static_stub}
  0x000000013ff2f360: df3f 03d5 

  0x000000013ff2f364: ;   {metadata(nullptr)}
  0x000000013ff2f364: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f380: ;   {static_stub}
  0x000000013ff2f380: df3f 03d5 

  0x000000013ff2f384: ;   {metadata(nullptr)}
  0x000000013ff2f384: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f3a0: ;   {static_stub}
  0x000000013ff2f3a0: df3f 03d5 

  0x000000013ff2f3a4: ;   {metadata(nullptr)}
  0x000000013ff2f3a4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f3c0: ;   {static_stub}
  0x000000013ff2f3c0: df3f 03d5 

  0x000000013ff2f3c4: ;   {metadata(nullptr)}
  0x000000013ff2f3c4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f3e0: ;   {static_stub}
  0x000000013ff2f3e0: df3f 03d5 

  0x000000013ff2f3e4: ;   {metadata(nullptr)}
  0x000000013ff2f3e4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f400: ;   {static_stub}
  0x000000013ff2f400: df3f 03d5 

  0x000000013ff2f404: ;   {metadata(nullptr)}
  0x000000013ff2f404: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f420: ;   {static_stub}
  0x000000013ff2f420: df3f 03d5 

  0x000000013ff2f424: ;   {metadata(nullptr)}
  0x000000013ff2f424: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f440: ;   {static_stub}
  0x000000013ff2f440: df3f 03d5 

  0x000000013ff2f444: ;   {metadata(nullptr)}
  0x000000013ff2f444: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f460: ;   {static_stub}
  0x000000013ff2f460: df3f 03d5 

  0x000000013ff2f464: ;   {metadata(nullptr)}
  0x000000013ff2f464: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f480: ;   {static_stub}
  0x000000013ff2f480: df3f 03d5 

  0x000000013ff2f484: ;   {metadata(nullptr)}
  0x000000013ff2f484: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f4a0: ;   {static_stub}
  0x000000013ff2f4a0: df3f 03d5 

  0x000000013ff2f4a4: ;   {metadata(nullptr)}
  0x000000013ff2f4a4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f4c0: ;   {static_stub}
  0x000000013ff2f4c0: df3f 03d5 

  0x000000013ff2f4c4: ;   {metadata(nullptr)}
  0x000000013ff2f4c4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f4e0: ;   {static_stub}
  0x000000013ff2f4e0: df3f 03d5 

  0x000000013ff2f4e4: ;   {metadata(nullptr)}
  0x000000013ff2f4e4: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f500: ;   {static_stub}
  0x000000013ff2f500: df3f 03d5 

  0x000000013ff2f504: ;   {metadata(nullptr)}
  0x000000013ff2f504: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 

  0x000000013ff2f520: ;   {static_stub}
  0x000000013ff2f520: df3f 03d5 

  0x000000013ff2f524: ;   {metadata(nullptr)}
  0x000000013ff2f524: 0c00 80d2 | 0c00 a0f2 | 0c00 c0f2 | 0800 80d2 | 0800 a0f2 | 0800 c0f2 | 0001 1fd6 
[Exception Handler]
  0x000000013ff2f540: ;   {runtime_call ExceptionBlob}
  0x000000013ff2f540: 7070 dc17 
[Deopt Handler Code]
  0x000000013ff2f544: 1e00 0010 

  0x000000013ff2f548: ;   {runtime_call DeoptimizationBlob}
  0x000000013ff2f548: be1d d917 | 0000 0000 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00006000026bc5e0, length=85, elements={
0x000000011d808800, 0x000000012d823a00, 0x000000012d820000, 0x000000012d820800,
0x000000011d8af800, 0x000000011d8b0000, 0x000000011d8b0800, 0x000000011d8b9000,
0x000000012e00c000, 0x000000012d95c400, 0x000000012d95cc00, 0x000000012e7df000,
0x000000012d96f000, 0x000000011e009000, 0x000000011dc11600, 0x000000011dc11e00,
0x000000011dc17e00, 0x000000012d97e200, 0x000000011dcdb000, 0x000000012db06800,
0x000000012eb97c00, 0x000000012dac7e00, 0x000000012eb67600, 0x000000012db39200,
0x000000012db3a000, 0x000000011e223800, 0x000000011e224000, 0x000000010f809c00,
0x000000010f8bda00, 0x000000011c816600, 0x000000012ec24a00, 0x000000012ec27200,
0x000000012ec27a00, 0x000000010f8bbe00, 0x000000012ec28e00, 0x000000011f05c800,
0x000000011dda5000, 0x000000012ec35200, 0x000000012db53c00, 0x000000011c81c400,
0x000000012db54400, 0x000000012db6c800, 0x000000011dda6000, 0x000000011ddbfe00,
0x000000010f87e000, 0x000000012ec56000, 0x000000010f8c0400, 0x000000011e26be00,
0x000000011df88800, 0x000000012ecb9400, 0x000000011e8b4800, 0x000000011e8b1800,
0x000000012e00d000, 0x000000015e147800, 0x000000012edc9a00, 0x000000011e529400,
0x000000011dfa7a00, 0x000000010f964800, 0x000000011f139a00, 0x000000011e8e5c00,
0x000000012ec1cc00, 0x000000010f9e2600, 0x000000012e052e00, 0x00000002a2259400,
0x000000011e82d200, 0x000000011e84d600, 0x000000010f9e5600, 0x000000012ea4cc00,
0x000000010f908a00, 0x000000011ca1b200, 0x00000002a3f88000, 0x00000002a261ea00,
0x000000015e3cd200, 0x00000002a2238800, 0x00000002a224b000, 0x00000002a39ebe00,
0x000000010f9fc400, 0x000000011ca0c600, 0x00000002a3f5d400, 0x000000011f192a00,
0x000000011cb1c200, 0x000000011cb67600, 0x000000012deb2c00, 0x00000002aa2ffc00,
0x00000002aa79ae00
}

Java Threads: ( => current thread )
  0x000000011d808800 JavaThread "main"                              [_thread_blocked, id=8451, stack(0x000000016b3e0000,0x000000016b5e3000) (2060K)]
  0x000000012d823a00 JavaThread "Reference Handler"          daemon [_thread_blocked, id=24579, stack(0x000000016c558000,0x000000016c75b000) (2060K)]
  0x000000012d820000 JavaThread "Finalizer"                  daemon [_thread_blocked, id=29443, stack(0x000000016c764000,0x000000016c967000) (2060K)]
  0x000000012d820800 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=25091, stack(0x000000016c970000,0x000000016cb73000) (2060K)]
  0x000000011d8af800 JavaThread "Service Thread"             daemon [_thread_blocked, id=25603, stack(0x000000016cb7c000,0x000000016cd7f000) (2060K)]
  0x000000011d8b0000 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=25859, stack(0x000000016cd88000,0x000000016cf8b000) (2060K)]
  0x000000011d8b0800 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=26115, stack(0x000000016cf94000,0x000000016d197000) (2060K)]
  0x000000011d8b9000 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=26627, stack(0x000000016d1a0000,0x000000016d3a3000) (2060K)]
  0x000000012e00c000 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=28419, stack(0x000000016d3ac000,0x000000016d5af000) (2060K)]
  0x000000012d95c400 JavaThread "Orchestration Server"       daemon [_thread_in_native, id=27907, stack(0x000000016d5b8000,0x000000016d7bb000) (2060K)]
  0x000000012d95cc00 JavaThread "Orchestration Main"         daemon [_thread_blocked, id=26883, stack(0x000000016d7c4000,0x000000016d9c7000) (2060K)]
  0x000000012e7df000 JavaThread "Compose Hot Reload: Logger"        [_thread_blocked, id=27139, stack(0x000000016d9d0000,0x000000016dbd3000) (2060K)]
  0x000000012d96f000 JavaThread "process reaper (pid 96331)" daemon [_thread_in_native, id=41475, stack(0x000000016e618000,0x000000016e64f000) (220K)]
  0x000000011e009000 JavaThread "DevTools: Stdout"           daemon [_thread_in_native, id=40963, stack(0x000000016e658000,0x000000016e85b000) (2060K)]
  0x000000011dc11600 JavaThread "DevTools: Stderr"           daemon [_thread_in_native, id=40451, stack(0x000000016e864000,0x000000016ea67000) (2060K)]
  0x000000011dc11e00 JavaThread "Notification Thread"        daemon [_thread_blocked, id=33795, stack(0x000000016ea70000,0x000000016ec73000) (2060K)]
  0x000000011dc17e00 JavaThread "Compose Runtime Analyzer"   daemon [_thread_blocked, id=39683, stack(0x000000016ec7c000,0x000000016ee7f000) (2060K)]
  0x000000012d97e200 JavaThread "AppKit Thread"              daemon [_thread_in_native, id=259, stack(0x000000016ab58000,0x000000016b354000) (8176K)]
  0x000000011dcdb000 JavaThread "AWT-Shutdown"                      [_thread_blocked, id=49159, stack(0x000000016f02c000,0x000000016f22f000) (2060K)]
  0x000000012db06800 JavaThread "Orchestration Client Reader" daemon [_thread_blocked, id=79371, stack(0x000000016f2c4000,0x000000016f4c7000) (2060K)]
  0x000000012eb97c00 JavaThread "Orchestration Client Writer (/127.0.0.1:54393)" daemon [_thread_blocked, id=78855, stack(0x000000016f4d0000,0x000000016f6d3000) (2060K)]
  0x000000012dac7e00 JavaThread "Java2D Queue Flusher"       daemon [_thread_blocked, id=76827, stack(0x000000016f6dc000,0x000000016f8df000) (2060K)]
  0x000000012eb67600 JavaThread "Java2D Disposer"            daemon [_thread_blocked, id=76291, stack(0x000000016f8e8000,0x000000016faeb000) (2060K)]
  0x000000012db39200 JavaThread "Thread-10"                         [_thread_blocked, id=89607, stack(0x000000016faf4000,0x000000016fcf7000) (2060K)]
  0x000000012db3a000 JavaThread "Thread-12"                         [_thread_blocked, id=90371, stack(0x0000000288004000,0x0000000288207000) (2060K)]
  0x000000011e223800 JavaThread "Thread-13"                         [_thread_blocked, id=90627, stack(0x0000000288210000,0x0000000288413000) (2060K)]
  0x000000011e224000 JavaThread "Thread-14"                         [_thread_blocked, id=91139, stack(0x000000028841c000,0x000000028861f000) (2060K)]
  0x000000010f809c00 JavaThread "Thread-15"                         [_thread_blocked, id=91651, stack(0x0000000288628000,0x000000028882b000) (2060K)]
  0x000000010f8bda00 JavaThread "Thread-16"                         [_thread_blocked, id=124931, stack(0x0000000288834000,0x0000000288a37000) (2060K)]
  0x000000011c816600 JavaThread "Thread-17"                         [_thread_blocked, id=124675, stack(0x0000000288a40000,0x0000000288c43000) (2060K)]
  0x000000012ec24a00 JavaThread "Thread-18"                         [_thread_blocked, id=124163, stack(0x0000000288c4c000,0x0000000288e4f000) (2060K)]
  0x000000012ec27200 JavaThread "Thread-19"                         [_thread_blocked, id=123651, stack(0x0000000288e58000,0x000000028905b000) (2060K)]
  0x000000012ec27a00 JavaThread "Thread-20"                         [_thread_blocked, id=123395, stack(0x0000000289064000,0x0000000289267000) (2060K)]
  0x000000010f8bbe00 JavaThread "Thread-21"                         [_thread_blocked, id=92675, stack(0x0000000289270000,0x0000000289473000) (2060K)]
  0x000000012ec28e00 JavaThread "Thread-22"                         [_thread_blocked, id=122883, stack(0x000000028947c000,0x000000028967f000) (2060K)]
  0x000000011f05c800 JavaThread "Thread-23"                         [_thread_blocked, id=92931, stack(0x0000000289688000,0x000000028988b000) (2060K)]
  0x000000011dda5000 JavaThread "JettyServerThreadPool-63"          [_thread_in_native, id=96259, stack(0x000000028ab00000,0x000000028ad03000) (2060K)]
  0x000000012ec35200 JavaThread "JettyServerThreadPool-64"          [_thread_in_native, id=119299, stack(0x000000028ad0c000,0x000000028af0f000) (2060K)]
  0x000000012db53c00 JavaThread "JettyServerThreadPool-65"          [_thread_in_native, id=118787, stack(0x000000028af18000,0x000000028b11b000) (2060K)]
  0x000000011c81c400 JavaThread "JettyServerThreadPool-66"          [_thread_blocked, id=96771, stack(0x000000028b124000,0x000000028b327000) (2060K)]
  0x000000012db54400 JavaThread "JettyServerThreadPool-67-acceptor-0@340a94d6-ServerConnector@2a215831{HTTP/1.1, (http/1.1)}{0.0.0.0:5000}"        [_thread_in_native, id=97283, stack(0x000000028b330000,0x000000028b533000) (2060K)]
  0x000000012db6c800 JavaThread "JettyServerThreadPool-68"          [_thread_blocked, id=118531, stack(0x000000028b53c000,0x000000028b73f000) (2060K)]
  0x000000011dda6000 JavaThread "JettyServerThreadPool-69"          [_thread_in_native, id=98051, stack(0x000000028b748000,0x000000028b94b000) (2060K)]
  0x000000011ddbfe00 JavaThread "JettyServerThreadPool-70"          [_thread_blocked, id=98307, stack(0x000000028b954000,0x000000028bb57000) (2060K)]
  0x000000010f87e000 JavaThread "Scheduler-388127633-1"             [_thread_blocked, id=118019, stack(0x000000028bb60000,0x000000028bd63000) (2060K)]
  0x000000012ec56000 JavaThread "Session-HouseKeeper-5245c274-1"        [_thread_blocked, id=117763, stack(0x000000028bd6c000,0x000000028bf6f000) (2060K)]
  0x000000010f8c0400 JavaThread "Thread-38"                         [_thread_blocked, id=99331, stack(0x000000028bf78000,0x000000028c17b000) (2060K)]
=>0x000000011e26be00 JavaThread "AWT-EventQueue-0"                  [_thread_in_vm, id=99843, stack(0x000000028c184000,0x000000028c387000) (2060K)]
  0x000000011df88800 JavaThread "kotlinx.coroutines.DefaultExecutor" daemon [_thread_blocked, id=100099, stack(0x000000028c390000,0x000000028c593000) (2060K)]
  0x000000012ecb9400 JavaThread "DefaultDispatcher-worker-7" daemon [_thread_blocked, id=116995, stack(0x000000028c59c000,0x000000028c79f000) (2060K)]
  0x000000011e8b4800 JavaThread "Reference Cleaner"          daemon [_thread_blocked, id=116739, stack(0x000000028c7a8000,0x000000028c9ab000) (2060K)]
  0x000000011e8b1800 JavaThread "DefaultDispatcher-worker-4" daemon [_thread_blocked, id=101635, stack(0x000000028cbc0000,0x000000028cdc3000) (2060K)]
  0x000000012e00d000 JavaThread "AWTThreading pool-1-thread-1" daemon [_thread_blocked, id=115715, stack(0x000000028cdcc000,0x000000028cfcf000) (2060K)]
  0x000000015e147800 JavaThread "TimerQueue"                 daemon [_thread_blocked, id=106247, stack(0x000000029897c000,0x0000000298b7f000) (2060K)]
  0x000000012edc9a00 JavaThread "pool-2-thread-1"            daemon [_thread_blocked, id=110867, stack(0x0000000298b88000,0x0000000298d8b000) (2060K)]
  0x000000011e529400 JavaThread "Connector-Scheduler-2a215831-1"        [_thread_blocked, id=30727, stack(0x00000002a3204000,0x00000002a3407000) (2060K)]
  0x000000011dfa7a00 JavaThread "DefaultDispatcher-worker-1" daemon [_thread_blocked, id=133647, stack(0x0000000289cac000,0x0000000289eaf000) (2060K)]
  0x000000010f964800 JavaThread "DefaultDispatcher-worker-3" daemon [_thread_blocked, id=173835, stack(0x000000028a4dc000,0x000000028a6df000) (2060K)]
  0x000000011f139a00 JavaThread "DefaultDispatcher-worker-2" daemon [_thread_blocked, id=108679, stack(0x000000028c9b4000,0x000000028cbb7000) (2060K)]
  0x000000011e8e5c00 JavaThread "DefaultDispatcher-worker-8" daemon [_thread_blocked, id=133383, stack(0x000000028cfd8000,0x000000028d1db000) (2060K)]
  0x000000012ec1cc00 JavaThread "DefaultDispatcher-worker-5" daemon [_thread_blocked, id=173099, stack(0x000000029b3c4000,0x000000029b5c7000) (2060K)]
  0x000000010f9e2600 JavaThread "DefaultDispatcher-worker-6" daemon [_thread_blocked, id=100903, stack(0x00000002a841c000,0x00000002a861f000) (2060K)]
  0x000000012e052e00 JavaThread "process reaper"             daemon [_thread_blocked, id=171539, stack(0x000000016c4f8000,0x000000016c52f000) (220K)]
  0x00000002a2259400 JavaThread "DefaultDispatcher-worker-9" daemon [_thread_blocked, id=132139, stack(0x000000016fd00000,0x000000016ff03000) (2060K)]
  0x000000011e82d200 JavaThread "DefaultDispatcher-worker-10" daemon [_thread_blocked, id=112399, stack(0x0000000289eb8000,0x000000028a0bb000) (2060K)]
  0x000000011e84d600 JavaThread "DefaultDispatcher-worker-12" daemon [_thread_blocked, id=59199, stack(0x000000028a0c4000,0x000000028a2c7000) (2060K)]
  0x000000010f9e5600 JavaThread "DefaultDispatcher-worker-14" daemon [_thread_blocked, id=119879, stack(0x000000028a2d0000,0x000000028a4d3000) (2060K)]
  0x000000012ea4cc00 JavaThread "DefaultDispatcher-worker-16" daemon [_thread_blocked, id=30235, stack(0x000000028a6e8000,0x000000028a8eb000) (2060K)]
  0x000000010f908a00 JavaThread "DefaultDispatcher-worker-15" daemon [_thread_blocked, id=114231, stack(0x000000028d1e4000,0x000000028d3e7000) (2060K)]
  0x000000011ca1b200 JavaThread "DefaultDispatcher-worker-11" daemon [_thread_blocked, id=121375, stack(0x000000029b5d0000,0x000000029b7d3000) (2060K)]
  0x00000002a3f88000 JavaThread "DefaultDispatcher-worker-13" daemon [_thread_blocked, id=51079, stack(0x000000029b7dc000,0x000000029b9df000) (2060K)]
  0x00000002a261ea00 JavaThread "DefaultDispatcher-worker-17" daemon [_thread_blocked, id=82719, stack(0x000000029c968000,0x000000029cb6b000) (2060K)]
  0x000000015e3cd200 JavaThread "DefaultDispatcher-worker-18" daemon [_thread_blocked, id=174095, stack(0x000000029cb74000,0x000000029cd77000) (2060K)]
  0x00000002a2238800 JavaThread "DefaultDispatcher-worker-19" daemon [_thread_blocked, id=172063, stack(0x000000029d754000,0x000000029d957000) (2060K)]
  0x00000002a224b000 JavaThread "DefaultDispatcher-worker-22" daemon [_thread_blocked, id=131699, stack(0x000000029d960000,0x000000029db63000) (2060K)]
  0x00000002a39ebe00 JavaThread "DefaultDispatcher-worker-21" daemon [_thread_blocked, id=70703, stack(0x000000029db6c000,0x000000029dd6f000) (2060K)]
  0x000000010f9fc400 JavaThread "DefaultDispatcher-worker-26" daemon [_thread_blocked, id=109639, stack(0x00000002a1b04000,0x00000002a1d07000) (2060K)]
  0x000000011ca0c600 JavaThread "DefaultDispatcher-worker-23" daemon [_thread_blocked, id=114471, stack(0x00000002a8004000,0x00000002a8207000) (2060K)]
  0x00000002a3f5d400 JavaThread "DefaultDispatcher-worker-24" daemon [_thread_blocked, id=134171, stack(0x00000002a8210000,0x00000002a8413000) (2060K)]
  0x000000011f192a00 JavaThread "DefaultDispatcher-worker-25" daemon [_thread_blocked, id=109099, stack(0x00000002a8628000,0x00000002a882b000) (2060K)]
  0x000000011cb1c200 JavaThread "DefaultDispatcher-worker-20" daemon [_thread_blocked, id=55423, stack(0x00000002a8834000,0x00000002a8a37000) (2060K)]
  0x000000011cb67600 JavaThread "Orchestration Client Reader" daemon [_thread_blocked, id=132447, stack(0x00000002aa904000,0x00000002aab07000) (2060K)]
  0x000000012deb2c00 JavaThread "Orchestration Client Writer (/127.0.0.1:55530)" daemon [_thread_blocked, id=134967, stack(0x00000002aab10000,0x00000002aad13000) (2060K)]
  0x00000002aa2ffc00 JavaThread "Orchestration Client Reader" daemon [_thread_blocked, id=48035, stack(0x00000002aad1c000,0x00000002aaf1f000) (2060K)]
  0x00000002aa79ae00 JavaThread "Orchestration Client Writer (/127.0.0.1:55537)" daemon [_thread_blocked, id=133963, stack(0x00000002aaf28000,0x00000002ab12b000) (2060K)]
Total: 85

Other Threads:
  0x000000012d609040 VMThread "VM Thread"                           [id=19971, stack(0x000000016c234000,0x000000016c437000) (2060K)]
  0x000000011d60bd00 WatcherThread "VM Periodic Task Thread"        [id=21251, stack(0x000000016c028000,0x000000016c22b000) (2060K)]
  0x000000011d606c20 WorkerThread "GC Thread#0"                     [id=13827, stack(0x000000016b5ec000,0x000000016b7ef000) (2060K)]
  0x000000012d61cfa0 WorkerThread "GC Thread#1"                     [id=43267, stack(0x000000016dbdc000,0x000000016dddf000) (2060K)]
  0x000000012d61d510 WorkerThread "GC Thread#2"                     [id=42755, stack(0x000000016dde8000,0x000000016dfeb000) (2060K)]
  0x000000012d61de70 WorkerThread "GC Thread#3"                     [id=33283, stack(0x000000016dff4000,0x000000016e1f7000) (2060K)]
  0x000000012d61e7d0 WorkerThread "GC Thread#4"                     [id=42243, stack(0x000000016e200000,0x000000016e403000) (2060K)]
  0x000000012d61f130 WorkerThread "GC Thread#5"                     [id=33539, stack(0x000000016e40c000,0x000000016e60f000) (2060K)]
  0x000000011d64a170 WorkerThread "GC Thread#6"                     [id=93187, stack(0x0000000289894000,0x0000000289a97000) (2060K)]
  0x000000011d64a6e0 WorkerThread "GC Thread#7"                     [id=93443, stack(0x0000000289aa0000,0x0000000289ca3000) (2060K)]
  0x000000011d6073c0 ConcurrentGCThread "G1 Main Marker"            [id=12291, stack(0x000000016b7f8000,0x000000016b9fb000) (2060K)]
  0x000000011d607d30 WorkerThread "G1 Conc#0"                       [id=13059, stack(0x000000016ba04000,0x000000016bc07000) (2060K)]
  0x000000012f090fd0 WorkerThread "G1 Conc#1"                       [id=95747, stack(0x000000028a8f4000,0x000000028aaf7000) (2060K)]
  0x000000011d8a1600 ConcurrentGCThread "G1 Refine#0"               [id=16387, stack(0x000000016bc10000,0x000000016be13000) (2060K)]
  0x000000011d60a250 ConcurrentGCThread "G1 Service"                [id=16899, stack(0x000000016be1c000,0x000000016c01f000) (2060K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0  518926 42212       4       org.objectweb.asm.ClassReader::getFirstAttributeOffset (136 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000000b800000000-0x000000b800d30000-0x000000b800d30000), size 13828096, SharedBaseAddress: 0x000000b800000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000000b801000000-0x000000b841000000, reserved size: 1073741824
Narrow klass base: 0x000000b800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 8 total, 8 available
 Memory: 16384M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 256M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 389120K, used 119750K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 25 young (51200K), 6 survivors (12288K)
 Metaspace       used 77216K, committed 78080K, reserved 1179648K
  class space    used 8254K, committed 8640K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%|HS|  |TAMS 0x0000000700000000| PB 0x0000000700000000| Complete 
|   1|0x0000000700200000, 0x00000007003ffff8, 0x0000000700400000| 99%| O|  |TAMS 0x0000000700200000| PB 0x0000000700200000| Untracked 
|   2|0x0000000700400000, 0x00000007005ffff0, 0x0000000700600000| 99%| O|  |TAMS 0x0000000700400000| PB 0x0000000700400000| Untracked 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%| O|  |TAMS 0x0000000700600000| PB 0x0000000700600000| Untracked 
|   4|0x0000000700800000, 0x00000007009fffe8, 0x0000000700a00000| 99%| O|  |TAMS 0x0000000700800000| PB 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700bfffe8, 0x0000000700c00000| 99%| O|  |TAMS 0x0000000700a00000| PB 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700dfff28, 0x0000000700e00000| 99%| O|  |TAMS 0x0000000700c00000| PB 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000700ffffd8, 0x0000000701000000| 99%| O|  |TAMS 0x0000000700e00000| PB 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x00000007011ffff8, 0x0000000701200000| 99%| O|  |TAMS 0x0000000701000000| PB 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x00000007013fffe0, 0x0000000701400000| 99%| O|  |TAMS 0x0000000701200000| PB 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701400000| PB 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x00000007017fffe8, 0x0000000701800000| 99%| O|  |TAMS 0x0000000701600000| PB 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x00000007019fffc8, 0x0000000701a00000| 99%| O|  |TAMS 0x0000000701800000| PB 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701bffdd0, 0x0000000701c00000| 99%| O|  |TAMS 0x0000000701a00000| PB 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701dfffe8, 0x0000000701e00000| 99%| O|  |TAMS 0x0000000701c00000| PB 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000701ffffd8, 0x0000000702000000| 99%| O|  |TAMS 0x0000000701e00000| PB 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x00000007021fff80, 0x0000000702200000| 99%| O|  |TAMS 0x0000000702000000| PB 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x00000007023fff90, 0x0000000702400000| 99%| O|  |TAMS 0x0000000702200000| PB 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x00000007025ffff8, 0x0000000702600000| 99%| O|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x00000007027fffe8, 0x0000000702800000| 99%| O|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x00000007029fe640, 0x0000000702a00000| 99%| O|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702bfffe0, 0x0000000702c00000| 99%| O|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702dffff0, 0x0000000702e00000| 99%| O|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000702fdf9a8, 0x0000000703000000| 93%| O|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x00000007031fceb0, 0x0000000703200000| 99%| O|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|  25|0x0000000703200000, 0x00000007033fffe8, 0x0000000703400000| 99%| O|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|  27|0x0000000703600000, 0x000000070376af40, 0x0000000703800000| 70%| O|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x00000007039e8378, 0x0000000703a00000| 95%| O|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703bfd8e0, 0x0000000703c00000| 99%| O|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703f087a8, 0x0000000704000000| 51%| O|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x000000070435eb40, 0x0000000704400000| 68%| O|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|Cm|TAMS 0x0000000705c00000| PB 0x0000000705c00000| Complete 
|  47|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  50|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|Cm|TAMS 0x0000000706a00000| PB 0x0000000706a00000| Complete 
|  54|0x0000000706c00000, 0x0000000706c82d90, 0x0000000706e00000| 25%| S|CS|TAMS 0x0000000706c00000| PB 0x0000000706c00000| Complete 
|  55|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| S|CS|TAMS 0x0000000706e00000| PB 0x0000000706e00000| Complete 
|  56|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  57|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| S|CS|TAMS 0x0000000707200000| PB 0x0000000707200000| Complete 
|  58|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| S|CS|TAMS 0x0000000707400000| PB 0x0000000707400000| Complete 
|  59|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  60|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| S|CS|TAMS 0x0000000707800000| PB 0x0000000707800000| Complete 
|  61|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| S|CS|TAMS 0x0000000707a00000| PB 0x0000000707a00000| Complete 
|  62|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  63|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  64|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  65|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  66|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  67|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  68|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  69|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  70|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  71|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  72|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  73|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  74|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  75|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  76|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  77|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  78|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  79|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  80|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  81|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  82|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  83|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  84|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  85|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  86|0x000000070ac00000, 0x000000070acdc4e8, 0x000000070ae00000| 43%| O|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  87|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  88|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  89|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  90|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  91|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  92|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  93|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  94|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  95|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
| 101|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
| 102|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
| 103|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
| 104|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
| 105|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
| 106|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
| 107|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
| 108|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
| 109|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
| 110|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
| 111|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
| 112|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
| 113|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
| 114|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
| 115|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
| 116|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
| 117|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
| 118|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
| 119|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 120|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 121|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 122|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 123|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 124|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
| 125|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
| 126|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
| 127|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
| 128|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
| 129|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
| 130|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
| 131|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
| 132|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
| 133|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
| 134|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
| 135|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
| 136|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
| 137|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
| 138|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
| 139|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Untracked 
| 140|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000| PB 0x0000000711800000| Untracked 
| 141|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000| PB 0x0000000711a00000| Untracked 
| 142|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000| PB 0x0000000711c00000| Untracked 
| 143|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000| PB 0x0000000711e00000| Untracked 
| 144|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000| PB 0x0000000712000000| Untracked 
| 145|0x0000000712200000, 0x0000000712200000, 0x0000000712400000|  0%| F|  |TAMS 0x0000000712200000| PB 0x0000000712200000| Untracked 
| 146|0x0000000712400000, 0x0000000712400000, 0x0000000712600000|  0%| F|  |TAMS 0x0000000712400000| PB 0x0000000712400000| Untracked 
| 147|0x0000000712600000, 0x0000000712600000, 0x0000000712800000|  0%| F|  |TAMS 0x0000000712600000| PB 0x0000000712600000| Untracked 
| 148|0x0000000712800000, 0x0000000712800000, 0x0000000712a00000|  0%| F|  |TAMS 0x0000000712800000| PB 0x0000000712800000| Untracked 
| 149|0x0000000712a00000, 0x0000000712a00000, 0x0000000712c00000|  0%| F|  |TAMS 0x0000000712a00000| PB 0x0000000712a00000| Untracked 
| 150|0x0000000712c00000, 0x0000000712c00000, 0x0000000712e00000|  0%| F|  |TAMS 0x0000000712c00000| PB 0x0000000712c00000| Untracked 
| 151|0x0000000712e00000, 0x0000000712e00000, 0x0000000713000000|  0%| F|  |TAMS 0x0000000712e00000| PB 0x0000000712e00000| Untracked 
| 152|0x0000000713000000, 0x0000000713000000, 0x0000000713200000|  0%| F|  |TAMS 0x0000000713000000| PB 0x0000000713000000| Untracked 
| 153|0x0000000713200000, 0x0000000713200000, 0x0000000713400000|  0%| F|  |TAMS 0x0000000713200000| PB 0x0000000713200000| Untracked 
| 154|0x0000000713400000, 0x0000000713400000, 0x0000000713600000|  0%| F|  |TAMS 0x0000000713400000| PB 0x0000000713400000| Untracked 
| 155|0x0000000713600000, 0x0000000713600000, 0x0000000713800000|  0%| F|  |TAMS 0x0000000713600000| PB 0x0000000713600000| Untracked 
| 156|0x0000000713800000, 0x0000000713800000, 0x0000000713a00000|  0%| F|  |TAMS 0x0000000713800000| PB 0x0000000713800000| Untracked 
| 157|0x0000000713a00000, 0x0000000713a00000, 0x0000000713c00000|  0%| F|  |TAMS 0x0000000713a00000| PB 0x0000000713a00000| Untracked 
| 158|0x0000000713c00000, 0x0000000713c00000, 0x0000000713e00000|  0%| F|  |TAMS 0x0000000713c00000| PB 0x0000000713c00000| Untracked 
| 159|0x0000000713e00000, 0x0000000713e00000, 0x0000000714000000|  0%| F|  |TAMS 0x0000000713e00000| PB 0x0000000713e00000| Untracked 
| 160|0x0000000714000000, 0x0000000714000000, 0x0000000714200000|  0%| F|  |TAMS 0x0000000714000000| PB 0x0000000714000000| Untracked 
| 161|0x0000000714200000, 0x0000000714200000, 0x0000000714400000|  0%| F|  |TAMS 0x0000000714200000| PB 0x0000000714200000| Untracked 
| 231|0x000000071ce00000, 0x000000071ce00000, 0x000000071d000000|  0%| F|  |TAMS 0x000000071ce00000| PB 0x000000071ce00000| Untracked 
| 232|0x000000071d000000, 0x000000071d000000, 0x000000071d200000|  0%| F|  |TAMS 0x000000071d000000| PB 0x000000071d000000| Untracked 
| 233|0x000000071d200000, 0x000000071d200000, 0x000000071d400000|  0%| F|  |TAMS 0x000000071d200000| PB 0x000000071d200000| Untracked 
| 234|0x000000071d400000, 0x000000071d400000, 0x000000071d600000|  0%| F|  |TAMS 0x000000071d400000| PB 0x000000071d400000| Untracked 
| 235|0x000000071d600000, 0x000000071d600000, 0x000000071d800000|  0%| F|  |TAMS 0x000000071d600000| PB 0x000000071d600000| Untracked 
| 236|0x000000071d800000, 0x000000071d800000, 0x000000071da00000|  0%| F|  |TAMS 0x000000071d800000| PB 0x000000071d800000| Untracked 
| 237|0x000000071da00000, 0x000000071da00000, 0x000000071dc00000|  0%| F|  |TAMS 0x000000071da00000| PB 0x000000071da00000| Untracked 
| 238|0x000000071dc00000, 0x000000071dc00000, 0x000000071de00000|  0%| F|  |TAMS 0x000000071dc00000| PB 0x000000071dc00000| Untracked 
| 239|0x000000071de00000, 0x000000071de00000, 0x000000071e000000|  0%| F|  |TAMS 0x000000071de00000| PB 0x000000071de00000| Untracked 
| 240|0x000000071e000000, 0x000000071e1f7b28, 0x000000071e200000| 98%| E|  |TAMS 0x000000071e000000| PB 0x000000071e000000| Complete 
| 241|0x000000071e200000, 0x000000071e400000, 0x000000071e400000|100%| E|CS|TAMS 0x000000071e200000| PB 0x000000071e200000| Complete 
| 242|0x000000071e400000, 0x000000071e600000, 0x000000071e600000|100%| E|CS|TAMS 0x000000071e400000| PB 0x000000071e400000| Complete 
| 243|0x000000071e600000, 0x000000071e800000, 0x000000071e800000|100%| E|CS|TAMS 0x000000071e600000| PB 0x000000071e600000| Complete 
| 244|0x000000071e800000, 0x000000071ea00000, 0x000000071ea00000|100%| E|  |TAMS 0x000000071e800000| PB 0x000000071e800000| Complete 
| 245|0x000000071ea00000, 0x000000071ec00000, 0x000000071ec00000|100%| E|CS|TAMS 0x000000071ea00000| PB 0x000000071ea00000| Complete 
| 246|0x000000071ec00000, 0x000000071ee00000, 0x000000071ee00000|100%| E|CS|TAMS 0x000000071ec00000| PB 0x000000071ec00000| Complete 
| 247|0x000000071ee00000, 0x000000071f000000, 0x000000071f000000|100%| E|CS|TAMS 0x000000071ee00000| PB 0x000000071ee00000| Complete 
| 248|0x000000071f000000, 0x000000071f200000, 0x000000071f200000|100%| E|CS|TAMS 0x000000071f000000| PB 0x000000071f000000| Complete 
| 249|0x000000071f200000, 0x000000071f400000, 0x000000071f400000|100%| E|CS|TAMS 0x000000071f200000| PB 0x000000071f200000| Complete 
| 250|0x000000071f400000, 0x000000071f600000, 0x000000071f600000|100%| E|CS|TAMS 0x000000071f400000| PB 0x000000071f400000| Complete 
| 251|0x000000071f600000, 0x000000071f800000, 0x000000071f800000|100%| E|CS|TAMS 0x000000071f600000| PB 0x000000071f600000| Complete 
| 252|0x000000071f800000, 0x000000071fa00000, 0x000000071fa00000|100%| E|CS|TAMS 0x000000071f800000| PB 0x000000071f800000| Complete 
| 253|0x000000071fa00000, 0x000000071fc00000, 0x000000071fc00000|100%| E|CS|TAMS 0x000000071fa00000| PB 0x000000071fa00000| Complete 
| 254|0x000000071fc00000, 0x000000071fe00000, 0x000000071fe00000|100%| E|CS|TAMS 0x000000071fc00000| PB 0x000000071fc00000| Complete 
| 255|0x000000071fe00000, 0x0000000720000000, 0x0000000720000000|100%| E|CS|TAMS 0x000000071fe00000| PB 0x000000071fe00000| Complete 
| 256|0x0000000720000000, 0x0000000720200000, 0x0000000720200000|100%| E|CS|TAMS 0x0000000720000000| PB 0x0000000720000000| Complete 
| 257|0x0000000720200000, 0x0000000720400000, 0x0000000720400000|100%| E|CS|TAMS 0x0000000720200000| PB 0x0000000720200000| Complete 
|2047|0x00000007ffe00000, 0x0000000800000000, 0x0000000800000000|100%| E|CS|TAMS 0x00000007ffe00000| PB 0x00000007ffe00000| Complete 

Card table byte_map: [0x00000001057d0000,0x0000000105fd0000] _byte_map_base: 0x0000000101fd0000

Marking Bits: (CMBitMap*) 0x000000011d809e10
 Bits: [0x000000010b700000, 0x000000010f700000)

Polling page: 0x0000000104ac0000

Metaspace:

Usage:
  Non-class:     67.35 MB used.
      Class:      8.06 MB used.
       Both:     75.41 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      67.81 MB ( 53%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.44 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      76.25 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  11.30 MB
       Class:  7.48 MB
        Both:  18.78 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 124.81 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 11.
num_arena_births: 1356.
num_arena_deaths: 26.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1220.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 42.
num_chunks_taken_from_freelist: 3941.
num_chunk_merges: 11.
num_chunk_splits: 2695.
num_chunks_enlarged: 1998.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120032Kb used=8561Kb max_used=12605Kb free=111471Kb
 bounds [0x000000013fac8000, 0x0000000140718000, 0x0000000147000000]
CodeHeap 'profiled nmethods': size=120016Kb used=21463Kb max_used=36840Kb free=98553Kb
 bounds [0x0000000138000000, 0x000000013a400000, 0x000000013f534000]
CodeHeap 'non-nmethods': size=5712Kb used=2745Kb max_used=2865Kb free=2966Kb
 bounds [0x000000013f534000, 0x000000013f814000, 0x000000013fac8000]
 total_blobs=12264 nmethods=10253 adapters=1919
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 518.769 Thread 0x000000011d8b9000 nmethod 42202 0x000000013a057d90 code [0x000000013a058000, 0x000000013a0588d8]
Event: 518.769 Thread 0x000000011d8b9000 42205       2       java.io.FileInputStream$1::<init> (10 bytes)
Event: 518.769 Thread 0x000000011d8b9000 nmethod 42205 0x000000013a057a10 code [0x000000013a057b80, 0x000000013a057ce8]
Event: 518.769 Thread 0x000000011d8b9000 42204   !   2       java.io.FileInputStream::close (69 bytes)
Event: 518.769 Thread 0x000000011d8b9000 nmethod 42204 0x000000013a057210 code [0x000000013a057400, 0x000000013a057838]
Event: 518.769 Thread 0x000000011d8b9000 42208       1       javassist.CtMember::next (5 bytes)
Event: 518.769 Thread 0x000000011d8b9000 nmethod 42208 0x000000014013fc90 code [0x000000014013fe00, 0x000000014013fea8]
Event: 518.769 Thread 0x000000011d8b9000 42197       2       javassist.bytecode.ClassFile::read (303 bytes)
Event: 518.771 Thread 0x000000011d8b9000 nmethod 42197 0x000000013a053e10 code [0x000000013a0543c0, 0x000000013a055ec8]
Event: 518.772 Thread 0x000000011d8b0800 nmethod 42086 0x000000014013f790 code [0x000000014013f900, 0x000000014013fb20]
Event: 518.772 Thread 0x000000011d8b9000 42210       2       javassist.CtField::<init> (11 bytes)
Event: 518.772 Thread 0x000000011d8b0800 42082       4       jdk.internal.jimage.ImageStringsReader::unmaskedHashCode (166 bytes)
Event: 518.772 Thread 0x000000011d8b9000 nmethod 42210 0x000000013a053910 code [0x000000013a053ac0, 0x000000013a053c98]
Event: 518.772 Thread 0x000000011d8b9000 42201       2       javassist.bytecode.Utf8Info::equals (30 bytes)
Event: 518.772 Thread 0x000000011d8b9000 nmethod 42201 0x00000001396aaa90 code [0x00000001396aac40, 0x00000001396aae30]
Event: 518.772 Thread 0x000000011d8b9000 42206       2       java.util.concurrent.FutureTask::report (37 bytes)
Event: 518.772 Thread 0x000000011d8b9000 nmethod 42206 0x00000001396aa510 code [0x00000001396aa6c0, 0x00000001396aa920]
Event: 518.772 Thread 0x000000011d8b9000 42207       2       java.io.ByteArrayInputStream::<init> (26 bytes)
Event: 518.773 Thread 0x000000011d8b9000 nmethod 42207 0x00000001396aa110 code [0x00000001396aa280, 0x00000001396aa3f8]
Event: 518.774 Thread 0x000000011d8b0800 nmethod 42082 0x000000014013f190 code [0x000000014013f340, 0x000000014013f548]

GC Heap History (20 events):
Event: 406.591 GC heap before
{Heap before GC invocations=54 (full 2):
 garbage-first heap   total 419840K, used 353563K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 123 young (251904K), 14 survivors (28672K)
 Metaspace       used 75662K, committed 76544K, reserved 1179648K
  class space    used 8141K, committed 8512K, reserved 1048576K
}
Event: 406.625 GC heap after
{Heap after GC invocations=55 (full 2):
 garbage-first heap   total 419840K, used 100020K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 10 survivors (20480K)
 Metaspace       used 75662K, committed 76544K, reserved 1179648K
  class space    used 8141K, committed 8512K, reserved 1048576K
}
Event: 438.169 GC heap before
{Heap before GC invocations=55 (full 2):
 garbage-first heap   total 419840K, used 329396K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 123 young (251904K), 10 survivors (20480K)
 Metaspace       used 75758K, committed 76608K, reserved 1179648K
  class space    used 8143K, committed 8512K, reserved 1048576K
}
Event: 438.228 GC heap after
{Heap after GC invocations=56 (full 2):
 garbage-first heap   total 419840K, used 133428K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 16 survivors (32768K)
 Metaspace       used 75758K, committed 76608K, reserved 1179648K
  class space    used 8143K, committed 8512K, reserved 1048576K
}
Event: 456.471 GC heap before
{Heap before GC invocations=56 (full 2):
 garbage-first heap   total 419840K, used 155956K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 28 young (57344K), 16 survivors (32768K)
 Metaspace       used 75865K, committed 76672K, reserved 1179648K
  class space    used 8153K, committed 8512K, reserved 1048576K
}
Event: 456.493 GC heap after
{Heap after GC invocations=57 (full 2):
 garbage-first heap   total 419840K, used 119222K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 75865K, committed 76672K, reserved 1179648K
  class space    used 8153K, committed 8512K, reserved 1048576K
}
Event: 457.255 GC heap before
{Heap before GC invocations=58 (full 2):
 garbage-first heap   total 419840K, used 397750K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 108 young (221184K), 3 survivors (6144K)
 Metaspace       used 75869K, committed 76672K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 457.257 GC heap after
{Heap after GC invocations=59 (full 2):
 garbage-first heap   total 419840K, used 104700K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 75869K, committed 76672K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 467.729 GC heap before
{Heap before GC invocations=59 (full 2):
 garbage-first heap   total 432128K, used 426236K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 113 young (231424K), 2 survivors (4096K)
 Metaspace       used 75920K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 467.734 GC heap after
{Heap after GC invocations=60 (full 2):
 garbage-first heap   total 436224K, used 119249K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 75920K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 467.737 GC heap before
{Heap before GC invocations=60 (full 2):
 garbage-first heap   total 436224K, used 121297K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 2 survivors (4096K)
 Metaspace       used 75920K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 467.739 GC heap after
{Heap after GC invocations=61 (full 2):
 garbage-first heap   total 436224K, used 119298K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 75920K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 468.547 GC heap before
{Heap before GC invocations=62 (full 2):
 garbage-first heap   total 436224K, used 408066K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 127 young (260096K), 2 survivors (4096K)
 Metaspace       used 75924K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 468.551 GC heap after
{Heap after GC invocations=63 (full 2):
 garbage-first heap   total 440320K, used 102145K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 13 survivors (26624K)
 Metaspace       used 75924K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 471.985 GC heap before
{Heap before GC invocations=63 (full 2):
 garbage-first heap   total 440320K, used 411393K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 129 young (264192K), 13 survivors (26624K)
 Metaspace       used 75950K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 471.987 GC heap after
{Heap after GC invocations=64 (full 2):
 garbage-first heap   total 440320K, used 109443K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 75950K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 471.989 GC heap before
{Heap before GC invocations=64 (full 2):
 garbage-first heap   total 440320K, used 115587K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 2 survivors (4096K)
 Metaspace       used 75950K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 471.990 GC heap after
{Heap after GC invocations=65 (full 2):
 garbage-first heap   total 440320K, used 108408K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 75950K, committed 76800K, reserved 1179648K
  class space    used 8157K, committed 8512K, reserved 1048576K
}
Event: 509.251 GC heap before
{Heap before GC invocations=66 (full 2):
 garbage-first heap   total 389120K, used 345976K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 114 young (233472K), 1 survivors (2048K)
 Metaspace       used 76840K, committed 77696K, reserved 1179648K
  class space    used 8242K, committed 8576K, reserved 1048576K
}
Event: 509.263 GC heap after
{Heap after GC invocations=67 (full 2):
 garbage-first heap   total 389120K, used 84934K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 6 survivors (12288K)
 Metaspace       used 76840K, committed 77696K, reserved 1179648K
  class space    used 8242K, committed 8576K, reserved 1048576K
}

Dll operation events (19 events):
Event: 0.004 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjava.dylib
Event: 0.041 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libzip.dylib
Event: 0.047 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libinstrument.dylib
Event: 0.049 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libnio.dylib
Event: 0.050 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libzip.dylib
Event: 0.062 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjimage.dylib
Event: 0.123 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libnet.dylib
Event: 0.126 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libextnet.dylib
Event: 0.217 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libverify.dylib
Event: 0.233 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libawt.dylib
Event: 0.235 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libawt_lwawt.dylib
Event: 0.241 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libfontmanager.dylib
Event: 1.285 Loaded shared library /private/var/folders/p4/5q8rp4gn70g3q_y9788vy6700000gn/T/sqlite-3.44.1.0-c9a1cf5e-599b-416b-acfb-4c8e0d236537-libsqlitejdbc.dylib
Event: 1.286 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libmanagement.dylib
Event: 1.290 Loaded shared library /Users/<USER>/.skiko/4e94e1f84e9425da38ae4a4c905cf7dd710053fb4fed13948115962b20bf3356/libskiko-macos-arm64.dylib
Event: 1.292 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libmanagement_ext.dylib
Event: 1.549 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libosxui.dylib
Event: 2.469 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjavajpeg.dylib
Event: 4.442 Loaded shared library /Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/liblcms.dylib

Deoptimization events (20 events):
Event: 518.709 Thread 0x000000010f9e2600 Uncommon trap: trap_request=0xffffff47 fr.pc=0x00000001406fea78 relative=0x00000000000010f8
Event: 518.709 Thread 0x000000010f9e2600 Uncommon trap: reason=unstable_if action=none pc=0x00000001406fea78 method=kotlinx.coroutines.DispatchedTaskKt.dispatch(Lkotlinx/coroutines/DispatchedTask;I)V @ 95 c2
Event: 518.709 Thread 0x000000010f908a00 Uncommon trap: trap_request=0xffffff47 fr.pc=0x00000001406fea78 relative=0x00000000000010f8
Event: 518.709 Thread 0x000000010f9e2600 DEOPT PACKING pc=0x00000001406fea78 sp=0x00000002a861de80
Event: 518.709 Thread 0x000000010f908a00 Uncommon trap: reason=unstable_if action=none pc=0x00000001406fea78 method=kotlinx.coroutines.DispatchedTaskKt.dispatch(Lkotlinx/coroutines/DispatchedTask;I)V @ 95 c2
Event: 518.709 Thread 0x000000010f9e2600 DEOPT UNPACKING pc=0x000000013f57701c sp=0x00000002a861dd00 mode 2
Event: 518.709 Thread 0x000000010f908a00 DEOPT PACKING pc=0x00000001406fea78 sp=0x000000028d3e5e80
Event: 518.709 Thread 0x000000010f908a00 DEOPT UNPACKING pc=0x000000013f57701c sp=0x000000028d3e5d00 mode 2
Event: 518.713 Thread 0x000000010f908a00 Uncommon trap: trap_request=0xffffff47 fr.pc=0x00000001406fea78 relative=0x00000000000010f8
Event: 518.713 Thread 0x000000010f908a00 Uncommon trap: reason=unstable_if action=none pc=0x00000001406fea78 method=kotlinx.coroutines.DispatchedTaskKt.dispatch(Lkotlinx/coroutines/DispatchedTask;I)V @ 95 c2
Event: 518.713 Thread 0x000000010f908a00 DEOPT PACKING pc=0x00000001406fea78 sp=0x000000028d3e5e80
Event: 518.713 Thread 0x000000010f908a00 DEOPT UNPACKING pc=0x000000013f57701c sp=0x000000028d3e5d00 mode 2
Event: 518.713 Thread 0x000000010f908a00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000001406f7a88 relative=0x00000000000000c8
Event: 518.713 Thread 0x000000010f908a00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000001406f7a88 method=kotlinx.coroutines.channels.BufferedChannel.isClosed(JZ)Z @ 12 c2
Event: 518.713 Thread 0x000000010f908a00 DEOPT PACKING pc=0x00000001406f7a88 sp=0x000000028d3e61b0
Event: 518.713 Thread 0x000000010f908a00 DEOPT UNPACKING pc=0x000000013f57701c sp=0x000000028d3e6100 mode 2
Event: 518.739 Thread 0x000000011e26be00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000000014002af38 relative=0x00000000000000f8
Event: 518.739 Thread 0x000000011e26be00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000000014002af38 method=javassist.bytecode.LongVector.elementAt(I)Ljavassist/bytecode/ConstInfo; @ 9 c2
Event: 518.739 Thread 0x000000011e26be00 DEOPT PACKING pc=0x000000014002af38 sp=0x000000028c385e00
Event: 518.739 Thread 0x000000011e26be00 DEOPT UNPACKING pc=0x000000013f57701c sp=0x000000028c385da0 mode 2

Classes loaded (20 events):
Event: 160.245 Loading class java/util/zip/GZIPInputStream
Event: 160.245 Loading class java/util/zip/GZIPInputStream done
Event: 160.245 Loading class java/io/CharArrayReader
Event: 160.246 Loading class java/io/CharArrayReader done
Event: 160.246 Loading class java/nio/charset/IllegalCharsetNameException
Event: 160.246 Loading class java/nio/charset/IllegalCharsetNameException done
Event: 160.246 Loading class java/util/regex/Pattern$Bound
Event: 160.246 Loading class java/util/regex/Pattern$Bound done
Event: 160.247 Loading class java/util/DualPivotQuicksort
Event: 160.247 Loading class java/util/DualPivotQuicksort done
Event: 160.260 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 160.260 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 248.357 Loading class javax/swing/JLabel
Event: 248.359 Loading class javax/swing/JLabel done
Event: 518.741 Loading class java/lang/Throwable$WrappedPrintStream
Event: 518.741 Loading class java/lang/Throwable$PrintStreamOrWriter
Event: 518.741 Loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 518.741 Loading class java/lang/Throwable$WrappedPrintStream done
Event: 518.808 Loading class java/lang/ClassFormatError
Event: 518.808 Loading class java/lang/ClassFormatError done

Classes unloaded (13 events):
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b8013bc400 'java/lang/invoke/LambdaForm$DMH+0x000000b8013bc400'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801384400 'java/lang/invoke/LambdaForm$DMH+0x000000b801384400'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801374c00 'java/lang/invoke/LambdaForm$DMH+0x000000b801374c00'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801374400 'java/lang/invoke/LambdaForm$DMH+0x000000b801374400'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b80133a000 'java/lang/invoke/LambdaForm$MH+0x000000b80133a000'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801339800 'java/lang/invoke/LambdaForm$MH+0x000000b801339800'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801339c00 'java/lang/invoke/LambdaForm$DMH+0x000000b801339c00'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801339000 'java/lang/invoke/LambdaForm$DMH+0x000000b801339000'
Event: 1.961 Thread 0x000000012d609040 Unloading class 0x000000b801338800 'java/lang/invoke/LambdaForm$DMH+0x000000b801338800'
Event: 184.226 Thread 0x000000012d609040 Unloading class 0x000000b801720400 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor20'
Event: 184.226 Thread 0x000000012d609040 Unloading class 0x000000b80171ac00 'jdk/internal/reflect/GeneratedSerializationConstructorAccessor19'
Event: 184.226 Thread 0x000000012d609040 Unloading class 0x000000b8016ba800 'java/lang/invoke/LambdaForm$DMH+0x000000b8016ba800'
Event: 184.226 Thread 0x000000012d609040 Unloading class 0x000000b8016b9800 'java/lang/invoke/LambdaForm$DMH+0x000000b8016b9800'

Classes redefined (20 events):
Event: 344.923 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$8$1, count=2
Event: 344.923 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$9$1, count=2
Event: 344.923 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$10$1, count=2
Event: 344.923 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$11$1, count=2
Event: 424.446 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt, count=3
Event: 424.446 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$12$1, count=1
Event: 424.446 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$10$1, count=3
Event: 439.545 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$7$1, count=2
Event: 439.545 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$10$1, count=4
Event: 439.545 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$12$1, count=2
Event: 439.545 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt, count=4
Event: 439.545 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$10$1$invokeSuspend$$inlined$collectEvent$1$1$1, count=1
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$10$1$invokeSuspend$$inlined$collectEvent$1$1, count=1
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$10$1$invokeSuspend$$inlined$collectEvent$1, count=1
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$11$1, count=3
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$9$1, count=3
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$8$1, count=3
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$3$1, count=2
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$4$1, count=2
Event: 439.546 Thread 0x000000012d609040 redefined class name=dima.telegram.app.TelegramAppKt$TelegramApp$6$1, count=2

Internal exceptions (20 events):
Event: 470.973 Thread 0x000000012ec1cc00 Exception <a 'java/net/ConnectException'{0x0000000714f244e0}> (0x0000000714f244e0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 473.982 Thread 0x000000012ecb9400 Exception <a 'java/net/ConnectException'{0x000000071397c650}> (0x000000071397c650) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 476.990 Thread 0x00000002a3f88000 Exception <a 'java/net/ConnectException'{0x000000071cf01cd0}> (0x000000071cf01cd0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 479.995 Thread 0x000000010f908a00 Exception <a 'java/net/ConnectException'{0x000000071df99a00}> (0x000000071df99a00) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 483.082 Thread 0x000000010f908a00 Exception <a 'java/net/ConnectException'{0x000000071df9a500}> (0x000000071df9a500) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 486.098 Thread 0x000000010f908a00 Exception <a 'java/net/ConnectException'{0x000000071df9b0b0}> (0x000000071df9b0b0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 489.107 Thread 0x000000011e82d200 Exception <a 'java/net/ConnectException'{0x000000071dfec4f0}> (0x000000071dfec4f0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 492.116 Thread 0x000000011e82d200 Exception <a 'java/net/ConnectException'{0x000000071dfedca8}> (0x000000071dfedca8) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 495.125 Thread 0x000000011e82d200 Exception <a 'java/net/ConnectException'{0x000000071dfef3d8}> (0x000000071dfef3d8) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 498.131 Thread 0x000000011e82d200 Exception <a 'java/net/ConnectException'{0x000000071dff0b08}> (0x000000071dff0b08) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 501.142 Thread 0x00000002a3f88000 Exception <a 'java/net/ConnectException'{0x000000071cf02828}> (0x000000071cf02828) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 504.147 Thread 0x00000002a3f88000 Exception <a 'java/net/ConnectException'{0x000000071cf03ed0}> (0x000000071cf03ed0) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 507.151 Thread 0x000000010f908a00 Exception <a 'java/net/ConnectException'{0x000000071df9c758}> (0x000000071df9c758) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 507.999 Thread 0x000000011e26be00 Exception <a 'java/lang/NoSuchMethodError'{0x0000000709be1ac8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x0000000709be1ac8) 
thrown [src/hotspot/share/interpreter/linkResolver.cpp, line 784]
Event: 509.174 Thread 0x000000011e26be00 Implicit null exception at 0x0000000140464084 to 0x000000014046416c
Event: 509.175 Thread 0x000000011e26be00 Implicit null exception at 0x000000013ff20284 to 0x000000013ff2036c
Event: 510.158 Thread 0x00000002a3f88000 Exception <a 'java/net/ConnectException'{0x00000007fff0d2f8}> (0x00000007fff0d2f8) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 513.166 Thread 0x000000011dfa7a00 Exception <a 'java/net/ConnectException'{0x000000071f890a80}> (0x000000071f890a80) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 516.172 Thread 0x000000010f9e2600 Exception <a 'java/net/ConnectException'{0x000000071f837908}> (0x000000071f837908) 
thrown [src/hotspot/share/prims/jni.cpp, line 535]
Event: 518.808 Thread 0x000000011e26be00 Exception <a 'java/lang/ClassFormatError'{0x000000071e1cb7c8}: Illegal field modifiers in class dima/globalState/AppState: 0x9> (0x000000071e1cb7c8) 
thrown [src/hotspot/share/classfile/classFileParser.cpp, line 4562]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 509.251 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 509.263 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 510.053 Executing VM operation: ICBufferFull
Event: 510.054 Executing VM operation: ICBufferFull done
Event: 511.055 Executing VM operation: Cleanup
Event: 511.055 Executing VM operation: Cleanup done
Event: 513.062 Executing VM operation: Cleanup
Event: 513.062 Executing VM operation: Cleanup done
Event: 514.067 Executing VM operation: Cleanup
Event: 514.068 Executing VM operation: Cleanup done
Event: 515.073 Executing VM operation: Cleanup
Event: 515.073 Executing VM operation: Cleanup done
Event: 516.078 Executing VM operation: Cleanup
Event: 516.078 Executing VM operation: Cleanup done
Event: 517.083 Executing VM operation: Cleanup
Event: 517.083 Executing VM operation: Cleanup done
Event: 518.088 Executing VM operation: Cleanup
Event: 518.088 Executing VM operation: Cleanup done
Event: 518.735 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 518.738 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (20 events):
Event: 456.470 Protecting memory [0x000000028a0c4000,0x000000028a0d0000] with protection modes 0
Event: 456.470 Protecting memory [0x000000028a2d0000,0x000000028a2dc000] with protection modes 0
Event: 456.470 Protecting memory [0x000000028a6e8000,0x000000028a6f4000] with protection modes 0
Event: 456.470 Protecting memory [0x000000028d1e4000,0x000000028d1f0000] with protection modes 0
Event: 456.470 Protecting memory [0x000000029b5d0000,0x000000029b5dc000] with protection modes 0
Event: 456.470 Protecting memory [0x000000029b7dc000,0x000000029b7e8000] with protection modes 0
Event: 456.471 Protecting memory [0x000000029c968000,0x000000029c974000] with protection modes 0
Event: 456.500 Protecting memory [0x000000029cb74000,0x000000029cb80000] with protection modes 0
Event: 456.501 Protecting memory [0x000000029d754000,0x000000029d760000] with protection modes 0
Event: 456.502 Protecting memory [0x000000029d960000,0x000000029d96c000] with protection modes 0
Event: 456.511 Protecting memory [0x000000029db6c000,0x000000029db78000] with protection modes 0
Event: 456.519 Protecting memory [0x00000002a1b04000,0x00000002a1b10000] with protection modes 0
Event: 456.533 Protecting memory [0x00000002a8004000,0x00000002a8010000] with protection modes 0
Event: 456.549 Protecting memory [0x00000002a8210000,0x00000002a821c000] with protection modes 0
Event: 456.570 Protecting memory [0x00000002a8628000,0x00000002a8634000] with protection modes 0
Event: 456.579 Protecting memory [0x00000002a8834000,0x00000002a8840000] with protection modes 0
Event: 516.115 Protecting memory [0x00000002aa904000,0x00000002aa910000] with protection modes 0
Event: 516.117 Protecting memory [0x00000002aab10000,0x00000002aab1c000] with protection modes 0
Event: 518.689 Protecting memory [0x00000002aad1c000,0x00000002aad28000] with protection modes 0
Event: 518.691 Protecting memory [0x00000002aaf28000,0x00000002aaf34000] with protection modes 0

Nmethod flushes (20 events):
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d7e790
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d7f190
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d7ff90
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d83390
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d83a10
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d85210
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d86010
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d86f10
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139d88310
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139df3a90
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139df3f10
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e1a690
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e1ad10
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e1b210
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e1b510
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e21f10
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e22290
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e22d10
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e23190
Event: 472.026 Thread 0x000000012d609040 flushing  nmethod 0x0000000139e23990

Events (20 events):
Event: 456.470 Thread 0x000000012ec1cc00 Thread added: 0x000000011e84d600
Event: 456.470 Thread 0x000000010f9e2600 Thread added: 0x000000010f9e5600
Event: 456.470 Thread 0x000000010f964800 Thread added: 0x000000012ea4cc00
Event: 456.470 Thread 0x00000002a2259400 Thread added: 0x000000010f908a00
Event: 456.470 Thread 0x000000011dfa7a00 Thread added: 0x000000011ca1b200
Event: 456.470 Thread 0x000000011e8b1800 Thread added: 0x00000002a3f88000
Event: 456.471 Thread 0x000000011e26be00 Thread added: 0x00000002a261ea00
Event: 456.500 Thread 0x00000002a3f88000 Thread added: 0x000000015e3cd200
Event: 456.501 Thread 0x000000012ea4cc00 Thread added: 0x00000002a2238800
Event: 456.501 Thread 0x000000011e26be00 Thread added: 0x00000002a224b000
Event: 456.506 Thread 0x000000011ca1b200 Thread added: 0x00000002a39ebe00
Event: 456.515 Thread 0x000000011e82d200 Thread added: 0x000000010f9fc400
Event: 456.532 Thread 0x000000011e84d600 Thread added: 0x000000011ca0c600
Event: 456.546 Thread 0x00000002a2238800 Thread added: 0x00000002a3f5d400
Event: 456.558 Thread 0x000000015e3cd200 Thread added: 0x000000011f192a00
Event: 456.579 Thread 0x000000010f908a00 Thread added: 0x000000011cb1c200
Event: 516.115 Thread 0x000000012d95c400 Thread added: 0x000000011cb67600
Event: 516.117 Thread 0x000000012d95cc00 Thread added: 0x000000012deb2c00
Event: 518.688 Thread 0x000000012d95c400 Thread added: 0x00000002aa2ffc00
Event: 518.690 Thread 0x000000012d95cc00 Thread added: 0x00000002aa79ae00


Dynamic libraries:
0x0000000104f30000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjli.dylib
0x00000001a3765000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x000000018d443000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x0000000190467000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x000000018ad4f000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00000001963e9000 	/usr/lib/libSystem.B.dylib
0x000000018e754000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x00000001fe6e8000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x000000019d4ee000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00000001944a2000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00000001989ca000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x0000000198a51000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x0000000220931000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x000000018a9d9000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x0000000197e72000 	/usr/lib/libspindump.dylib
0x000000018e908000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x0000000192403000 	/usr/lib/libapp_launch_measurement.dylib
0x000000019181b000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000019240a000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x0000000193cd5000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x0000000194c0b000 	/usr/lib/liblangid.dylib
0x00000001944a8000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x000000018f2c3000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000018f753000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x000000019dc35000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x0000000197cd1000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x0000000193cb3000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000019184a000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x000000019632d000 	/usr/lib/libz.1.dylib
0x00000001a12f8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000019448a000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x000000018ccb0000 	/usr/lib/libicucore.A.dylib
0x0000000199a1e000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00000001989d6000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001b17d4000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x000000018f216000 	/usr/lib/libMobileGestalt.dylib
0x00000001941ca000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x0000000191d1c000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x000000018c8ef000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x000000019d52e000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x0000000192133000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x000000018c202000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000019192e000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00000001982c4000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x000000018f214000 	/usr/lib/libenergytrace.dylib
0x00000001a767b000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x000000018d2fa000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x000000019d932000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x0000000192390000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001f1838000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x0000000192454000 	/usr/lib/libxml2.2.dylib
0x0000000195832000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00000001897f8000 	/usr/lib/libobjc.A.dylib
0x0000000189adf000 	/usr/lib/libc++.1.dylib
0x000000019d8af000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000018fe30000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x0000000189c26000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00000001947f8000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x000000018c00e000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001f485e000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001f4d40000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x00000001944e1000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001f912a000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001963ef000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001993f1000 	/usr/lib/swift/libswiftCore.dylib
0x00000001ae5af000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001ac38f000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001ae5fb000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001ac396000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000019ef8f000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001ae5fc000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001b9e5c000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001c66f1000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001a1760000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x00000001bdaab000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001c1aa5000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001ae5c5000 	/usr/lib/swift/libswiftXPC.dylib
0x0000000228c5f000 	/usr/lib/swift/libswift_Concurrency.dylib
0x00000001a1764000 	/usr/lib/swift/libswiftos.dylib
0x00000001b1738000 	/usr/lib/swift/libswiftsimd.dylib
0x000000019659b000 	/usr/lib/libcompression.dylib
0x0000000198924000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x0000000197984000 	/usr/lib/libate.dylib
0x00000001963e3000 	/usr/lib/system/libcache.dylib
0x000000019639f000 	/usr/lib/system/libcommonCrypto.dylib
0x00000001963ca000 	/usr/lib/system/libcompiler_rt.dylib
0x00000001963c0000 	/usr/lib/system/libcopyfile.dylib
0x0000000189942000 	/usr/lib/system/libcorecrypto.dylib
0x0000000189a16000 	/usr/lib/system/libdispatch.dylib
0x0000000189bcd000 	/usr/lib/system/libdyld.dylib
0x00000001963d9000 	/usr/lib/system/libkeymgr.dylib
0x0000000196377000 	/usr/lib/system/libmacho.dylib
0x0000000195917000 	/usr/lib/system/libquarantine.dylib
0x00000001963d6000 	/usr/lib/system/libremovefile.dylib
0x000000018f28a000 	/usr/lib/system/libsystem_asl.dylib
0x00000001898db000 	/usr/lib/system/libsystem_blocks.dylib
0x0000000189a60000 	/usr/lib/system/libsystem_c.dylib
0x00000001963ce000 	/usr/lib/system/libsystem_collections.dylib
0x0000000194bf9000 	/usr/lib/system/libsystem_configuration.dylib
0x0000000193c89000 	/usr/lib/system/libsystem_containermanager.dylib
0x000000019601c000 	/usr/lib/system/libsystem_coreservices.dylib
0x000000018cf69000 	/usr/lib/system/libsystem_darwin.dylib
0x0000000228f87000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00000001963da000 	/usr/lib/system/libsystem_dnssd.dylib
0x0000000189a5d000 	/usr/lib/system/libsystem_featureflags.dylib
0x0000000189bf9000 	/usr/lib/system/libsystem_info.dylib
0x000000019633c000 	/usr/lib/system/libsystem_m.dylib
0x00000001899df000 	/usr/lib/system/libsystem_malloc.dylib
0x000000018f1fa000 	/usr/lib/system/libsystem_networkextension.dylib
0x000000018d3db000 	/usr/lib/system/libsystem_notify.dylib
0x0000000194bfe000 	/usr/lib/system/libsystem_sandbox.dylib
0x00000001963d3000 	/usr/lib/system/libsystem_secinit.dylib
0x0000000189b85000 	/usr/lib/system/libsystem_kernel.dylib
0x0000000189bf2000 	/usr/lib/system/libsystem_platform.dylib
0x0000000189bc0000 	/usr/lib/system/libsystem_pthread.dylib
0x0000000190c3d000 	/usr/lib/system/libsystem_symptoms.dylib
0x0000000189927000 	/usr/lib/system/libsystem_trace.dylib
0x00000001963ad000 	/usr/lib/system/libunwind.dylib
0x00000001898e0000 	/usr/lib/system/libxpc.dylib
0x0000000189b6d000 	/usr/lib/libc++abi.dylib
0x00000001963b8000 	/usr/lib/liboah.dylib
0x0000000197837000 	/usr/lib/liblzma.5.dylib
0x00000001963eb000 	/usr/lib/libfakelink.dylib
0x000000018ee29000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000019643e000 	/usr/lib/libarchive.2.dylib
0x000000019b831000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00000001fe6fc000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x00000002140ee000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x00000002146dc000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x0000000228da8000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000018d279000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x000000019593e000 	/usr/lib/libbsm.0.dylib
0x000000019637f000 	/usr/lib/system/libkxld.dylib
0x00000001923cc000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x000000018cf74000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x0000000191893000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x0000000196022000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00000001964c7000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x0000000190bbf000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x000000018a0fd000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00000001977e0000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00000001923d9000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x0000000196566000 	/usr/lib/libapple_nghttp2.dylib
0x0000000190856000 	/usr/lib/libsqlite3.dylib
0x0000000190c46000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x0000000227715000 	/usr/lib/libCoreEntitlements.dylib
0x000000020f677000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000019083c000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x0000000195f4b000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x0000000195926000 	/usr/lib/libcoretls.dylib
0x0000000197850000 	/usr/lib/libcoretls_cfhelpers.dylib
0x0000000196595000 	/usr/lib/libpam.2.dylib
0x00000001978c1000 	/usr/lib/libxar.1.dylib
0x0000000197ca8000 	/usr/lib/libheimdal-asn1.dylib
0x000000018ee28000 	/usr/lib/libnetwork.dylib
0x00000001963f0000 	/usr/lib/libpcap.A.dylib
0x0000000190c33000 	/usr/lib/libdns_services.dylib
0x0000000228cfc000 	/usr/lib/swift/libswift_RegexParser.dylib
0x0000000194c06000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x000000019562b000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x000000019600f000 	/usr/lib/libbz2.1.0.dylib
0x000000019591a000 	/usr/lib/libCheckFix.dylib
0x000000018f2a2000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x0000000194c0d000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x000000019184c000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x0000000195950000 	/usr/lib/libmecab.dylib
0x000000018aa6c000 	/usr/lib/libCRFSuite.dylib
0x00000001959ac000 	/usr/lib/libgermantok.dylib
0x000000019653e000 	/usr/lib/libThaiTokenizer.dylib
0x0000000191937000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x000000019d909000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x0000000197908000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x0000000195507000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x000000018a4fb000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x000000019666e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00000001959af000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x0000000196580000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x0000000196669000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x0000000194d2f000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x000000018a972000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x000000020deb6000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x0000000196425000 	/usr/lib/libiconv.2.dylib
0x0000000196373000 	/usr/lib/libcharset.1.dylib
0x00000001923ac000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x000000019239c000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x0000000197852000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x0000000195858000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00000001978d0000 	/usr/lib/libutil.dylib
0x000000020c1a4000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x000000018d2b9000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001fc31b000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x00000001ae58e000 	/usr/lib/libmis.dylib
0x00000001bdf6e000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00000001eec67000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x0000000196540000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x000000018b9a2000 	/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling
0x00000001978d4000 	/usr/lib/libxslt.1.dylib
0x000000019642c000 	/usr/lib/libcmph.dylib
0x00000001955f8000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x0000000194d29000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000018a885000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00000001958e6000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000002278fe000 	/usr/lib/libTLE.dylib
0x000000019818a000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x0000000197c8d000 	/usr/lib/libexpat.1.dylib
0x0000000198783000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00000001987ae000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x0000000198899000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00000001981d0000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x000000019883e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x0000000198835000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x0000000194065000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x0000000190b5d000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00000001a3f6d000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00000001982c0000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000018bb83000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x0000000193f23000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x0000000193ccb000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000019253d000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x0000000196593000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x0000000198303000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00000001925d8000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x0000000190a8d000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x0000000194c04000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00000001fc2a3000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x0000000198830000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x0000000198810000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x0000000198838000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000002082b1000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x00000001f182b000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x0000000204f91000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x000000019889f000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001f4e42000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x000000019f7ca000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x0000000197e5d000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x000000019a39c000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x000000018bca7000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x0000000193ef9000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x0000000199c18000 	/usr/lib/libAudioStatistics.dylib
0x00000001ad931000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x0000000199eef000 	/usr/lib/libSMC.dylib
0x00000001a35f4000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x000000019874a000 	/usr/lib/libAudioToolboxUtility.dylib
0x00000001a92fc000 	/System/Library/PrivateFrameworks/OSAServicesClient.framework/Versions/A/OSAServicesClient
0x000000019a3aa000 	/usr/lib/libperfcheck.dylib
0x0000000197b77000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x000000019584a000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001a2728000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x00000001c99cb000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x0000000213183000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x0000000197e0f000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001f188d000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001f184c000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001f1a25000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001f1855000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001f1849000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000002278b7000 	/usr/lib/libRosetta.dylib
0x00000001f1832000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00000002031b4000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x0000000197e1b000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00000001920e1000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x0000000197e67000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x00000002031b5000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x0000000194b7c000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x0000000195ebe000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00000001959c4000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x0000000195db2000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x0000000195bbe000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x0000000195de1000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001f69c8000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001f69aa000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x000000018a3b0000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001b2905000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001bde7b000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001ae5a2000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x000000019f6ba000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001ae55f000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x0000000197a1e000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x000000019f675000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x00000001bd1d3000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x000000021ee8c000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x0000000197c41000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x0000000199c60000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000018ffdf000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00000001988ac000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x000000019a048000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x000000019a03c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x0000000199c30000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x0000000198869000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x0000000199fcf000 	/usr/lib/libcups.2.dylib
0x000000019a3b8000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x000000019a3c9000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x0000000199cdf000 	/usr/lib/libresolv.9.dylib
0x0000000197e78000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001a16cd000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x000000019a423000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00000001f3c0c000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001a934b000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x0000000199b9c000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x000000019b5d1000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x0000000197d4b000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x00000001999e0000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x000000019a054000 	/System/Library/PrivateFrameworks/AudioResourceArbitration.framework/Versions/A/AudioResourceArbitration
0x000000019e654000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x000000019e577000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001a16ce000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00000001956b7000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x000000020180b000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x000000020c037000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x000000019f1f0000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x00000001a0cba000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000019f2a9000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x0000000197cb3000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x000000019d51a000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x00000001a9366000 	/usr/lib/libAccessibility.dylib
0x000000019dc81000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001ccd67000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001ae4ef000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x0000000106140000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/server/libjvm.dylib
0x0000000104ad4000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjimage.dylib
0x0000000104b00000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libinstrument.dylib
0x0000000104b48000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjava.dylib
0x0000000104db8000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libzip.dylib
0x0000000104de0000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libnio.dylib
0x0000000104e00000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libnet.dylib
0x0000000104b2c000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libextnet.dylib
0x0000000104e38000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libverify.dylib
0x000000010605c000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libawt.dylib
0x000000011bf78000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libmlib_image.dylib
0x00000001f59fe000 	/System/Library/Frameworks/JavaRuntimeSupport.framework/Versions/A/JavaRuntimeSupport
0x00000001ad2d4000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Carbon
0x00000001a56f8000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/CommonPanels.framework/Versions/A/CommonPanels
0x00000001a0ff1000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Help.framework/Versions/A/Help
0x00000001a56fc000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/ImageCapture.framework/Versions/A/ImageCapture
0x00000001a56d0000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/OpenScripting.framework/Versions/A/OpenScripting
0x00000001a56f4000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink
0x00000001a56f0000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SecurityHI.framework/Versions/A/SecurityHI
0x000000011c394000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libawt_lwawt.dylib
0x0000000104e8c000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libosxapp.dylib
0x00000001ac40b000 	/System/Library/Frameworks/ExceptionHandling.framework/Versions/A/ExceptionHandling
0x000000011c530000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libfontmanager.dylib
0x000000011c2a0000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libfreetype.dylib
0x000000019e899000 	/System/Library/Frameworks/FileProvider.framework/Versions/A/FileProvider
0x00000001909f3000 	/System/Library/Frameworks/Accounts.framework/Versions/A/Accounts
0x000000019ea3f000 	/System/Library/PrivateFrameworks/GenerationalStorage.framework/Versions/A/GenerationalStorage
0x000000019e648000 	/System/Library/PrivateFrameworks/SymptomDiagnosticReporter.framework/Versions/A/SymptomDiagnosticReporter
0x000000018e91e000 	/System/Library/PrivateFrameworks/DesktopServicesPriv.framework/Versions/A/DesktopServicesPriv
0x000000018d3ec000 	/usr/lib/libsandbox.1.dylib
0x000000019a02f000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000019b25a000 	/System/Library/PrivateFrameworks/Sharing.framework/Versions/A/Sharing
0x000000019259b000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00000001a0af4000 	/System/Library/PrivateFrameworks/Apple80211.framework/Versions/A/Apple80211
0x0000000196054000 	/System/Library/PrivateFrameworks/AuthKit.framework/Versions/A/AuthKit
0x000000019563b000 	/System/Library/Frameworks/CoreWLAN.framework/Versions/A/CoreWLAN
0x00000001ae5a6000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x00000001b360a000 	/usr/lib/swift/libswiftCoreLocation.dylib
0x0000000197a15000 	/usr/lib/libIOReport.dylib
0x00000001a0af8000 	/System/Library/PrivateFrameworks/CoreWiFi.framework/Versions/A/CoreWiFi
0x00000001d3a69000 	/System/Library/PrivateFrameworks/WiFiPeerToPeer.framework/Versions/A/WiFiPeerToPeer
0x00000001961e9000 	/System/Library/Frameworks/UserNotifications.framework/Versions/A/UserNotifications
0x000000019a557000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x0000000199d0c000 	/System/Library/PrivateFrameworks/MultiverseSupport.framework/Versions/A/MultiverseSupport
0x00000001a0f47000 	/System/Library/PrivateFrameworks/RemoteServiceDiscovery.framework/Versions/A/RemoteServiceDiscovery
0x000000019e775000 	/System/Library/PrivateFrameworks/DiskManagement.framework/Versions/A/DiskManagement
0x000000019e67c000 	/System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/Versions/A/AppleIDAuthSupport
0x00000001fa40b000 	/System/Library/PrivateFrameworks/AAAFoundation.framework/Versions/A/AAAFoundation
0x000000019a52f000 	/System/Library/PrivateFrameworks/KeychainCircle.framework/Versions/A/KeychainCircle
0x00000001a0f5b000 	/System/Library/PrivateFrameworks/RemoteXPC.framework/Versions/A/RemoteXPC
0x00000001a0ffd000 	/usr/lib/libcsfde.dylib
0x0000000197bbd000 	/usr/lib/libCoreStorage.dylib
0x000000019e741000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x000000019e689000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x000000019b6b0000 	/System/Library/PrivateFrameworks/ProtectedCloudStorage.framework/Versions/A/ProtectedCloudStorage
0x00000001a0ff5000 	/System/Library/PrivateFrameworks/EFILogin.framework/Versions/A/EFILogin
0x000000019ea67000 	/System/Library/PrivateFrameworks/OctagonTrust.framework/Versions/A/OctagonTrust
0x0000000193b7b000 	/System/Library/Frameworks/CoreLocation.framework/Versions/A/CoreLocation
0x000000019b96e000 	/System/Library/PrivateFrameworks/GeoServices.framework/Versions/A/GeoServices
0x00000001999aa000 	/System/Library/PrivateFrameworks/LocationSupport.framework/Versions/A/LocationSupport
0x0000000208fee000 	/System/Library/PrivateFrameworks/GeoServicesCore.framework/Versions/A/GeoServicesCore
0x00000001a9340000 	/System/Library/PrivateFrameworks/PhoneNumbers.framework/Versions/A/PhoneNumbers
0x0000000197bb5000 	/usr/lib/libMatch.1.dylib
0x00000001b6dbf000 	/System/Library/CoreServices/RawCamera.bundle/Contents/MacOS/RawCamera
0x000000019a436000 	/System/Library/PrivateFrameworks/MobileAsset.framework/Versions/A/MobileAsset
0x00000001fc0e3000 	/System/Library/PrivateFrameworks/AppleJPEGXL.framework/Versions/A/AppleJPEGXL
0x00000001b3f7e000 	/System/Library/PrivateFrameworks/SoftwareUpdateCoreSupport.framework/Versions/A/SoftwareUpdateCoreSupport
0x00000001bb2f3000 	/System/Library/PrivateFrameworks/SoftwareUpdateCoreConnect.framework/Versions/A/SoftwareUpdateCoreConnect
0x00000001a16f4000 	/System/Library/PrivateFrameworks/StreamingZip.framework/Versions/A/StreamingZip
0x00000001b3c3a000 	/System/Library/PrivateFrameworks/MSUDataAccessor.framework/Versions/A/MSUDataAccessor
0x00000001aff65000 	/usr/lib/libbootpolicy.dylib
0x00000001c1c8d000 	/usr/lib/libpartition2_dynamic.dylib
0x00000001fd679000 	/System/Library/PrivateFrameworks/CMPhoto.framework/Versions/A/CMPhoto
0x0000000198abd000 	/System/Library/Frameworks/MediaToolbox.framework/Versions/A/MediaToolbox
0x000000019e865000 	/System/Library/PrivateFrameworks/CoreAVCHD.framework/Versions/A/CoreAVCHD
0x000000019e861000 	/System/Library/PrivateFrameworks/Mangrove.framework/Versions/A/Mangrove
0x000000019ee64000 	/System/Library/Frameworks/CoreTelephony.framework/Versions/A/CoreTelephony
0x000000019e855000 	/System/Library/PrivateFrameworks/CoreAUC.framework/Versions/A/CoreAUC
0x000000019a561000 	/usr/lib/libTelephonyUtilDynamic.dylib
0x00000001b35f0000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/Libraries/libCGInterfaces.dylib
0x000000011c6e8000 	/usr/lib/libobjc-trampolines.dylib
0x00000002275de000 	/usr/lib/libAccessibilityBaseImplementations.dylib
0x0000000197f48000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/login
0x000000019f3bf000 	/System/Library/Frameworks/LocalAuthentication.framework/Versions/A/LocalAuthentication
0x000000019f405000 	/System/Library/Frameworks/LocalAuthentication.framework/Support/SharedUtils.framework/Versions/A/SharedUtils
0x000000019f35c000 	/System/Library/Frameworks/CryptoTokenKit.framework/Versions/A/CryptoTokenKit
0x0000000192280000 	/System/Library/PrivateFrameworks/ViewBridge.framework/Versions/A/ViewBridge
0x00000001f1c94000 	/System/Library/Extensions/AGXMetal13_3.bundle/Contents/MacOS/AGXMetal13_3
0x00000001a82b7000 	/System/Library/PrivateFrameworks/IOGPU.framework/Versions/A/IOGPU
0x000000015dd08000 	/private/var/folders/p4/5q8rp4gn70g3q_y9788vy6700000gn/T/sqlite-3.44.1.0-c9a1cf5e-599b-416b-acfb-4c8e0d236537-libsqlitejdbc.dylib
0x000000012ff34000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libmanagement.dylib
0x000000015f208000 	/Users/<USER>/.skiko/4e94e1f84e9425da38ae4a4c905cf7dd710053fb4fed13948115962b20bf3356/libskiko-macos-arm64.dylib
0x0000000199393000 	/usr/lib/libusrtcp.dylib
0x000000012ff48000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libmanagement_ext.dylib
0x000000018ed5f000 	/usr/lib/libboringssl.dylib
0x000000012ffc4000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libosxui.dylib
0x0000000160ce8000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjawt.dylib
0x000000021b724000 	/System/Library/PrivateFrameworks/TextInputUI.framework/Versions/A/TextInputUI
0x000000021b72c000 	/System/Library/PrivateFrameworks/TextInputUIMacHelper.framework/Versions/A/TextInputUIMacHelper
0x0000000167054000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/libjavajpeg.dylib
0x00000001facaa000 	/System/Library/PrivateFrameworks/AccessibilityBundles.framework/Versions/A/AccessibilityBundles
0x000000029b9e4000 	/Users/<USER>/.gradle/jdks/jetbrains_s_r_o_-21-aarch64-os_x.2/jbrsdk_jcef-21.0.6-osx-aarch64-b895.109/Contents/Home/lib/liblcms.dylib
0x00000001eea56000 	/System/Library/PrivateFrameworks/VoiceShortcutClient.framework/Versions/A/VoiceShortcutClient
0x00000001a04d7000 	/System/Library/Frameworks/Intents.framework/Versions/A/Intents
0x000000018eb36000 	/System/Library/PrivateFrameworks/CoreDuet.framework/Versions/A/CoreDuet
0x00000001a0482000 	/System/Library/PrivateFrameworks/CoreDuetContext.framework/Versions/A/CoreDuetContext
0x0000000196233000 	/System/Library/Frameworks/CoreSpotlight.framework/Versions/A/CoreSpotlight
0x00000001a177d000 	/System/Library/PrivateFrameworks/IntentsFoundation.framework/Versions/A/IntentsFoundation
0x00000001a1087000 	/System/Library/PrivateFrameworks/AppSupport.framework/Versions/A/AppSupport
0x00000001f3d37000 	/System/Library/Frameworks/AppIntents.framework/Versions/A/AppIntents
0x00000001b2987000 	/System/Library/PrivateFrameworks/IntentsCore.framework/Versions/A/IntentsCore
0x000000020dc76000 	/System/Library/PrivateFrameworks/LinkServices.framework/Versions/A/LinkServices
0x00000001bf593000 	/System/Library/PrivateFrameworks/BiomePubSub.framework/Versions/A/BiomePubSub
0x000000020db18000 	/System/Library/PrivateFrameworks/LinkMetadata.framework/Versions/A/LinkMetadata
0x00000001c1ab4000 	/usr/lib/swift/libswiftAccelerate.dylib
0x0000000228b3a000 	/usr/lib/swift/libswiftFileProvider.dylib
0x00000001ae5b6000 	/usr/lib/swift/libswiftIntents.dylib
0x000000019259e000 	/System/Library/PrivateFrameworks/ApplePushService.framework/Versions/A/ApplePushService
0x0000000192601000 	/System/Library/Frameworks/CloudKit.framework/Versions/A/CloudKit
0x00000001a2727000 	/System/Library/PrivateFrameworks/CoreDuetDaemonProtocol.framework/Versions/A/CoreDuetDaemonProtocol
0x00000001a25b3000 	/System/Library/PrivateFrameworks/ProactiveSupport.framework/Versions/A/ProactiveSupport
0x000000019f7d4000 	/System/Library/PrivateFrameworks/Rapport.framework/Versions/A/Rapport
0x00000001bf5f1000 	/System/Library/PrivateFrameworks/BiomeStorage.framework/Versions/A/BiomeStorage
0x00000001b7caf000 	/System/Library/PrivateFrameworks/BiomeFoundation.framework/Versions/A/BiomeFoundation
0x00000001a12f9000 	/System/Library/PrivateFrameworks/InternationalTextSearch.framework/Versions/A/InternationalTextSearch
0x0000000190a77000 	/System/Library/PrivateFrameworks/CommonUtilities.framework/Versions/A/CommonUtilities
0x0000000198114000 	/System/Library/PrivateFrameworks/Bom.framework/Versions/A/Bom
0x0000000199d1c000 	/usr/lib/libParallelCompression.dylib
0x00000002275e6000 	/usr/lib/libAppleArchive.dylib
0x00000001f8e37000 	/System/Library/Frameworks/SharedWithYouCore.framework/Versions/A/SharedWithYouCore
0x00000002276fd000 	/usr/lib/libBASupport.dylib
0x00000001a13bc000 	/System/Library/PrivateFrameworks/AppleAccount.framework/Versions/A/AppleAccount
0x00000001ad739000 	/System/Library/Frameworks/AVFoundation.framework/Versions/A/AVFoundation
0x00000001a1797000 	/System/Library/PrivateFrameworks/C2.framework/Versions/A/C2
0x00000001a178e000 	/System/Library/Frameworks/PushKit.framework/Versions/A/PushKit
0x00000001ceec5000 	/System/Library/Frameworks/CryptoKit.framework/Versions/A/CryptoKit
0x00000001d39d7000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x00000001c69a0000 	/usr/lib/swift/libswiftAVFoundation.dylib
0x00000001b361f000 	/usr/lib/swift/libswiftCoreMIDI.dylib
0x00000001c521b000 	/usr/lib/swift/libswiftCoreMedia.dylib
0x000000019ae67000 	/System/Library/PrivateFrameworks/IDS.framework/Versions/A/IDS
0x00000001a1525000 	/System/Library/PrivateFrameworks/AppleIDSSOAuthentication.framework/Versions/A/AppleIDSSOAuthentication
0x0000000199931000 	/System/Library/PrivateFrameworks/IMFoundation.framework/Versions/A/IMFoundation
0x00000001a8a70000 	/System/Library/PrivateFrameworks/Marco.framework/Versions/A/Marco
0x000000019aff7000 	/System/Library/PrivateFrameworks/IDSFoundation.framework/Versions/A/IDSFoundation
0x00000001ae12a000 	/System/Library/PrivateFrameworks/FTServices.framework/Versions/A/FTServices
0x00000001a56b8000 	/System/Library/PrivateFrameworks/Engram.framework/Versions/A/Engram
0x00000001a5659000 	/usr/lib/libtidy.A.dylib
0x00000001ba520000 	/System/Library/PrivateFrameworks/FTAWD.framework/Versions/A/FTAWD
0x000000019efa8000 	/System/Library/PrivateFrameworks/AVFCore.framework/Versions/A/AVFCore
0x00000001a6ec8000 	/System/Library/PrivateFrameworks/AVFCapture.framework/Versions/A/AVFCapture
0x00000001fd60f000 	/System/Library/PrivateFrameworks/CMImaging.framework/Versions/A/CMImaging
0x00000001a709b000 	/System/Library/PrivateFrameworks/Quagga.framework/Versions/A/Quagga
0x00000001a71a3000 	/System/Library/PrivateFrameworks/CMCapture.framework/Versions/A/CMCapture
0x0000000199df1000 	/System/Library/Frameworks/CoreMediaIO.framework/Versions/A/CoreMediaIO
0x00000001a182d000 	/System/Library/PrivateFrameworks/Espresso.framework/Versions/A/Espresso
0x00000001ef2e9000 	/System/Library/PrivateFrameworks/ANECompiler.framework/Versions/A/ANECompiler
0x00000001a34c0000 	/System/Library/PrivateFrameworks/AppleNeuralEngine.framework/Versions/A/AppleNeuralEngine
0x00000001f69dc000 	/System/Library/Frameworks/MetalPerformanceShadersGraph.framework/Versions/A/MetalPerformanceShadersGraph
0x000000020e533000 	/System/Library/PrivateFrameworks/MLCompilerServices.framework/Versions/A/MLCompilerServices
0x00000001a258f000 	/System/Library/PrivateFrameworks/ANEServices.framework/Versions/A/ANEServices
0x00000001b28d1000 	/usr/lib/libncurses.5.4.dylib
0x000000020e4cf000 	/System/Library/PrivateFrameworks/MLCompilerRuntime.framework/Versions/A/MLCompilerRuntime
0x00000001a3271000 	/System/Library/PrivateFrameworks/RTCReporting.framework/Versions/A/RTCReporting
0x00000001a16c7000 	/System/Library/PrivateFrameworks/CryptoKitCBridging.framework/Versions/A/CryptoKitCBridging
0x00000001c5219000 	/usr/lib/swift/libswiftCryptoTokenKit.dylib
0x0000000201d60000 	/System/Library/PrivateFrameworks/DoNotDisturb.framework/Versions/A/DoNotDisturb
0x00000001ba97d000 	/System/Library/PrivateFrameworks/BiomeStreams.framework/Versions/A/BiomeStreams
0x000000020d79f000 	/System/Library/PrivateFrameworks/Koa.framework/Versions/A/Koa
0x00000001fce3e000 	/System/Library/PrivateFrameworks/BiomeSync.framework/Versions/A/BiomeSync
0x00000001fca9d000 	/System/Library/PrivateFrameworks/BiomeFlexibleStorage.framework/Versions/A/BiomeFlexibleStorage
0x00000001fca82000 	/System/Library/PrivateFrameworks/BiomeDSL.framework/Versions/A/BiomeDSL
0x00000001fce23000 	/System/Library/PrivateFrameworks/BiomeSequence.framework/Versions/A/BiomeSequence
0x00000001ccf8f000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x0000000228866000 	/usr/lib/libskit.dylib
0x00000001b261d000 	/usr/lib/libmorphun.dylib
0x000000020fca6000 	/System/Library/PrivateFrameworks/MorphunAssets.framework/Versions/A/MorphunAssets
0x00000001aedec000 	/System/Library/PrivateFrameworks/Trial.framework/Versions/A/Trial
0x000000022831e000 	/usr/lib/libmarisa.dylib
0x000000019f882000 	/System/Library/PrivateFrameworks/EmbeddedAcousticRecognition.framework/Versions/A/EmbeddedAcousticRecognition
0x0000000192a1f000 	/System/Library/Frameworks/CoreML.framework/Versions/A/CoreML
0x00000001ae242000 	/System/Library/Frameworks/NaturalLanguage.framework/Versions/A/NaturalLanguage
0x00000001b611a000 	/System/Library/PrivateFrameworks/EmojiFoundation.framework/Versions/A/EmojiFoundation
0x00000001bd5a6000 	/System/Library/PrivateFrameworks/SDAPI.framework/Versions/A/SDAPI
0x0000000195c15000 	/System/Library/Frameworks/MLCompute.framework/Versions/A/MLCompute
0x000000020e38b000 	/System/Library/PrivateFrameworks/MLAssetIO.framework/Versions/A/MLAssetIO
0x0000000190146000 	/System/Library/PrivateFrameworks/Montreal.framework/Versions/A/Montreal
0x00000001b6d07000 	/System/Library/PrivateFrameworks/DifferentialPrivacy.framework/Versions/A/DifferentialPrivacy
0x00000001fcabc000 	/System/Library/PrivateFrameworks/BiomeLibrary.framework/Versions/A/BiomeLibrary
0x000000021df39000 	/System/Library/PrivateFrameworks/UnifiedAssetFramework.framework/Versions/A/UnifiedAssetFramework
0x00000001a454f000 	/System/Library/PrivateFrameworks/AssistantServices.framework/Versions/A/AssistantServices
0x00000002150f1000 	/System/Library/PrivateFrameworks/SiriAnalytics.framework/Versions/A/SiriAnalytics
0x00000001b2d6e000 	/System/Library/PrivateFrameworks/SiriInstrumentation.framework/Versions/A/SiriInstrumentation
0x00000001aed6c000 	/System/Library/PrivateFrameworks/TrialProto.framework/Versions/A/TrialProto
0x00000001b102f000 	/System/Library/PrivateFrameworks/FeedbackLogger.framework/Versions/A/FeedbackLogger
0x00000001fc07f000 	/System/Library/PrivateFrameworks/AppleFlatBuffers.framework/Versions/A/AppleFlatBuffers
0x00000001a8ddd000 	/System/Library/PrivateFrameworks/ProactiveEventTracker.framework/Versions/A/ProactiveEventTracker
0x000000019f5b6000 	/System/Library/PrivateFrameworks/SAObjects.framework/Versions/A/SAObjects
0x00000001a3fad000 	/System/Library/PrivateFrameworks/MediaRemote.framework/Versions/A/MediaRemote
0x00000002191e3000 	/System/Library/PrivateFrameworks/SiriTTSService.framework/Versions/A/SiriTTSService
0x000000021586c000 	/System/Library/PrivateFrameworks/SiriCrossDeviceArbitrationFeedback.framework/Versions/A/SiriCrossDeviceArbitrationFeedback
0x00000001a88d6000 	/System/Library/PrivateFrameworks/MediaServices.framework/Versions/A/MediaServices
0x0000000197de4000 	/System/Library/PrivateFrameworks/PersistentConnection.framework/Versions/A/PersistentConnection
0x00000001b42e1000 	/usr/lib/libtailspin.dylib
0x00000001c4d1f000 	/System/Library/PrivateFrameworks/Osprey.framework/Versions/A/Osprey
0x00000002188d0000 	/System/Library/PrivateFrameworks/SiriTTS.framework/Versions/A/SiriTTS
0x000000019f348000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x0000000217d20000 	/System/Library/PrivateFrameworks/SiriPowerInstrumentation.framework/Versions/A/SiriPowerInstrumentation
0x00000001d39e4000 	/usr/lib/swift/libswiftNaturalLanguage.dylib
0x000000021b440000 	/System/Library/PrivateFrameworks/TailspinSymbolication.framework/Versions/A/TailspinSymbolication
0x000000020196b000 	/System/Library/PrivateFrameworks/Darwinup.framework/Versions/A/Darwinup
0x00000001be6b1000 	/System/Library/PrivateFrameworks/SignpostSupport.framework/Versions/A/SignpostSupport
0x00000001ae111000 	/System/Library/PrivateFrameworks/FeatureFlagsSupport.framework/Versions/A/FeatureFlagsSupport
0x00000001bde81000 	/System/Library/PrivateFrameworks/ktrace.framework/Versions/A/ktrace
0x00000001be907000 	/System/Library/PrivateFrameworks/SampleAnalysis.framework/Versions/A/SampleAnalysis
0x00000001c63d1000 	/System/Library/PrivateFrameworks/kperfdata.framework/Versions/A/kperfdata
0x000000019b82a000 	/usr/lib/libdscsym.dylib
0x00000001c66e1000 	/System/Library/PrivateFrameworks/BulkSymbolication.framework/Versions/A/BulkSymbolication
0x00000001d202a000 	/usr/lib/libedit.3.dylib
0x000000019dcba000 	/System/Library/PrivateFrameworks/SystemAdministration.framework/Versions/A/SystemAdministration
0x000000019d4e2000 	/System/Library/Frameworks/DirectoryService.framework/Versions/A/DirectoryService
0x00000001a0e48000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x000000019590c000 	/System/Library/Frameworks/ServiceManagement.framework/Versions/A/ServiceManagement
0x00000001a107f000 	/System/Library/PrivateFrameworks/LoginUIKit.framework/Versions/A/Frameworks/LoginUICore.framework/Versions/A/LoginUICore
0x0000000198110000 	/usr/lib/libodfde.dylib
0x00000001a1009000 	/usr/lib/libcurl.4.dylib
0x0000000227c5c000 	/usr/lib/libcrypto.46.dylib
0x00000002288ed000 	/usr/lib/libssl.48.dylib
0x00000001a0cde000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001a0d1a000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x0000000199cf8000 	/usr/lib/libsasl2.2.dylib


VM Arguments:
jvm_args: -Dcompose.reload.argfile=/Users/<USER>/Developer/kotlin-emacs/composeApp/build/run/desktopMain/desktopMain.argfile -Dcompose.reload.build.continuous=true -Dcompose.reload.buildSystem=Gradle -Dcompose.reload.devToolsClasspath=/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-devtools/1.0.0-alpha11/7271425ca174e47a07c9c9e548f740368134ca61/hot-reload-devtools-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-runtime-api-jvm/1.0.0-alpha11/e784fec47791cba1a21f056aed2408a90bb72576/hot-reload-runtime-api-jvm-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-runtime-jvm/1.0.0-alpha11/f62217e3b4ce58047ec1412be4ef66c2ae91edb4/hot-reload-runtime-jvm-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.desktop/desktop-jvm/1.8.1/7a5516c08efe9fef2e6aa38a10e5fc17fb556148/desktop-jvm-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material3/material3-desktop/1.8.1/763803ef38c846a60dadabe187bd402c5a52222e/material3-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.components/components-resources-desktop/1.8.1/10110d785f486bc13235f40e1d0b91a48b5f3ee6/library-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.sellmair/evas-compose-jvm/1.3.0/1214dcce9040d1b9f856b02b443cf8c2ebe1573a/evas-compose-jvm-1.3.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-desktop/1.8.1/ef0aa2ceffba36f64a1ba916c1d0116fd3c383c6/material-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-ripple-desktop/1.8.1/5364a917218879742c9f08f55791adb3a7c26c8a/material-ripple-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.foundation/foundation-desktop/1.8.1/43f4d514df27ef16c4fbe77807c2a7e12e18b6fd/foundation-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-icons-core-desktop/1.7.3/414b3096050fe647b0638832eace80f22c06b85c/material-icons-core-des-Dcompose.reload.devToolsEnabled=true -Dcompose.reload.devToolsHeadless=false -Dcompose.reload.devToolsTransparencyEnabled=true -Dcompose.reload.dirtyResolveDepthLimit=5 -Dcompose.reload.launchMode=GradleBlocking -Dcompose.reload.mainClass=MainKt -Dcompose.reload.pidFile=/Users/<USER>/Developer/kotlin-emacs/composeApp/build/run/desktopMain/desktopMain.pid -Dcompose.reload.virtualMethodResolveEnabled=true -Dgradle.build.project=:composeApp -Dgradle.build.root=/Users/<USER>/Developer/kotlin-emacs -Dgradle.build.task=hotReloadDesktopMain -Dorg.gradle.java.home=/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home -XX:+IgnoreUnrecognizedVMOptions -XX:+AllowEnhancedClassRedefinition -javaagent:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-agent/1.0.0-alpha11/c4ee8ce7bac236244150134658fff9a6f21443ad/hot-reload-agent-1.0.0-alpha11.jar -Dfile.encoding=UTF-8 -Duser.country=DE -Duser.language=en -Duser.variant 
java_command: MainKt
java_class_path (initial): /Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-agent/1.0.0-alpha11/c4ee8ce7bac236244150134658fff9a6f21443ad/hot-reload-agent-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-orchestration/1.0.0-alpha11/e260a9d74bf80c3afeb17e82b53e3b20f0963085/hot-reload-orchestration-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-analysis/1.0.0-alpha11/5db9c3ab22190a5edcb508013d46fbe79d2a1d08/hot-reload-analysis-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-core/1.0.0-alpha11/8e14c2cbf8906d1c0f5cd6231b31704537fa6523/hot-reload-core-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-api/2.0.17/d9e58ac9c7779ba3bf8142aff6c830617a7fe60f/slf4j-api-2.0.17.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.javassist/javassist/3.30.2-GA/284580b5e42dfa1b8267058566435d9e93fae7f7/javassist-3.30.2-GA.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/2.1.21/97a0975aa19d925e109537af60eb46902920015c/kotlin-stdlib-2.1.21.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm-tree/9.8/18419ca5b77a2f81097c741e7872e6ab8d2f40d/asm-tree-9.8.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.ow2.asm/asm/9.8/dc19ecb3f7889b7860697215cae99c0f9b6f6b4b/asm-9.8.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar:/Users/<USER>/Developer/kotlin-emacs/composeApp/build/run/desktopMain/classpath/hot:/Users/<USER>/Developer/kotlin-emacs/composeApp/build/run/desktopMain/classpath/classes:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.exposed/exposed-dao/0.52.0/76b64f049610732a4a25cea39e84dc1f5229d013/exposed-dao-0.52.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.exposed/exposed-jdbc/0.52.0/60e006c1f98ef5d5fc93a539621074fa516339cb/exposed-jdbc-0.52.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.exposed/exposed-core/0.52.0/e2e0b4f14a20dbc074e0258bc68b5e1040e460a5/exposed-core-0.52.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.jediterm/jediterm-core/3.51/34cfde9fc98dcd18b4b1bf094683efd13eee825a/jediterm-core-3.51.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.Keelar/ExprK/91fdabf/dc335724bd9edf42cb0aa26b5104eab5250a6ca/ExprK-91fdabf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.burnett01/kotlin-expression-builder/1.2.2/298fa646374302a5ac82f57e6ef82c63c60f78fc/kotlin-expression-builder-1.2.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/dev.tclement.fonticons/font-fa-brands-desktop/1.4.1/1bfa1316d325b3dbdc32d7911e4855cae69a6f8b/font-fa-brands-desktop.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/dev.tclement.fonticons/font-fa-desktop/1.4.1/e167954a8208e7a3e9ac9e031324ed2de0151420/font-fa-desktop.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/dev.tclement.fonticons/core-desktop/1.4.1/1745a65eec8a189e97290e1cfbd12fc8ea3f8286/core-desktop.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.desktop/desktop-jvm/1.8.1/7a5516c08efe9fef2e6aa38a10e5fc17fb556148/desktop-jvm-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-desktop/1.8.1/ef0aa2ceffba36f64a1ba916c1d0116fd3c383c6/material-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.components/components-resources-desktop/1.8.1/10110d785f486bc13235f40e1d0b91a48b5f3ee6/library-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.composables/icons-lucide-jvm/1.0.0/94048c84e00269e02a264d50175729fb33d1ca1d/icons-lucide-jvm-1.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-ripple-desktop/1.8.1/5364a917218879742c9f08f55791adb3a7c26c8a/material-ripple-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.foundation/foundation-desktop/1.8.1/43f4d514df27ef16c4fbe77807c2a7e12e18b6fd/foundation-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-icons-extended-desktop/1.6.11/67bbcbd9b088c4355bf044e6486ca3dbabe50d12/material-icons-extended-desktop-1.6.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.material/material-icons-core-desktop/1.6.11/47e99680d4e5f234dc4514fd12fec4daf1af4597/material-icons-core-desktop-1.6.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.animation/animation-desktop/1.8.1/29a0e3ff19c37e121840db37f984c74cbab1e480/animation-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.foundation/foundation-layout-desktop/1.8.1/493312c9bba0d7ce31f71211f069338edb75674b/foundation-layout-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.animation/animation-core-desktop/1.8.1/d0f4df5aa6af2ba7b8b72173d46c248c0d4f7d5a/animation-core-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-desktop/1.8.1/56dc109fa60970bea9d33bc54e01e7a63cf76cf8/ui-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.sellmair/evas-compose-jvm/1.2.0/bc43ce22c5d8814fd551c1287976734e51e6d673/evas-compose-jvm-1.2.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-runtime-api-jvm/1.0.0-alpha11/e784fec47791cba1a21f056aed2408a90bb72576/hot-reload-runtime-api-jvm-1.0.0-alpha11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.hot-reload/hot-reload-runtime-jvm/1.0.0-alpha11/7595b4fbdc8c1e1ce433c60256188dfda47a6c9e/hot-reload-runtime-jvm-1.0.0-alpha11-dev.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-text-desktop/1.8.1/2685be46f1df382dbf175d75a7061640bde8a848/ui-text-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.ajalt.colormath/colormath-ext-jetpack-compose-jvm/3.6.0/2896c70328c61c240e3feb29abffbd532e9168fd/colormath-ext-jetpack-compose-jvm.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-graphics-desktop/1.8.1/8970dad2e556a7fac4a454c2158d3c6a94372ef7/ui-graphics-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-backhandler-desktop/1.8.1/cc5d993298a1e08c7532c046991aceb0fc2675c3/ui-backhandler-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.androidx.lifecycle/lifecycle-runtime-compose-desktop/2.8.4/e950e2a504a045ac652718dc20e736f28c5ff4c2/lifecycle-runtime-compose-desktop-2.8.4.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.runtime/runtime-saveable-desktop/1.8.1/c746b6378de826d1201ee33ef7f48f3537d7e9ad/runtime-saveable-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-unit-desktop/1.8.1/65eee0086e59109657cc7a7ad7569e1be78f1a93/ui-unit-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-geometry-desktop/1.8.1/6301b97db2ec7ea140bf185f0712891cfa1f480d/ui-geometry-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-tooling-preview-desktop/1.8.1/7e1d509da90394d063aef5b444bf218024b13250/ui-tooling-preview-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.runtime/runtime-desktop/1.8.1/ab97b0865e86b38fc0db7b3f9e44ba2d32f9e56a/runtime-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.components/components-ui-tooling-preview-desktop/1.8.1/a4a2a2895122b4e880ad5a7c143aee17c81af432/library-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.sellmair/evas-jvm/1.2.0/55599a2fce527efbf8516ab45cd17989bcf92638/evas-jvm-1.2.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-reflect/1.9.21/5570877dec93532519eda165abb3674ea1e07cbc/kotlin-reflect-1.9.21.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-client-cio-jvm/3.0.1/7b099d238772b3cabda68b92910249fdcd22af29/ktor-client-cio-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-client-core-jvm/3.0.1/357b59ca4d5cd7f0042618c4293f6284b41f88b0/ktor-client-core-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-events-jvm/3.0.1/57de46874766851b3c5674d1d8c1bbc715ebe98b/ktor-events-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-websocket-serialization-jvm/3.0.1/78a5c2e822a6d4dd3b9f71c32752e2389cb79d91/ktor-websocket-serialization-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-sse-jvm/3.0.1/dfa785c9646eaac908af7821e4aaab3d9c270d8e/ktor-sse-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-http-cio-jvm/3.0.1/999363727bfc57f98afa5fc8983ba81c21159595/ktor-http-cio-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-serialization-jvm/3.0.1/e54217058819739e036a41e71f5329bf8ea8d09d/ktor-serialization-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-websockets-jvm/3.0.1/4b4e2dc5b1f774efb60020cf5a8651ac86c9f83/ktor-websockets-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-network-tls-jvm/3.0.1/b9f3edaee337fa587a3d2a61475f12c73f4d63f5/ktor-network-tls-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-http-jvm/3.0.1/29ef95f2394c06852aea77e19b8abf4cf516bc34/ktor-http-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-network-jvm/3.0.1/59eaa6ce06e20d4c1ddd9d833c70813824ed7bf6/ktor-network-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-utils-jvm/3.0.1/995263b20bbf1e891e9fbd7ce3bc8508e0547df9/ktor-utils-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-serialization-core-jvm/1.7.3/1f226780b845ff9206474c05159245d861556249/kotlinx-serialization-core-jvm-1.7.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.mikepenz/multiplatform-markdown-renderer-code-jvm/0.32.0-b01/c8a157eb0ef0235295b3a31a288b2f1ad3e22e49/multiplatform-markdown-renderer-code-jvm-0.32.0-b01.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/dev.snipme/highlights-jvm/1.0.0/c4dbc1a34666a506161a9fc7155dc45f4b70b6a4/highlights-jvm-1.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-serialization-json-jvm/1.7.3/6701e8c68d9e82387ce72ee96e8ddf058208d58f/kotlinx-serialization-json-jvm-1.7.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.mikepenz/multiplatform-markdown-renderer-m2-jvm/0.32.0-b01/ae24d27f6f552113ca5c74edb5e5470f4f204d9/multiplatform-markdown-renderer-m2-jvm-0.32.0-b01.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.prof18.rssparser/rssparser-jvm/6.0.8/666729a602808a2f9f8c104d8079d71d8d72de5a/rssparser-jvm.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.ajalt.colormath/colormath-jvm/3.6.0/d5593e3f07b2447bec0b17f5cb7141a0f0de9688/colormath-jvm.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.compose.ui/ui-util-desktop/1.8.1/2a882d6cfc3d1228c1284ca34a2ebfc2b5ca3815/ui-util-desktop-1.8.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.javalin/javalin/6.3.0/efb95b467eb8c5f0d6d42dca84d3f3d46d9313b6/javalin-6.3.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.github.kotlin-telegram-bot.kotlin-telegram-bot/telegram/6.1.0/279eb2db18e11704e4db5cb94974f1236e2465fa/telegram-6.1.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.retrofit2/converter-gson/2.9.0/fc93484fc67ab52b1e0ccbdaa3922d8a6678e097/converter-gson-2.9.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.retrofit2/retrofit/2.9.0/d8fdfbd5da952141a665a403348b74538efc05ff/retrofit-2.9.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/logging-interceptor/3.8.0/f765f004f3e201bd6ad0904266e605d7fb776d5/logging-interceptor-3.8.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp/4.12.0/2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd/okhttp-4.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okio/okio-jvm/3.6.0/5600569133b7bdefe1daf9ec7f4abeb6d13e1786/okio-jvm-3.6.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/2.1.10/3009fa6a21ebb400fe38c70eaf218a0c669ddbe2/kotlin-stdlib-jdk8-2.1.10.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.skiko/skiko-awt-runtime-macos-arm64/*******/4b7f8be5bdc9ac86ee865f1c99897fb0c8c27ee9/skiko-awt-runtime-macos-arm64-*******.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.skiko/skiko-awt/*******/1c5d9ec55031cef90d7874c20f001b179a0f656c/skiko-awt-*******.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/atomicfu-jvm/0.23.2/a4601dc42dceb031a586058e8356ff778a57dea0/atomicfu-jvm-0.23.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/2.1.10/c9d8cb43c1d65c500ffbbb6eb591b9c8928caf6d/kotlin-stdlib-jdk7-2.1.10.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.mikepenz/multiplatform-markdown-renderer-jvm/0.32.0-b01/4c879954de5a3995a2a4f0a6dac70cc31c50d8b8/multiplatform-markdown-renderer-jvm-0.32.0-b01.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/markdown-jvm/0.7.3/83df25f4bb0a51e979774c02530824af8ec6b03e/markdown-jvm-0.7.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-viewmodel-desktop/2.8.5/572f6fed6a70c5d87c9a0d17d05828ff1a6f19f8/lifecycle-viewmodel-desktop-2.8.5.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-runtime-desktop/2.8.5/fed8b9973735cbe77645edf153d5f7bb412bfd45/lifecycle-runtime-desktop-2.8.5.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-jvm/2.8.5/7174a594afb73a9ad9ac9074ce78b94af3cc52a7/lifecycle-common-jvm-2.8.5.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-slf4j/1.10.2/1271f9d4a929150bb87ab8d1dc1e86d4bcf039f3/kotlinx-coroutines-slf4j-1.10.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.ktor/ktor-io-jvm/3.0.1/1f33b575c0b31a43c922b42cc5a4a691334d346b/ktor-io-jvm-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.10.2/4a9f78ef49483748e2c129f3d124b8fa249dafbf/kotlinx-coroutines-core-jvm-1.10.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection-jvm/1.5.0/7ba2c69414d46ebc2dd76598bdd0a75c54281a57/collection-jvm-1.5.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.9.1/b17951747e38bf3986a24431b9ba0d039958aa5f/annotation-jvm-1.9.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-io-core-jvm/0.5.4/16566a36d64d88af421fbee163eeeb6aa6406a43/kotlinx-io-core-jvm-0.5.4.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-io-bytestring-jvm/0.5.4/d02e90094c25c6c4689a9f4359c3dc3543f66958/kotlinx-io-bytestring-jvm-0.5.4.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.xerial/sqlite-jdbc/3.44.1.0/12e6182deef32d366ade664aa33bec9f4dd3ffe/sqlite-jdbc-3.44.1.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.pgreze/kotlin-process/1.5/e5a30e2830910c89c48c01ebf1edd5ef6901e266/kotlin-process-1.5.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-simple/2.0.11/267ded84c4845c513677e4f7ca6f03240be9ce3f/slf4j-simple-2.0.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.mail/javax.mail/1.6.2/935151eb71beff17a2ffac15dd80184a99a0514f/javax.mail-1.6.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jsoup/jsoup/1.18.1/cb7cd991d47b44101cbe4655dec611cdc01f8a02/jsoup-1.18.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/uk.co.caprica/vlcj/4.8.3/a7de99ff0a5973b6750f8982d610990e2397a535/vlcj-4.8.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.twelvemonkeys.imageio/imageio-webp/3.12.0/390599d525f6af2af4d94bd549411d6e1d47ea07/imageio-webp-3.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.drewnoakes/metadata-extractor/2.19.0/4d3ab42dd9965b8f2b8bd1b7572e028d6f90a34f/metadata-extractor-2.19.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.jthink/jaudiotagger/3.0.1/ca3075f6bd92da24daf853651adc8852ecccb999/jaudiotagger-3.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacv-platform/1.5.11/4b41f4be61b58256c425c2e02714a795c038991a/javacv-platform-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.zeroturnaround/zt-exec/1.12/51a8d135518365a169a8c94e074c7eaaf864e147/zt-exec-1.12.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.apis/google-api-services-calendar/v3-rev20220715-2.0.0/4d124f56ddb22acbc87a9c657714a49725598b0/google-api-services-calendar-v3-rev20220715-2.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.api-client/google-api-client/2.0.0/c3daf5048a26a73182b5e6c81ad141fb859f0787/google-api-client-2.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.oauth-client/google-oauth-client-jetty/1.34.1/a6aff2c1be148bac622e5048e20f536f71b3380d/google-oauth-client-jetty-1.34.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-java/4.33.0/2cc38c8dbef9d6e8774f05c2d2e8088180858ed4/selenium-java-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/24.0.1/13c5c75c4206580aa4d683bffee658caae6c9f43/annotations-24.0.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.websocket/websocket-jetty-server/11.0.23/6287a389b84ff74c72d65f60f67eac1a7ce10c93/websocket-jetty-server-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-webapp/11.0.23/2546d8f399b469314542b83eb88112502f0f19d3/jetty-webapp-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.websocket/websocket-servlet/11.0.23/89689e908d2c00cba3d4c5a507fd7a79b142e4fe/websocket-servlet-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-servlet/11.0.23/548ffedb7daef0a890497fb1c653144762e8c2ed/jetty-servlet-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-security/11.0.23/f4154d55790ca3de9c95f0100bbb3e2ed7c62cba/jetty-security-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.websocket/websocket-core-server/11.0.23/f4df828ac2ec618151cc9511f06ba778dba5872/websocket-core-server-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-server/11.0.23/3190e72825d60e6df519f8216532b6b48f53f8d0/jetty-server-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.websocket/websocket-jetty-common/11.0.23/661c97cb8ba037b050bbf1d35b88bf0079d8961b/websocket-jetty-common-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.websocket/websocket-core-common/11.0.23/36852608e0a0293936be1d18ae72ff3be4c92b16/websocket-core-common-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-http/11.0.23/c782d374e9fb6f531ef5df50e8cbc149600880cb/jetty-http-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-io/11.0.23/6c92097b0fd0d413dd2a68e236b436eed5501e7b/jetty-io-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-xml/11.0.23/a6ccaea1c390ad9268a1e9a6b8f73faf1fbc9c65/jetty-xml-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty/jetty-util/11.0.23/c447d8763fe58769e3fa665a1327613b91db3f21/jetty-util-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.slf4j/slf4j-api/2.0.16/172931663a09a1fa515567af5fbef00897d3c04/slf4j-api-2.0.16.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.activation/activation/1.1/e6cb541461c2834bdea3eb920f1884d1eb508b50/activation-1.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/uk.co.caprica/vlcj-natives/4.8.3/2a84facef1dac138801fdd3d85ffb77d3d1b10a7/vlcj-natives-4.8.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.twelvemonkeys.imageio/imageio-metadata/3.12.0/6997264db25396a2c8fddf6520174c9a907e3403/imageio-metadata-3.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.twelvemonkeys.imageio/imageio-core/3.12.0/4a57990b52fd36fe60c356a3e5fd57054a85a502/imageio-core-3.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.twelvemonkeys.common/common-image/3.12.0/1ffc9fb885eea7f0bdab0f6e7f780d3e41b5ad01/common-image-3.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.twelvemonkeys.common/common-io/3.12.0/1f739ad65a48fd2bc7b8c0a8bb54218c0e219c84/common-io-3.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.twelvemonkeys.common/common-lang/3.12.0/170b88f23daa4d4d64189285ebebe4eb59d51c93/common-lang-3.12.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.adobe.xmp/xmpcore/6.1.11/852f14101381e527e6d43339d7db1698c970436c/xmpcore-6.1.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacv/1.5.11/8bcc9af0d9995080960c061edcfd6da9be84b0b5/javacv-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv-platform/4.10.0-1.5.11/359538d58a6971ce50d150cfd709a6f26a0b01cb/opencv-platform-4.10.0-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas-platform/0.3.28-1.5.11/f08d1c50ba304328962f9e6e9af6b8dbd6618c5d/openblas-platform-0.3.28-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg-platform/7.1-1.5.11/b31abc689e758c771f6d945118f38ea234f84181/ffmpeg-platform-7.1-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture-platform/2.13.3.31-1.5.9/18253937e77a52026bd87db432a1d4900e511763/flycapture-platform-2.13.3.31-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394-platform/2.2.6-1.5.9/72f5c0340514f89601bdac20304ce7ccf686b6c5/libdc1394-platform-2.2.6-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect-platform/0.5.7-1.5.9/c757108e09de1a87802637919e49b5500589630f/libfreenect-platform-0.5.7-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect2-platform/0.2.0-1.5.9/7c30d421f2e679119120c835ad73a8a8a2913539/libfreenect2-platform-0.2.0-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense-platform/1.12.4-1.5.9/1596fc41fe524b00f9e825f8a0bf103469fe51f5/librealsense-platform-1.12.4-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2-platform/2.53.1-1.5.9/8bd869a086ca856d787806f0927966195d328d9a/librealsense2-platform-2.53.1-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/videoinput-platform/0.200-1.5.9/cdd2794ec4b99530a95bbcce4d3d7244780234b9/videoinput-platform-0.200-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus-platform/2.3.1-1.5.9/2a0b81d30cb1e17eb3ae9d7af3fe8f53b8a27735/artoolkitplus-platform-2.3.1-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract-platform/5.5.0-1.5.11/d7a4808a307cd7595ac2063d1aa0670caf7c8911/tesseract-platform-5.5.0-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica-platform/1.85.0-1.5.11/e0307cadb6ed171f8c76eb2dbe1d97dbfc5b64ff/leptonica-platform-1.85.0-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp-platform/1.5.11/3be0f5396552ec336fffd83f2042aacaa02248d1/javacpp-platform-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.oauth-client/google-oauth-client-java6/1.34.1/af6b85ed729e96a5257741986e823aa241f3d9a4/google-oauth-client-java6-1.34.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.oauth-client/google-oauth-client/1.34.1/4a4f88c5e13143f882268c98239fb85c3b2c6cb2/google-oauth-client-1.34.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.http-client/google-http-client-gson/1.42.1/19528e0766bbd63187ca0a8d3f5d77577c2c10b5/google-http-client-gson-1.42.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.http-client/google-http-client-apache-v2/1.42.1/949cc0ed789e76c1ec539d8a5e60ee527b22b6c4/google-http-client-apache-v2-1.42.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.http-client/google-http-client/1.42.1/808ea5d28383a32db7531a19462d45266ad0745f/google-http-client-1.42.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-chrome-driver/4.33.0/87ac9f66aee4901425b84d4e6d093f5a2ffb17f2/selenium-chrome-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-devtools-v135/4.33.0/ec829d09c9f4e5ca36bf94323ba373d58bbaf7d0/selenium-devtools-v135-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-devtools-v136/4.33.0/781a3fdfb26cffed529801b8a329c9557a5a3c97/selenium-devtools-v136-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-devtools-v137/4.33.0/56e572bb614ee9a0ad1f49ab5e474c7eb231f74d/selenium-devtools-v137-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-edge-driver/4.33.0/857a5156af7d592457a682ceefd726d562d7ccb3/selenium-edge-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-firefox-driver/4.33.0/6f15b600b055f2cee61600c0efdfbfa848b9d1a8/selenium-firefox-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-ie-driver/4.33.0/f0609e34b70675495c1ed9859630e9d68b6ce7fc/selenium-ie-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-safari-driver/4.33.0/89b52b82d62d7ee129b3242e93cf9aee8b05daed/selenium-safari-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-support/4.33.0/4994820bf2866e5407afcb89e721bfba38a836fa/selenium-support-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-chromium-driver/4.33.0/30ba14ad6df821a5ee1ed691fbe04d2e3ea2df3b/selenium-chromium-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-remote-driver/4.33.0/22424dd246722763a35623d94f7a2b0299bfa5de/selenium-remote-driver-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opencensus/opencensus-contrib-http-util/0.31.1/3c13fc5715231fadb16a9b74a44d9d59c460cfa8/opencensus-contrib-http-util-0.31.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/33.4.8-jre/e70a3268e6cd3e7d458aa15787ce6811c34e96ae/guava-33.4.8-jre.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpclient/4.5.13/e5f6cae5ca7ecaac1ec2827a9e2d65ae2869cada/httpclient-4.5.13.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.httpcomponents/httpcore/4.4.15/7f2e0c573eaa7a74bac2e89b359e1f73d92a0a1d/httpcore-4.4.15.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-manager/4.33.0/110b0565298889ab4f971b29dfc00b775c3c062/selenium-manager-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-http/4.33.0/8e72bf24c45ea591faf205ca7323339a0ab0c164/selenium-http-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-json/4.33.0/29358392fe46e47a9b12411cb8ab4376af1d0f68/selenium-json-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-os/4.33.0/2e7ab2c1b46c9002de68078fae3bbba8aeefb9e9/selenium-os-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.seleniumhq.selenium/selenium-api/4.33.0/8805e9fe7b20f4e325ca392d0e869482cf977d0e/selenium-api-4.33.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.9.0/8a1167e089096758b49f9b34066ef98b2f4b37aa/gson-2.9.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.toolchain/jetty-jakarta-servlet-api/5.0.2/27fce6d666a203526236d33d00e202a4136230f/jetty-jakarta-servlet-api-5.0.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.eclipse.jetty.websocket/websocket-jetty-api/11.0.23/ba1b74fd2cdcbb7acc800756558544878dacc8f4/websocket-jetty-api-11.0.23.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.java.dev.jna/jna-platform-jpms/5.14.0/24fde384a42c2c75c46116edda48895e5763fc85/jna-platform-jpms-5.14.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.java.dev.jna/jna-jpms/5.14.0/9513388696e34db7c9b564aaec0ccbd7395e9389/jna-jpms-5.14.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/16a0fbc18bbc3d25d9c1496d4e84f9fddc7d0dc3/opencv-4.10.0-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/7fcf5023e93edcebc1caa3d09b5f601211d2da81/opencv-4.10.0-1.5.11-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/c34dbd031537dcbc2b4f0ffd278a4a1694bfa101/opencv-4.10.0-1.5.11-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/f2233295da383aee804b39a3725a0390ee538fd0/opencv-4.10.0-1.5.11-ios-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/c7f95f0dfa2e9f7448226000dba85dc3772fb120/opencv-4.10.0-1.5.11-ios-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/9d2c2eb57fe2c6785f72cfa380cd717f5e25e169/opencv-4.10.0-1.5.11-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/878737bbc5c80fbb87c5e48d205ac2b6d9647bb2/opencv-4.10.0-1.5.11-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/cbdcb806f9b9c08187d13eac3ff6635cf9b0987f/opencv-4.10.0-1.5.11-macosx-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/b803f0af1c962c95f3b6bbeda64f206cd6bf171c/opencv-4.10.0-1.5.11-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/opencv/4.10.0-1.5.11/2a4ad7ad335cd22ab09fe18bdf4c60d3bad867cb/opencv-4.10.0-1.5.11-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/88092a82318819af79499f2e3a05aa2432086dbd/openblas-0.3.28-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/e2ecd6aaf3f8ee631958b9dc358ff5e98b1d5422/openblas-0.3.28-1.5.11-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/62a38e285718726e42473eb518817336e103ee4c/openblas-0.3.28-1.5.11-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/98fa8a7e67d9ca8209f90a8bce54e193313c212/openblas-0.3.28-1.5.11-ios-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/98276e3e3a2d5968460ed9441ee98d4948ef0a20/openblas-0.3.28-1.5.11-ios-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/a42042b97071d74688e649030c20b2083a0d4313/openblas-0.3.28-1.5.11-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/ab3221791ebd7b458cc889c9feab247eb509424d/openblas-0.3.28-1.5.11-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/615d1568241e3f20240c6bca3e3a07b7268b16a7/openblas-0.3.28-1.5.11-macosx-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/cf423f5d7d4b8f182f64efafae07e5a8cca0fa02/openblas-0.3.28-1.5.11-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/openblas/0.3.28-1.5.11/ad4209e2a05042830b04d0b35378dccf21fe59ad/openblas-0.3.28-1.5.11-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/9704bf6e3ba57d8984fea5eaa0221cf049ec90d4/ffmpeg-7.1-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/461b9bc3f00b0cd81bba7146c3e15142bf1b5ff/ffmpeg-7.1-1.5.11-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/e80ee3960e98b9df5d1adbf8d8f007b25898ea27/ffmpeg-7.1-1.5.11-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/4a4c76feccc8cd5cff5a797af2080024ab87a880/ffmpeg-7.1-1.5.11-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/97fce97abc52946522c577a201f217f2972799d/ffmpeg-7.1-1.5.11-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/21d65a22081f278cbe1ea8c351ab1548ab4701e0/ffmpeg-7.1-1.5.11-macosx-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/f4defc8d098a7f12d647e9c29a4b81e30229e19/ffmpeg-7.1-1.5.11-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/ffmpeg/7.1-1.5.11/85302b8049f9c5869bf7b61307f39c5b53069339/ffmpeg-7.1-1.5.11-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/c653e61d46ebe5a5c0e71c17681f126320422f4/flycapture-2.13.3.31-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/85ec8546502a3a17e86ecf585b58fa98436d12a4/flycapture-2.13.3.31-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/d51d148f00a79619f1300983c9a2cc8cb973dadb/flycapture-2.13.3.31-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/d97b553f9bad0ac14b4cb5d25abad69f18c39325/flycapture-2.13.3.31-1.5.9-linux-armhf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/b4de090166d6c49a05f6edec100fa2dd5912d41a/flycapture-2.13.3.31-1.5.9-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/176902709475936e5d187768c47fbd6a023e48d8/flycapture-2.13.3.31-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/flycapture/2.13.3.31-1.5.9/aefe6bb84ee84669117b1f8d2b0ecafd6e666383/flycapture-2.13.3.31-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/fa32142e37d3be048b64fb52ca51deca6d5de667/libdc1394-2.2.6-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/f7d9403ef3ac56375bae667c13bce4512ea3ad14/libdc1394-2.2.6-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/7acad7231f224ff3cdf47c484ef3fa6f20963659/libdc1394-2.2.6-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/e5b12f9ca1ff3f57f361c4152d1ec5505538f6e1/libdc1394-2.2.6-1.5.9-linux-armhf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/ea94313a066e52cd2cee9b200823c807acbabccd/libdc1394-2.2.6-1.5.9-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/a3084219b82faa7c61fa27e8cd3362d2baadc016/libdc1394-2.2.6-1.5.9-linux-ppc64le.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/d27a2a94a6eb8bf3c41c4db0a521950efd08e0a1/libdc1394-2.2.6-1.5.9-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/e5be5dc0f4e6c786adbe2ae17ed0044df86d615b/libdc1394-2.2.6-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libdc1394/2.2.6-1.5.9/d82e9b05c5b4c2fd4a70b9c95ecdaf53cd7f672d/libdc1394-2.2.6-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/16c037cd4f9e9f35a8e88fea29979e96b711f6ff/libfreenect-0.5.7-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/638424a37edc92a9a1e17ccc43f3dffea3378eb4/libfreenect-0.5.7-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/7c0f3fc2ce482165be5f36de3106ae99e644b77f/libfreenect-0.5.7-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/807e7b683af1b031fd3908b4e82dde74df31e767/libfreenect-0.5.7-1.5.9-linux-armhf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/164abca835d05af2095b027822d25b6835110fc8/libfreenect-0.5.7-1.5.9-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/a93ba40fff7feb1ae19968b64b8928842bcc231c/libfreenect-0.5.7-1.5.9-linux-ppc64le.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/12fa8e9975eb1dc0efa2a6e152fee15bfc2139d7/libfreenect-0.5.7-1.5.9-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/2a20883b37c2ee1e166cbe3f3d66f4ac0d413134/libfreenect-0.5.7-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect/0.5.7-1.5.9/d2de417f6b540cfe8496f8240bad2467e84b56a0/libfreenect-0.5.7-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect2/0.2.0-1.5.9/2008343f5ea8bbbf1d516bf630a2656c91711a9/libfreenect2-0.2.0-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect2/0.2.0-1.5.9/497991d72c5e9e81fa697f1a2f4abb7059d18661/libfreenect2-0.2.0-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect2/0.2.0-1.5.9/24ab1291b067d0933ea76807597d81a1255e217a/libfreenect2-0.2.0-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect2/0.2.0-1.5.9/af06c4b4202f1f73552f5123ffce0b1948c12663/libfreenect2-0.2.0-1.5.9-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/libfreenect2/0.2.0-1.5.9/fe018286861aca25a55604ebda28d08d7a1e2785/libfreenect2-0.2.0-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/27fb41a9d834522fb6d3acd56defbe56ccfb45e1/librealsense-1.12.4-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/769ae6d3e1c15b73260ffe0794273e414acf2ea/librealsense-1.12.4-1.5.9-linux-armhf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/a93e17e2e6818111ae122d238ffb5e3bb6259e27/librealsense-1.12.4-1.5.9-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/38cb2fa593545baa50245b86132dd832591d35d2/librealsense-1.12.4-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/5a021abc673fdd5606be939aad408765dba54878/librealsense-1.12.4-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/3ea7930d61096c0b769ddb163010d38dad4feb6b/librealsense-1.12.4-1.5.9-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/1950b2bb729af2d9c2dbf424b419716b1509f2db/librealsense-1.12.4-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense/1.12.4-1.5.9/3ea18855380a5d528160c583e485491e7e8bdd47/librealsense-1.12.4-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/3123b090f70551bbf78bb973b2ee6d555c2efe5e/librealsense2-2.53.1-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/13ff01106992a38b4b02e771a648304930a77cf6/librealsense2-2.53.1-1.5.9-linux-armhf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/dd0b5d2dedd3ab01c7bb97042cee57640d5c1fb4/librealsense2-2.53.1-1.5.9-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/13bac2d3e040ca0e729b6a6f5827b4876dc4000/librealsense2-2.53.1-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/db319728fe26a004effd83884132e0c115e99e8a/librealsense2-2.53.1-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/1a9d77eeb22088653ee398721e769a4b7ecfbd1a/librealsense2-2.53.1-1.5.9-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/4c33bb1987aa748c8348957f2bc83182d1a229ab/librealsense2-2.53.1-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/librealsense2/2.53.1-1.5.9/fe3ea25e1f6991a68e51c5ddcf8e9a6005efe553/librealsense2-2.53.1-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/videoinput/0.200-1.5.9/296ef2dc4f171920b3c8dc50efb9db6e915a1bd2/videoinput-0.200-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/videoinput/0.200-1.5.9/61dcd5e7316e858496f73b1d5957babff6e87a8a/videoinput-0.200-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/videoinput/0.200-1.5.9/65c40cb15d7ce1ab64a1eda96dacbd0ad1e07c7b/videoinput-0.200-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/af6a8e3a14873b7de80d632e06eb0b3c9cc00c86/artoolkitplus-2.3.1-1.5.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/c313c942533c0a265c46fd5fad46f0d4812143ac/artoolkitplus-2.3.1-1.5.9-android-arm.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/25d0cebb636d507f92c6a16ad93ad0f715e94c1e/artoolkitplus-2.3.1-1.5.9-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/21a671f8bc10a8c88cb034d999d59f9f4a376543/artoolkitplus-2.3.1-1.5.9-android-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/a61070a49741e58a37243bad3a9f28b52593dce9/artoolkitplus-2.3.1-1.5.9-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/e078d9ce9c354a4c7d24ba1c02d3647f9e72170b/artoolkitplus-2.3.1-1.5.9-linux-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/1477cc4c8e3706b76528bcfaae4d98212e65498e/artoolkitplus-2.3.1-1.5.9-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/d8ae0a865ad7a05672077e0d01b76c2c9d8f4641/artoolkitplus-2.3.1-1.5.9-linux-armhf.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/2dba8f53a32a54c56795d529b0e86699c6c40604/artoolkitplus-2.3.1-1.5.9-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/fc83b541f8265486873a77e09bfb4812145e55ef/artoolkitplus-2.3.1-1.5.9-linux-ppc64le.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/b9e93bfe25743fc66f9e1c49bd5c15de50098d12/artoolkitplus-2.3.1-1.5.9-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/5a0d26b1db496fa0b0f11ab9607e0a8be8854dba/artoolkitplus-2.3.1-1.5.9-windows-x86.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/artoolkitplus/2.3.1-1.5.9/63cd2f8e29ad476e1adc0370312546eac1bc97a6/artoolkitplus-2.3.1-1.5.9-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/e448786eccec591fcf28f4804988df3888594281/tesseract-5.5.0-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/eeb0de3822cceffc20fe7705800dc96c61aebeea/tesseract-5.5.0-1.5.11-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/3f0b08ceff228f3fd9228bbcfa3cccf9d4bc6b80/tesseract-5.5.0-1.5.11-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/f7a6ed255170cd7821a14b76e6fe13f52757e333/tesseract-5.5.0-1.5.11-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/6bc866cda9424fbca6969dcbf62ce67d8497b706/tesseract-5.5.0-1.5.11-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/d28f7f2ade77968242c4c5bed6aec98f081976d0/tesseract-5.5.0-1.5.11-macosx-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/9d540f71f9e68569fd933fc6bcd8bb854f234c62/tesseract-5.5.0-1.5.11-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/tesseract/5.5.0-1.5.11/e79178c2e597ea6f8caa3f17cdb67b138eaf04f3/tesseract-5.5.0-1.5.11-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/845b7104a67fddcc5102ac5f476973ae61ca770f/leptonica-1.85.0-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/efd78fa9da193602f2074487df54795f86536ffc/leptonica-1.85.0-1.5.11-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/311df55a0c9d6f25f71c4501a18b3df61dface29/leptonica-1.85.0-1.5.11-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/2f580dbbcfb892e42b0e7774f39c186726542ff0/leptonica-1.85.0-1.5.11-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/a333f4ea1f36cebbfc4e45ffc0c630861addc71f/leptonica-1.85.0-1.5.11-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/55d4746b20a29d89823aa911d848b50e0c80ed0a/leptonica-1.85.0-1.5.11-macosx-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/e5c2c021cadb2576c0a0cf77e20349fbd33c1393/leptonica-1.85.0-1.5.11-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/leptonica/1.85.0-1.5.11/75921aa50cf7fe946afad7cc6b9b0e139d2cf438/leptonica-1.85.0-1.5.11-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/76a0197853ab5e2d3643804e62f9018053750ddf/javacpp-1.5.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/ea5132919ded9a30bd4e74192fab988159be0272/javacpp-1.5.11-android-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/6dcebfecd64ca1f19c52cb090ec4c3d23b6941c9/javacpp-1.5.11-android-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/150f55b6508b335af49d77d87eae83bc228a7307/javacpp-1.5.11-ios-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/a4318f2a5d4b36468944d2f7b91affad6ac6e51a/javacpp-1.5.11-ios-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/c16d00b57fc6cd7d59dd00da48745d8ddb6406fe/javacpp-1.5.11-linux-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/7fff5ed541aeb4f7fcb30ac878e6721fe6b93b55/javacpp-1.5.11-linux-ppc64le.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/a0638ad2ac26da1116d3f72555865ef3a8c95525/javacpp-1.5.11-linux-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/df05a996ce8b0599995109c62d29e84c3819b461/javacpp-1.5.11-macosx-arm64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/50dfb81327a90385e5defc8a8d36de81d3d58cb0/javacpp-1.5.11-macosx-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.bytedeco/javacpp/1.5.11/b1245824a612462e16021b26aa1004e2b69aead0/javacpp-1.5.11-windows-x86_64.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/failureaccess/1.0.3/aeaffd00d57023a2c947393ed251f0354f0985fc/failureaccess-1.0.3.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/b421526c5f297295adef1c886e5246c39d4ac629/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.36.0/227d4d4957ccc3dc5761bd897e3a0ee587e750a7/error_prone_annotations-2.36.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.j2objc/j2objc-annotations/3.0.0/7399e65dd7e9ff3404f4535b2f017093bdb134c7/j2objc-annotations-3.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/commons-logging/commons-logging/1.2/4bfc12adfe4842bf07b657f0369c4cb522955686/commons-logging-1.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/commons-codec/commons-codec/1.11/3acb4705652e16236558f0f4f2192cc33c3bd189/commons-codec-1.11.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opencensus/opencensus-api/0.31.1/66a60c7201c2b8b20ce495f0295b32bb0ccbbc57/opencensus-api-0.31.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jspecify/jspecify/1.0.0/7425a601c1c7ec76645a78d22b8c6a627edee507/jspecify-1.0.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.auto.service/auto-service-annotations/1.1.1/da12a15cd058ba90a0ff55357fb521161af4736d/auto-service-annotations-1.1.1.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-exporter-logging/1.50.0/f346b37a6c02428fc4606a089566c87ad82dc211/opentelemetry-exporter-logging-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-extension-autoconfigure/1.50.0/ad69f784c48db4bdc216f90fddead6c7e26b0727/opentelemetry-sdk-extension-autoconfigure-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.50.0/47676b3c16db83a30b002d0e9cd9e6405236dd21/opentelemetry-sdk-extension-autoconfigure-spi-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk/1.50.0/1c6ffdd0bf5a3e42154912f0ab1b1bbd73499af1/opentelemetry-sdk-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-trace/1.50.0/19d60d3b72aa2d0d89a02361eedf7157ecc1fc7d/opentelemetry-sdk-trace-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-metrics/1.50.0/49432de0171798f671bda82bf7eef6ae283b3b7d/opentelemetry-sdk-metrics-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-logs/1.50.0/fb7ace15eca8878a2c3cf86320ed1d2fd1c0f206/opentelemetry-sdk-logs-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-sdk-common/1.50.0/928c43d75cd0675070d38d041973370823d6face/opentelemetry-sdk-common-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-api/1.50.0/83a69114c56ff2664d21906c62431bceb1c294ed/opentelemetry-api-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.opentelemetry/opentelemetry-context/1.50.0/dcdc20a193e82d66ecf5a1a978a799bc19e9b210/opentelemetry-context-1.50.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy/1.17.5/88450f120903b7e72470462cdbd2b75a3842223c/byte-buddy-1.17.5.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.grpc/grpc-context/1.27.2/1789190601b7a5361e4fa52b6bc95ec2cd71e854/grpc-context-1.27.2.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.commons/commons-exec/1.4.0/e9061b21958bfaf1cb736eb22e36cbf02d8fe9f/commons-exec-1.4.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     bool AllowEnhancedClassRedefinition           = true                                      {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool IgnoreUnrecognizedVMOptions              = true                                      {product} {command line}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {ergonomic}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839564                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909338                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909338                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseNUMA                                  = false                                     {product} {ergonomic}
     bool UseNUMAInterleaving                      = false                                     {product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=/Users/<USER>/.local/bin:/opt/homebrew/sbin:/Users/<USER>/.qlot/bin:/Users/<USER>/.cargo/bin:/Users/<USER>/Library/pnpm:/opt/homebrew/bin:/Users/<USER>/.go/bin:/opt/homebrew/opt/llvm/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/Developer/depot_tools:/Users/<USER>/.bun/bin:/Users/<USER>/.roswell/bin:/Users/<USER>/go/bin:/Users/<USER>/.gem/ruby/3.2.0/bin:/opt/homebrew/opt/ruby/bin:/opt/homebrew/opt/openjdk/bin:/opt/homebrew/bin/:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/X11/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/TeX/texbin
SHELL=/opt/homebrew/bin/fish
DISPLAY=/private/tmp/com.apple.launchd.tuRXN7RsXl/org.xquartz:0
LC_CTYPE=en_US.UTF-8
TMPDIR=/var/folders/p4/5q8rp4gn70g3q_y9788vy6700000gn/T/

Active Locale:
LC_ALL=C/en_US.UTF-8/C/C/C/C
LC_COLLATE=C
LC_CTYPE=en_US.UTF-8
LC_MESSAGES=C
LC_MONETARY=C
LC_NUMERIC=C
LC_TIME=C

Signal Handlers:
   SIGSEGV: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO, unblocked
    SIGBUS: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
    SIGFPE: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGPIPE: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGXFSZ: javaSignalHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGILL: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked
   SIGUSR2: SR_handler in libjvm.dylib, mask=00000000000000000000000000000000, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGHUP: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
    SIGINT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTERM: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGQUIT: UserHandler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, blocked
   SIGTRAP: crash_handler in libjvm.dylib, mask=11100110000111110111111111111111, flags=SA_RESTART|SA_SIGINFO, unblocked


Periodic native trim disabled

JNI global refs:
JNI global refs: 155, weak refs: 25

JNI global refs memory usage: 2099, weak refs: 1465

Process memory usage:
Resident Set Size: 290512K (1% of 16777216K total physical memory with 136016K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 54366K
Loader bootstrap                                                                       : 22090K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 683K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 77960B

Classes loaded by more than one classloader:
Class dima.telegram.app.TelegramAppKt$TelegramApp$10$1                                : loaded 5 times (x 92B)
Class dima.telegram.app.TelegramAppKt                                                 : loaded 5 times (x 68B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$9$1                                 : loaded 4 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$11$1                                : loaded 4 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$8$1                                 : loaded 4 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$7$1                                 : loaded 3 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$4$1                                 : loaded 3 times (x 72B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$12$1                                : loaded 3 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$6$1                                 : loaded 3 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$3$1                                 : loaded 3 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$10$1$invokeSuspend$$inlined$collectEvent$1: loaded 2 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$9$1$invokeSuspend$$inlined$collectEvent$1$1$1: loaded 2 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$9$1$invokeSuspend$$inlined$collectEvent$1: loaded 2 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$9$1$invokeSuspend$$inlined$collectEvent$1$1: loaded 2 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$10$1$invokeSuspend$$inlined$collectEvent$1$1: loaded 2 times (x 92B)
Class dima.telegram.app.TelegramAppKt$TelegramApp$10$1$invokeSuspend$$inlined$collectEvent$1$1$1: loaded 2 times (x 92B)

---------------  S Y S T E M  ---------------

OS:
uname: Darwin 23.0.0 Darwin Kernel Version 23.0.0: Fri Sep 15 14:41:34 PDT 2023; root:xnu-10002.1.13~1/RELEASE_ARM64_T8103 arm64
OS uptime: 41 days 13:49 hours
rlimit (soft/hard): STACK 8176k/65520k , CORE 0k/infinity , NPROC 2666/4000 , NOFILE 12544/infinity , AS infinity/infinity , CPU infinity/infinity , DATA infinity/infinity , FSIZE infinity/infinity , MEMLOCK infinity/infinity , RSS infinity/infinity
load average: 3.59 4.80 4.81

CPU: total 8 (initial active 8) 0x61:0x0:0x1b588bb3:0, fp, asimd, aes, pmull, sha1, sha256, crc32, lse, sha3, sha512
machdep.cpu.brand_string:Apple M1
hw.cachelinesize:128
hw.l1icachesize:131072
hw.l1dcachesize:65536
hw.l2cachesize:4194304

Memory: 16k page, physical 16777216k(136016k free), swap 11534336k(1269760k free)

vm_info: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109) for bsd-aarch64 JRE (21.0.6+9-b895.109), built on 2025-03-26 by "builduser" with clang Apple LLVM 14.0.3 (clang-1403.0.22.14.1)

END.
