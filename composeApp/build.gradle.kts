import org.gradle.internal.classpath.Instrumented.systemProperty
import org.jetbrains.compose.desktop.application.dsl.TargetFormat
import org.jetbrains.compose.reload.ComposeHotRun
import org.jetbrains.kotlin.compose.compiler.gradle.ComposeFeatureFlag

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.jetbrainsCompose)
    alias(libs.plugins.compose.compiler)
    kotlin("plugin.serialization") version "2.0.20"
    id("org.jetbrains.compose.hot-reload") version "1.0.0-alpha11"
}

tasks.withType<Test> {
    useJUnitPlatform()
}

// set for hot reload
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(21))
    }
}

kotlin {
    // set for hot reload
    jvmToolchain(21)
    jvm("desktop")

    sourceSets {
        val desktopMain by getting
        // https://github.com/mikepenz/multiplatform-markdown-renderer/releases
        val markdownRendererVersion = "0.32.0-b01"
        val ktorVersion = "3.0.1"
        val exposedVersion = "0.52.0"
        val colorMathVersion = "3.6.0"

        commonTest.dependencies {
            implementation(libs.junit.jupiter)
            implementation("io.strikt:strikt-jvm:0.34.0")
        }

        commonMain.dependencies {
            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation(compose.material)
            implementation(compose.ui)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)
            implementation("io.sellmair:evas:1.2.0")
            implementation("io.sellmair:evas-compose:1.2.0")

            implementation("org.jetbrains.exposed:exposed-core:$exposedVersion")
            implementation("org.jetbrains.exposed:exposed-dao:$exposedVersion")
            implementation("org.jetbrains.exposed:exposed-jdbc:$exposedVersion")
            implementation("org.xerial:sqlite-jdbc:********")

            implementation("org.jetbrains.jediterm:jediterm-core:3.51")
            implementation("com.composables:icons-lucide:1.0.0")
            implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.7.3")
            implementation("com.github.pgreze:kotlin-process:1.5")
            implementation("io.github.kotlin-telegram-bot.kotlin-telegram-bot:telegram:6.1.0")
            implementation("com.mikepenz:multiplatform-markdown-renderer-m2:${markdownRendererVersion}")
            implementation("com.mikepenz:multiplatform-markdown-renderer-code:${markdownRendererVersion}")
            // https://github.com/javalin/javalin
            // for versions
            implementation("io.javalin:javalin:6.3.0")
            implementation("org.slf4j:slf4j-simple:2.0.11")
            implementation("com.sun.mail:javax.mail:1.6.2")
            implementation("com.prof18.rssparser:rssparser:6.0.8")
            implementation("org.jsoup:jsoup:1.18.1")
            implementation("uk.co.caprica:vlcj:4.8.3")
            // Ktor is used because Fuel incorrectly returns response headers. In Fuel, only the first
            // character is returned for each response header which seems to be a bug
            implementation("io.ktor:ktor-client-core:$ktorVersion")
            implementation("io.ktor:ktor-client-cio:$ktorVersion")
            implementation("com.github.ajalt.colormath:colormath:$colorMathVersion")
            implementation("com.github.ajalt.colormath:colormath-ext-jetpack-compose:$colorMathVersion")
            // add WEBP support for images in HTML emails
            implementation("com.twelvemonkeys.imageio:imageio-webp:3.12.0")

            // see all available here:
            // https://fonts.google.com/icons
            // use like this: Icons.Default.Folder
            implementation("org.jetbrains.compose.material:material-icons-extended-desktop:1.6.11")
            implementation("dev.tclement.fonticons:core:1.4.1")
            implementation("dev.tclement.fonticons:font-fa-brands:1.4.1")

            implementation("com.twelvemonkeys.imageio:imageio-webp:3.12.0")
            // this is for images and some video files, but not mp3
            implementation("com.drewnoakes:metadata-extractor:2.19.0")
            implementation("net.jthink:jaudiotagger:3.0.1")
            implementation("org.bytedeco:javacv-platform:1.5.11")
            implementation("com.github.Keelar:ExprK:91fdabf")
            implementation("org.zeroturnaround:zt-exec:1.12")
            implementation("com.github.burnett01:kotlin-expression-builder:1.2.2")

            implementation("com.google.api-client:google-api-client:2.0.0")
            implementation("com.google.oauth-client:google-oauth-client-jetty:1.34.1")
            implementation("com.google.apis:google-api-services-calendar:v3-rev20220715-2.0.0")

            // see https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-java
            // for versions
            implementation("org.seleniumhq.selenium:selenium-java:4.33.0")
        }
        desktopMain.dependencies {
            implementation(compose.desktop.currentOs)
        }
    }
}

tasks.withType<ComposeHotRun>().configureEach {
    mainClass.set("MainKt")
    javaLauncher.set(javaToolchains.launcherFor {
        languageVersion.set(JavaLanguageVersion.of(21))
    })
}

composeCompiler {
    featureFlags.add(ComposeFeatureFlag.OptimizeNonSkippingGroups)
}

compose.desktop {
    application {
        mainClass = "MainKt"

        // disable version logging in static code block
        systemProperty("ice.pdf.core.log.level", "OFF")

        nativeDistributions {
//            targetFormats(TargetFormat.Dmg, TargetFormat.Msi, TargetFormat.Deb)
            targetFormats(TargetFormat.Dmg)
            macOS {
                iconFile.set(project.file("icon.png"))
            }
            packageName = "Kotlin Emacs"
            packageVersion = "1.0.0"
        }
    }
}
