## in the vero log app
## AiSinglePromptApp
## dark mode
## generic dialog
## notifications
## Network
## Feeds
## AI
## Process List
## Transcribe App
## Email
## ai chat dialog
## dired copy move dialog
## Dired

in regular dired html preview the text needs more right padding to the scrollbar when right side is focused


find file dialog json file preview has incorrect scrollbar color at right preview

in diredapp
a directory with 5000x3000 image files is just awfully slow,
it happens often that the thumbnails stay blank for multiple seconds
this is on keeping key.m or key.v held for a bit to scroll over multiple files quickly
any way to improve that?

even in Emacs that is faster

fix broken keys ctmv for dired pdf preview, it does not scroll at all
it is probably related to LazyScrollVelocity, it is also buggy in vero log app

The Dired PDF preview is using LazyScrollVelocity, but it is fully broken, remove it LazyScrollVelocity and instead always use 
composeApp/src/desktopMain/kotlin/dima/utils/ScrollVelocity.kt

add some option, maybe in transient?
to not reset scroll state in right side preview for all files, this can be then used to compare PDF files
maybe this does not work for pdf files though?

## Calendar

in calendarapp.kt
in w34, fix slight vertical visual gaps across Vero halber Urlaub
it always look good apart from days where there are no events when the Vero halber Urlaub is in lane #2
investigate private fun EmptyLaneSpace() { height
both 6.dp or 7.dp are not correct
don't use .sp here, instead use .dp with a fixed height value, start with 25.dp, I will decrease it visually
.height(CalendarAppUiStyling.leftEventFontSize.value.dp + 6.dp) // Match exact event heigh

## audio player
## Telegram

port my python to https://github.com/Lonami/grammers, is https://github.com/FedericoBruzzone/tgt of any value?
benchmark once, for fastest performance a server should be used, then responses will be very fast, but this is for later

I only need the chat/group fetching + file attaching
just a read-only view for now with media display and 'infinite scrollback'

## panes

There is a pane implementation.
On clicking the other inactive pane (when there are at least 2), shifts focus to the pane and then I see the `Lost Focus` overlay incorrectly? Can you add debug logging to find out the issue.

weirdly, the tailwindcss can only modify bg color once?

once panes are fully done, make dired picker to browse to external volume, for copy/move

## Inline Text Editor

how to create standalone @Composable TextEditorInline() which has the same keybindings?
And it maybe has only 1 Text with newlines in it?

how to make a standalone version which can be embedded inside VineApp search and in text dialog?
textEditorMode/globalEvent is interesting
settings:

- single line which always disables line numbers and search
- show line numbers

## Text Editor App

how to get the current text editor text to save it? in saveCurrentContentToDatabase()

how to implement search? I think like swiper is best, 1/2 Searcher
display line numbers and use textmarked?

add tabs and store state in sqlite, designate if buffer belongs to file - every space t n should open a new buffer+tab
with clipboard, like in emacs

how to load first buffer from database?

implement column goal for line down on empty/short lines, this is only for keys c, t, m and v

do I need columnGoalChanged in code?

in visual line mode, the anchor also needs to be moved

implement first directionUp, then implement move line down, consider visual mode as well for directiondown

### visual mode

something here is broken, hit key.y, then key.n to highlight the current lines, multiple ones will be highlighted

can visualModeBackground() be re-used inside the readonly dialog?

### read only dialog

implement insert mode, maybe directly into this?

### ideas

how about https://languagetool.org/http-api/ API?  https://dev.languagetool.org/public-http-api

