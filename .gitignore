# WebDriver stuff
composeApp/MediaDeviceSalts
composeApp/MediaDeviceSalts-journal
composeApp/jcef-bundle/
composeApp/kcef-bundle/

# the custom settings, without it, it does not compile
composeApp/src/desktopMain/kotlin/dima/CustomSettings.kt

composeApp/selenium
composeApp/state.json
composeApp/cache

*.iml
.kotlin
.gradle
**/build/
xcuserdata
!src/**/build/
local.properties
.idea
.DS_Store
captures
.externalNativeBuild
.cxx
*.xcodeproj/*
!*.xcodeproj/project.pbxproj
!*.xcodeproj/xcshareddata/
!*.xcodeproj/project.xcworkspace/
!*.xcworkspace/contents.xcworkspacedata
**/xcshareddata/WorkspaceSettings.xcsettings

.qodo
.aider*
