[versions]
# see https://github.com/JetBrains/compose-multiplatform for versions
# https://plugins.gradle.org/plugin/org.jetbrains.compose
# https://github.com/JetBrains/compose-multiplatform/releases
compose-plugin = "1.8.1"
kotlin = "2.1.21"
junit-jupiter = "5.8.1"

[libraries]
junit-jupiter = { group = "org.junit.jupiter", name = "junit-jupiter", version.ref = "junit-jupiter" }

[plugins]
jetbrainsCompose = { id = "org.jetbrains.compose", version.ref = "compose-plugin" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlinMultiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }