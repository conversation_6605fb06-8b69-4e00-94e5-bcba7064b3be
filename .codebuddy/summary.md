# Kotlin Emacs Project Summary

## Overview
Kotlin Emacs is a project aimed at recreating the text editing capabilities of Emacs while introducing modern graphical capabilities through the Jetpack Compose framework using Kotlin. The project seeks to provide support for complex applications such as email clients and interfaces for large language models (LLMs) while maintaining the simplicity and efficiency of text editing.

## Languages, Frameworks, and Main Libraries Used
- **Programming Language**: Kotlin
- **Framework**: Jetpack Compose
- **Main Libraries**:
  - `org.jetbrains.kotlinx:kotlinx-serialization-json`
  - `io.ktor:ktor-client-core`
  - `io.ktor:ktor-client-cio`
  - `org.jetbrains.exposed:exposed-core`
  - `org.jetbrains.exposed:exposed-dao`
  - `org.jetbrains.exposed:exposed-jdbc`
  - `org.jetbrains.exposed:exposed-kotlin-datetime`
  - `com.mikepenz:multiplatform-markdown-renderer`
  - `com.sun.mail:javax.mail`
  - `org.seleniumhq.selenium:selenium-java`
  - `uk.co.caprica:vlcj`
  - `com.twelvemonkeys.imageio:imageio-webp`
  - `com.github.burnett01:kotlin-expression-builder`

## Purpose of the Project
The purpose of Kotlin Emacs is to enhance the traditional text editing experience provided by Emacs by integrating modern GUI capabilities, allowing users to build more complex applications while leveraging Kotlin's features and Jetpack Compose's ease of use.

## Build and Configuration Files
- **Main Build File**: 
  - `/Users/<USER>/Developer/kotlin-emacs/build.gradle.kts`
- **Compose App Build File**: 
  - `/Users/<USER>/Developer/kotlin-emacs/composeApp/build.gradle.kts`
- **Gradle Wrapper Properties**: 
  - `/Users/<USER>/Developer/kotlin-emacs/gradle/wrapper/gradle-wrapper.properties`
- **Settings File**: 
  - `/Users/<USER>/Developer/kotlin-emacs/settings.gradle.kts`
- **Gradle Properties**: 
  - `/Users/<USER>/Developer/kotlin-emacs/gradle.properties`
- **Version Management**: 
  - `/Users/<USER>/Developer/kotlin-emacs/gradle/libs.versions.toml`

## Source Files Location
Source files can be found in the following directories:
- **Common Resources**: 
  - `/Users/<USER>/Developer/kotlin-emacs/composeApp/src/commonMain/composeResources`
- **Desktop Main Source Files**: 
  - `/Users/<USER>/Developer/kotlin-emacs/composeApp/src/desktopMain/kotlin`
- **Desktop Test Files**: 
  - `/Users/<USER>/Developer/kotlin-emacs/composeApp/src/desktopTest/kotlin`

## Documentation Files Location
Documentation files are located in the following paths:
- **Main Documentation**: 
  - `/Users/<USER>/Developer/kotlin-emacs/README.md`
- **Compose App Documentation**: 
  - `/Users/<USER>/Developer/kotlin-emacs/composeApp/README.md`

This summary provides an extensive overview of the Kotlin Emacs project, outlining its structure, purpose, and key components.