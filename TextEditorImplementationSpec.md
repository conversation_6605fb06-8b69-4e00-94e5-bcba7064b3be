# Full Text Editor Implementation Spec

This document outlines the complete implementation specification for a keyboard-driven text editor with modal editing
capabilities.

## Modes

The editor supports four distinct modes of operation:

- **Command Mode**: Default mode for navigation and executing commands. Cursor appears as a box.
- **Insert Mode**: For text input and editing. Cursor appears as a vertical bar.
- **Visual Mode**: For selecting text by character. Selection is highlighted with a blue background.
- **Visual Line Mode**: For selecting entire lines. Behaves like visual mode but highlights entire lines.

## Keymap

### Navigation

| Key | Function                 |
|-----|--------------------------|
| c   | Go to previous line      |
| t   | Go to next line          |
| h   | Go to previous character |
| n   | Go to next character     |
| d   | Jump to start of line    |
| s   | Jump to end of line      |
| g   | Jump to previous word    |
| r   | Jump to next word        |
| m   | Jump 6 lines down        |
| v   | Jump 6 lines up          |

### Editing

| Key     | Function                            |
|---------|-------------------------------------|
| e       | Delete character to left of cursor  |
| (       | Delete character to right of cursor |
| f       | Undo                                |
| shift-f | Redo                                |
| escape  | Switch to command mode              |

### Mode Switching

| Key    | Function             |
|--------|----------------------|
| u      | Enter insert mode    |
| y      | Enter visual mode    |
| escape | Exit to command mode |

### Special Functions

| Key | Function                                                           |
|-----|--------------------------------------------------------------------|
| l   | Initiate jump hints                                                |
| j   | Copy selection (in visual modes) or current line (in command mode) |
| k   | Paste text                                                         |
| q   | Delete current line or selection                                   |

### Space-Prefixed Commands

| Key Sequence | Function                                  |
|--------------|-------------------------------------------|
| space-v      | Enter visual line mode                    |
| space-w-h    | Duplicate current line                    |
| space-i      | Kill to end of line                       |
| space-q      | Kill all text in editor                   |
| space-j      | Copy all text in editor                   |
| space-b      | Scroll to bottom                          |
| space-w-j    | Join line to previous                     |

## Jump Hints

The jump hint system provides a quick way to navigate to specific positions in the text without using cursor movement
keys:

1. When activated with the **l** key, the editor enters `WaitingForFirstCharToJumpTo` mode
2. The user types a character they want to jump to
3. The editor highlights all occurrences of that character in the visible text
4. If multiple occurrences exist, each is labeled with a hint character from the set: C, D, E, G, H, J, K, M, N, O, P,
   Q, R, T, U, V, W, X
5. The user presses the corresponding hint character to jump directly to that position
6. If only one occurrence exists, the cursor jumps there immediately

This system is inspired by the Vimium browser extension and provides efficient navigation through text.

## Undo System

The editor maintains a comprehensive action-based undo/redo history:

- Each editor action is tracked in the undo history as a specific action type with parameters
- The `TextEditorUndoManager` maintains a list of `EditorAction` objects
- Actions include operations like `MoveToStartOfLine`, `PasteText(text)`, `DeleteCharacterLeft`, etc.
- Each action knows how to apply itself and how to create a reverse action for undo
- Pressing **f** moves backward in the history by applying reverse actions (undo)
- Pressing **shift-f** moves forward in the history by reapplying actions (redo)
- The undo system preserves the exact state of each operation
- New edits after an undo operation clear the redo history

### Supported Action Types

| Action                 | Parameters                   | Description                         |
|------------------------|------------------------------|-------------------------------------|
| `MoveToStartOfLine`    | `fromPosition`               | Move cursor to start of line        |
| `MoveToEndOfLine`      | `fromPosition`, `lineLength` | Move cursor to end of line          |
| `MoveCursorTo`         | `position`                   | Move cursor to specific position    |
| `MoveToNextLine`       | `fromPosition`               | Move cursor to next line            |
| `MoveToPreviousLine`   | `fromPosition`               | Move cursor to previous line        |
| `MoveToNextWord`       | `fromPosition`               | Move cursor to next word            |
| `MoveToPreviousWord`   | `fromPosition`               | Move cursor to previous word        |
| `InsertText`           | `text`, `position`           | Insert text at position             |
| `DeleteText`           | `length`, `position`         | Delete text of specified length     |
| `PasteText`            | `text`, `position`           | Paste text at position              |
| `DeleteCharacterLeft`  | `position`                   | Delete character to left of cursor  |
| `DeleteCharacterRight` | `position`                   | Delete character to right of cursor |
| `DeleteLine`           | `lineNumber`                 | Delete entire line                  |
| `JoinLines`            | `lineNumber`                 | Join current line with next line    |
| `InsertNewLine`        | `position`                   | Insert a new line at position       |
| `DuplicateLine`        | `lineNumber`                 | Duplicate the current line          |
| `ToggleCase`           | `range`                      | Toggle case of text in range        |

## Colors and Visual Styling

| Element                           | Style                                                                               |
|-----------------------------------|-------------------------------------------------------------------------------------|
| Command mode cursor               | Box with gray border (TailwindCssColors.gray300)                                    |
| Insert mode cursor                | Vertical bar with orange border (TailwindCssColors.orange400)                       |
| Visual mode selection             | Blue highlight (TailwindCssColors.blue300 in light mode, custom color in dark mode) |
| Visual mode cursor                | Box with blue border (TailwindCssColors.blue400)                                    |
| Jump hint characters              | Green border (TailwindCssColors.green500)                                           |
| Line numbers                      | Gray for regular lines, highlighted for current line                                |
| Selected line in visual line mode | Blue background (TailwindCssColors.blue600)                                         |

## Implementation Notes

- Text is stored as a list of `Line` objects, each with text content and line number
- The cursor position is represented by a `Position` object with line number and column
- Visual selections track both the cursor position and an anchor position
- Jump hints are implemented using the `CharJumperEntry` class
- The editor uses Jetpack Compose for rendering and handles keyboard input through the `onPreviewKeyEvent` modifier
- Text styling is centralized in the `TextEditorStyling` object
- The undo system is implemented using the action-based pattern with the `EditorAction` hierarchy
- Each editor operation creates a specific action object that is recorded in the undo history
- Actions know how to apply themselves and create reverse actions for undo operations
- The `EditorState` class encapsulates the current state of the editor for action application
- Editor operations are mapped to specific action types in the key handlers
- Complex operations create and record multiple actions in sequence
- The undo manager provides methods to record actions, undo, and redo
- When an action is recorded, it's immediately applied and the result is used to update the editor
