# Technical Context

## Technology Stack
- **Language**: <PERSON><PERSON><PERSON>
- **UI Framework**: Compose Multiplatform
- **Testing Framework**: JUnit with Strikt assertions
- **HTTP Server**: Javalin
- **Concurrency**: Kotlin Coroutines
- **State Management**: MutableState & Mutex
- **Build System**: Gradle
- **Database**: SQLite (Exposed)
- **Code Highlighting**: Tree-Sitter
- **API Integration**: Ktor Client
- **Date/Time**: java.time.* with custom utilities

## Testing Framework
- JUnit for test execution and lifecycle management
- Strikt assertion library for expressive assertions
  - All tests must use `import strikt.api.expectThat`
  - Provides fluent assertion API with clear error messages
- Tests are organized by feature/component in `composeApp/src/desktopTest/kotlin/dima/`
- Test file naming convention: `*Test.kt`
- Clear test method naming using descriptive patterns:
  - Function-based: `test function name()`
  - Behavior-based: `verify behavior description()`
  - Given-When-Then: `given condition when action then result()`

### Test Organization
- Tests mirror main source structure
- Apps tests in `apps/` directory
- Utility tests in `utils/` directory
- Integration tests for external services
- Dialog system tests in `dialogs/` directory

### Example Test Structure
```kotlin
class DateTimeFormatTest {
    @Test
    fun `test UTC timezone`() {
        val start = LocalDateTime.now(ZoneId.systemDefault())
        val inUtc = start.atZone(ZoneId.systemDefault())
            .withZoneSameInstant(ZoneId.of("UTC"))
            .format(DateTimeFormat.isoDateTimeWithTandZ)
            
        expectThat(inUtc)
            .isNotEqualTo(localTimeZone)
    }
}
```

## Core System Components

### Tailwind CSS Colors
The `TailwindCssColors` object provides a complete implementation of the Tailwind CSS color palette in Compose Color format. Key features:

- Fundamental colors:
  - `white`: Pure white (0xFFFFFFFF)
  - `black`: Pure black (0xFF000000)
  - `transparent`: Fully transparent (0x00000000)
- All standard Tailwind colors from 50 to 950 shades
- Additional semantic colors for UI elements
- Colors are defined as ARGB hex values
- Commonly used for:
  - Text editor styling
  - Notification backgrounds
  - Selection states
  - Visual indicators

Example usage:
```kotlin
val backgroundColor = TailwindCssColors.gray100
val errorColor = TailwindCssColors.red600
val selectedColor = TailwindCssColors.blue200
val whiteText = TailwindCssColors.white
val clearOverlay = TailwindCssColors.transparent
```

### Date and Time Stack
```kotlin
// Core java.time.* types used
import java.time.LocalDateTime
import java.time.LocalDate
import java.time.LocalTime
import java.time.Duration
import java.time.Period

// Custom utilities
import dima.dateTime.DateTimeFormat      // Predefined format patterns
import dima.dateTime.DateTimeUtils       // Common operations
import dima.dateTime.DateTimeFormatterBuilder  // Type-safe format creation
```

### DateTimeFormatterBuilder
The DateTimeFormatterBuilder class provides a type-safe DSL for building custom date/time formatters. Key features:

```kotlin
class DateTimeFormatterBuilder {
    // Builder methods
    fun isoDate() // Adds ISO date format (yyyy-MM-dd)
    fun hour() // Adds 24-hour format (00-23)
    fun minute() // Adds minutes (00-59)
    fun second() // Adds seconds (00-59)
    fun secondFraction(digits: Int) // Adds fractional seconds
    fun year() // Adds 4-digit year
    fun monthNumber() // Adds 2-digit month (01-12)
    fun monthName() // Adds full month name
    fun dayOfMonth() // Adds 2-digit day of month (01-31)
    fun dayOfMonthNoPadding() // Adds day of month without padding (1-31)
    fun monthNameAbbrev() // Adds abbreviated month name
    fun char(c: Char) // Adds a single literal character
    fun chars(s: String) // Adds a literal string
    fun hourMinuteSecondsSeperatedByColon() // Adds time in HH:mm:ss format
    fun build(): DateTimeFormatter // Builds the formatter
}

// Usage example
val formatter = dateTimeFormat {
    isoDate()
    chars("T")
    hourMinuteSecondsSeperatedByColon()
    secondFraction(3)
    chars("Z")
}
```

Key Features:
- Type-safe date/time operations
- German localization support
- ISO and human-readable formats
- Duration and period calculations
- Relative time formatting

### File Organization
- All application code lives in composeApp/src/desktopMain/kotlin/dima/
- Apps are organized in their own packages under apps/
- Dialog system components are in dialogs/
- Common utilities and helpers are in utils/

### Code Style
- Follow Kotlin coding conventions  
- Use proper coroutine scoping  
- Implement clean separation of concerns  
- Document thread safety considerations  
- Keep related functionality in same package  
- Use clear descriptive file names  
- Follow established package structure  
- Maintain clean architecture principles  
- Format modifier chains with each operation on a new line, placed at the end of parameter lists:
```kotlin
Box(
    modifier = Modifier
        .fillMaxSize()
        .padding(8.dp)
        .background(Color.White)
) {
    // content
}

FileList(
    modifier = Modifier
        .weight(1f)
)
```

### 1. App Management
```kotlin
enum class AppType(val displayName: String) {
    ChatGpt("ChatGPT")
    Dired("Dired")
    Calculator("Calculator")
    Calendar("Calendar")
    AmazonVine("Amazon Vine")
    Email("Email")
    // ... more apps
}

object AppRing {
    private val history = Ring<AppType>(30)

    fun remember(app: AppType)
    fun switchToPrevious()
    fun toList(): List<AppType>
}
```

### 2. Global Events
```kotlin
enum class GlobalEvent {
    ScrollToTop
    ScrollToBottom
    Reload
    ReloadData
    KillAll
    // ... more events
}

var globalEvent by mutableStateOf<GlobalEvent?>(null)
}
```

### 3. Thread Safety Mechanisms
```kotlin
/**
 * Mutex for synchronizing global chat state updates.
 */
private val stateMutex = Mutex()

/**
 * Mutex for synchronizing prompt updates from the Javalin server.
 */
private val promptMutex = Mutex()

// Thread-safe state updates
internal suspend fun updateCardState(model: String, state: ChatGptCardState) {
    stateMutex.withLock {
        chatGptState = chatGptState.toMutableMap().apply {
            this[model] = state
        }
    }
}
```

### 4. Network Activity System
```kotlin
object NetworkActivity {
    // Thread-safe network activity logging
    internal var entries by mutableStateOf(listOf<Entry>())
    private val semaphore = Semaphore(1)

    data class Entry(
        val id: Int,
        val method: RequestMethod?,
        val url: String?,
        val requestHeaders: StringValues,
        val responseHeaders: StringValues,
        val requestBody: String?,
        val responseBody: String?,
        val statusCode: Int?,
        val startDate: LocalDateTime,
        val endDate: LocalDateTime?
    )
}

// HTTP Client Extensions
suspend fun HttpClient.postLogged(url: String, request: HttpRequestBuilder.() -> Unit = {}): HttpResponse {
    // Creates NetworkActivity entry
    // Executes request with logging
    // Updates entry with response
    // Handles exceptions
}
```

### 5. Notification System
```kotlin
// Notification Types
enum class NotificationType {
  Info,
  Error,
  Loading
}

// Thread-safe notification management
class LoadingNotification(val id: Long) {
  fun toInfo(title: String? = null, message: String? = null, durationMillis: Long = 3000)
  fun toError(title: String? = null, message: String? = null, durationMillis: Long = 3000)
  fun update(title: String? = null, message: String? = null)
  fun dismiss()
}

// Notification Creation
fun showLoadingNotification(title: String, message: String? = null): LoadingNotification
fun showNotification(title: String, message: String? = null, durationMillis: Long? = 3000)
fun showErrorNotification(title: String, message: String? = null)
```

### 6. Dialog System
```kotlin
object DialogUtils {
    fun openDialog(content: @Composable () -> Unit)
    fun closeDialog()
    fun closeAllDialogs()
}

// Dialog Types
fun openCompletionDialog(title: String, candidates: List<String>, onSelect: (String) -> Unit)
fun openConfirmationDialog(title: String, onConfirm: () -> Unit)
fun openTextInputDialog(title: String, onInput: (String) -> TextInputDialogConfirmAction)
```

## Development Patterns

### 1. State Management Pattern
```kotlin
class GlobalState {
    var app: AppType
    var chatGpt: ChatGptState
    var dired: DiredState
    var calculator: CalculatorState
    // ... more app states
}
```

### 2. Coroutine Management
1. LaunchedEffect Placement
- Always place LaunchedEffect at the very bottom of a @Composable function

2. Job Tracking
```kotlin
var currentJob by remember { mutableStateOf<Job?>(null) }
currentJob?.cancel() // Cancel existing
currentJob = coroutineScope.launch { /* new work */ }
```

3. Continuous Loops
```kotlin
LaunchedEffect(Unit) {
    filePreviewScrollVelocity.loopForeverAndTick() 
}
```

- Use `LaunchedEffect(Unit)` for coroutines that should run for the lifetime of the composable
- Place it at the bottom of the composable function
- Use for continuous operations like animation loops, scroll velocity tracking, or periodic updates

2. Thread Safety
```kotlin
suspend fun threadSafeOperation() {
    mutex.withLock {
        // Atomic operations
    }
}
```

### 3. App Integration Pattern
1. State Definition
```kotlin
data class AppState(
    val config: Config,
    val selectedItem: String?,
    val history: List<String>
)
```

2. Event Handling
```kotlin
LaunchedEffectGlobalEventForApps {
    when (globalEvent) {
        GlobalEvent.Reload -> { /* handle */ }
        GlobalEvent.ScrollToTop -> { /* handle */ }
        else -> { }
    }
}
```

3. Dialog Integration
```kotlin
fun openAppDialog() {
    openCompletionDialog(
        title = "Select Option",
        candidates = listOf("Option 1", "Option 2"),
        onSelect = { selected -> /* handle selection */ }
    )
}
```

### 4. Network Activity Monitoring
1. Request Logging
```kotlin
// Automatic logging of HTTP requests
client.postLogged("https://api.example.com") {
    setBody(requestData)
    bearerAuth(token)
}
```

2. Activity Tracking
```kotlin
NetworkActivity.entries.filter { entry ->
    entry.statusCode == 200 &&
    entry.method == RequestMethod.POST
}
```

3. Network Status Updates
```kotlin
entry.updateWithResponse(
    responseBody = response,
    statusCode = 200,
    responseHeaders = headers
)
```

### 5. Error Handling
1. User Feedback
```kotlin
showErrorNotification("Error Title", "Error Message")
showLoadingNotification("Loading...")
showNotification("Success", durationMillis = 3000)
```

2. State Recovery
```kotlin
try {
    // Operation
} catch (e: Exception) {
    updateCardState(model, CardState.Failed)
    showErrorNotification("Operation Failed", e.message)
}
