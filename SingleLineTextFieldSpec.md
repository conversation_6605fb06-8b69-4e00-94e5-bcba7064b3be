# Single Line Text Field Specification

This document outlines the specification for a simplified single-line text input field component that ignores line
breaks and doesn't include jump hints or visual line features.

## Core Requirements

- **Single Line Only**: The text field should only allow a single line of text
    - Enter key presses should be ignored or captured for other actions
    - Any attempt to insert newline characters should be prevented
    - Text with newlines should have them filtered out

- **No Jump Hints**: Unlike the full text editor, this component should not include jump hint functionality

- **No Visual Line**: The component should not include visual line selection or highlighting

## Component Interface

```kotlin
@Composable
fun SingleLineTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String? = null,
    placeholder: String? = null,
    modifier: Modifier = Modifier,
    requestFocus: Boolean = false,
    onEnterPressed: (() -> Unit)? = null
)
```

### Parameters

| Parameter        | Type               | Description                                                   |
|------------------|--------------------|---------------------------------------------------------------|
| `value`          | `String`           | The current text value                                        |
| `onValueChange`  | `(String) -> Unit` | Callback that provides updated text when the input changes    |
| `label`          | `String?`          | Optional label for the text field                             |
| `placeholder`    | `String?`          | Optional placeholder text                                     |
| `modifier`       | `Modifier`         | Modifier for the text field                                   |
| `requestFocus`   | `Boolean`          | Whether to request focus when the component is first composed |
| `onEnterPressed` | `(() -> Unit)?`    | Optional callback for when Enter key is pressed               |

## Advanced Version

An advanced version of the component should also be provided that uses `TextFieldValue` for more control over selection
and cursor position:

```kotlin
@Composable
fun SingleLineTextFieldWithValue(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    label: String? = null,
    placeholder: String? = null,
    modifier: Modifier = Modifier,
    requestFocus: Boolean = false,
    onEnterPressed: (() -> Unit)? = null
)
```

## Visual Styling

- The component should use minimal styling when no label is provided
- When a label is provided, it should use the standard TextField styling
- The component should respect the application's theme colors
- The text field should have rounded corners and a subtle shadow for depth

## Behavior

- **Enter Key**: When the Enter key is pressed, the `onEnterPressed` callback should be invoked if provided, and the key
  event should be consumed to prevent line breaks
- **Focus**: If `requestFocus` is true, the component should request focus when first composed
- **Newline Filtering**: Any newline characters in the input should be filtered out before calling `onValueChange`

## Implementation Notes

- Use `singleLine = true` property on the underlying TextField/BasicTextField
- Intercept key events to handle Enter key presses
- Filter out newline characters from input text
- Preserve selection and composition when filtering text in the TextFieldValue version
