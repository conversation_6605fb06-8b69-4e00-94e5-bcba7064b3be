# Text field and form design

https://uxdesign.cc/text-fields-forms-design-ui-components-series-2b32b2beebd0

# UI padding guidelines

- apps have `GlobalStyling.paddingToOuterWindow` padding left and right
- scrollbars in apps have `GlobalStyling.ScrollBar.outerPadding` to selected border

# Keybinding overview

Nested keymaps, both in Emacs and Kotlin Emacs:
Space c
Space n
Space h
Space t
Space w
Space - for Vero transient

## Top left hand row keys

; format buffer in Emacs ; UNBOUND in Kotlin Emacs
( delete character to right of cursor in Emacs ; UNBOUND in Kotlin Emacs
) un-/comment sexp/line in Emacs ; UNBOUND in Kotlin Emacs

## Top right hand row keys

- git transient in Emacs ; audio player in Kotlin Emacs

+ reload Dired or eval sexp in Emacs ; reload app/state in Kotlin Emacs  
  = open file in Kotlin Emacs from Emacs or eval sexp from minibuffer ; open file in Emacs from Kotlin Emacs

space - git transient in Emacs ; vero transient in Kotlin Emacs
space + eval buffer in Emacs
space = to open eshell in Emacs ; open kitty in Kotlin Emacs

# Kotlin Emacs

I really like Emacs, and it is near perfect in editing text, but it has no support for anything complex in graphical
capabilities, creating GUIs or complex layouts.
With Kotlin Emacs, I eventually hope to recreate my most often used textual editing capabilities from Emacs, while still
having the ability for more complex apps like Emails or LLM-interfaces (Emacs can also do them, but having proper
graphics is nice sometimes).

The Jetpack Compose framework is used which uses my favorite programming language, Kotlin. The framework has a trivial
way to
build up GUIs and handling of mutable state. With the help of its `TextMeasurer` class, the text editor can also be
implemented.

# Setup

Always start via `run` in IntelliJ for quick changes, since some code uses relative paths for the `cache` directory.

You need to create a `CustomSettings.kt` file in `kotlin/dima/CustomSettings.kt` with this content.
Modify the return `Settings.All()` object to set settings.

```kotlin
package dima

val settings = CustomSettings.init()

object CustomSettings {

    fun init(): Settings.All {
        return Settings.All()
    }
}
```

# Optional dependencies

Generally, Kotlin Emacs should still work, even if all dependencies here are missing.

- `kitty` for its `kitten` CLI to execute shell commands in a new tab
- `macos-app-switcher-by-pid` from https://github.com/Dima-369/macos-app-switcher-by-pid for fastest window switching
  performance, use it from other applications to quickly switch to Kotlin Emacs.
- `hs` binary from Hammerspoon for notifications and automations
- `chromedriver` and `geckodriver` for Instagram scraping and other things
- `ditto`, `tar` and `atool` for archive extraction in Dired
- `cmus` or `vlc` for audio playback (`cmus` is preferred which needs to be started in some terminal and continues
  playback in background)
- `magick` for rotating images
- `ffprobe` from `ffmpeg` for video info in Dired
- `ripgrep` (`rg` binary) to recursively find all files which is a lot faster than `ugrep`
- `ugrep` to recursively find text in files which supports `--bool AND` and is slightly faster than `ripgrep` in some
  circumstances
- `trash` to trash files and directories to macOS trash
- `dot` (from `Graphviz`) to render tree-sitter graphs
- `yt-dlp` for downloading YouTube audio and cover files
- `youtube-summary` for summarizing YouTube videos
  from https://github.com/Dima-369/rust-cli-youtube-video-transcript-summarizer-via-ai-llm
- `pdf2png` for PDF previews from https://github.com/Dima-369/PDF-to-PNG-via-Rust
- `gpt4free` from https://github.com/xtekky/gpt4free - run the server and set the base URL in `CustomSettings.kt` to
  access some LLM models for free

# Stable dialog size implementation

See `MiniHelpDialog.kt` for how to implement a dialog that has a stable size.

It is important to use `dialogPadding = null` and then apply padding after setting the fixed width and height, in the
same modifier.

# Big ideas

## Terminal via Jediterm

// TODO: Implement proper text drawing using TextMeasurer and drawText in Terminal.kt consume()

Your code fails to provide an **interactive pseudo-terminal (pty)** for the subprocess. That’s why a shell (like bash)
won’t act interactively: `ProcessBuilder` only gives you pipes, not a real terminal device. Many terminal-based programs
check *is this a tty?* (`isatty`) and enable/disable features accordingly.

**Solve this by using a PTY library.**  
For Java/Kotlin, try [JetBrains PTY4J](https://github.com/JetBrains/pty4j) (which is what jediterm expects for
interactive shells).

### Quick Fix

1. **Add dependency** (Gradle, for example):

   ```kotlin
   implementation("com.pty4j:pty4j:0.12") // or latest version
   ```

2. **Start your process using a PTY**:

   ```kotlin
   import com.pty4j.PtyProcess
   ```

   Replace your `ProcessBuilder` usage with:

   ```kotlin
   val env = mutableMapOf<String, String>()
   env.putAll(System.getenv())
   val process = PtyProcess.exec(command, env, null)  // 'null' means current working dir
   ```

   This `PtyProcess` is a true pseudo-terminal; your terminal emulator and the child process will communicate as real
   terminal.

3. **Pass PtyProcess** to your `ProcessTtyConnector` (should accept an Input/OutputStream; if it doesn’t, you may need
   small rewrites).

#### Example:

```kotlin
val process = PtyProcess.exec(command, env, null)
val ttyConnector = object : ProcessTtyConnector(process, Charset.defaultCharset()) {
    override fun getName(): String = "Compose Terminal"
}
```

That should let programs like `bash`, `ssh`, `htop`, etc. behave interactively.

---

**Summary:**  
`ProcessBuilder` is fine only for non-interactive (dumb) processes. For a real terminal, switch to PTY (PtyProcess from
pty4j). That’s how real terminal emulators work.

---

**References:**

- [jediterm/pty4j terminal demo](https://github.com/JetBrains/pty4j/blob/master/demo/ru/pty4j/ptydemo/Main.java)
- [Why shells don't behave interactively with ProcessBuilder](https://stackoverflow.com/questions/1401002/how-do-i-launch-an-interactive-shell-from-java)

If you want example code for exactly your use case, let me know!

## Others

- have read-only text editor dialog to quickly select text
- finish TextEditor implementation with buffers
- have 'mini' embeddable text editor and entirely replace `TextField` with it, so command mode is available everywhere

# Chars I keep losing, circle, enter/return, tab

•
⏎
⇥

# Making Cursor hidden like in Emacs on no interaction

Note that other inner elements usually set the click pointer, so this does not work everywhere.
I think setting it dynamically based on global mutable state should work, though!

```kotlin
            fun createBlankAWTCursor(): Cursor {
    val img = BufferedImage(16, 16, BufferedImage.TYPE_INT_ARGB)
    return Toolkit.getDefaultToolkit().createCustomCursor(img, Point(0, 0), "blank cursor")
}

val blankIcon = PointerIcon(
    createBlankAWTCursor(),
)

    // set on outer Column in main.kt
    .pointerHoverIcon(blankIcon)
```

# Network Library

## Ktor

Works and is great, but when a proxy is specified in the settings, there is a bug somewhere that all Vero network
requests 404 because some URL path is broken. It is not my code since the curl works.

## Fuel

I used this initially and while it was great, there is a bug that all response header names only have a single
character, not the full name, so it is not used.

# Free LLM (like ChatGPT) access

https://pollinations.ai/

https://cas.zukijourney.com

https://github.com/zukixa/cool-ai-stuff

https://github.com/xtekky/gpt4free

# PDF Preview Research

## My custom Rust application which converts PDFs to PNGs

It is the fastest and outputs plain PNGs without any display issues.

https://github.com/Dima-369/PDF-to-PNG-via-Rust

## PDFBox

It is painfully slow to generate any image, regardless of DPI and beefy MacBook.

## IcePDF

Faster than PDFBox and thus better, but spams the log, often about fonts or other things which is really annoying,
without any easy way to disable them since `Apache Common Logging` is used.
Additionally, the text is sometimes not rendering properly and overlaps.

# Date library decision

`kotlin/dima/utils/DateTimeUtils.kt` should be used for common helper methods.

At first, I used `kotlinx-datetime`, but I noticed that the AIs keep messing up all the time because the libraries share
the same class:

- `java.time.LocalDate`
- `kotlinx.datetime.LocalDate`

And I also keep messing them up, so it is easiest to just use `java.time.*` everywhere and have own helper functions.

# Regular expression builder

I like to use Kotlin DSLs to build up regular expressions. In this codebase, I decided to use:
https://github.com/Burnett01/kotlin-expression-builder

```kotlin
private val dateRegex: Regex = expression({
    start()
    capture {
        digit { exact(4) }
        literal('-')
        digit { exact(2) }
        literal('-')
        digit { exact(2) }
    }
})!!.compile()
```

# Audio Player Research

The audio player engine can be set in `CustomSettings.kt`.

## Rust

https://github.com/Dima-369/rust-cli-audio-player

In experimental stage, it will work like `cmus`, just with a lot less features.
Main changes to `cmus` are:

- status updates are pushed to Kotlin Emacs instead of Kotlin Emacs needing to poll
- no queue, only a single track
- when playback stops, it sends a `stopped` status update with `lastFile` set, so that Kotlin Emacs can play the next
  track
- uses a HTTP server, but might be rewritten to Unix sockets for better performance

This has been over 95% coded by AI agents like Jules from Google.

## cmus

https://github.com/cmus/cmus

`cmus` is a very good option, it plays pretty much any audio file and can be used stand-alone in the terminal. If this
audio
engine is used, `cmus-remote --query` is run every second (only takes a few milliseconds every time) to get the
current state.

To change the volume on `macOS`, `~/.config/cmus/autosave` needs to have `set softvol=true`, although it may
slightly degrade audio quality. I have not tested this, maybe the quality is not even affected.

## vlcj

Java framework for the vlc media player
https://github.com/caprica/vlcj

I used this for my first full audio player implementation.
The biggest issue I have with it, is that audio playback stops when Kotlin Emacs is not running since I recompile very
often.
And my subpar implementation launches `vlcj` on the main thread which freezes Kotlin Emacs for a few seconds initially.

## Other rejected

- `mpd` relies on a database location, from what I see, so it can not be used to play files in arbitrary file system
  locations
- `mopidy` has Python 3.X installation and run issues on my system

# tree-sitter for code highlighting

My first implementation used this library: https://github.com/seart-group/java-tree-sitter
It relies on a compiled `.dylib` (and custom changes in its `pom.xml`) and it worked fine, until I wanted to another
tree-sitter grammar. I failed in
compiling it myself or integrating my custom build into this code, so I decided to implement a Rust CLI wrapper which is
found here: https://github.com/Dima-369/tree-sitter-cli-via-rust

It needs to be cloned with all submodules and then set in `treeSitterCliDirectory` in `Settings.kt`.

## Info about string/byte handling

My `tree-sitter` CLI returns byte ranges for each capture.

Since the JVM has the string behavior shown below, the byte range needs to be converted to JVM String offsets, so that
`AnnotatedString.addStyle()` receives the correct offsets.

See `String.getStringLengthRangesFromByteRange()` for the full implementation of the conversion.

```kotlin
"val".length == 3

"😄".length == 2
Character.toString("😄".codePointAt(0)).toByteArray(Charsets.UTF_8).size === 4
```

## Info about the `.scm` files, like `hightlights.scm`.

https://zed.dev/docs/extensions/languages

See this for the silicon macOS `pom.xml` changes when compiling for silicon macOS.

# Stacked dialogs

Dialogs are created like this in `main.kt`.

```kotlin
dialogs.forEach { it.composable(it.id, it.data) }
```

Note that any state with `remember` inside those `Composable` dialog functions is remembered across multiple dialog
invocations!

Additionally, the `FocusManager` needs special care to know what element to focus on dialog closing.
See `ConfirmationDialog.kt` on how to restore focus on dialog changes.

# Font rendering

Apparently, the font rendering isn't the same as in IntelliJ, so using Avenir Next looks too thin and a bit corny
compared to IntelliJ. Maybe because it is loaded from a TTF? Since the default Roboto font looks quite good in Compose,
so I am just sticking with it.

I tried with Avenir Next Medium (500 weight) to see if it looks better since the default is too thin, and it is too bold
as well.

# Naming

- show... for dialogs
- open... for notifications

# (probably rejected) TextEditor ideas

I haven't tried out those below. My implementation is 100 % custom.

- try out https://github.com/Qawaz/compose-code-editor
- try out https://github.com/n34t0/compose-code-editor
